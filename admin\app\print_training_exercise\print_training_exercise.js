var app = angular.module('hkjflApp', ['ngRoute']);

app.controller('print_training_exerciseCtrl', function ($scope, $rootScope, $http, $routeParams, $location) {
    var url = $location.absUrl().split('?')[1];
    var arrayParameter = url.split("/");

    exercise_id = arrayParameter[0];
    lang = arrayParameter[1];

    $scope.parameterData = {
        'lang': lang,
        'exercises_id': exercise_id
    };

    $http({
        method: 'POST',
        url: SERVER_PATH + "training-scheme/getHTMLTrainingExercises",
        headers: {	
            'x-user-id': $rootScope.user_id,
            'x-user-email': $rootScope.user_name
        },
        data: $.param($scope.parameterData),
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    }).success(function (response) {
        if (response.status === 'OK') {
            $("#training_exercise_content").html(response.info);
        } else {
            BootstrapDialog.show({
                title: 'ERROR',
                type: BootstrapDialog.TYPE_WARNING,
                message: response.message
            });
        }
    });

    $scope.print = function () {
        $('#print-button').hide();
        window.print();
    }
})