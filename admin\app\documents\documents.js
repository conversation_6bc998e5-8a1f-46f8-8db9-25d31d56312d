app.controller(
  'documentsCtrl',
  function ($scope, $rootScope, $routeParams, $http) {
    $('#page-wrapper').removeClass('nav-small');
    var table;

    function getcropperHTML(src) {
      var cropperHTML =
        '<div class="cropper"><img style="height: 600px" class="js-avatar-preview" src="' +
        src +
        '"></div>';
      return cropperHTML;
    }

    var editor = new $.fn.dataTable.Editor({
      ajax: {
        type: 'POST',
        url: SERVER_PATH + 'document/setDocuments',
        headers: {	
          'x-user-id': $rootScope.user_id,
          'x-user-email': $rootScope.user_name
        },
        data: {},
        async: false,
        dataType: 'json',
        complete: function (response) {
          var jsonData = JSON.parse(response.responseText);
          // --- may need to reload
          if (DEVELOPMENT_ENVIRONMENT)
            console.log('status = ' + jsonData.status);
          if (jsonData.status == 'OK') {
            table.ajax.reload();
          }
        },
        error: function (xhr, status, error) {},
      },
      table: '#documentsTable',
      formOptions: {
        main: {
          onBlur: 'none',
        },
      },
      i18n: {
        create: {
          button: 'New',
          title: 'Create new documents',
          submit: 'Create',
        },
        edit: {
          button: 'Edit',
          title: 'Edit documents',
          submit: 'Update',
        },
        remove: {
          button: 'Delete',
          title: 'Delete documents',
          submit: 'Delete',
          confirm: {
            _: 'Are you sure you want to delete these documents?',
            1: 'Are you sure you want to delete this documents?',
          },
        },
        error: {
          system: 'System error, please contact administrator.',
        },
      },
      fields: [
        {
          label: 'Name',
          name: 'documents.name',
        },
        {
          label: 'Photo',
          name: 'documents.document_photo',
          type: 'upload',
          display: function (data) {
            return '<a class="openImage"><img src="' + UPLOAD_FILE_PATH + data + '" width="100%"></a>';
          },
          clearText: 'Clear',
          noImageText: 'No image',
        },
        {
          label: 'Description',
          name: 'documents.description',
          type: 'ckeditor',
        },
        {
          label: 'PDF',
          name: 'documents.document_file',
          type: 'upload',
          display: function (data) {
            return data;
          },
          noFileText: 'No files',
          clearText: 'Clear',
        },
        {
          label: 'order',
          name: 'documents.order',
          type: 'hidden',
        },
        {
          label: 'Created at',
          name: 'documents.created_at',
          type: 'hidden',
          def: function () {
            return moment().format('YYYY-MM-DD HH:mm:ss');
          },
        },
        {
          label: 'Status',
          name: 'documents.status',
          type: 'radio',
          options: [
            {
              label: 'Public',
              value: 1,
            },
            {
              label: 'Private',
              value: 0,
            },
          ],
          def: 1,
        },
      ],
    });

    var table = $('#documentsTable').DataTable({
      dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
      stateSave: true,
      deferRender: true,
      ajax: {
        url: SERVER_PATH + 'document/getDocuments',
        type: 'POST',
        headers: {	
          'x-user-id': $rootScope.user_id,
          'x-user-email': $rootScope.user_name
        },
        data: {},
        dataType: 'json',
        complete: function (response) {
          console.log(response);
        },
        error: function (xhr, status, error) {},
      },
      language: {
        paginate: {
          previous: '<i class="fa fa-chevron-left"></i>',
          next: '<i class="fa fa-chevron-right"></i>',
        },
      },
      columns: [
        {
          data: 'documents.order',
        },
        {
          data: 'documents.document_photo',
          className: 'avatar',
          orderable: false,
          render: function (data) {
            if (data !== null && data !== '') {
              return '<img src="' + UPLOAD_FILE_PATH + data + '">';
            } else {
              return '<img src="' + SYSTEM_IMAGE_PATH + 'favicon.png">';
            }
          },
        },
        {
          data: 'documents.name',
          sortable: false,
        },
        {
          data: 'documents.document_file',
          sortable: false,
          render: function (data, type, full, meta) {
            if (data != null) {
              return `<a href="javascript:window.open('${UPLOAD_FILE_PATH}${data}')">Open</a>`;
            }
            return '';
          },
        },
        {
          data: 'documents.created_at',
          className: 'center',
          sortable: false,
        },
        {
          data: 'documents.status',
          render: function (data) {
            return data == 1 ? 'Public' : 'Private';
          },
          sortable: false,
        },
        {
          data: null,
          render: function (data) {
            return `<a data-match-route="/documents/${data.documents.id}/${data.documents.name}" href="#/documents/${data.documents.id}/${data.documents.name}">Details</a>`;
          },
        },
      ],
      rowReorder: {
        dataSrc: 'documents.order',
        editor: editor,
      },
      select: {
        style: 'single',
        selector: 'td:not(:last-child)',
      },
      order: [[0, 'asc']],
      columnDefs: [
        {
          visible: false,
          target: [3],
        },
      ],
      lengthMenu: [
        [10, 25, 50, 100, -1],
        [10, 25, 50, 100, 'All'],
      ],
      buttons: [
        {
          extend: 'create',
          editor: editor,
        },
        {
          extend: 'edit',
          editor: editor,
        },
        {
          extend: 'remove',
          editor: editor,
        },
        {
          extend: 'colvis',
          text: 'Columns',
        },
      ],
    });

    // Open image
    $('#documentsTable').on('click', 'tbody td img', function (e, row) {
      console.log('Click on openImage');
      var $row = $(this).closest('tr');
      var data = table.row($row).data();
      console.log(data.documents);
      // show modal
      BootstrapDialog.show({
        title: data.documents.name,
        size: BootstrapDialog.SIZE_NORMAL,
        message: '<img src="' + UPLOAD_FILE_PATH + data.documents.document_photo + '" width="100%">',
      });
    });
  }
);
