app.controller('seasonsCtrl', function ($scope, $rootScope, $routeParams, $http) {
	$('#page-wrapper').removeClass('nav-small');

	// get info event
	var event_id = $routeParams.id;
	jQuery.ajax({
		type: 'POST',
		url: SERVER_PATH + "event/getEventInfo",
		async: false,
		headers: {	
			'x-user-id': $rootScope.user_id,
			'x-user-email': $rootScope.user_name
		},
		data: {
			"event_id": event_id
		},
		dataType: 'json',
		complete: function (response) {
			var jsonData = JSON.parse(response.responseText);
			var event = jsonData.info;
			event_name = event.name;
			event_type = event.type;
		}
	});
	console.log('paymentsCtrl - event_id, name, type  = ' + event_id + ', ' + event_name + ', ' + event_type);
	$scope.event_name = event_name;
	$scope.event_type = event_type;

	jQuery.ajax({
		type: 'POST',
		url: SERVER_PATH + "event/getGroupsByEvent",
		async: false,
		headers: {	
			'x-user-id': $rootScope.user_id,
			'x-user-email': $rootScope.user_name
		},
		data: {
			"event_id": event_id,
			"user_id": $rootScope.user_id
		},
		dataType: 'json',
		complete: function (response) {
			var jsonData = JSON.parse(response.responseText);
			if (jsonData.status == "OK") {
				$scope.groups = jsonData.info;
				$scope.selectedGroup = $scope.groups[0];
				// getGroup();
			}
		}
	});

	$('button').click(function (e) {
		getGroup();
	});

	function getGroup() {
		var group_id = $scope.selectedGroup.id;
		var group_name = $scope.selectedGroup.name;

		// initialize html
		var html = '';
		html += getGroupTeamsTableHtml(group_id);
		$('#seasonsPageContent').html(html);

		// initialize data	
		initGroupTeamsTable(group_id, group_name);
	}

	$scope.goBack = function () {
        window.history.back();
    };

	function getGroupTeamsTableHtml(group_id) {
		var str = '' +
			'<div class="row">' +
			'<div class="col-lg-12">' +
			'<div class="main-box clearfix">' +
			'<div class="main-box-body clearfix">' +
			'<div class="table-responsive">' +
			'<table id="tblGroupTeams_' + group_id + '" class="table table-striped table-bordered table-hover" cellspacing="0" width="100%">' +
			'<thead>' +
			'<tr>' +
			'<th></th>' +
			'<th>Team</th>' +
			'<th>Club</th>' +
			'<th>Chinese name</th>' +
			'</tr>' +
			'</thead>' +
			'</table>' +
			'</div>' +
			'</div>' +
			'</div>' +
			'</div>' +
			'</div>';
		return str;
	}

	function getTeamPlayersTableHtml(team_id) {
		var str = '' +
			'<div class="table-responsive">' +
			'<table id="tblTeamPlayers_' + team_id + '" class="table table-striped table-bordered table-hover" cellspacing="0" width="100%">' +
			'<thead>' +
			'<tr>' +
			'<th>Photo</th>' +
			'<th>Name</th>' +
			'<th>Year</th>' +
			'</tr>' +
			'</thead>' +
			'</table>' +
			'</div>';
		return str;
	}
	function getCreateTeamHtml() {
		var str = "<form method='get' id='addteam' action=''><div class='form-group'><label class='control-label'> Team Name </label><div><input type='text' class='form-control input-lg' id='team_name' name='team_name'></div></div><div class='form-group'></form><p>please separate the two team names with a ','  </p>";
		return str;
	}
	function initGroupTeamsTable(group_id, group_name) {
		var editorGroupTeams = new $.fn.dataTable.Editor({
			ajax: {
				type: 'POST',
				url: SERVER_PATH + "team/setGroupTeams",
				headers: {	
					'x-user-id': $rootScope.user_id,
					'x-user-email': $rootScope.user_name
				},
				data: {
					"group_id": group_id,
					"event_id": event_id,
					"user_id": $rootScope.user_id,
				},
				dataType: 'json',
				complete: function (response) {
				},
				error: function (xhr, status, error) {
				},
			},
			table: '#tblGroupTeams_' + group_id,
			formOptions: {
				main: {
					onBlur: 'none'
				}
			},
			i18n: {
				create: {
					button: "Add",
					title: "Add new team",
					submit: "Add"
				},
				edit: {
					button: "Edit",
					title: "Edit team",
					submit: "Save"
				},
				remove: {
					button: "Delete",
					title: "Delete team",
					submit: "Delete"
				},
				error: {
					system: "System error, please contact administrator."
				},
			},
			fields: [
				{
					label: "Name:",
					name: "teams.name",
				},
				{
					label: "Clubs:",
					name: "clubs.id",
					type: "select2",
					opts: {
						placeholder: "Select a club"
					}
				},
				{
					label: "Chinese name",
					name: "teams.chinese_name",
				}
			]
		});

		// just allow change team name
		editorGroupTeams.on('initEdit', function () {
			editorGroupTeams.disable('clubs.id');
			
		});
		editorGroupTeams.on('initCreate', function () {
			editorGroupTeams.enable('clubs.id');

		});
		var uploadEditor = new $.fn.dataTable.Editor({
			fields: [{
				label: 'CSV file:',
				name: 'csv',
				type: 'upload',
				ajax: function (files, done) {
					var form = new FormData();
					form.append("file", files[0]);
					form.append("group_id", group_id);
					form.append("event_id", event_id);
					var settings = {
						"url": SERVER_PATH + "team/importTeamToGroupExcel",
						"method": "POST",
						"timeout": 0,
						"processData": false,
						"mimeType": "multipart/form-data",
						"contentType": false,
						"data": form,
						"headers": {	
							'x-user-id': $rootScope.user_id,
							'x-user-email': $rootScope.user_name
						}
					};

					$.ajax(settings).done(function (response) {
						var results = JSON.parse(response);
						if (results.status == 'ERROR') {
							uploadEditor.field('csv').error(' Import was failed: ' + results.message);
						}
						else {
							uploadEditor.close();
							BootstrapDialog.show({
								title: 'SUCCESS',
								type: BootstrapDialog.TYPE_SUCCESS,
								message: results.message,
								onhidden: function (dialogRef) {
									tableGroupTeams.ajax.reload();
								}
							});
						}
					});
				}
			}]
		});
		tableGroupTeams = $('#tblGroupTeams_' + group_id).DataTable({
			dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
			stateSave: true,
			deferRender: true,
			ajax: {
				url: SERVER_PATH + "team/getGroupTeams",
				type: 'POST',
				data: {
					"group_id": group_id,
					"event_id": event_id,
					"user_id": $rootScope.user_id,
				},
				headers: {	
					'x-user-id': $rootScope.user_id,
					'x-user-email': $rootScope.user_name
				},
				dataType: 'json',
				complete: function (response) {
					// response = JSON.parse(response.responseText);
				},
				error: function (xhr, status, error) {
				},
			},
			language: {
				paginate: {
					previous: '<i class="fa fa-chevron-left"></i>',
					next: '<i class="fa fa-chevron-right"></i>'
				}
			},
			columns: [
				{
					"className": 'details-control',
					"orderable": false,
					"data": null,
					"defaultContent": ''
				},
				{
					data: "teams.name",
					// className: "center"
				},
				{
					data: "clubs.name",
				},
				{
					data: "teams.chinese_name"
				}
				
			],
			select: {
				style: 'single'
			},
			buttons: [
				{
					extend: 'collection',
					text: 'Actions',
					autoClose: true,
					buttons: [
						{
							extend: 'excel',
							name: 'excel',
							text: '<i class="fa fa-file-excel-o"></i>&emsp;Export to Excel',
							titleAttr: 'Export data to an Excel file',
							filename: event_name,
							title: event_name,
							exportOptions: {
								columns: ':visible',
								modifier: {
								}
							}
						},
						{
							extend: 'selectedSingle',
							text: '<i class="fa fa-file-pdf-o"></i>&emsp; Generate Blank Form B',
							// define show when 
							action: function (e, dt, node, config) {
								var table_selected = tableGroupTeams.rows( { selected: true } ).data();

								// get first selected row
								var row = table_selected[0];
								
								// call ajax to get form b
								jQuery.ajax({
                                    type: 'POST',
                                    url:
                                        SERVER_PATH + 'team/generateBlankFormB',
									headers: {	
										'x-user-id': $rootScope.user_id,
										'x-user-email': $rootScope.user_name
									},
                                    data: {
                                        team_id: row.teams.id,
                                    },
									
                                    dataType: 'json',
                                    beforeSend: function () {
										Swal.fire({
											icon: 'info',
											text: "Export in process, please wait…",
											allowOutsideClick: false,
											confirmButtonColor: '#3085d6',
										});
									},
                                    complete: function (response) {
										Swal.close();

										var jsonData = JSON.parse(response.responseText);

										console.log(jsonData);

										if (jsonData.info == undefined) {
                                            BootstrapDialog.show({
                                                size: BootstrapDialog.SIZE_NORMAL,
                                                type: BootstrapDialog.TYPE_WARNING,
                                                message: jsonData.message,
                                            });
                                        } else {
                                            if (jsonData.status == 'OK') {
												// download file
												let file_name = jsonData.info;

                                                downloadFile(
                                                    PRODUCT_IMAGE_PATH +
                                                        file_name
                                                            .split(' ')
                                                            .join('%20'),
                                                    file_name
                                                );
                                                // show success message with sweet alert
                                                Swal.fire({
                                                    type: 'success',
                                                    icon: 'success',
                                                    title: jsonData.message,
                                                    confirmButtonClass:
                                                        'btn btn-primary',
                                                    buttonsStyling: false,
                                                });
                                            }
                                        }
									},
                                });
							}
						}
					]
				},
				{
					extend: 'create', text: 'Add', action:
						function () {
							var msg = getCreateTeamHtml();
							BootstrapDialog.show({
								size: BootstrapDialog.SIZE_WIDE,
								type: BootstrapDialog.TYPE_DANGER,
								closable: true,
								closeByBackdrop: false,
								closeByKeyboard: true,
								title: 'Add new team',
								message: msg,
								onshown: function(dialogRef){
									$("#addteam").submit(false);
								},
								buttons: [{
									label: 'Close',
									action: function (dialogRef) {
										dialogRef.close();
									},
								}, {
									label: 'Save',
									action: function (dialogRef) {
										var $form = $("#addteam");
										jQuery.validator.addMethod("uniqueteam", function (value, element) {
											return true;
										}, "Invalid Team Name");
										$form.validate({
											onfocusout: false,
											onkeyup: false,
											onclick: false,
											rules: {
												"team_name": {
													required: true,
													uniqueteam: true
												},
											},
											messages: {
												"team_name": {
													required: "this field is required",
												},
											}
										})
										if ($form.valid()) {
											var teamName = dialogRef.getModalBody().find('#team_name').val();
											jQuery.ajax({
												type: 'POST',
												url: SERVER_PATH + "team/addTeamToSeason",
												async: false,
												headers: {	
													'x-user-id': $rootScope.user_id,
													'x-user-email': $rootScope.user_name
												},
												data: {
													"group_id": group_id,
													"event_id": event_id,
													"team_name": teamName
												},
												dataType: 'json',
												complete: function (response) {
													var jsonData = JSON.parse(response.responseText);
													if (jsonData.status == 'ERROR') {
														jQuery.validator.addMethod("uniqueteam", function (value, element) {
															return false;
														}, jsonData.message);
														$form.valid();
													}
													else {
														dialogRef.close();
														BootstrapDialog.show({
															title: 'SUCCESS',
															type: BootstrapDialog.TYPE_SUCCESS,
															message: jsonData.message,
															onhidden: function (dialogRef) {
																tableGroupTeams.ajax.reload();
															}
														});
													}
												}
											});
										}
									}
								}]
							});
						}
				},
				{ extend: 'edit', editor: editorGroupTeams, text: 'Edit' },
				{ extend: 'remove', editor: editorGroupTeams, text: 'Delete' },
				{
					text: 'Import file team',
					action: function () {
						uploadEditor.create({
							title: 'Import file team'
						});
					}
				},
			],
			order: [[1, 'asc']],
			columnDefs: [
                { type: 'mysort', targets: 3},
            ],

			displayLength: -1,
		});

		// Add event listener for opening and closing details
		$('#tblGroupTeams_' + group_id + ' tbody').on('click', 'td.details-control', function () {
			var tr = $(this).closest('tr');
			var row = tableGroupTeams.row(tr);

			if (row.child.isShown()) {
				// This row is already open - close it
				row.child.hide();
				tr.removeClass('shown');
			} else {
				// Open this row
				row.child(getTeamPlayersTableHtml(row.data().teams.id)).show();
				tr.addClass('shown');

				// add class for next tr (child row)
				$(this).closest('tr').next().addClass('child-row-detail');

				initTeamPlayersTable(row.data().teams.id, row.data().teams.name, group_name);
			}
		});
	}

	function initTeamPlayersTable(team_id, team_name, group_name) {

		tableTeamPlayers = $('#tblTeamPlayers_' + team_id).DataTable({
			dom: '<"row"<"col-sm-6"B><"col-sm-6"f>>rt<"row"i>',
			stateSave: true,
			deferRender: true,
			ajax: {
				url: SERVER_PATH + "team/getTeamPlayers",
				type: 'POST',
				headers: {	
					'x-user-id': $rootScope.user_id,
					'x-user-email': $rootScope.user_name
				},
				data: {
					"team_id": team_id
				},
				dataType: 'json',
				complete: function (response) {
					// response = JSON.parse(response.responseText);
				},
				error: function (xhr, status, error) {
				},
			},
			language: {
				paginate: {
					previous: '<i class="fa fa-chevron-left"></i>',
					next: '<i class="fa fa-chevron-right"></i>'
				}
			},
			columns: [
				{
					data: "players.player_photo",
					className: "avatar",
					orderable: false,
					render: function (data) {
						if (data !== null && data !== '') {
							return '<img src="' + PRODUCT_IMAGE_PATH + data + '">';
						} else {
							return '<img src="' + SYSTEM_IMAGE_PATH + 'favicon.png">';
						}
					}
				},
				{
					data: null,
					render: function (data, type, row) {
						return data.players.surname + ' ' + data.players.other_name;
					}
				},
				{
					data: "players.dob",
					className: "center"
				},
			],
			select: {
				style: SELECT_MODE,
				selector: 'td:first-child'
			},
			buttons: [
				{
					extend: 'collection',
					text: 'Actions',
					autoClose: true,
					buttons: [
						{
							extend: 'excel',
							name: 'excel',
							text: '<i class="fa fa-file-excel-o"></i>&emsp;Export to Excel',
							titleAttr: 'Export data to an Excel file',
							filename: group_name + ' - ' + team_name,
							title: team_name,
							exportOptions: {
								columns: ':visible',
								modifier: {
									autoFilter: true,
									// selected: true
								}
							}
						}
					]
				},
				
				{ extend: 'colvis', text: 'Columns' }
			],
			order: [[1, 'asc']],
			displayLength: -1,
		});
	}

	function downloadFile(urlToSend,fileName) {
		var req = new XMLHttpRequest();
		req.open("GET", urlToSend, true);
		req.responseType = "blob";
		req.onload = function (event) {
			var blob = req.response;
			//if you have the fileName header available
			var link = document.createElement('a');
			link.href = window.URL.createObjectURL(blob);
			link.download = fileName;
			link.click();
		};

		req.send();
	}

});
