app.controller(
    'teamDistrictDetailsCtrl',
    function ($scope, $rootScope, $routeParams, $http) {
        $('#page-wrapper').removeClass('nav-small');
        var event_id = $routeParams.id;
        $scope.team_id = $routeParams.teamId;

        var link=window.location.href;
        var index=link.indexOf('team_name=');
        // set class_code
        $scope.team_name = link.substring(index+10,link.length);
        // check class_code is urlEncoded
        if($scope.team_name.indexOf('%')!=-1){
            $scope.team_name = decodeURIComponent($scope.team_name);
        }

        // datetime custom sort
        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            'DateTime-asc': (a, b) => {
                let a_temp = new Date(a);
                let b_temp = new Date(b);
                return a_temp.getTime() - b_temp.getTime();
            },
            'DateTime-desc': (a, b) => {
                let a_temp = new Date(a);
                let b_temp = new Date(b);
                return b_temp.getTime() - a_temp.getTime();
            },
        });

        // get info event
        jQuery.ajax({
            type: 'POST',
            url: SERVER_PATH + 'event/getEventInfo',
            async: false,
            headers: {	
                'x-user-id': $rootScope.user_id,
                'x-user-email': $rootScope.user_name
            },
            data: {
                event_id: event_id,
            },
            dataType: 'json',
            complete: function (response) {
                var jsonData = JSON.parse(response.responseText);
                var event = jsonData.info;
                $scope.selectedEvent = jsonData.info;
                $scope.selectedEvent.id = event_id;
                event_name = event.name;
                event_type = event.type;
                normalizedType = normalizeEventType(event_type);
            },
        });

        $scope.event_name = event_name;
        $scope.event_type = event_type;
        initPlayerTbl();
        initSessionTbl();

        $scope.normalizedType = normalizedType;

        function normalizeEventType(eventType) {
            switch (eventType) {
                case 'Summer Scheme':
                    return 'summer-scheme';
                case 'Regional':
                    return 'regional';
                case 'PL Junior':
                    return 'pl-junior';
                case 'Golden Age':
                    return 'golden-age';
                case 'Beginner':
                    return 'beginner';
                case 'District':
                    return 'district';
                default:
                    return '';
            }
        }

        function initPlayerTbl() {
            console.log('init table player');
            tablePlayer = $('#distric_player_tbl').DataTable({
                dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
                stateSave: false,
                deferRender: true,
                ajax: {
                    url: SERVER_PATH + 'district/getPlayerInTeam',
                    type: 'POST',
                    headers: {	
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name
                    },
                    data: {
                        team_district_id: $scope.team_id,
                    },
                    dataType: 'json',
                    complete: function (response) {
                        // console.log('res: ',response);
                    },
                    error: function (xhr, status, error) {},
                },
                order: [[1, 'asc']],
                language: {
                    paginate: {
                        previous: '<i class="fa fa-chevron-left"></i>',
                        next: '<i class="fa fa-chevron-right"></i>',
                    },
                },
                columns: [
                    {
                        data: 'DT_RowId',
                        // defaultContent: '',
                        // className: 'select-checkbox',
                        // orderable: false
                        targets: 0,
                        render: function (data, type, row, meta) {
                            if (type === 'display') {
                                data =
                                    '<div class="checkbox"><input type="checkbox" class="dt-checkboxes"><label></label></div>';
                            }

                            return data;
                        },
                        checkboxes: {
                            selectRow: true,
                            selectAllRender:
                                '<div class="checkbox"><input type="checkbox" class="dt-checkboxes"><label></label></div>',
                        },
                    },
                    { data: 'players.surname' },
                    { data: 'players.other_name' },
                    {
                        data: null,
                        render: function (data) {
                            return (
                                data.parens.surname +
                                ' ' +
                                data.parens.other_name
                            );
                        },
                    },
                    { data: 'parens.email' },
                    { data: 'parens.phone' },
                    { data: 'registrations.approval_status' },
                ],
                select: {
                    style: SELECT_MODE,
                    selector: 'td:first-child',
                },
                buttons: [
                    {
                        extend: 'selected',
                        text: 'Change district team',
                        action: function (e, dt, node, config) {
                            // get id of selected players
                            var selected_players = dt.rows({
                                selected: true,
                            });
                            var player_ids = [];
                            selected_players.every(function () {
                                player_ids.push(this.data().players.id);
                            });

                            let teams = [];

                            $.ajax({
                                url:
                                    SERVER_PATH +
                                    'district-session/getAllTeamSameGroupAndDistrict',
                                type: 'POST',
                                headers: {	
                                    'x-user-id': $rootScope.user_id,
                                    'x-user-email': $rootScope.user_name
                                },
                                data: {
                                    team_id: $scope.team_id,
                                },
                                success: function (response) {
                                    // transform response to json object
                                    response = JSON.parse(response);
                                    if (response.status == 'OK') {
                                        teams = response.data;

                                        editorChangeTeam =
                                            new $.fn.dataTable.Editor({
                                                ajax: {
                                                    type: 'POST',
                                                    url:
                                                        SERVER_PATH +
                                                        'district-session/changeTeamOfPlayers',
                                                    headers: {	
                                                        'x-user-id': $rootScope.user_id,
                                                        'x-user-email': $rootScope.user_name
                                                    },
                                                    data: {
                                                        player_ids:
                                                            player_ids.join(
                                                                ','
                                                            ),
                                                        current_team_id:
                                                            $scope.team_id,
                                                    },
                                                    async: false,
                                                    dataType: 'json',
                                                    preSubmit: function (data) {
                                                        // show loading edicator
                                                        Swal.fire({
                                                            title: 'Please wait',
                                                            html: 'Loading...',
                                                            allowOutsideClick: false,
                                                            showConfirmButton: false,
                                                            onBeforeOpen:
                                                                () => {
                                                                    Swal.showLoading();
                                                                },
                                                        });
                                                    },
                                                    complete: function (
                                                        response
                                                    ) {
                                                        response =
                                                            response.responseJSON;
                                                        if (
                                                            response.status ==
                                                            'OK'
                                                        ) {
                                                            Swal.close();

                                                            Swal.fire({
                                                                title: 'Success',
                                                                text: response.message,
                                                                icon: 'success',
                                                                confirmButtonText:
                                                                    'OK',
                                                            });

                                                            tablePlayer.ajax.reload();
                                                            tableSession.ajax.reload();
                                                        }
                                                    },
                                                    error: function (
                                                        xhr,
                                                        status,
                                                        error
                                                    ) {},
                                                },
                                                formOptions: {
                                                    main: {
                                                        onBlur: 'none',
                                                    },
                                                },
                                                fields: [
                                                    {
                                                        label: 'Select team',
                                                        name: 'team_id',
                                                        type: 'select2',
                                                        options: teams.map(
                                                            (item) => {
                                                                return {
                                                                    label: item.name,
                                                                    value: item.id,
                                                                };
                                                            }
                                                        ),
                                                    },
                                                ],
                                            });

                                        editorChangeTeam
                                            .title('Change distict Team')
                                            .buttons({
                                                label: 'Save',
                                                fn: function () {
                                                    this.submit();
                                                },
                                            })
                                            .edit()
                                            .open();
                                    } else {
                                        Swal.fire({
                                            title: 'Error',
                                            text: response.message,
                                            icon: 'error',
                                            confirmButtonText: 'OK',
                                        });
                                    }
                                },
                                error: function (xhr, status, error) {
                                    // Handle errors
                                    console.log(error);
                                },
                            });
                        },
                    },
                ],
            });
        }
        function getModalCoachesSessionTableHtml(session_id) {
            var str =
                '' +
                '<div class="main-box-body clearfix">' +
                '<div class="table-responsive">' +
                '<table id="tableCoachesSession_' +
                session_id +
                '" class="table table-striped table-bordered table-hover" cellspacing="0" width="100%">' +
                '<thead>' +
                '<tr>' +
                '<th>Coach Name</th>' +
                '<th>Email</th>' +
                '<th>Phone</th>' +
                '<th>Role</th>' +
                '<th>Level</th>' +
                '<th>Attendance </th>' +
                '<th>Overlapped</th>' +
                '</tr>' +
                '</thead>' +
                '</table>' +
                '</div>' +
                '</div>' +
                '';
            return str;
        }

        function getModalSubstituteCoachesLogsTableHtml(session_id) {
            var str =
                '' +
                '<div class="main-box-body clearfix">' +
                '<div class="table-responsive">' +
                '<table id="tableSubstituteCoachesLogs_' +
                session_id +
                '" class="table table-striped table-bordered table-hover" cellspacing="0" width="100%">' +
                '<thead>' +
                '<tr>' +
                '<th>Old Coach</th>' +
                '<th>New Coach</th>' +
                '<th>Change by</th>' +
                '<th>Change at</th>' +
                '</tr>' +
                '</thead>' +
                '</table>' +
                '</div>' +
                '</div>' +
                '';
            return str;
        }

        var tableCoaches = null;
        function getModalAttendancePlayersTableHtml(session_id) {
            var str =
                '' +
                '<div class="main-box-body clearfix">' +
                '<div class="table-responsive">' +
                '<table id="tableAttendancePlayers_' +
                session_id +
                '" class="table table-striped table-bordered table-hover" cellspacing="0" width="100%">' +
                '<thead>' +
                '<tr>' +
                '<th>Surname</th>' +
                '<th>Other Name</th>' +
                '<th>Parent Name</th>' +
                '<th>Parent Email</th>' +
                '<th>Parent Phone</th>' +
                '<th>Attendance</th>' +
                '<th>Update By</th>' +
                '<th>Update At</th>' +
                '</tr>' +
                '</thead>' +
                '</table>' +
                '</div>' +
                '</div>' +
                '';
            return str;
        }

        function initCoachTbl(d) {
            console.log(d);
            var user_role = d.role;
            console.log('initCoachTbl', user_role);
            editorCoachSession = new $.fn.dataTable.Editor({
                ajax: {
                    type: 'POST',
                    url: SERVER_PATH + 'district-session/getCoachSessions',
                    headers: {	
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name
                    },
                    data: {
                        district_session_id: d.district_sessions.id,
                        user_id: $scope.user_id,
                    },
                    async: false,
                    dataType: 'json',
                    complete: function (response) {},
                    error: function (xhr, status, error) {},
                },
                table: '#tableCoachesSession_' + d.district_sessions.id,
                formOptions: {
                    main: {
                        onBlur: 'none',
                    },
                },
                i18n: {
                    create: {
                        button: 'Add',
                        title: 'Create new coach',
                        submit: 'Create',
                    },
                    edit: {
                        button: 'Change Role',
                        title: 'Change Role',
                        submit: 'Update',
                    },
                    remove: {
                        button: 'Delete',
                        title: 'Delete coach',
                        submit: 'Delete',
                        confirm: {
                            _: 'Are you sure you want to delete these coach?',
                            1: 'Are you sure you want to delete this coach?',
                        },
                    },

                    error: {
                        system: 'System error, please contact administrator.',
                    },
                },
                fields: [
                    {
                        type: 'hidden',
                        name: 'district_session_coaches.district_session_id',
                        default: d.district_sessions.id,
                    },
                    {
                        label: 'Select Coach',
                        name: 'district_session_coaches.coach_id',
                        type: 'select2',
                        opts: {
                            multiple: false,
                            placeholder: 'Search and select coach...',
                        },
                    },
                    // {
                    //   label: 'Select substitute Coach',
                    //   name: 'substitute_coach_id',
                    //   def: 0,
                    //   type: 'select2',
                    //   opts: {
                    //     multiple: false,
                    //     placeholder: 'Search and select coach...',
                    //   },
                    // },
                    {
                        label: 'Role',
                        name: 'district_session_coaches.session_role',
                        type: 'radio',
                        options: [
                            { label: ROLE_HEAD_COACH, value: ROLE_HEAD_COACH },
                            {
                                label: ROLE_ASSITANT_COACH,
                                value: ROLE_ASSITANT_COACH,
                            },
                            {
                                label: ROLE_GOALKEEPER_COACH,
                                value: ROLE_GOALKEEPER_COACH,
                            },
                        ],
                        default: ROLE_HEAD_COACH,
                    },
                ],
            });

            editorCoachSession.on('preOpen', function (e, mode, action) {
                if (action === 'edit') {
                    // hide field district_session_coaches.coach_id
                    editorCoachSession
                        .field('district_session_coaches.coach_id')
                        .disable();
                }
                if (action === 'create') {
                    // show field district_session_coaches.coach_id
                    editorCoachSession
                        .field('district_session_coaches.coach_id')
                        .enable();
                }
            });

            editorSubstituteCoach = new $.fn.dataTable.Editor({
                ajax: {
                    type: 'POST',
                    url: SERVER_PATH + 'district-session/setSubstitueCoach',
                    headers: {	
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name
                    },
                    data: {
                        district_session_id: d.district_sessions.id,
                        user_id: $scope.user_id,
                    },
                    async: false,
                    dataType: 'json',
                    complete: function (response) {},
                    error: function (xhr, status, error) {},
                },
                table: '#tableCoachesSession_' + d.district_sessions.id,
                formOptions: {
                    main: {
                        onBlur: 'none',
                    },
                },
                i18n: {
                    create: {
                        button: 'New',
                        title: 'Create new coach',
                        submit: 'Create',
                    },
                    edit: {
                        button: 'OK',
                        title: 'Substitute Coach',
                        submit: 'Update',
                    },
                    remove: {
                        button: 'Delete',
                        title: 'Delete Coach',
                        submit: 'Delete',
                        confirm: {
                            _: 'Are you sure you want to delete these coach?',
                            1: 'Are you sure you want to delete this coach?',
                        },
                    },

                    error: {
                        system: 'System error, please contact administrator.',
                    },
                },
                fields: [
                    {
                        type: 'hidden',
                        name: 'district_session_coaches.district_session_id',
                        default: d.district_sessions.id,
                    },
                    {
                        label: 'Select substitute Coach',
                        name: 'substitute_coach_id',
                        def: 0,
                        type: 'select2',
                        opts: {
                            multiple: false,
                            placeholder: 'Search and select coach...',
                        },
                    },
                    {
                        label: 'Role',
                        name: 'district_session_coaches.session_role',
                        type: 'hidden',
                    },
                ],
            });

            $scope.init = function () {
                let timer = localStorage.getItem('timer_registration');
                timer = timer == null ? 10000 : timer;

                Swal.fire({
                    title: 'Loading...',
                    html: 'Synchronizing data in <b></b> seconds.',
                    timer: timer,
                    allowOutsideClick: false,
                    timerProgressBar: true,
                    didOpen: () => {
                        Swal.showLoading();
                        const b = Swal.getHtmlContainer().querySelector('b');
                        Swal.getTimerLeft();
                        timerInterval = setInterval(() => {
                            b.textContent = Swal.getTimerLeft() / 1000;
                        }, 100);
                    },
                });
            };

            tableCoaches = $(
                '#tableCoachesSession_' + d.district_sessions.id
            ).DataTable({
                dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
                stateSave: false,
                deferRender: true,
                ajax: {
                    url: SERVER_PATH + 'district-session/getCoachSessions',
                    type: 'POST',
                    async: true,
                    headers: {	
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name
                    },
                    data: {
                        district_session_id: d.district_sessions.id,
                        user_id: $scope.user_id,
                    },
                    dataType: 'json',
                    complete: function (response) {},
                    error: function (xhr, status, error) {},
                },
                language: {
                    paginate: {
                        previous: '<i class="fa fa-chevron-left"></i>',
                        next: '<i class="fa fa-chevron-right"></i>',
                    },
                },

                columns: [
                    {
                        data: null,
                        render: function (data) {
                            return (
                                data.parens.surname +
                                ' ' +
                                data.parens.other_name
                            );
                        },
                    },
                    { data: 'parens.email' },
                    { data: 'parens.phone' },
                    { data: 'district_session_coaches.session_role' },
                    {
                        data: 'coach_levels.level',
                        className: 'text-center',
                    },
                    {
                        data: 'district_session_coaches.check_attendance',
                        className: 'center',
                        render: function (data, type, row) {
                            ATTENDANCE_STATUS.forEach(function (item, key) {
                                if (key == data) {
                                    data = item;
                                }
                            });

                            return data;
                        },
                    },
                    {
                        data: 'district_session_coaches.district_session_id',
                        className: 'center',
                        render: function (data, type, row) {
                            if (row.is_overlap) {
                                return '<a href="javascript:void(0)"><i class="fa fa-flag fa-lg"></i></a>';
                            } else {
                                return '';
                            }
                        },
                    },
                ],
                select: {
                    style: 'single',
                    selector: 'td:not(:last-child)',
                },
                order: [[0, 'asc']],
                buttons: [
                    {
                        extend: 'create',
                        className: 'coach-session',
                        editor: editorCoachSession,
                    },
                    {
                        extend: 'edit',
                        className: 'coach-session',
                        editor: editorCoachSession,
                    },
                    {
                        extend: 'edit',
                        className: 'coach-session',
                        text: 'Substitute Coach',
                        className: 'coach-session',
                        editor: editorSubstituteCoach,
                    },
                    {
                        extend: 'selectedSingle',
                        text: 'Change Attendance',
                        className: 'coach-attendance',
                        action: function (e, dt, node, config) {
                            let data = tableCoaches
                                .rows({ selected: true })
                                .data()[0];
                            Swal.fire({
                                title: 'Change Attendance',
                                html: 'Please select the new attendance status:',
                                showCloseButton: true,
                                focusConfirm: false,
                                confirmButtonText: ATTENDANCE_STATUS.get(
                                    COACH_ATTENDANCE_STATUS_ATTENDED
                                ),
                                showDenyButton: true,
                                denyButtonText: ATTENDANCE_STATUS.get(
                                    COACH_ATTENDANCE_STATUS_DID_NOT_ATTEND
                                ),
                                denyButtonColor: 'red',
                                confirmButtonColor: 'green',
                            }).then((result) => {
                                if (result.isConfirmed) {
                                    jQuery.ajax({
                                        type: 'POST',
                                        url:
                                            SERVER_PATH +
                                            'district-session/changeAttendanceStatus',
                                        headers: {	
                                            'x-user-id': $rootScope.user_id,
                                            'x-user-email': $rootScope.user_name
                                        },
                                        data: {
                                            district_session_coaches_id:
                                                data.district_session_coaches
                                                    .id,
                                            attendance_status:
                                                COACH_ATTENDANCE_STATUS_ATTENDED,
                                            user_id: $rootScope.user_id,
                                            event_id: event_id,
                                        },
                                        async: false,
                                        dataType: 'json',
                                        complete: function (response) {
                                            response = response.responseJSON;
                                            if (response.status == 'OK') {
                                                Swal.close();
                                                tableCoaches.ajax.reload();
                                                // close modal
                                            } else {
                                                Swal.fire({
                                                    title: 'Error',
                                                    text: response.message,
                                                    icon: 'error',
                                                    confirmButtonText: 'OK',
                                                });
                                            }
                                        },
                                    });
                                } else if (result.isDenied) {
                                    jQuery.ajax({
                                        type: 'POST',
                                        url:
                                            SERVER_PATH +
                                            'district-session/changeAttendanceStatus',
                                        headers: {	
                                            'x-user-id': $rootScope.user_id,
                                            'x-user-email': $rootScope.user_name
                                        },
                                        data: {
                                            district_session_coaches_id:
                                                data.district_session_coaches
                                                    .id,
                                            attendance_status:
                                                COACH_ATTENDANCE_STATUS_DID_NOT_ATTEND,
                                            user_id: $rootScope.user_id,
                                            event_id: event_id,
                                        },
                                        async: false,
                                        dataType: 'json',
                                        complete: function (response) {
                                            response = response.responseJSON;
                                            if (response.status == 'OK') {
                                                Swal.close();
                                                tableCoaches.ajax.reload();
                                            } else {
                                                Swal.fire({
                                                    title: 'Error',
                                                    text: response.message,
                                                    icon: 'error',
                                                    confirmButtonText: 'OK',
                                                });
                                            }
                                        },
                                    });
                                } else {
                                    return;
                                }
                            });
                        },
                    },
                    {
                        extend: 'remove',
                        className: 'coach-session',
                        editor: editorCoachSession,
                    },
                    {
                        extend: 'colvis',
                        text: 'Columns',
                    },
                ],
            });

            // show loading edicator
            tableCoaches.on(
                'processing.dt',
                function (e, settings, processing) {
                    if (processing) {
                        Swal.fire({
                            title: 'Please wait',
                            html: 'Loading...',
                            allowOutsideClick: false,
                            showConfirmButton: false,
                            onBeforeOpen: () => {
                                Swal.showLoading();
                            },
                        });
                    } else {
                        Swal.close();
                    }
                }
            );

            tableCoaches.on('init.dt', function () {
                // hide buttons
                getRoleByUserID($scope.user_id).then(function (role) {
                    if (role != TYPE_SUPER_ADMIN && role != TYPE_LEAGUE_ADMIN) {
                        tableCoaches.buttons('.coach-session').remove();
                    }
                });
            });

            // reload table after submit
            editorCoachSession.on('submitSuccess', function (e, json, data) {
                tableCoaches.ajax.reload();
            });
            editorSubstituteCoach.on('submitSuccess', function (e, json, data) {
                tableCoaches.ajax.reload();
            });
        }

        function initPlayerAttendanceReportTbl(d) {
            tablePlayerAttendanceReport = $(
                '#tableAttendancePlayers_' + d.district_sessions.id
            ).DataTable({
                dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
                stateSave: false,
                deferRender: true,
                ajax: {
                    url: SERVER_PATH + 'attendance/getDistrictPlayerAttendance',
                    type: 'POST',
                    headers: {	
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name
                    },
                    data: {
                        district_session_id: d.district_sessions.id,
                    },
                    dataType: 'json',
                    complete: function (response) {},
                    error: function (xhr, status, error) {},
                },
                language: {
                    paginate: {
                        previous: '<i class="fa fa-chevron-left"></i>',
                        next: '<i class="fa fa-chevron-right"></i>',
                    },
                },
                columns: [
                    { data: 'players.surname' },
                    { data: 'players.other_name' },
                    {
                        data: null,
                        render: function (data) {
                            return (
                                data.parens.surname +
                                ' ' +
                                data.parens.other_name
                            );
                        },
                    },
                    { data: 'parens.email' },
                    { data: 'parens.phone' },
                    {
                        data: null,
                        render: function (data) {
                            if (
                                data.district_session_players
                                    .check_attendance == 1
                            ) {
                                return 'Present';
                            } else {
                                return '';
                            }
                        },
                    },
                    {
                        data: null,
                        render: function (data) {
                            return (
                                data.update_by.surname +
                                ' ' +
                                data.update_by.other_name
                            );
                        },
                    },
                    { data: 'district_session_players.updated_at' },
                ],
                select: {
                    style: 'single',
                    selector: 'td:not(:last-child)',
                },
                order: [[0, 'asc']],
            });
        }

        function initSubstituteCoachLogsTbl(d) {
            console.log('init table substitute coach logs');
            tableSubstituteCoachLogs = $(
                '#tableSubstituteCoachesLogs_' + d.district_sessions.id
            ).DataTable({
                dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
                stateSave: false,
                deferRender: true,
                ajax: {
                    url: SERVER_PATH + 'district-session/getSubstitueCoachLogs',
                    type: 'POST',
                    headers: {	
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name
                    },
                    data: {
                        session_id: d.district_sessions.id,
                        user_id: $scope.user_id,
                    },
                    dataType: 'json',
                    complete: function (response) {},
                    error: function (xhr, status, error) {},
                },
                language: {
                    paginate: {
                        previous: '<i class="fa fa-chevron-left"></i>',
                        next: '<i class="fa fa-chevron-right"></i>',
                    },
                },

                columns: [
                    {
                        data: 'substitute_coach_log.old_coach_id',
                        render: function (data, type, row) {
                            if (data)
                                return (
                                    row.old_coach.other_name +
                                    ' ' +
                                    row.old_coach.surname +
                                    '(' +
                                    row.old_coach.email +
                                    ')'
                                );
                        },
                    },
                    {
                        data: 'substitute_coach_log.new_coach_id',
                        render: function (data, type, row) {
                            if (data)
                                return (
                                    row.new_coach.other_name +
                                    ' ' +
                                    row.new_coach.surname +
                                    '(' +
                                    row.new_coach.email +
                                    ')'
                                );
                        },
                    },
                    {
                        data: 'substitute_coach_log.change_by',
                        render: function (data, type, row) {
                            if (data)
                                return (
                                    row.change_by.other_name +
                                    ' ' +
                                    row.change_by.surname +
                                    '(' +
                                    row.change_by.email +
                                    ')'
                                );
                        },
                    },
                    { data: 'substitute_coach_log.time' },
                    // { data: 'substitute_coach_log.modified' },
                ],
                select: {
                    style: 'single',
                    selector: 'td:not(:last-child)',
                },
                order: [[0, 'asc']],
                buttons: [
                    {
                        extend: 'colvis',
                        text: 'Columns',
                    },
                ],
            });
        }

        function getRoleByUserID() {
            return new Promise((resolve, reject) => {
                $.ajax({
                    type: 'POST',
                    url: SERVER_PATH + 'district-session/getRoleByUserID',
                    headers: {	
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name
                    },
                    data: {
                        team_id: $scope.team_id,
                        user_id: $scope.user_id,
                    },
                    async: false,
                    dataType: 'json',
                    complete: function (response) {
                        console.log('getRoleByUserID', response);

                        if (response.status == 200) {
                            resolve(response.responseJSON.data);
                        }
                    },
                    error: function (xhr, status, error) {
                        console.log(error);
                    },
                });
            });
        }

        function initSessionTbl() {
            console.log('init table session');
            editor = new $.fn.dataTable.Editor({
                ajax: {
                    type: 'POST',
                    url: SERVER_PATH + 'district-session/setDistrictSessions',
                    headers: {	
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name
                    },
                    data: {
                        team_id: $scope.team_id,
                        user_id: $scope.user_id,
                    },
                    async: false,
                    dataType: 'json',
                    complete: function (response) {},
                    error: function (xhr, status, error) {},
                },
                table: '#distric_session_tbl',
                formOptions: {
                    main: {
                        onBlur: 'none',
                    },
                },
                i18n: {
                    error: {
                        system: 'System error, please contact administrator.',
                    },
                    edit: {
                        title: 'Edit session',
                        submit: 'Update',
                    },
                    create: {
                        title: 'Create session',
                        submit: 'Create',
                    },
                },
                fields: [
                    {
                        label: 'Name',
                        name: 'district_sessions.name',
                    },
                    {
                        label: 'Start time',
                        name: 'district_sessions.start_time',
                        type: 'datetime',
                        format: 'DD-MMM-YYYY hh:mm A',
                        def: function () {
                            return moment().format('DD-MMM-YYYY hh:mm A');
                        },
                    },
                    {
                        label: 'End time',
                        name: 'district_sessions.end_time',
                        type: 'datetime',
                        // 24 hour format
                        format: 'DD-MMM-YYYY hh:mm A',
                        def: function () {
                            return moment().format('DD-MMM-YYYY hh:mm A');
                        },
                    },
                    {
                        label: 'Type',
                        name: 'district_sessions.type',
                        type: 'select2',
                        options: DISTRICT_SESSION_TYPE.map((item) => {
                            return {
                                label: item.label,
                                value: item.value,
                            };
                        }),
                        default: DISTRICT_SESSION_TYPE[0].value,
                    },
                    {
                        label: 'Venue',
                        name: 'district_sessions.venue_id',
                        type: 'select2',
                    },
                    {
                        label: 'Team',
                        name: 'district_sessions.team_id',
                        def: $scope.team_id,
                        type: 'hidden',
                    },
                    {
                        label: 'Status',
                        name: 'district_sessions.status',
                        def: SESSION_STATUS_ACTIVE,
                        type: 'hidden',
                    },
                    {
                        label: 'Reason*',
                        name: 'district_sessions.cancelled_reason',
                        type: 'hidden',
                    },
                    {
                        // created by
                        label: 'Created by',
                        name: 'district_sessions.created_by',
                        def: $rootScope.user_id,
                        type: 'hidden',
                    },
                ],
            });
            editor.on('initEdit', function (e, node, data) {
                // set created by if not exist
                if (!data.district_sessions.created_by) {
                    // set field value
                    editor
                        .field('district_sessions.created_by')
                        .val($rootScope.user_id);
                }
            });
            editor.on('preSubmit', function (e, data, action) {
                console.log('preSubmit', data);
            });

            editorCancel = new $.fn.dataTable.Editor({
                ajax: {
                    type: 'POST',
                    url: SERVER_PATH + 'district-session/getDistrictSessions',
                    headers: {	
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name
                    },
                    data: {
                        team_id: $scope.team_id,
                        user_id: $scope.user_id,
                    },
                    async: false,
                    dataType: 'json',
                    complete: function (response) {},
                    error: function (xhr, status, error) {},
                },
                table: '#distric_session_tbl',
                formOptions: {
                    main: {
                        onBlur: 'none',
                    },
                },
                i18n: {
                    error: {
                        system: 'System error, please contact administrator.',
                    },
                },
                fields: [
                    {
                        label: 'Name',
                        name: 'district_sessions.name',
                        type: 'hidden',
                    },
                    {
                        label: 'Start time',
                        name: 'district_sessions.start_time',
                        type: 'hidden',
                    },
                    {
                        label: 'End time',
                        name: 'district_sessions.end_time',
                        type: 'hidden',
                    },
                    {
                        label: 'Status',
                        name: 'district_sessions.status',
                        def: SESSION_STATUS_CANCELED,
                        type: 'hidden',
                    },
                    {
                        label: 'Reason*',
                        name: 'district_sessions.cancelled_reason',
                        type: 'textarea',
                    },
                    {
                        label: 'Cancel by',
                        name: 'district_sessions.cancelled_by',
                        type: 'hidden',
                        def: $rootScope.user_id,
                    },
                ],
            });
            editorCancel.on('initEdit', function (e, o, action) {
                console.log($rootScope.user_id);
                // set cancel by
                editorCancel
                    .field('district_sessions.cancelled_by')
                    .val($rootScope.user.person_id);
                editorCancel
                    .field('district_sessions.status')
                    .val(SESSION_STATUS_CANCELED);
            });
            let btnCancel = {
                extend: 'selectedSingle',
                text: 'Cancel',
                action: function (e, dt, node, config) {
                    // show confirm dialog
                    var row = tableSession.row({ selected: true }).data();
                    if (
                        row.district_sessions.status.toLowerCase() ==
                        SESSION_STATUS_CANCELED.toLowerCase()
                    ) {
                        BootstrapDialog.show({
                            title: 'Warning',
                            message: 'This session has been canceled',
                            type: BootstrapDialog.TYPE_WARNING,
                            buttons: [
                                {
                                    label: 'OK',
                                    action: function (dialog) {
                                        dialog.close();
                                    },
                                },
                            ],
                        });
                        return;
                    }
                    BootstrapDialog.show({
                        title: 'Confirm',
                        message:
                            'Are you sure you want to cancel this session?',
                        type: BootstrapDialog.TYPE_WARNING,
                        buttons: [
                            {
                                label: 'Cancel',
                                action: function (dialog) {
                                    dialog.close();
                                },
                            },
                            {
                                label: 'OK',
                                action: function (dialog) {
                                    // log rows selected
                                    var rows = dt
                                        .rows({ selected: true })
                                        .data();
                                    console.log(rows);
                                    editorCancel
                                        .edit(
                                            tableSession
                                                .rows({ selected: true })
                                                .indexes()
                                        )
                                        .title('Cancel Session')
                                        .buttons('Submit')

                                        .open();
                                    dialog.close();
                                },
                            },
                        ],
                    });
                },
            };

            let button_session = [
                {
                    extend: 'create',
                    editor: editor,
                    text: 'Add',
                },
                {
                    extend: 'edit',
                    editor: editor,
                    text: 'Edit',
                },
                {
                    extend: 'remove',
                    editor: editor,
                    text: 'Delete',
                },
                btnCancel,
                {
                    extend: 'selected',
                    name: 'generate_attendance_sheet',
                    text: 'Generate Attendance Sheet',
                    titleAttr: 'Generate and download player attendance sheet',
                    action: function () {
                        let coursesSelected = tableSession
                            .rows({ selected: true })
                            .data()
                            .toArray();

                        let course_ids = [];
                        coursesSelected.forEach((course) => {
                            course_ids.push(course.district_sessions.id);
                        });

                        if (course_ids !== undefined && course_ids.length > 0) {
                            if (course_ids.length > 10) {
                                Swal.fire({
                                    title: 'Warning',
                                    text: 'You can only generate attendance sheet for 10 courses at once. Please select less than 10 courses.',
                                    icon: 'warning',
                                    type: 'warning',
                                    confirmButtonText: 'OK',
                                    confirmButtonColor: '#ed1c24',
                                });
                            } else {
                                let editor = new $.fn.dataTable.Editor({
                                    ajax: {
                                        type: 'POST',
                                        url:
                                            SERVER_PATH +
                                            'district-session/autoCreateApprovedAttendanceSheets',
                                        headers: {	
                                            'x-user-id': $rootScope.user_id,
                                            'x-user-email': $rootScope.user_name
                                        },
                                        data: {
                                            type: 'approved',
                                            session_ids: course_ids.join(','),
                                        },
                                        dataType: 'json',
                                        complete: function (response) {
                                            console.log(response);
                                            var jsonData = JSON.parse(
                                                response.responseText
                                            );

                                            if (jsonData.status == 'ERROR') {
                                                Swal.fire({
                                                    title: 'Error',
                                                    html: jsonData.message,
                                                    icon: 'error',
                                                    type: 'error',
                                                    confirmButtonText: 'OK',
                                                    confirmButtonColor:
                                                        '#ed1c24',
                                                });
                                            } else {
                                                if (jsonData.status == 'OK') {
                                                    fileNames = jsonData.info;
                                                    fileNames.forEach(
                                                        (fileName) => {
                                                            downloadFile(
                                                                PRODUCT_IMAGE_PATH +
                                                                    fileName
                                                                        .split(
                                                                            ' '
                                                                        )
                                                                        .join(
                                                                            '%20'
                                                                        ),
                                                                fileName
                                                            );
                                                        }
                                                    );
                                                    Swal.fire({
                                                        title: 'Success',
                                                        html: jsonData.message,
                                                        icon: 'success',
                                                        type: 'success',
                                                        confirmButtonText: 'OK',
                                                        confirmButtonColor:
                                                            '#ed1c24',
                                                    });
                                                }
                                            }
                                        },
                                    },
                                });
                                let swal = null;
                                editor.on(
                                    'preSubmit',
                                    function (e, data, action) {
                                        swal = Swal.fire({
                                            title: 'Processing!',
                                            html: 'Please wait...',
                                            allowEscapeKey: false,
                                            allowOutsideClick: false,
                                            confirmButtonColor: '#ed1c24',
                                            onBeforeOpen: () => {
                                                Swal.showLoading();
                                            },
                                        });
                                    }
                                );
                                editor.on(
                                    'submitComplete',
                                    function (e, data, action) {
                                        swal.close();
                                    }
                                );
                                editor.create(false).submit();
                            }
                        }
                    },
                },
                {
                    extend: 'colvis',
                    text: 'Columns',
                },
            ];
            getRoleByUserID().then(function (role) {
                console.log('role', role);
                if (
                    role != ROLE_HEAD_COACH &&
                    role != TYPE_LEAGUE_ADMIN &&
                    role != TYPE_SUPER_ADMIN
                ) {
                    console.log('head coach', ROLE_HEAD_COACH);
                    console.log('head admin', TYPE_SUPER_ADMIN);

                    button_session = [btnCancel];
                }
                tableSession = $('#distric_session_tbl').DataTable({
                    dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
                    stateSave: false,
                    deferRender: true,
                    ajax: {
                        url:
                            SERVER_PATH +
                            'district-session/getDistrictSessions',
                        type: 'POST',
                        headers: {	
                            'x-user-id': $rootScope.user_id,
                            'x-user-email': $rootScope.user_name
                        },
                        data: {
                            team_id: $scope.team_id,
                            user_id: $rootScope.user_id,
                        },
                        dataType: 'json',
                        complete: function (response) {
                            // console.log('res: ',response);
                        },
                        error: function (xhr, status, error) {},
                    },
                    select: {
                        // style: 'single',
                        selector: 'td:not(:last-child)',
                    },
                    language: {
                        paginate: {
                            previous: '<i class="fa fa-chevron-left"></i>',
                            next: '<i class="fa fa-chevron-right"></i>',
                        },
                    },
                    columnDefs: [
                        {
                            type: 'DateTime',
                            targets: 1,
                        },
                        {
                            type: 'DateTime',
                            targets: 2,
                        },
                    ],
                    columns: [
                        { data: 'district_sessions.name' },
                        { data: 'district_sessions.start_time' },
                        { data: 'district_sessions.end_time' },
                        { data: 'venues.name' },
                        {
                            data: 'district_sessions.type',
                            className: 'center',
                            render: function (data) {
                                // find in array
                                var type = DISTRICT_SESSION_TYPE.find(
                                    (item) => {
                                        return item.value == data;
                                    }
                                );

                                return type ? type.label : data;
                            },
                        },
                        {
                            data: 'district_sessions.status',
                            className: 'center',
                            render: function (data) {
                                switch (data) {
                                    case SESSION_STATUS_CANCELED:
                                        return '<span class="label label-danger">Cancelled</span>';
                                    case SESSION_STATUS_ACTIVE:
                                        return '<span class="label label-success">Active</span>';
                                    default:
                                        return (
                                            '<span style="color:blue">' +
                                            data +
                                            '</span>'
                                        );
                                }
                            },
                        },
                        { data: 'district_sessions.created_by_name' },
                        { data: 'district_sessions.cancelled_reason' },
                        {
                            data: 'role',
                            className: 'center',
                            sortable: false,
                            render: function (data) {
                                var actions =
                                    '<button type="button" data-toggle="modal" data-target="#modal-coaches" class="btn btn-primary modal-coaches" >Coach Attendance</button> &nbsp';
                                return actions;
                            },
                        },
                        // {
                        //     data: 'role',
                        //     className: 'center',
                        //     sortable: false,
                        //     render: function (data) {
                        //         if (
                        //             data == ROLE_HEAD_COACH ||
                        //             parseInt(data) == USER_SUPER_ADMIN
                        //         ) {
                        //             var actions = '<button type="button" data-toggle="modal" data-target="#modal-logs" class="btn btn-info modal-logs" >Substitute Coach Logs</button>' ;
                        //             return actions;
                        //         }
                        //         return '';
                        //     },
                        // },
                        {
                            data: 'role',
                            className: 'center',
                            sortable: false,
                            render: function (data) {
                                var actions =
                                    '<button type="button" data-toggle="modal" data-target="#modal-player-attendance" class="btn btn-success modal-player-attendance" >Player Attendance</button>';
                                return actions;
                            },
                        },
                    ],
                    order: [[1, 'asc']],
                    buttons: button_session,
                });
            });

            function downloadFile(urlToSend, fileName) {
                var req = new XMLHttpRequest();
                req.open('GET', urlToSend, true);
                req.responseType = 'blob';
                req.onload = function (event) {
                    var blob = req.response;
                    //if you have the fileName header available
                    var link = document.createElement('a');
                    link.href = window.URL.createObjectURL(blob);
                    link.download = fileName;
                    link.click();
                };

                req.send();
            }

            // on init table
            // tableSession.on('init.dt', function (e, settings, json) {

            //   // if role is not admin, hide buttons
            //   getRoleByUserID().then(function (role) {
            //     console.log("role: ", role);
            //     if (role != ROLE_HEAD_COACH && role != TYPE_SUPER_ADMIN) {
            //       tableSession.buttons().remove();
            //     }
            //   });
            // });

            $('#distric_session_tbl').on(
                'click',
                'tbody .modal-coaches',
                function () {
                    let row = $(this).closest('tr');
                    let data = tableSession.row(row).data();

                    var msg = getModalCoachesSessionTableHtml(
                        data.district_sessions.id
                    );
                    // Show dialog
                    BootstrapDialog.show({
                        title:
                            'Coach Attendance - ' + data.district_sessions.name,
                        message: msg,
                        size: BootstrapDialog.SIZE_WIDE,
                        onshown: function (dialogRef) {
                            initCoachTbl(data);
                        },
                    });
                }
            );

            $('#distric_session_tbl').on(
                'click',
                'tbody .modal-logs',
                function () {
                    let row = $(this).closest('tr');
                    let data = tableSession.row(row).data();
                    console.log('data: ', data);
                    var msg = getModalSubstituteCoachesLogsTableHtml(
                        data.district_sessions.id
                    );
                    // Show dialog
                    BootstrapDialog.show({
                        title:
                            'Substitute Coach Logs - ' +
                            data.district_sessions.name,
                        message: msg,
                        size: BootstrapDialog.SIZE_WIDE,
                        onshown: function (dialogRef) {
                            initSubstituteCoachLogsTbl(data);
                        },
                    });
                }
            );

            $('#distric_session_tbl').on(
                'click',
                'tbody .modal-player-attendance',
                function () {
                    let row = $(this).closest('tr');
                    let data = tableSession.row(row).data();

                    var msg = getModalAttendancePlayersTableHtml(
                        data.district_sessions.id
                    );

                    // Show dialog
                    BootstrapDialog.show({
                        title:
                            ' Player attendance - ' +
                            data.district_sessions.name,
                        message: msg,
                        size: BootstrapDialog.SIZE_WIDE,
                        onshown: function (dialogRef) {
                            initPlayerAttendanceReportTbl(data);
                        },
                    });
                }
            );
        }

        $scope.send = function (id) {
            // $scope.loadingElement.style.display = 'block';
            $http({
                method: 'POST',
                url: SERVER_PATH + 'message/sendMessage',
                headers: {	
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name
                },
                data: 'id=' + id,
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
            }).success(function (response) {
                console.log(response);
                // $scope.loadingElement.style.display = 'none';

                BootstrapDialog.show({
                    title: response.status,
                    type:
                        response.status == 'OK'
                            ? BootstrapDialog.TYPE_SUCCESS
                            : BootstrapDialog.TYPE_WARNING,
                    message: response.message,
                });
            });
        };

        $scope.generateReport = function () {
            console.log('generate report');
            createReport($scope.team_id);
        };

        function getReportTableHtml(ipg, data) {
            var str =
                '<div class="table-responsive">' +
                '<button id="export" style="margin: 20px 0 20px 0;">' +
                '<span>Export to Excel</span>' +
                '</button>' +
                '<table id="tblReport_' +
                ipg +
                '" class="table table-striped table-bordered table-hover" cellspacing="0" width="100%">';

            for (var r = 0; r < data.length; r++) {
                const row = data[r];
                str += '<tr>';
                for (var c = 0; c < row.length; c++) {
                    class_name = '';
                    if (r > 0 && c > 0) {
                        class_name =
                            row[c] == 'Y'
                                ? 'text-center bg-success'
                                : 'text-center';
                    }
                    if (r == 0) {
                        class_name = 'text-center';
                    }
                    if (c == 0) {
                        str += '<td class="text-left ' + class_name + '" style="text-wrap: nowrap">';
                    } else {
                        str += '<td class="text-left ' + class_name + '">';
                    }
                    str += row[c];
                    str += '</td>';
                }
                str += '</tr>';
            }
            str += '</table>' + '</div>';
            return str;
        }

        function createReport(team_id) {
            jQuery.ajax({
                type: 'POST',
                url: SERVER_PATH + 'team/getPlayerAttendanceReport',
                async: false,
                headers: {	
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name
                },
                data: {
                    team_id: team_id,
                },
                dataType: 'json',
                complete: function (response) {
                    var json = JSON.parse(response.responseText);
                    if (json.status == 'ERROR') {
                        Swal.fire({
                            title: 'Error',
                            text: json.message,
                            icon: 'error',
                            confirmButtonText: 'OK',
                        });
                        return;
                    } else {
                        var data = json.data;
                        var htmlReport = getReportTableHtml(
                            $scope.selectedEvent.id,
                            data
                        );
                        $('#tab-report').html(htmlReport);
                    }
                },
            });

            $('#export').click(function () {
                ExportFile(event_id);
            });
        }

        function ExportFile(event_id) {
            var tab_text = "<table border='2px'><tr >";
            var j = 0;
            tab = document.getElementById("tblReport_" + event_id); // id of table

            for (j = 0; j < tab.rows.length; j++) {
                tab_text = tab_text + tab.rows[j].innerHTML + "</tr>";
                //tab_text=tab_text+"</tr>";
            }

            tab_text = tab_text + "</table>";
            tab_text = tab_text.replace(/<A[^>]*>|<\/A>/g, ""); //remove if u want links in your table
            tab_text = tab_text.replace(/<img[^>]*>/gi, ""); // remove if u want images in your table
            tab_text = tab_text.replace(/<input[^>]*>|<\/input>/gi, ""); // reomves input params

            var ua = window.navigator.userAgent;
            var msie = ua.indexOf("MSIE ");

            if (msie > 0 || !!navigator.userAgent.match(/Trident.*rv\:11\./)) {
                // If Internet Explorer
                txtArea1.document.open("txt/html", "replace");
                txtArea1.document.write(tab_text);
                txtArea1.document.close();
                txtArea1.focus();
                sa = txtArea1.document.execCommand(
                "SaveAs",
                true,
                "Say Thanks to Sumit.xls"
                );
            } //other browser not tested on IE 11
            else
                sa = window.open(
                "data:application/vnd.ms-excel," + encodeURIComponent(tab_text)
                );

            return sa;
        }
    }
);
