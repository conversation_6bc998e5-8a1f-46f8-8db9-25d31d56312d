[{"pkg": "@capacitor-community/barcode-scanner", "classpath": "com.getcapacitor.community.barcodescanner.BarcodeScanner"}, {"pkg": "@capacitor/app", "classpath": "com.capacitorjs.plugins.app.AppPlugin"}, {"pkg": "@capacitor/app-launcher", "classpath": "com.capacitorjs.plugins.applauncher.AppLauncherPlugin"}, {"pkg": "@capacitor/browser", "classpath": "com.capacitorjs.plugins.browser.BrowserPlugin"}, {"pkg": "@capacitor/clipboard", "classpath": "com.capacitorjs.plugins.clipboard.ClipboardPlugin"}, {"pkg": "@capacitor/inappbrowser", "classpath": "com.capacitorjs.osinappbrowser.InAppBrowserPlugin"}, {"pkg": "@capacitor/push-notifications", "classpath": "com.capacitorjs.plugins.pushnotifications.PushNotificationsPlugin"}, {"pkg": "@capacitor/splash-screen", "classpath": "com.capacitorjs.plugins.splashscreen.SplashScreenPlugin"}, {"pkg": "@capacitor/status-bar", "classpath": "com.capacitorjs.plugins.statusbar.StatusBarPlugin"}, {"pkg": "@capgo/capacitor-updater", "classpath": "ee.forgr.capacitor_updater.CapacitorUpdaterPlugin"}]