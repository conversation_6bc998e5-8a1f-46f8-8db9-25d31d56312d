<div class="row" id="tab-page">
    <div class="row">
        <div class="col-lg-12">
            <ol class="breadcrumb">
                <li>Setup</li>
                <li class="active"><span>Products</span></li>
            </ol>
        </div>
    </div>
</div>
<div class="row">
    <div class="col-lg-12">
        <div class="clearfix">
            <h1 class="pull-left">Products</h1>
        </div>
    </div>
</div>
<div class="col-lg-12 col-md-12 col-sm-12">
    <div class="main-box clearfix">
        <div class="container">
            <form role="form">
                <div class="row">
                    <div class="form-group col-md-4">
                        <label for="selectMainSeason">Select Main Seasons</label>
                        <select class="form-control" id="selSeason" ng-model="selectedMainEvent"
                            ng-options="event.id as event.name for event in mainEvents" ng-change="changeMainEvent()">
                        </select>
                    </div>
                    <div class="form-group col-md-4">
                        <label for="selectMainSeason">Select Seasons</label>
                        <select class="form-control" id="selSeasonChild" ng-model="selectedChildEvent"
                            ng-options="event.id as event.name + ' (' + event.type + ')' for event in childEvents">
                        </select>
                    </div>
                    <div class="form-group col-md-4">
                        <button type="button" id="btnSelect" class="btn btn-primary btn-lg"
                            ng-click="selectEvent()">Select</button>
                    </div>
                </div>
            </form>
        </div>
        <div class="tabs-wrapper profile-tabs" ng-if="isSelected">
            <ul class="nav nav-tabs">
                <li class="active"><a showtab="" data-target="#tab-tshirt" data-toggle="tab">T Shirt</a>
                </li>
                <li><a showtab="" data-target="#tab-ball" data-toggle="tab">Ball</a></li>
                <li><a showtab="" data-target="#tab-product" data-toggle="tab">Products</a></li>
            </ul>
            <div class="tab-content">
                <div class="tab-pane fade in active" id="tab-tshirt">
                    <div class="col-lg-12 ">
                        <div class="container">
                            <div class="row">
                                <h1>T Shirt</h1>
                                <div class="table-responsive">
                                    <table id="tbTShirt" class="table table-striped table-bordered table-hover"
                                        cellspacing="0" width="100%">
                                        <thead>
                                            <tr>
                                                <th>Size name</th>
                                                <th>Chest Width</th>
                                                <th>Shirt Lenght</th>
                                                <th>Group Suggestion</th>
                                                <th>Product Code</th>
                                            </tr>
                                        </thead>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="tab-pane fade" id="tab-ball">
                    <div class="col-lg-12 ">
                        <div class="container">
                            <div class="row">
                                <h1>Ball</h1>
                                <div class="table-responsive">
                                    <table id="tbBall" class="table table-striped table-bordered table-hover"
                                        cellspacing="0" width="100%">
                                        <thead>
                                            <tr>
                                                <th>Size</th>
                                                <th>Groups name</th>
                                                <th>Product Code</th>
                                            </tr>
                                        </thead>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="tab-pane fade" id="tab-product">
                    <div class="col-lg-12 ">
                        <div class="container">
                            <div class="row">
                                <h1>Products</h1>
                                <div class="table-responsive">
                                    <table id="tableProduct" class="table table-striped table-bordered table-hover"
                                        cellspacing="0" width="100%">
                                        <thead>
                                            <tr>
                                                <th>SKU</th>
                                                <th>Name</th>
                                                <th>Group</th>
                                                <th>Category</th>
                                                <th>Ordered</th>
                                            </tr>
                                        </thead>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    #btnSelect {
        margin-top: 20px;
        margin-bottom: 20px;
    }
    .select2-search__field {
        width: 100% !important;
    }
</style>