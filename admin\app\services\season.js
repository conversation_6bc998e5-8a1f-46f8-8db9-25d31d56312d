app.factory('seasonService', function ($http, $q) {
    var seasons = [];
    var selectedSeasonId = null;

    function autoSelectSeason() {
        if (localStorage.getItem('hkfaSelectedSeasonId')) {
            selectedSeasonId = localStorage.getItem('hkfaSelectedSeasonId');
        } else if (seasons.length > 0) {
            selectedSeasonId = seasons[0].id;
            localStorage.setItem('hkfaSelectedSeasonId', selectedSeasonId);
        }
    }

    return {
        loadSeasons: function () {
            var deferred = $q.defer();
            $http({
                method: 'POST',
                url: SERVER_PATH + 'product/getAllMainSeasion',
            }).then(
                function (res) {
                    if (res.data.status === 'OK') {
                        seasons = res.data.info;
                        autoSelectSeason();
                        deferred.resolve(seasons);
                    } else {
                        deferred.reject(res.data);
                    }
                },
                function (err) {
                    deferred.reject(err);
                }
            );
            return deferred.promise;
        },
        getSeasons: function () {
            return seasons;
        },
        getSelectedSeasonId: function () {
            return selectedSeasonId;
        },
        setSelectedSeasonId: function (id) {
            selectedSeasonId = id;
            localStorage.setItem('hkfaSelectedSeasonId', id);
        },
    };
});
