<style>
    .shipping_chkbox {
        width: 20px;
        height: 20px;
        border: 1px solid #ccc;
        border-radius: 5px;
        margin: auto;
    }

    .shipping_chkbox:checked {
        width: 20px;
        height: 20px;
        border: 1px solid #ccc;
        border-radius: 5px;
        margin: auto;
    }

    .toggle-switch {
        display: inline-block;
        width: 60px;
        height: 28px;
        background-color: #ccc;
        border-radius: 17px;
        position: relative;
        cursor: pointer;
    }

    .toggle-switch::before {
        content: "";
        position: absolute;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background-color: #fff;
        top: 4px;
        left: 4px;
        transition: transform 0.3s ease;
    }

    #girl-offer:checked+.toggle-switch::before {
        transform: translateX(30px);
    }

    #girl-offer:checked+.toggle-switch {
        background-color: red;
    }

    #girl-offer {
        display: none;
    }

    #girl-offer-content .form-control {
        margin-top: 7%;
        display: flex;
        align-items: center;
        gap: 16px;
    }

    #girl-offer-content .form-control {
        background-color: unset !important;
        padding: 0px;
    }

    #girl-offer-content .form-control label {
        margin: 0px;
    }
</style>
<div class="row" id="tab-page">
    <div class="row">
        <div class="col-lg-12">
            <ol class="breadcrumb">
                <li>Events</li>
                <li><a href="" ng-click="goBack()">{{event_type}}</a></li>
                <li>{{event_name}}</li>
                <li class="active"><span>Registrations</span></li>
            </ol>
        </div>
    </div>
</div>
<div class="row">
    <div class="col-lg-12">
        <div class="clearfix">
            <h1 class="pull-left">Registrations</h1>
        </div>
    </div>
</div>
<!-- form -->
<form role="form">
    <div class="row">
        <!-- Location -->
        <div class="form-group col-lg-3" id="age-group-content">
            <label>Filter by age group</label>
        </div>
        <div class="form-group col-lg-3" id="approval-content">
            <label>Filter by Payment status </label>
        </div>
        <div class="form-group col-lg-3" id="girl-offer-content">
            <div class="form-control">
                <input type="checkbox" id="girl-offer" />
                <label for="girl-offer" class="toggle-switch"></label>
                Girl offer
            </div>
        </div>
    </div>
</form>

<div class="row">
    <div class="col-lg-12">
        <div class="main-box clearfix">
            <div class="main-box-body clearfix">
                <div class="table-responsive">
                    <table id="pl-junior-table" class="table table-striped table-bordered table-hover" cellspacing="0"
                        width="100%">
                        <thead>
                            <tr>
                                <th ng-repeat="column in html_content_table">{{column}}</th>
                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>