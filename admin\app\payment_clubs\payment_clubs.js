app.controller('paymentClubsCtrl', function($scope, $rootScope, $routeParams, $http) {
    $('#page-wrapper').removeClass('nav-small');
    
    $.fn.dataTable.moment( 'D-MMM-YYYY HH:mm:ss' );
    $.fn.dataTable.moment( 'D-MMM-YYYY' );
    
    var event_id = $routeParams.id;

    var event_id = $routeParams.id;
    // get event info
    jQuery.ajax({
        type: 'POST',
        url: SERVER_PATH + "event/getEventInfo",
        async: false,
        headers: {	
            'x-user-id': $rootScope.user_id,
            'x-user-email': $rootScope.user_name
        },
        data: {
            "event_id": event_id
        },
        dataType: 'json',
        complete: function (response) {
            var jsonData = JSON.parse(response.responseText);
            var event = jsonData.info;
            event_name = event.name;
            event_type = event.type;
        }
    });
    console.log('registrationsCtrl - event_id, name, type  = ' + event_id + ', ' + event_name + ', ' + event_type);
    $scope.event_id = event_id;
    $scope.event_name = event_name;

    jQuery.ajax({
    
        type: 'POST',
          url: SERVER_PATH + "club/getClubByUser",
          async: false,
          headers: {	
            'x-user-id': $rootScope.user_id,
            'x-user-email': $rootScope.user_name
            },
          data: {
            "user_id": $rootScope.user_id
        },
          dataType: 'json',
          complete: function (response) {
            var jsonData = JSON.parse(response.responseText);
            if (jsonData.status == "OK") {
                var club = jsonData.info;
                club_id = club.id;
                club_name = club.name;

                setTimeout(() => {
                    // initialize html
                    PaymentClubPlayersTableHtml = getPaymentClubPlayersTableHtml(club_id);
                    
                    var html = '<div class="tabs-wrapper cgroups-tabs">' +
                        '<div class="tab-content">' +
                            '<div class="tab-pane fade in active" id="tab-ClubPlayers">' +
                            PaymentClubPlayersTableHtml +
                            '</div>' +
                        '</div>' +
                    '</div>';
                    
                    $('#paymentClubsPageContent').html(html);
    
                    initPaymentClubsTable(club_id);
                }, 100);
                
            } else {
                BootstrapDialog.show({
                    title: 'ERROR',
                    type: BootstrapDialog.TYPE_WARNING,
                    message: jsonData.message
                });
            }
              
          }
    });
    
    $scope.club_name = club_name;
    
    function getPaymentClubPlayersTableHtml(club_id) {
        var str = ''+
        '<div class="form-group">' +
            '<label>Filter by Status</label>' +
            '<br/>' +
        '</div>' +
          '<div class="table-responsive">' +
              '<table id="tblPaymentClubPlayers_' + club_id + '" class="table table-striped table-bordered table-hover" cellspacing="0" width="100%">' +
                  '<thead>' +
                      '<tr>' +
                        '<th></th>' +
                        '<th>Player</th>' +
                        '<th>Year</th>' +
                        '<th>Group</th>' +
                        '<th>Parent</th>' +
                        '<th>Email</th>' +
                        '<th>App.date</th>' +
                        '<th>App.status</th>' +
                        '<th>Inv.number</th>' +
                        '<th>Amount</th>' +
                        '<th>Status</th>' +
                      '</tr>' +
                  '</thead>' +
              '</table>' +
          '</div>';
      return str;
      }

      function initPaymentClubsTable(club_id) {
        function cbDropdown(column) {
            return $('<ul>', {
              'class': 'cb-dropdown'
            }).appendTo($('<div>', {
              'class': 'cb-dropdown-wrap'
            }).appendTo(column));
          }
        tablePaymentClubPlayers_ = $('#tblPaymentClubPlayers_' + club_id).DataTable( {
            dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
            stateSave: true,
            deferRender: true,
            ajax: {
                url: SERVER_PATH + "invoice/getPaymentClubPlayers",
                type: 'POST',
                headers: {	
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name
                },
                  data: {
                    "club_id": club_id,
                    "event_id": event_id
                },
                  dataType: 'json',
                  complete: function (response) {
                },
                  error: function(xhr, status, error) {
                  },
            },
            language: {
                paginate: {
                    previous: '<i class="fa fa-chevron-left"></i>',
                    next: '<i class="fa fa-chevron-right"></i>'
                }
            },
              columns: [
                {
                    data: 'DT_RowId',
                    // defaultContent: '',
                    // className: 'select-checkbox',
                    // orderable: false
                    targets: 0,
                    render: function (data, type, row, meta) {
                        if (type === 'display') {
                            data = '<div class="checkbox"><input type="checkbox" class="dt-checkboxes"><label></label></div>';
                        }

                        return data;
                    },
                    checkboxes: {
                        selectRow: true,
                        selectAllRender: '<div class="checkbox"><input type="checkbox" class="dt-checkboxes"><label></label></div>'
                    }
                },
                {
                    data: null,
                    render: function (data, type, row) {
                        return data.players.surname + ' ' + data.players.other_name;
                    }
                },
                { data: "players.dob", className: "center" },
                // { data: "pgroups.name" },
                { data: "groups.name", className: "center" },
                {
                    data: null,
                    render: function (data, type, row) {
                        return data.parens.surname + ' ' + data.parens.other_name;
                    }
                },
                { data: "parens.email" },
                // { data: "parens.hkfc_account", className: "center" },
                { data: "registrations.approved_date", className: "center" },
                {
                    data: "registrations.approval_status",
                    className: "center",
                    render: function (data, type, full, meta) {
                        switch (data) {
                            case APPROVAL_STATUS_Approve: return '<span class="label label-primary">' + data + '</span>';
                           
                            default: return '<span class="label label-default">' + data + '</span>';
                        }
                    }
                },
                { data: "invoices.invoice_number", className: "center" },
                { data: "registrations.amount", className: "center" },
                { 
                    data: "invoices.status",
                    name: "name",
                    name_index: "name_index",
                    className: "center"
                }
            ],
            select: {
                style: 'multi',
            },
            buttons: [
                { extend: 'colvis', text: 'Columns' }
            ],
            initComplete: function () {
                this.api().columns('10').every(function () {
                    var column = this;
                    //added class "mymsel"
                    var ddmenu = cbDropdown($(column.header()))
                        .on('change',':checkbox', function () {
                            var vals = $(':checked', ddmenu).map(function (index, element) {
                                return $.fn.dataTable.util.escapeRegex($(element).val());
                            }).toArray().join('|');

                            column
                                .search(vals.length > 0 ? '^(' + vals + ')$' : '', true, false)
                                .draw();
                        });

                    column.data().unique().sort().each(function (d, j) {
                        var // wrapped
                            $label = $('<label>'),
                            $text = $('<span>', {
                                style: 'margin: 0px 15px 10px 0px',
                                text: d
                            }),
                            $cb = $('<input>', {
                                type: 'checkbox',
                                style: 'height: 17px; width: 17px; vertical-align: bottom; position: relative; top: -1px; *overflow: hidden; margin-right: 2px;',
                                value: d
                            });
                        
                        $cb.appendTo($label);
                        $text.appendTo($label);

                        ddmenu.append($label);
                        ddmenu.add().css('margin','0px 0px -20px -35px').appendTo(".form-group")
                    });
                });

                $(".cb-dropdown-wrap").each(function () {
                    console.log($(this).parent().width());
                    $(this).width($(this).parent().width());
                });
            },
            order: [[1, 'asc']],
            "lengthMenu": [[10, 25, 50, 100, -1], [10, 25, 50, 100, "All"]]
        } );
    }

});