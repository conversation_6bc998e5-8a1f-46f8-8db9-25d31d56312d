<div class="row">
    <div class="col-lg-12">
        <div class="clearfix">
            <h1 class="pull-left">Parents</h1>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-12">
        <div class="main-box clearfix">
            <div class="main-box-body clearfix">
                <div class="table-responsive">
                    <table id="parent_table" class="table table-striped table-bordered table-hover" cellspacing="0"
                        width="100%">
                        <thead>
                            <tr>
                                <th>Surname</th>
                                <th>Other name</th>
                                <th>Email</th>
                                <th>Phone number</th>
                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript" language="javascript" class="init">

    $(document).ready(function () {

        user_id = localStorage.getItem('hkjflApp.user_id');
		user_name = localStorage.getItem('hkjflApp.user_name');

        var table;
        var editor = new $.fn.dataTable.Editor({
            ajax: {
                type: 'POST',
                url: SERVER_PATH + "paren/setParens",
                data: {
                    // "pgroup_id": pgroup_id
                },
                headers: {	
                    'x-user-id': user_id,
                    'x-user-email': user_name
                },
                dataType: 'json',
                complete: function (response) {
                    var jsonData = JSON.parse(response.responseText);
                    // --- may need to reload
                    if (DEVELOPMENT_ENVIRONMENT) console.log('status = ' + jsonData.status);
                    if (jsonData.status == 'OK') {
                        if (DEVELOPMENT_ENVIRONMENT) console.log('Before reload');
                        table.ajax.reload();
                    }
                },
                error: function (xhr, status, error) {
                },
            },
            table: "#parent_table",
            formOptions: {
                main: {
                    onBlur: 'none'
                }
            },
            i18n: {
                create: {
                    button: "New",
                    title: "Create new parent",
                    submit: "Create"
                },
                edit: {
                    button: "Edit",
                    title: "Edit parent",
                    submit: "Save"
                },
                remove: {
                    button: "Delete",
                    title: "Delete parent",
                    submit: "Delete",
                    confirm: {
                        _: "Are you sure you want to delete these parents?",
                        1: "Are you sure you want to delete this parent?"
                    }
                },
                error: {
                    system: "System error, please contact parent."
                },
            },
            fields: [
                {
                    label: "Surname:",
                    name: "parens.surname"
                }, {
                    label: "Other name",
                    name: "parens.other_name"
                }, {
                    label: "Email",
                    name: "parens.email"
                }, {
                    name: "parens.type",
                    type: "hidden",
                    def: TYPE_PARENT
                },
                {
                    label: "Create user:",
                    name: "create_user",
                    type: "checkbox",
                    separator: "|",
                    options: [
                        { label: '', value: 1 }
                    ]
                },
                {
                    name: "parens.country_code",
                    type: "hidden",
                    def: '+852'
                },
                {
                    name: "parens.iso_code",
                    type: "hidden",
                    def: 'hk'
                },
                {
                    label: "Phone number",
                    name: "parens.phone",
                    type: "telephone",
                    opts: {
                        preferredCountries: ['hk', 'cn'],
                        initialCountry: 'hk'
                    }
                }
            ]
        });

        var table = $('#parent_table').DataTable({
            dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
            stateSave: true,
            deferRender: true,
            processing: true,
            serverSide: true,
            ajax: {
                url: SERVER_PATH + "paren/getParens",
                type: 'POST',
                data: {
                    // "pgroup_id": pgroup_id
                },
                headers: {	
                    'x-user-id': user_id,
                    'x-user-email': user_name
                },
                dataType: 'json',
                complete: function (response) {
                },
                error: function (xhr, status, error) {
                },
            },
            language: {
                info: "Showing _START_ to _END_ of _TOTAL_ parent",
                infoEmpty: "Showing 0 to 0 of 0 parents",
                lengthMenu: "Show _MENU_ parents",
                select: {
                    rows: {
                        "_": "You have selected %d parents",
                        "0": "Click an parent to select",
                        "1": "1 parent selected"
                    },
                },
                paginate: {
                    previous: '<i class="fa fa-chevron-left"></i>',
                    next: '<i class="fa fa-chevron-right"></i>'
                }
            },
            columns: [
                { data: "parens.surname" },
                { data: "parens.other_name" },
                { data: "parens.email" },
                { data: "parens.phone", className: "center" }
            ],
            select: {
                style: 'single',
                selector: 'td:first-child',
            },
            order: [[1, 'asc']],
            lengthMenu: [[10, 25, 50, 100, 1000], [10, 25, 50, 100, 1000]],
            buttons: [
                { extend: "create", editor: editor },
                { extend: "edit", editor: editor },
                { extend: "remove", editor: editor },
                {
                    extend: "selectedSingle",
                    text: "Add to Coach",
                    action: function (e, dt, node, config) {
                        let data = table.rows({ selected: true }).data();

                        let parent_id = data[0]['parens']['id']

                        editorCoach = new $.fn.dataTable.Editor({
                            ajax: {
                                type: 'POST',
                                url: SERVER_PATH + "paren/addParentToCoach",
                                async: false,
                                data: {
                                    'parent_id': parent_id
                                },
                                headers: {	
                                    'x-user-id': user_id,
                                    'x-user-email': user_name
                                },
                                dataType: 'json',
                                complete: function (response) {
                                    var jsonData = JSON.parse(response.responseText);
                                    if (typeof jsonData.fieldErrors == 'undefined') {
                                        if (jsonData.status == "OK") {
                                            BootstrapDialog.show({
                                                title: 'SUCCESS',
                                                type: BootstrapDialog.TYPE_SUCCESS,
                                                message: jsonData.message
                                            });

                                            table.ajax.reload();
                                        } else {
                                            BootstrapDialog.show({
                                                title: 'Error',
                                                type: BootstrapDialog.TYPE_DANGER,
                                                message: jsonData.message
                                            });
                                        }
                                    }
                                },
                                error: function (xhr, status, error) {

                                },
                            },
                            formOptions: {
                                main: {
                                    onBlur: 'none'
                                }
                            },
                            fields: [
                                {
                                    label: "Coach ID:",
                                    name: "coach_id",
                                }
                            ]
                        });
                        editorCoach
                            .title('Add to Coach')
                            .buttons(
                                {
                                    label: "Submit",
                                    fn: function () { this.submit(); }
                                }
                            )
                            .edit()
                            .open();
                    }
                },
                { extend: 'colvis', text: 'Columns' }
            ]
        });
        editor.on('initEdit', function (e, type) {
            editor.disable('create_user');
            editor.hide('create_user');
        });
        editor.on('initCreate', function (e, type) {
            editor.enable('create_user');
            editor.show();
        });

    });

</script>