<style>
    * {
        font: 17px Calibri;
    }

    /* latin */
    @font-face {
        font-family: '<PERSON><PERSON>';
        font-style: normal;
        font-weight: 100;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/<PERSON>tham-Regular.ttf) format('truetype');
    }

    @font-face {
        font-family: 'Bentham';
        font-style: normal;
        font-weight: 200;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/<PERSON>tham-Regular.ttf) format('truetype');
    }

    @font-face {
        font-family: 'Ben<PERSON>';
        font-style: normal;
        font-weight: 300;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/<PERSON>tham-Regular.ttf) format('truetype');
    }

    @font-face {
        font-family: 'Bentham';
        font-style: normal;
        font-weight: 400;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/<PERSON>tham-Regular.ttf) format('truetype');
    }

    @font-face {
        font-family: '<PERSON><PERSON>';
        font-style: normal;
        font-weight: 500;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/<PERSON>tham-Regular.ttf) format('truetype');
    }

    @font-face {
        font-family: 'Bentham';
        font-style: normal;
        font-weight: 600;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/Bentham-Regular.ttf) format('truetype');
    }

    @font-face {
        font-family: 'Bentham';
        font-style: normal;
        font-weight: 700;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/Bentham-Regular.ttf) format('truetype');
    }

    @font-face {
        font-family: 'Bentham';
        font-style: normal;
        font-weight: 800;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/Bentham-Regular.ttf) format('truetype');
    }

    @font-face {
        font-family: 'Bentham';
        font-style: normal;
        font-weight: 900;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/Bentham-Regular.ttf) format('truetype');
    }

    /* vietnamese */
    @font-face {
        font-family: 'Dosis';
        font-style: normal;
        font-weight: 100;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/Dosis-ExtraLight.ttf) format('truetype');
    }

    /* vietnamese */
    @font-face {
        font-family: 'Dosis';
        font-style: normal;
        font-weight: 200;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/Dosis-ExtraLight.ttf) format('truetype');
    }

    /* vietnamese */
    @font-face {
        font-family: 'Dosis';
        font-style: normal;
        font-weight: 300;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/Dosis-Light.ttf) format('truetype');
    }

    /* vietnamese */
    @font-face {
        font-family: 'Dosis';
        font-style: normal;
        font-weight: 400;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/Dosis-Regular.ttf) format('truetype');
    }

    /* vietnamese */
    @font-face {
        font-family: 'Dosis';
        font-style: normal;
        font-weight: 500;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/Dosis-Medium.ttf) format('truetype');
    }

    /* vietnamese */
    @font-face {
        font-family: 'Dosis';
        font-style: normal;
        font-weight: 600;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/Dosis-SemiBold.ttf) format('truetype');
    }

    /* vietnamese */
    @font-face {
        font-family: 'Dosis';
        font-style: normal;
        font-weight: 700;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/Dosis-Bold.ttf) format('truetype');
    }

    /* vietnamese */
    @font-face {
        font-family: 'Dosis';
        font-style: normal;
        font-weight: 800;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/Dosis-ExtraBold.ttf) format('truetype');
    }

    /* vietnamese */
    @font-face {
        font-family: 'Dosis';
        font-style: normal;
        font-weight: 900;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/Dosis-ExtraBold.ttf) format('truetype');
    }

    /* cyrillic-ext */
    @font-face {
        font-family: 'Fira Sans';
        font-style: italic;
        font-weight: 100;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/FiraSans-ThinItalic.ttf) format('truetype');
    }

    /* cyrillic-ext */
    @font-face {
        font-family: 'Fira Sans';
        font-style: italic;
        font-weight: 200;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/FiraSans-ExtraLightItalic.ttf) format('truetype');
    }

    /* cyrillic-ext */
    @font-face {
        font-family: 'Fira Sans';
        font-style: italic;
        font-weight: 300;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/FiraSans-LightItalic.ttf) format('truetype');
    }

    /* cyrillic-ext */
    @font-face {
        font-family: 'Fira Sans';
        font-style: italic;
        font-weight: 400;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/FiraSans-Italic.ttf) format('truetype');
    }

    /* cyrillic-ext */
    @font-face {
        font-family: 'Fira Sans';
        font-style: italic;
        font-weight: 500;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/FiraSans-MediumItalic.ttf) format('truetype');
    }

    /* cyrillic-ext */
    @font-face {
        font-family: 'Fira Sans';
        font-style: italic;
        font-weight: 600;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/FiraSans-SemiBoldItalic.ttf) format('truetype');
    }

    /* cyrillic-ext */
    @font-face {
        font-family: 'Fira Sans';
        font-style: italic;
        font-weight: 700;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/FiraSans-BoldItalic.ttf.ttf) format('truetype');
    }

    /* cyrillic-ext */
    @font-face {
        font-family: 'Fira Sans';
        font-style: italic;
        font-weight: 800;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/FiraSans-ExtraBoldItalic.ttf) format('truetype');
    }

    /* cyrillic-ext */
    @font-face {
        font-family: 'Fira Sans';
        font-style: italic;
        font-weight: 900;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/FiraSans-BlackItalic.ttf) format('truetype');
    }

    /* cyrillic-ext */
    @font-face {
        font-family: 'Fira Sans';
        font-style: normal;
        font-weight: 100;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/FiraSans-Thin.ttf) format('truetype');
        unicode-range: U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
    }

    /* cyrillic-ext */
    @font-face {
        font-family: 'Fira Sans';
        font-style: normal;
        font-weight: 200;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/FiraSans-ExtraLight.ttf) format('truetype');
    }

    /* cyrillic-ext */
    @font-face {
        font-family: 'Fira Sans';
        font-style: normal;
        font-weight: 300;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/FiraSans-Light.ttf) format('truetype');
    }

    /* cyrillic-ext */
    @font-face {
        font-family: 'Fira Sans';
        font-style: normal;
        font-weight: 400;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/FiraSans-Regular.ttf) format('truetype');
    }

    /* cyrillic-ext */
    @font-face {
        font-family: 'Fira Sans';
        font-style: normal;
        font-weight: 500;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/FiraSans-Medium.ttf) format('truetype');
    }

    /* cyrillic-ext */
    @font-face {
        font-family: 'Fira Sans';
        font-style: normal;
        font-weight: 600;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/FiraSans-SemiBold.ttf) format('truetype');
    }

    /* cyrillic-ext */
    @font-face {
        font-family: 'Fira Sans';
        font-style: normal;
        font-weight: 700;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/FiraSans-Bold.ttf.ttf) format('truetype');
    }

    /* cyrillic-ext */
    @font-face {
        font-family: 'Fira Sans';
        font-style: normal;
        font-weight: 800;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/FiraSans-ExtraBold.ttf) format('truetype');
    }

    /* cyrillic-ext */
    @font-face {
        font-family: 'Fira Sans';
        font-style: normal;
        font-weight: 900;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/FiraSans-Black.ttf) format('truetype');
    }


    /* latin */
    @font-face {
        font-family: 'Gloria Hallelujah';
        font-style: normal;
        font-weight: 100;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/GloriaHallelujah-Regular.ttf) format('truetype');
    }

    @font-face {
        font-family: 'Gloria Hallelujah';
        font-style: normal;
        font-weight: 200;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/GloriaHallelujah-Regular.ttf) format('truetype');
    }

    @font-face {
        font-family: 'Gloria Hallelujah';
        font-style: normal;
        font-weight: 300;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/GloriaHallelujah-Regular.ttf) format('truetype');
    }

    @font-face {
        font-family: 'Gloria Hallelujah';
        font-style: normal;
        font-weight: 400;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/GloriaHallelujah-Regular.ttf) format('truetype');
    }

    @font-face {
        font-family: 'Gloria Hallelujah';
        font-style: normal;
        font-weight: 500;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/GloriaHallelujah-Regular.ttf) format('truetype');
    }

    @font-face {
        font-family: 'Gloria Hallelujah';
        font-style: normal;
        font-weight: 600;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/GloriaHallelujah-Regular.ttf) format('truetype');
    }

    @font-face {
        font-family: 'Gloria Hallelujah';
        font-style: normal;
        font-weight: 700;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/GloriaHallelujah-Regular.ttf) format('truetype');
    }

    @font-face {
        font-family: 'Gloria Hallelujah';
        font-style: normal;
        font-weight: 800;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/GloriaHallelujah-Regular.ttf) format('truetype');
    }

    @font-face {
        font-family: 'Gloria Hallelujah';
        font-style: normal;
        font-weight: 900;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/GloriaHallelujah-Regular.ttf) format('truetype');
    }

    /* thai */
    @font-face {
        font-family: 'KoHo';
        font-style: italic;
        font-weight: 100;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/KoHo-ExtraLightItalic.ttf) format('truetype');
    }

    /* thai */
    @font-face {
        font-family: 'KoHo';
        font-style: italic;
        font-weight: 200;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/KoHo-ExtraLightItalic.ttf) format('truetype');
    }

    /* thai */
    @font-face {
        font-family: 'KoHo';
        font-style: italic;
        font-weight: 300;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/KoHo-LightItalic.ttf) format('truetype');
    }

    /* thai */
    @font-face {
        font-family: 'KoHo';
        font-style: italic;
        font-weight: 400;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/KoHo-Italic.ttf) format('truetype');
    }

    /* thai */
    @font-face {
        font-family: 'KoHo';
        font-style: italic;
        font-weight: 500;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/KoHo-MediumItalic.ttf) format('truetype');
    }

    /* thai */
    @font-face {
        font-family: 'KoHo';
        font-style: italic;
        font-weight: 600;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/KoHo-SemiBoldItalic.ttf) format('truetype');
    }

    /* thai */
    @font-face {
        font-family: 'KoHo';
        font-style: italic;
        font-weight: 700;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/KoHo-BoldItalic.ttf) format('truetype');
    }

    /* thai */
    @font-face {
        font-family: 'KoHo';
        font-style: italic;
        font-weight: 800;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/KoHo-BoldItalic.ttf) format('truetype');
    }

    /* thai */
    @font-face {
        font-family: 'KoHo';
        font-style: italic;
        font-weight: 900;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/KoHo-BoldItalic.ttf) format('truetype');
    }

    /* thai */
    @font-face {
        font-family: 'KoHo';
        font-style: normal;
        font-weight: 100;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/KoHo-ExtraLight.ttf) format('truetype');
    }

    /* thai */
    @font-face {
        font-family: 'KoHo';
        font-style: normal;
        font-weight: 200;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/KoHo-ExtraLight.ttf) format('truetype');
    }

    /* thai */
    @font-face {
        font-family: 'KoHo';
        font-style: normal;
        font-weight: 300;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/KoHo-Light.ttf) format('truetype');
    }

    /* thai */
    @font-face {
        font-family: 'KoHo';
        font-style: normal;
        font-weight: 400;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/KoHo-Regular.ttf) format('truetype');
    }

    /* thai */
    @font-face {
        font-family: 'KoHo';
        font-style: normal;
        font-weight: 500;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/KoHo-Medium.ttf) format('truetype');
    }

    /* thai */
    @font-face {
        font-family: 'KoHo';
        font-style: normal;
        font-weight: 600;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/KoHo-SemiBold.ttf) format('truetype');
    }

    /* thai */
    @font-face {
        font-family: 'KoHo';
        font-style: normal;
        font-weight: 700;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/KoHo-Bold.ttf) format('truetype');
    }

    /* thai */
    @font-face {
        font-family: 'KoHo';
        font-style: normal;
        font-weight: 800;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/KoHo-Bold.ttf) format('truetype');
    }

    /* thai */
    @font-face {
        font-family: 'KoHo';
        font-style: normal;
        font-weight: 900;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/KoHo-Bold.ttf) format('truetype');
    }

    /* cyrillic-ext */
    @font-face {
        font-family: 'Montserrat';
        font-style: italic;
        font-weight: 100;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/Montserrat-ThinItalic.ttf) format('truetype');
    }

    @font-face {
        font-family: 'Montserrat';
        font-style: italic;
        font-weight: 200;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/Montserrat-ExtraLightItalic.ttf) format('truetype');
    }

    @font-face {
        font-family: 'Montserrat';
        font-style: italic;
        font-weight: 300;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/Montserrat-LightItalic.ttf) format('truetype');
    }

    @font-face {
        font-family: 'Montserrat';
        font-style: italic;
        font-weight: 400;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/Montserrat-Italic.ttf) format('truetype');
    }

    @font-face {
        font-family: 'Montserrat';
        font-style: italic;
        font-weight: 500;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/Montserrat-MediumItalic.ttf) format('truetype');
    }

    @font-face {
        font-family: 'Montserrat';
        font-style: italic;
        font-weight: 600;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/Montserrat-SemiBoldItalic.ttf) format('truetype');
    }

    @font-face {
        font-family: 'Montserrat';
        font-style: italic;
        font-weight: 700;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/Montserrat-BoldItalic.ttf) format('truetype');
    }

    @font-face {
        font-family: 'Montserrat';
        font-style: italic;
        font-weight: 800;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/Montserrat-ExtraBoldItalic.ttf) format('truetype');
    }

    @font-face {
        font-family: 'Montserrat';
        font-style: italic;
        font-weight: 900;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/Montserrat-BlackItalic.ttf) format('truetype');
    }

    /* cyrillic-ext */
    @font-face {
        font-family: 'Montserrat';
        font-style: normal;
        font-weight: 100;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/Montserrat-Thin.ttf) format('truetype');
    }

    @font-face {
        font-family: 'Montserrat';
        font-style: normal;
        font-weight: 200;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/Montserrat-ExtraLight.ttf) format('truetype');
    }

    @font-face {
        font-family: 'Montserrat';
        font-style: normal;
        font-weight: 300;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/Montserrat-Light.ttf) format('truetype');
    }

    @font-face {
        font-family: 'Montserrat';
        font-style: normal;
        font-weight: 400;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/Montserrat-Regular.ttf) format('truetype');
    }

    @font-face {
        font-family: 'Montserrat';
        font-style: normal;
        font-weight: 500;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/Montserrat-Medium.ttf) format('truetype');
    }

    @font-face {
        font-family: 'Montserrat';
        font-style: normal;
        font-weight: 600;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/Montserrat-SemiBold.ttf) format('truetype');
    }

    @font-face {
        font-family: 'Montserrat';
        font-style: italic;
        font-weight: 700;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/Montserrat-Bold.ttf) format('truetype');
    }

    @font-face {
        font-family: 'Montserrat';
        font-style: italic;
        font-weight: 800;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/Montserrat-ExtraBold.ttf) format('truetype');
    }

    @font-face {
        font-family: 'Montserrat';
        font-style: italic;
        font-weight: 900;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/Montserrat-Black.ttf) format('truetype');
    }

    /* cyrillic-ext */
    @font-face {
        font-family: 'Open Sans';
        font-style: italic;
        font-weight: 100;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/OpenSans-LightItalic.ttf) format('truetype');
    }

    /* cyrillic-ext */
    @font-face {
        font-family: 'Open Sans';
        font-style: italic;
        font-weight: 200;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/OpenSans-LightItalic.ttf) format('truetype');
    }

    /* cyrillic-ext */
    @font-face {
        font-family: 'Open Sans';
        font-style: italic;
        font-weight: 300;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/OpenSans-LightItalic.ttf) format('truetype');
    }

    /* cyrillic-ext */
    @font-face {
        font-family: 'Open Sans';
        font-style: italic;
        font-weight: 400;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/OpenSans-Italic.ttf) format('truetype');
    }

    /* cyrillic-ext */
    @font-face {
        font-family: 'Open Sans';
        font-style: italic;
        font-weight: 500;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/OpenSans-MediumItalic.ttf) format('truetype');
    }

    /* cyrillic-ext */
    @font-face {
        font-family: 'Open Sans';
        font-style: italic;
        font-weight: 600;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/OpenSans-SemiBoldItalic.ttf) format('truetype');
    }

    /* cyrillic-ext */
    @font-face {
        font-family: 'Open Sans';
        font-style: italic;
        font-weight: 700;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/OpenSans-BoldItalic.ttf) format('truetype');
    }

    /* cyrillic-ext */
    @font-face {
        font-family: 'Open Sans';
        font-style: italic;
        font-weight: 800;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/OpenSans-ExtraBoldItalic.ttf) format('truetype');
    }

    /* cyrillic-ext */
    @font-face {
        font-family: 'Open Sans';
        font-style: italic;
        font-weight: 900;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/OpenSans-ExtraBoldItalic.ttf) format('truetype');
    }

    /* cyrillic-ext */
    @font-face {
        font-family: 'Open Sans';
        font-style: normal;
        font-weight: 100;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/OpenSans-Light.ttf) format('truetype');
    }

    /* cyrillic-ext */
    @font-face {
        font-family: 'Open Sans';
        font-style: normal;
        font-weight: 200;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/OpenSans-Light.ttf) format('truetype');
    }

    /* cyrillic-ext */
    @font-face {
        font-family: 'Open Sans';
        font-style: normal;
        font-weight: 300;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/OpenSans-Light.ttf) format('truetype');
    }

    /* cyrillic-ext */
    @font-face {
        font-family: 'Open Sans';
        font-style: normal;
        font-weight: 400;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/OpenSans-Regular.ttf) format('truetype');
    }

    /* cyrillic-ext */
    @font-face {
        font-family: 'Open Sans';
        font-style: normal;
        font-weight: 500;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/OpenSans-Medium.ttf) format('truetype');
    }

    /* cyrillic-ext */
    @font-face {
        font-family: 'Open Sans';
        font-style: normal;
        font-weight: 600;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/OpenSans-SemiBold.ttf) format('truetype');
    }

    /* cyrillic-ext */
    @font-face {
        font-family: 'Open Sans';
        font-style: normal;
        font-weight: 700;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/OpenSans-Bold.ttf) format('truetype');
    }

    /* cyrillic-ext */
    @font-face {
        font-family: 'Open Sans';
        font-style: normal;
        font-weight: 800;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/OpenSans-ExtraBold.ttf) format('truetype');
    }

    /* cyrillic-ext */
    @font-face {
        font-family: 'Open Sans';
        font-style: normal;
        font-weight: 900;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/OpenSans-ExtraBold.ttf) format('truetype');
    }

    /* cyrillic-ext */
    @font-face {
        font-family: 'Roboto';
        font-style: italic;
        font-weight: 100;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/Roboto-ThinItalic.ttf) format('truetype');
    }

    @font-face {
        font-family: 'Roboto';
        font-style: italic;
        font-weight: 200;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/Roboto-ThinItalic.ttf) format('truetype');
    }

    /* cyrillic-ext */
    @font-face {
        font-family: 'Roboto';
        font-style: italic;
        font-weight: 300;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/Roboto-LightItalic.ttf) format('truetype');
    }

    /* cyrillic-ext */
    @font-face {
        font-family: 'Roboto';
        font-style: italic;
        font-weight: 400;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/Roboto-Italic.ttf) format('truetype');
    }

    /* cyrillic-ext */
    @font-face {
        font-family: 'Roboto';
        font-style: italic;
        font-weight: 500;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/Roboto-MediumItalic.ttf) format('truetype');
    }

    /* cyrillic-ext */
    @font-face {
        font-family: 'Roboto';
        font-style: italic;
        font-weight: 600;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/Roboto-MediumItalic.ttf) format('truetype');
    }

    /* cyrillic-ext */
    @font-face {
        font-family: 'Roboto';
        font-style: italic;
        font-weight: 700;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/Roboto-BoldItalic.ttf.ttf) format('truetype');
    }

    /* cyrillic-ext */
    @font-face {
        font-family: 'Roboto';
        font-style: italic;
        font-weight: 800;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/Roboto-BoldItalic.ttf.ttf) format('truetype');
    }

    /* cyrillic-ext */
    @font-face {
        font-family: 'Roboto';
        font-style: italic;
        font-weight: 900;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/Roboto-BlackItalic.ttf) format('truetype');
    }

    /* cyrillic-ext */
    @font-face {
        font-family: 'Roboto';
        font-style: normal;
        font-weight: 100;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/Roboto-Thin.ttf) format('truetype');
    }

    /* cyrillic-ext */
    @font-face {
        font-family: 'Roboto';
        font-style: normal;
        font-weight: 200;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/Roboto-Thin.ttf) format('truetype');
    }

    /* cyrillic-ext */
    @font-face {
        font-family: 'Roboto';
        font-style: normal;
        font-weight: 300;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/Roboto-Light.ttf) format('truetype');
    }

    /* cyrillic-ext */
    @font-face {
        font-family: 'Roboto';
        font-style: normal;
        font-weight: 400;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/Roboto-Regular.ttf) format('truetype');
    }

    /* cyrillic-ext */
    @font-face {
        font-family: 'Roboto';
        font-style: normal;
        font-weight: 500;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/Roboto-Medium.ttf) format('truetype');
    }

    /* cyrillic-ext */
    @font-face {
        font-family: 'Roboto';
        font-style: normal;
        font-weight: 600;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/Roboto-Medium.ttf) format('truetype');
    }

    /* cyrillic-ext */
    @font-face {
        font-family: 'Roboto';
        font-style: normal;
        font-weight: 700;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/Roboto-Bold.ttf.ttf) format('truetype');
    }

    /* cyrillic-ext */
    @font-face {
        font-family: 'Roboto';
        font-style: normal;
        font-weight: 800;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/Roboto-Bold.ttf.ttf) format('truetype');
    }

    /* cyrillic-ext */
    @font-face {
        font-family: 'Roboto';
        font-style: normal;
        font-weight: 900;
        font-display: swap;
        src: url(../../framework/dompdf/lib/fonts/Roboto-Black.ttf) format('truetype');
    }

    .mainContainer {
        position: relative;
        padding: 0;
        min-width: 250px;
        min-height: 250px;
        display: inline-block;
        margin: 0 auto;
    }

    .mainContainer img {
        border: none;
    }

    #textArea {
        display: block;
        padding: 10px 5px;
    }

    #theText {
        position: absolute;
        top: 90px;
        left: 0;
        background: #000;
        background: rgba(0, 0, 0, 0.1);
        color: #fff;
        width: auto;
        text-align: left;
        border: dashed 2px #ff7f27;
        font: 15px Calibri;
        display: block;
        cursor: move;
    }

    canvas {
        max-width: 100%;
    }
</style>

<div class="row" ng-app="hkjflApp">
    <div class="col-lg-12">
        <ol class="breadcrumb">
            <li>Setup</li>
            <li>
                <a data-match-route="/tables/event_editor" href="#/tables/event_editor">{{event_name}}</a>
            </li>
            <li><a data-match-route="/certificates/{{eventId}}" href="#/certificates/{{eventId}}">Certificate</a></li>
            <li class="active text-uppercase"><span>Create Certificate</span></li>
        </ol>
        <h1 class="pull-left">Create Certificate</h1>
    </div>
</div>
<div class="container">
    <div class="row">
        <div class="col-lg-6 col-lg-offset-3">
            <div class="main-box">
                <header class="main-box-header clearfix">
                    <h2 class="left">Form Enter The Certificate</h2>
                </header>
                <div class="main-box-body clearfix">
                    <form name="formCreateCertificate" method="get" id="formCreateCertificate">
                        <div class="form-group">
                            <label for="certificate_name">Certificate Name</label>
                            <input type="text" class="form-control" id="certificate_name" ng-model="certificate_name"
                                name="certificate_name" placeholder="Enter certificate name" required ng-change="resetForm('certificate_name')">
                        </div>
                        <div class="form-group">
                            <label for="certificate_name">Description</label>
                            <textarea type="text" class="form-control" id="description" ng-model="description"
                                name="description" placeholder="Enter description" rows="4" required ng-change="resetForm('description')">
                            </textarea>
                        </div>
                        <div class="form-group">
                            <label for="certificate_name">Import Certificate File</label>
                            <input type="file" id="file" name="uploadFile" on-file-change="showImage" ng-model="file" />
                        </div>

                        <div class="form-group text-right">
                            <button type="submit" class="btn btn-success btn-lg"
                                ng-click="createNewCertificate()">Create</button>
                        </div>
                    </form>
                    <div class="form-group">
                        <label for="certificate_name">Demo text</label>
                        <input type="text" class="form-control" id="certificate_name" ng-model="t"
                            name="certificate_name" placeholder="Enter certificate name" required>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div style="text-align:center;">
        <div style="position: relative;width: 794px;margin: auto;background-color: white;height: 1132px;"
            ng-if="selectedPage=='portrait'">
            <div class="mainContainer" id='mainContainer'>

                <img style=" width: 794px !important;
                height:1123px !important;" src="{{systemImgPath}}/demo-img-ceneficate.png" id="myimage" alt="" />

                <div id='theText'
                    ng-style="{'color' : color,'font-size' : textSize, 'font-family' : fontFamily, 'font-weight' : fontWeight}" style="line-height: 1;"
                    onmousedown='this.style.border = "dashed 2px #FF7F27";'>
                    {{t}}</div>
            </div>
        </div>

        <div style="position: relative;margin: auto;background-color: white;width: 1132px ;padding-top:50px;padding-bottom: 50px;"
            ng-if="selectedPage=='landscape'">
            <div class="mainContainer" id='mainContainer'>

                <img src="{{systemImgPath}}/demo-img-ceneficate.png" id="myimage" alt="" style="width: 1000px;" />

                <div id='theText'
                    ng-style="{'color' : color,'font-size' : textSize, 'font-family' : fontFamily, 'font-weight' : fontWeight}"
                    onmousedown='this.style.border = "dashed 2px #FF7F27";'>
                    {{t}}</div>
            </div>
        </div>

        <div class="row" style="display: flex;justify-content:space-between;margin-top: 102px;">
            <div class="col-sm">
                <p>Font size</p>
                <select name="font_size" id="font_size" ng-model="textSize">
                    <option value="{{i}}" ng-repeat="i in getArraySize()">{{i}}</option>
                </select>
            </div>
            <div class="col-sm">
                <p>Color</p>
                <input type='text' id="color" ng-model="color" />
            </div>
            <div class="col-sm">
                <p>Font family</p>
                <select name="font_family" id="font_family" ng-model="fontFamily">
                    <option value="{{i.value}}" ng-repeat="i in fontFamilys">{{i.key}}</option>
                </select>
            </div>
            <div class="col-sm">
                <p>Font weight</p>
                <select name="font_weight" id="font_weight" ng-model="fontWeight">
                    <option value="{{i}}" ng-repeat="i in getFontWeight()">{{i}}</option>
                </select>
            </div>
        </div>
    </div>
</div>