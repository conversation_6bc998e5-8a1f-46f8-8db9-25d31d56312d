app.controller('dashboardCtrl', function (user, $scope, $rootScope, $http) {
    $('#page-wrapper').removeClass('nav-small');
    $scope.listAgeGroups = [];
    $scope.listLeague = [];

    $scope.isAdmin = function () {
        return user.role == USER_SUPER_ADMIN || user.role == USER_LEAGUE_ADMIN
            ? true
            : false;
    };

    $scope.start_date = moment().startOf('month').format('YYYY-MM-DD');
    $scope.end_date = moment().endOf('month').format('YYYY-MM-DD');

    if ($.fn.daterangepicker) {
        $('input[name="dateFilter"]').daterangepicker('destroy');
    }

    $('input[name="dateFilter"]').daterangepicker({
        startDate: moment().startOf('month'),
        endDate: moment().endOf('month'),
        alwaysShowCalendars: true,
        locale: {
            format: 'DD/MM/YYYY',
        },
        maxSpan: {
            days: 90,
        },
        ranges: {
            Yesterday: [
                moment().subtract(1, 'days'),
                moment().subtract(1, 'days'),
            ],
            Today: [moment(), moment()],
            'Last week': [
                moment().subtract(1, 'week').startOf('week').add(1, 'days'),
                moment().subtract(1, 'week').endOf('week').add(1, 'days'),
            ],
            'This week': [
                // start week on monday
                moment().startOf('week').add(1, 'days'),
                moment().endOf('week').add(1, 'days'),
            ],
            'Last Month': [
                moment().subtract(1, 'month').startOf('month'),
                moment().subtract(1, 'month').endOf('month'),
            ],
            'This Month': [moment().startOf('month'), moment().endOf('month')],
        },
    });

    $('input[name="dateFilter"]').on(
        'apply.daterangepicker',
        function (ev, picker) {
            console.log('date change');
            $scope.start_date = picker.startDate.format('YYYY-MM-DD');
            $scope.end_date = picker.endDate.format('YYYY-MM-DD');

            initDuplicateSessionTable();
        }
    );

    if ($scope.isAdmin()) {
        $('#dashboard').show();
        $('#hkfa-logo').hide();
    } else {
        $('#dashboard').hide();
        $('#hkfa-logo').show();
    }

    jQuery.ajax({
        type: 'POST',
        url: SERVER_PATH + 'dashboard/getGroupsAgeCurrentSeason',
        async: false,
        headers: {
            'x-user-id': $rootScope.user_id,
            'x-user-email': $rootScope.user_name,
        },
        data: {},
        dataType: 'json',
        complete: function (response) {
            var jsonData = JSON.parse(response.responseText);
            $scope.listAgeGroups = jsonData.info;
            setTimeout(() => {
                initDashboard();
            }, 100);
        },
    });

    function initDashboard() {
        getDashboardHTML();
    }

    function getDashboardHTML() {
        var tableHTML =
            '<div style="display:flex; text-align:center;padding: 0 0 2% 2%;">';
        $scope.listAgeGroups.forEach((element, index) => {
            if (index > 0) {
                tableHTML += "<div style='width:2%'></div>";
            }
            tableHTML +=
                '<div class="col-12 col-lg-12" style="text-align:center;background: white;">' +
                '<h1 style="margin: 0; margin-top:5%">' +
                element.name +
                '</h1>';
            tableHTML +=
                '<div class="table-responsive" style="margin: 0;">' +
                '<table id="league_' +
                element.id +
                '" class="table table-striped table-bordered table-hover" cellspacing="0" width="100%">' +
                '<thead>' +
                '<tr>' +
                '<th>Team</th>' +
                '<th>Respect Score</th>' +
                '<th>Green Card</th>' +
                '<th>Yellow Card</th>' +
                '<th>Red Card</th>' +
                '</tr>' +
                '</thead>' +
                '</table>' +
                '</div>';
            tableHTML += '</div>';
        });
        tableHTML += '</div>';

        $('#main-content > div').html(tableHTML);

        $scope.listAgeGroups.forEach((element, index) => {
            initSummariesLeagueTable(element);
        });
    }

    function initSummariesLeagueTable(group) {
        if ($.fn.dataTable.isDataTable('#league_' + group.id)) {
            $('#league_' + group.id)
                .DataTable()
                .destroy();
        }

        table = $('#league_' + group.id).DataTable({
            dom: 't',
            ajax: {
                url: SERVER_PATH + 'dashboard/getTableDashboard',
                type: 'POST',
                dataType: 'json',
                headers: {
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name,
                },
                data: {
                    group_id: group.id,
                },
                complete: function (response) {
                    console.log(response);
                },
                error: function (xhr, status, error) {},
            },
            language: {
                paginate: {
                    previous: '<i class="fa fa-chevron-left"></i>',
                    next: '<i class="fa fa-chevron-right"></i>',
                },
            },
            columns: [
                {
                    data: 'name',
                    sortable: false,
                },
                {
                    data: 'ResC',
                    sortable: true,
                },
                {
                    data: 'GreenC',
                    sortable: false,
                },
                {
                    data: 'YellowC',
                    sortable: false,
                },
                {
                    data: 'RedC',
                    sortable: false,
                },
            ],
            order: [[1, 'desc']],
            displayLength: -1,
        });
    }

    /**
     * Tab Summer Scheme
     */

    jQuery.ajax({
        type: 'POST',
        url: SERVER_PATH + 'dashboard/getRegisterByDaySummerScheme',
        async: false,
        headers: {
            'x-user-id': $rootScope.user_id,
            'x-user-email': $rootScope.user_name,
        },
        data: {},
        dataType: 'json',
        complete: function (response) {
            var jsonData = JSON.parse(response.responseText);
            renderChartParentRegisterByDay(jsonData.data);
        },
    });

    function renderChartParentRegisterByDay(data) {
        console.log(data);
        // Participants
        const configParticipants = {
            type: 'line',
            data: data.participants_by_day,
            options: {
                responsive: true,
                interaction: {
                    mode: 'index',
                    intersect: false,
                },
                stacked: false,
                plugins: {
                    title: {
                        display: true,
                        text: 'Number of participants join by day',
                    },
                },
                scales: {
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                    },
                },
            },
        };

        const ctxParticipants = document.getElementById(
            'chartParticipantsRegisterByDay'
        );

        new Chart(ctxParticipants, configParticipants);
    }

    /**
     * Tab Duplicate session
     */

    initDuplicateSessionTable();

    function initDuplicateSessionTable() {
        // check if the table is already initialized
        if ($.fn.DataTable.isDataTable('#duplicate_sessions_tbl')) {
            $('#duplicate_sessions_tbl').DataTable().destroy();
        }

        var duplicate_sessions_table = $('#duplicate_sessions_tbl').DataTable({
            dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
            stateSave: true,
            deferRender: true,
            ajax: {
                url: SERVER_PATH + 'attendance/getOverlapCourseTable',
                type: 'POST',
                headers: {
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name,
                },
                data: {
                    start_date: $scope.start_date,
                    end_date: $scope.end_date,
                },
                dataType: 'json',
                complete: function (response) {},
                error: function (xhr, status, error) {},
            },
            language: {
                info: 'Showing _START_ to _END_ of _TOTAL_ sessions',
                infoEmpty: 'Showing 0 to 0 of 0 sessions',
                lengthMenu: 'Show _MENU_ sessions',
                select: {
                    rows: {
                        _: 'You have selected %d sessions',
                        0: 'Click a session to select',
                        1: '1 session selected',
                    },
                },
                paginate: {
                    previous: '<i class="fa fa-chevron-left"></i>',
                    next: '<i class="fa fa-chevron-right"></i>',
                },
            },
            columns: [
                {
                    data: 'training_date',
                    visible: false,
                    sortable: false,
                    render: function (data, type, row) {
                        return moment(data).format('DD/MM/YYYY');
                    },
                },
                {
                    data: 'coach_name',
                    sortable: false,
                },
                {
                    data: 'coach_id',
                    sortable: false,
                },
                {
                    data: 'role',
                    sortable: false,
                },
                {
                    data: 'event_name',
                    sortable: false,
                },
                {
                    data: 'start_time',
                    render: function (data, type, row) {
                        return (
                            moment(data, 'HH:mm:ss').format('HH:mm') +
                            ' - ' +
                            moment(row.end_time, 'HH:mm:ss').format('HH:mm')
                        );
                    },
                    sortable: false,
                },
                {
                    data: 'session_name',
                    sortable: false,
                },
                {
                    data: 'status',
                    sortable: false,
                    render: function (data, type, row) {
                        return data == 'Active'
                            ? '<span class="badge badge-success">Active</span>'
                            : '<span class="badge badge-danger">' +
                                  data +
                                  '</span>';
                    },
                },
            ],
            select: {
                style: SELECT_MODE,
            },
            order: [[0, 'desc']],
            rowGroup: {
                order: [['training_date', 'desc']],
                dataSrc: function (row) {
                    return moment(row.training_date).format('DD/MM/YYYY');
                },
            },
            columnDefs: [
                {
                    targets: 0,
                    type: 'date-euro',
                },
            ],
            lengthMenu: [
                [10, 25, 50, 100, -1],
                [10, 25, 50, 100, 'All'],
            ],
            buttons: [],
        });
    }
});
