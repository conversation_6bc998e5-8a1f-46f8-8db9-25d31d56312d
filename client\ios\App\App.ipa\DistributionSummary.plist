<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>App.ipa</key>
	<array>
		<dict>
			<key>architectures</key>
			<array>
				<string>arm64</string>
			</array>
			<key>bitcode</key>
			<false/>
			<key>buildNumber</key>
			<string>2</string>
			<key>certificate</key>
			<dict>
				<key>SHA1</key>
				<string>3569A35DA84C6A264EF6E5DE27D9387C93AB63C5</string>
				<key>dateExpires</key>
				<string>04/06/2024</string>
				<key>type</key>
				<string>Apple Distribution</string>
			</dict>
			<key>embeddedBinaries</key>
			<array>
				<dict>
					<key>architectures</key>
					<array>
						<string>arm64</string>
					</array>
					<key>bitcode</key>
					<false/>
					<key>buildNumber</key>
					<string>2</string>
					<key>certificate</key>
					<dict>
						<key>SHA1</key>
						<string>3569A35DA84C6A264EF6E5DE27D9387C93AB63C5</string>
						<key>dateExpires</key>
						<string>04/06/2024</string>
						<key>type</key>
						<string>Apple Distribution</string>
					</dict>
					<key>name</key>
					<string>Capacitor.framework</string>
					<key>symbols</key>
					<true/>
					<key>team</key>
					<dict>
						<key>id</key>
						<string>55PGUKKS3B</string>
						<key>name</key>
						<string></string>
					</dict>
					<key>versionNumber</key>
					<string>1.4.5</string>
				</dict>
				<dict>
					<key>architectures</key>
					<array>
						<string>arm64</string>
					</array>
					<key>bitcode</key>
					<false/>
					<key>buildNumber</key>
					<string>2</string>
					<key>certificate</key>
					<dict>
						<key>SHA1</key>
						<string>3569A35DA84C6A264EF6E5DE27D9387C93AB63C5</string>
						<key>dateExpires</key>
						<string>04/06/2024</string>
						<key>type</key>
						<string>Apple Distribution</string>
					</dict>
					<key>name</key>
					<string>CapacitorApp.framework</string>
					<key>symbols</key>
					<true/>
					<key>team</key>
					<dict>
						<key>id</key>
						<string>55PGUKKS3B</string>
						<key>name</key>
						<string></string>
					</dict>
					<key>versionNumber</key>
					<string>1.4.5</string>
				</dict>
				<dict>
					<key>architectures</key>
					<array>
						<string>arm64</string>
					</array>
					<key>bitcode</key>
					<false/>
					<key>buildNumber</key>
					<string>2</string>
					<key>certificate</key>
					<dict>
						<key>SHA1</key>
						<string>3569A35DA84C6A264EF6E5DE27D9387C93AB63C5</string>
						<key>dateExpires</key>
						<string>04/06/2024</string>
						<key>type</key>
						<string>Apple Distribution</string>
					</dict>
					<key>name</key>
					<string>CapacitorBrowser.framework</string>
					<key>symbols</key>
					<true/>
					<key>team</key>
					<dict>
						<key>id</key>
						<string>55PGUKKS3B</string>
						<key>name</key>
						<string></string>
					</dict>
					<key>versionNumber</key>
					<string>1.4.5</string>
				</dict>
				<dict>
					<key>architectures</key>
					<array>
						<string>arm64</string>
					</array>
					<key>bitcode</key>
					<false/>
					<key>buildNumber</key>
					<string>2</string>
					<key>certificate</key>
					<dict>
						<key>SHA1</key>
						<string>3569A35DA84C6A264EF6E5DE27D9387C93AB63C5</string>
						<key>dateExpires</key>
						<string>04/06/2024</string>
						<key>type</key>
						<string>Apple Distribution</string>
					</dict>
					<key>name</key>
					<string>CapacitorCommunityBarcodeScanner.framework</string>
					<key>symbols</key>
					<true/>
					<key>team</key>
					<dict>
						<key>id</key>
						<string>55PGUKKS3B</string>
						<key>name</key>
						<string></string>
					</dict>
					<key>versionNumber</key>
					<string>1.4.5</string>
				</dict>
				<dict>
					<key>architectures</key>
					<array>
						<string>arm64</string>
					</array>
					<key>bitcode</key>
					<false/>
					<key>buildNumber</key>
					<string>2</string>
					<key>certificate</key>
					<dict>
						<key>SHA1</key>
						<string>3569A35DA84C6A264EF6E5DE27D9387C93AB63C5</string>
						<key>dateExpires</key>
						<string>04/06/2024</string>
						<key>type</key>
						<string>Apple Distribution</string>
					</dict>
					<key>name</key>
					<string>CapacitorPushNotifications.framework</string>
					<key>symbols</key>
					<true/>
					<key>team</key>
					<dict>
						<key>id</key>
						<string>55PGUKKS3B</string>
						<key>name</key>
						<string></string>
					</dict>
					<key>versionNumber</key>
					<string>1.4.5</string>
				</dict>
				<dict>
					<key>architectures</key>
					<array>
						<string>arm64</string>
					</array>
					<key>bitcode</key>
					<false/>
					<key>buildNumber</key>
					<string>2</string>
					<key>certificate</key>
					<dict>
						<key>SHA1</key>
						<string>3569A35DA84C6A264EF6E5DE27D9387C93AB63C5</string>
						<key>dateExpires</key>
						<string>04/06/2024</string>
						<key>type</key>
						<string>Apple Distribution</string>
					</dict>
					<key>name</key>
					<string>CapacitorSplashScreen.framework</string>
					<key>symbols</key>
					<true/>
					<key>team</key>
					<dict>
						<key>id</key>
						<string>55PGUKKS3B</string>
						<key>name</key>
						<string></string>
					</dict>
					<key>versionNumber</key>
					<string>1.4.5</string>
				</dict>
				<dict>
					<key>architectures</key>
					<array>
						<string>arm64</string>
					</array>
					<key>bitcode</key>
					<false/>
					<key>buildNumber</key>
					<string>2</string>
					<key>certificate</key>
					<dict>
						<key>SHA1</key>
						<string>3569A35DA84C6A264EF6E5DE27D9387C93AB63C5</string>
						<key>dateExpires</key>
						<string>04/06/2024</string>
						<key>type</key>
						<string>Apple Distribution</string>
					</dict>
					<key>name</key>
					<string>CapacitorStatusBar.framework</string>
					<key>symbols</key>
					<true/>
					<key>team</key>
					<dict>
						<key>id</key>
						<string>55PGUKKS3B</string>
						<key>name</key>
						<string></string>
					</dict>
					<key>versionNumber</key>
					<string>1.4.5</string>
				</dict>
				<dict>
					<key>architectures</key>
					<array>
						<string>arm64</string>
					</array>
					<key>bitcode</key>
					<false/>
					<key>buildNumber</key>
					<string>2</string>
					<key>certificate</key>
					<dict>
						<key>SHA1</key>
						<string>3569A35DA84C6A264EF6E5DE27D9387C93AB63C5</string>
						<key>dateExpires</key>
						<string>04/06/2024</string>
						<key>type</key>
						<string>Apple Distribution</string>
					</dict>
					<key>name</key>
					<string>Cordova.framework</string>
					<key>symbols</key>
					<true/>
					<key>team</key>
					<dict>
						<key>id</key>
						<string>55PGUKKS3B</string>
						<key>name</key>
						<string></string>
					</dict>
					<key>versionNumber</key>
					<string>1.4.5</string>
				</dict>
				<dict>
					<key>architectures</key>
					<array>
						<string>arm64</string>
					</array>
					<key>bitcode</key>
					<false/>
					<key>buildNumber</key>
					<string>2</string>
					<key>certificate</key>
					<dict>
						<key>SHA1</key>
						<string>3569A35DA84C6A264EF6E5DE27D9387C93AB63C5</string>
						<key>dateExpires</key>
						<string>04/06/2024</string>
						<key>type</key>
						<string>Apple Distribution</string>
					</dict>
					<key>name</key>
					<string>CordovaPlugins.framework</string>
					<key>symbols</key>
					<true/>
					<key>team</key>
					<dict>
						<key>id</key>
						<string>55PGUKKS3B</string>
						<key>name</key>
						<string></string>
					</dict>
					<key>versionNumber</key>
					<string>1.4.5</string>
				</dict>
				<dict>
					<key>architectures</key>
					<array>
						<string>arm64</string>
					</array>
					<key>bitcode</key>
					<false/>
					<key>buildNumber</key>
					<string>2</string>
					<key>certificate</key>
					<dict>
						<key>SHA1</key>
						<string>3569A35DA84C6A264EF6E5DE27D9387C93AB63C5</string>
						<key>dateExpires</key>
						<string>04/06/2024</string>
						<key>type</key>
						<string>Apple Distribution</string>
					</dict>
					<key>name</key>
					<string>FBLPromises.framework</string>
					<key>symbols</key>
					<true/>
					<key>team</key>
					<dict>
						<key>id</key>
						<string>55PGUKKS3B</string>
						<key>name</key>
						<string></string>
					</dict>
					<key>versionNumber</key>
					<string>1.4.5</string>
				</dict>
				<dict>
					<key>architectures</key>
					<array>
						<string>arm64</string>
					</array>
					<key>bitcode</key>
					<false/>
					<key>buildNumber</key>
					<string>2</string>
					<key>certificate</key>
					<dict>
						<key>SHA1</key>
						<string>3569A35DA84C6A264EF6E5DE27D9387C93AB63C5</string>
						<key>dateExpires</key>
						<string>04/06/2024</string>
						<key>type</key>
						<string>Apple Distribution</string>
					</dict>
					<key>name</key>
					<string>FirebaseCore.framework</string>
					<key>symbols</key>
					<true/>
					<key>team</key>
					<dict>
						<key>id</key>
						<string>55PGUKKS3B</string>
						<key>name</key>
						<string></string>
					</dict>
					<key>versionNumber</key>
					<string>1.4.5</string>
				</dict>
				<dict>
					<key>architectures</key>
					<array>
						<string>arm64</string>
					</array>
					<key>bitcode</key>
					<false/>
					<key>buildNumber</key>
					<string>2</string>
					<key>certificate</key>
					<dict>
						<key>SHA1</key>
						<string>3569A35DA84C6A264EF6E5DE27D9387C93AB63C5</string>
						<key>dateExpires</key>
						<string>04/06/2024</string>
						<key>type</key>
						<string>Apple Distribution</string>
					</dict>
					<key>name</key>
					<string>FirebaseCoreInternal.framework</string>
					<key>symbols</key>
					<true/>
					<key>team</key>
					<dict>
						<key>id</key>
						<string>55PGUKKS3B</string>
						<key>name</key>
						<string></string>
					</dict>
					<key>versionNumber</key>
					<string>1.4.5</string>
				</dict>
				<dict>
					<key>architectures</key>
					<array>
						<string>arm64</string>
					</array>
					<key>bitcode</key>
					<false/>
					<key>buildNumber</key>
					<string>2</string>
					<key>certificate</key>
					<dict>
						<key>SHA1</key>
						<string>3569A35DA84C6A264EF6E5DE27D9387C93AB63C5</string>
						<key>dateExpires</key>
						<string>04/06/2024</string>
						<key>type</key>
						<string>Apple Distribution</string>
					</dict>
					<key>name</key>
					<string>FirebaseInstallations.framework</string>
					<key>symbols</key>
					<true/>
					<key>team</key>
					<dict>
						<key>id</key>
						<string>55PGUKKS3B</string>
						<key>name</key>
						<string></string>
					</dict>
					<key>versionNumber</key>
					<string>1.4.5</string>
				</dict>
				<dict>
					<key>architectures</key>
					<array>
						<string>arm64</string>
					</array>
					<key>bitcode</key>
					<false/>
					<key>buildNumber</key>
					<string>2</string>
					<key>certificate</key>
					<dict>
						<key>SHA1</key>
						<string>3569A35DA84C6A264EF6E5DE27D9387C93AB63C5</string>
						<key>dateExpires</key>
						<string>04/06/2024</string>
						<key>type</key>
						<string>Apple Distribution</string>
					</dict>
					<key>name</key>
					<string>FirebaseMessaging.framework</string>
					<key>symbols</key>
					<true/>
					<key>team</key>
					<dict>
						<key>id</key>
						<string>55PGUKKS3B</string>
						<key>name</key>
						<string></string>
					</dict>
					<key>versionNumber</key>
					<string>1.4.5</string>
				</dict>
				<dict>
					<key>architectures</key>
					<array>
						<string>arm64</string>
					</array>
					<key>bitcode</key>
					<false/>
					<key>buildNumber</key>
					<string>2</string>
					<key>certificate</key>
					<dict>
						<key>SHA1</key>
						<string>3569A35DA84C6A264EF6E5DE27D9387C93AB63C5</string>
						<key>dateExpires</key>
						<string>04/06/2024</string>
						<key>type</key>
						<string>Apple Distribution</string>
					</dict>
					<key>name</key>
					<string>GoogleDataTransport.framework</string>
					<key>symbols</key>
					<true/>
					<key>team</key>
					<dict>
						<key>id</key>
						<string>55PGUKKS3B</string>
						<key>name</key>
						<string></string>
					</dict>
					<key>versionNumber</key>
					<string>1.4.5</string>
				</dict>
				<dict>
					<key>architectures</key>
					<array>
						<string>arm64</string>
					</array>
					<key>bitcode</key>
					<false/>
					<key>buildNumber</key>
					<string>2</string>
					<key>certificate</key>
					<dict>
						<key>SHA1</key>
						<string>3569A35DA84C6A264EF6E5DE27D9387C93AB63C5</string>
						<key>dateExpires</key>
						<string>04/06/2024</string>
						<key>type</key>
						<string>Apple Distribution</string>
					</dict>
					<key>name</key>
					<string>GoogleUtilities.framework</string>
					<key>symbols</key>
					<true/>
					<key>team</key>
					<dict>
						<key>id</key>
						<string>55PGUKKS3B</string>
						<key>name</key>
						<string></string>
					</dict>
					<key>versionNumber</key>
					<string>1.4.5</string>
				</dict>
				<dict>
					<key>architectures</key>
					<array>
						<string>arm64</string>
					</array>
					<key>bitcode</key>
					<false/>
					<key>buildNumber</key>
					<string>2</string>
					<key>certificate</key>
					<dict>
						<key>SHA1</key>
						<string>3569A35DA84C6A264EF6E5DE27D9387C93AB63C5</string>
						<key>dateExpires</key>
						<string>04/06/2024</string>
						<key>type</key>
						<string>Apple Distribution</string>
					</dict>
					<key>name</key>
					<string>nanopb.framework</string>
					<key>symbols</key>
					<true/>
					<key>team</key>
					<dict>
						<key>id</key>
						<string>55PGUKKS3B</string>
						<key>name</key>
						<string></string>
					</dict>
					<key>versionNumber</key>
					<string>1.4.5</string>
				</dict>
			</array>
			<key>entitlements</key>
			<dict>
				<key>application-identifier</key>
				<string>55PGUKKS3B.com.ezactive.hkfa</string>
				<key>aps-environment</key>
				<string>production</string>
				<key>beta-reports-active</key>
				<true/>
				<key>com.apple.developer.team-identifier</key>
				<string>55PGUKKS3B</string>
				<key>get-task-allow</key>
				<false/>
			</dict>
			<key>name</key>
			<string>App.app</string>
			<key>profile</key>
			<dict>
				<key>UUID</key>
				<string>a0aa05aa-7e3f-4285-8274-ff6e31b953c1</string>
				<key>dateExpires</key>
				<string>04/06/2024</string>
				<key>name</key>
				<string>HKFA - Distribution</string>
			</dict>
			<key>symbols</key>
			<true/>
			<key>team</key>
			<dict>
				<key>id</key>
				<string>55PGUKKS3B</string>
				<key>name</key>
				<string></string>
			</dict>
			<key>versionNumber</key>
			<string>1.4.5</string>
		</dict>
	</array>
</dict>
</plist>
