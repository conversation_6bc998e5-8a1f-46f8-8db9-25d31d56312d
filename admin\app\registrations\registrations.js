var ACTION_APPROVE_SEND_INVOICE = 1;
var ACTION_REMINDER = 2;
var ACTION_MAIL = 3;

app.controller(
    'registrationsCtrl',
    function ($scope, $rootScope, $routeParams, $http) {
        $('#page-wrapper').removeClass('nav-small');
        console.log('app name' + appName);
        // get hint for HKID/Passport type comment
        var commentPlayerPhoto = [];
        var commentHKIDPassportType = [];
        var commentHKIDPassportPhoto = [];
        $scope.filterClubs = [];
        $scope.selectedClub = null;

        jQuery.ajax({
            type: 'POST',
            url: SERVER_PATH + 'club/getClubforFilter',
            async: false,
            headers: {	
                'x-user-id': $rootScope.user_id,
                'x-user-email': $rootScope.user_name
            },
            data: {},
            dataType: 'json',
            complete: function (response) {
                var jsonData = JSON.parse(response.responseText);
                $scope.filterClubs = jsonData.info.map((element) => {
                    return { id: element.id, name: element.name };
                });
                $scope.filterClubs.unshift({ id: 0, name: 'All' });

                // get first club
                $scope.selectedClub = $scope.filterClubs[0].id;
            },
        });

        $scope.changeClub = function () {
            initRegistrationtable('registration_table', event_id, true);
        };

        jQuery.ajax({
            type: 'POST',
            url: SERVER_PATH + 'registration/getCommentSuggestion',
            async: false,
            headers: {
                'x-user-id': $rootScope.user_id,
                'x-user-email': $rootScope.user_name,
            },
            data: {},
            dataType: 'json',
            complete: function (response) {
                var jsonData = JSON.parse(response.responseText);
                jsonData.data.forEach((element) => {
                    switch (parseInt(element.comment_suggestion.type)) {
                        case 1: {
                            commentPlayerPhoto.push(
                                element.comment_suggestion.text
                            );
                            break;
                        }
                        case 2: {
                            commentHKIDPassportType.push(
                                element.comment_suggestion.text
                            );
                            break;
                        }
                        case 3: {
                            commentHKIDPassportPhoto.push(
                                element.comment_suggestion.text
                            );
                            break;
                        }
                    }
                });
                console.log(commentPlayerPhoto);
            },
        });
        var commentHintPlayerPhoto = new Bloodhound({
            datumTokenizer: Bloodhound.tokenizers.whitespace,
            queryTokenizer: Bloodhound.tokenizers.whitespace,
            local: commentPlayerPhoto,
        });
        var commentHintHKIDPassportType = new Bloodhound({
            datumTokenizer: Bloodhound.tokenizers.whitespace,
            queryTokenizer: Bloodhound.tokenizers.whitespace,
            local: commentHKIDPassportType,
        });
        var commentHintHKIDPassportPhoto = new Bloodhound({
            datumTokenizer: Bloodhound.tokenizers.whitespace,
            queryTokenizer: Bloodhound.tokenizers.whitespace,
            local: commentHKIDPassportPhoto,
        });
        $('#comment_player_photo').typeahead(
            {
                hint: true,
                highlight: true /* Enable substring highlighting */,
                minLength: 1 /* Specify minimum characters required for showing suggestions */,
            },
            {
                name: 'hint',
                source: commentHintPlayerPhoto,
            }
        );
        $('#comment_hkid_passport_type').typeahead(
            {
                hint: true,
                highlight: true /* Enable substring highlighting */,
                minLength: 1 /* Specify minimum characters required for showing suggestions */,
            },
            {
                name: 'hint',
                source: commentHintHKIDPassportType,
            }
        );
        $('#comment_hkid_passport_photo').typeahead(
            {
                hint: true,
                highlight: true /* Enable substring highlighting */,
                minLength: 1 /* Specify minimum characters required for showing suggestions */,
            },
            {
                name: 'hint',
                source: commentHintHKIDPassportPhoto,
            }
        );
        // get hint for HKID/Passport type comment
        var $form = $('#validate_form');
        angular.element(document).ready(function () {
            $form.validate({
                rules: {
                    surname: {
                        required: true,
                    },
                    other_name: {
                        required: true,
                    },
                    dob: {
                        required: true,
                    },
                    passport_expiry_date: {
                        required: true,
                    },
                },
                messages: {},
            });
        });
        var event_id = $routeParams.id;

        // get event info
        jQuery.ajax({
            type: 'POST',
            url: SERVER_PATH + 'event/getEventInfo',
            async: false,
            headers: {
                'x-user-id': $rootScope.user_id,
                'x-user-email': $rootScope.user_name,
            },
            data: {
                event_id: event_id,
            },
            dataType: 'json',
            complete: function (response) {
                var jsonData = JSON.parse(response.responseText);
                var event = jsonData.info;
                event_name = event.name;
                event_type = event.type;
            },
        });
        console.log(
            'registrationsCtrl - event_id, name, type  = ' +
                event_id +
                ', ' +
                event_name +
                ', ' +
                event_type
        );
        $scope.event_id = event_id;
        $rootScope.event_name = event_name;
        $rootScope.event_type = event_type;

        $.fn.dataTable.moment('D-MMM-YYYY HH:mm:ss');
        $.fn.dataTable.moment('D-MMM-YYYY');

        if ($.fn.dataTable.isDataTable('#registration_table')) {
            console.log('registration_table_' + event_id + ' is DataTable 1 ');
            if ($('#registration_table').DataTable().destroy()) {
                console.log('Destroy');
                initRegistrationtable('registration_table', event_id, true);
            }
        } else {
            console.log('registration_table_' + event_id + ' is NOT DataTable');
            setTimeout(() => {
                initRegistrationtable('registration_table', event_id, false);
            }, 400);
        }

        form_fields = [
            {
                field_id: 'field_surname',
                value_id: 'surname',
                valid_id: 'valid_surname',
                comment_id: 'comment_surname',
            },
            {
                field_id: 'field_other_name',
                value_id: 'other_name',
                valid_id: 'valid_other_name',
                comment_id: 'comment_other_name',
            },
            {
                field_id: 'field_dob',
                value_id: 'dob',
                valid_id: 'valid_dob',
                comment_id: 'comment_dob',
            },
            {
                field_id: 'field_gender',
                value_id: 'gender',
                valid_id: 'valid_gender',
                comment_id: 'comment_gender',
            },
            {
                field_id: 'field_hkid_passport_type',
                value_id: 'hkid_passport_type',
                valid_id: 'valid_hkid_passport_type',
                comment_id: 'comment_hkid_passport_type',
            },
            {
                field_id: 'field_hkid_passport_photo',
                value_id: 'hkid_passport_photo',
                valid_id: 'valid_hkid_passport_photo',
                comment_id: 'comment_hkid_passport_photo',
            },
            {
                field_id: 'field_player_photo',
                value_id: 'player_photo',
                valid_id: 'valid_player_photo',
                comment_id: 'comment_player_photo',
            },
            {
                field_id: 'field_passport_expiry_date',
                value_id: 'passport_expiry_date',
                valid_id: 'valid_passport_expiry_date',
                comment_id: 'comment_passport_expiry_date',
            },
        ];

        function initRegistrationtable(table_id, event_id, flag) {
            // destroy table if exist
            if ($.fn.dataTable.isDataTable('#' + table_id)) {
                $('#' + table_id).DataTable().destroy();
            }

            var editor = new $.fn.dataTable.Editor({
                ajax: {
                    type: 'POST',
                    url: SERVER_PATH + 'registration/setRegistrations',
                    headers: {
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name,
                    },
                    data: {
                        event_id: event_id,
                    },
                    dataType: 'json',
                    complete: function (response) {
                        table.ajax.reload();
                    },
                    error: function (xhr, status, error) {},
                },
                table: '#' + table_id,
                formOptions: {
                    main: {
                        onBlur: 'none',
                    },
                },
                i18n: {
                    edit: {
                        button: 'Edit',
                        title: 'Edit registration',
                        submit: 'Save',
                    },
                    error: {
                        system: 'System error, please contact administrator.',
                    },
                },
                fields: [
                    {
                        label: 'Club:',
                        name: 'registrations.club_id',
                        type: 'select2',
                    },
                    {
                        label: 'ID:',
                        name: 'registrations.id',
                        type: 'hidden',
                    },
                ],
            });

            table = $('#' + table_id).DataTable({
                dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
                stateSave: true,
                deferRender: true,
                processing: true,
                serverSide: true,
                ajax: {
                    url: SERVER_PATH + 'registration/getRegistrations',
                    type: 'POST',
                    headers: {
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name,
                    },
                    data: {
                        event_id: event_id,
                        club_id: $scope.selectedClub,
                    },
                    dataType: 'json',
                    complete: function (response) {
                        if (flag)
                            initRegistrationtable(
                                'registration_table',
                                event_id,
                                false
                            );
                    },
                    error: function (xhr, status, error) {},
                },
                language: {
                    info: 'Showing _START_ to _END_ of _TOTAL_ registration',
                    infoEmpty: 'Showing 0 to 0 of 0 registrations',
                    lengthMenu: 'Show _MENU_ registrations',
                    select: {
                        rows: {
                            _: 'You have selected %d registrations',
                            0: 'Click a registration to select',
                            1: '1 registration selected',
                        },
                    },
                    paginate: {
                        previous: '<i class="fa fa-chevron-left"></i>',
                        next: '<i class="fa fa-chevron-right"></i>',
                    },
                },
                columns: [
                    // {
                    //     data: 'DT_RowId',
                    //     // defaultContent: '',
                    //     // className: 'select-checkbox',
                    //     // orderable: false
                    //     targets: 0,
                    //     render: function (data, type, row, meta) {
                    //         if (type === 'display') {
                    //             data = '<div class="checkbox"><input type="checkbox" class="dt-checkboxes"><label></label></div>';
                    //         }

                    //         return data;
                    //     },
                    //     checkboxes: {
                    //         selectRow: true,
                    //         selectAllRender: '<div class="checkbox"><input type="checkbox" class="dt-checkboxes"><label></label></div>'
                    //     }
                    // },
                    {
                        data: 'players.player_photo',
                        className: 'avatar',
                        orderable: false,
                        render: function (data) {
                            if (data !== null && data !== '') {
                                return (
                                    '<img src="' +
                                    PRODUCT_IMAGE_PATH +
                                    data +
                                    '">'
                                );
                            } else {
                                return (
                                    '<img src="' +
                                    SYSTEM_IMAGE_PATH +
                                    'favicon.png">'
                                );
                            }
                        },
                    },
                    {
                        data: 'players.full_name',
                        render: function (data, type, row) {
                            return (
                                row.players.surname +
                                ' ' +
                                row.players.other_name
                            );
                        },
                    },
                    {
                        data: 'players.dob',
                        className: 'center',
                        // visible: false
                    },
                    {
                        data: 'players.gender',
                        className: 'center',
                        // visible: false
                    },
                    {
                        data: 'parens.surname',
                        render: function (data, type, row) {
                            return (
                                row.parens.surname + ' ' + row.parens.other_name
                            );
                        },
                        visible: false,
                    },
                    {
                        data: 'parens.email',
                        className: 'center',
                        visible: false,
                    },
                    {
                        data: 'players.validate_status',
                        className: 'center',
                        render: function (data, type, full, meta) {
                            switch (data) {
                                case VALIDATE_STATUS_Pending:
                                    return (
                                        '<span class="label label-info">' +
                                        data +
                                        '</span>'
                                    );
                                case VALIDATE_STATUS_Invalid:
                                    return (
                                        '<span class="label label-danger">' +
                                        data +
                                        '</span>'
                                    );
                                case VALIDATE_STATUS_Updated:
                                    return (
                                        '<span class="label label-warning">' +
                                        data +
                                        '</span>'
                                    );
                                case VALIDATE_STATUS_Validated:
                                    return (
                                        '<span class="label label-success">' +
                                        data +
                                        '</span>'
                                    );
                                default:
                                    return (
                                        '<span class="label label-default">' +
                                        data +
                                        '</span>'
                                    );
                            }
                        },
                    },
                    {
                        data: 'registrations.club_players',
                        render: function (data, type, full, meta) {
                            var club_players = '';
                            data.forEach((element) => {
                                club_players += element.name + ', ';
                            });

                            // remove last comma
                            club_players = club_players.replace(/,\s*$/, '');

                            return club_players;
                        },
                    },
               
                    {
                        data: 'registrations.registered_date',
                        className: 'center',
                    },
                    {
                        data: 'registrations.approved_date',
                        className: 'center',
                    },
                    {
                        data: 'registrations.approval_status',
                        className: 'center',
                        render: function (data, type, full, meta) {
                            switch (data) {
                                case APPROVAL_STATUS_Approve:
                                    return (
                                        '<span class="label label-success">' +
                                        data +
                                        '</span>'
                                    );
                                case APPROVAL_STATUS_Register:
                                    return (
                                        '<span class="label label-info">' +
                                        data +
                                        '</span>'
                                    );
                                case APPROVAL_STATUS_Reject:
                                    return (
                                        '<span class="label label-danger">' +
                                        data +
                                        '</span>'
                                    );
                                default:
                                    return (
                                        '<span class="label label-default">' +
                                        data +
                                        '</span>'
                                    );
                            }
                        },
                    }
                   
                ],
                select: {
                    style: SELECT_MODE,
                    // selector: 'td:first-child',
                },
                order: [[1, 'asc']],
                lengthMenu: [
                    [10, 25, 50, 100, 1000],
                    [10, 25, 50, 100, 1000],
                ],
                columnDefs: [
                    { orderable: false, searchable: false, targets: 7 },
                ],
                buttons: [
                    {
                        extend: 'collection',
                        text: '<i class="fa fa-list"></i>&emsp;Action',
                        // background: false,
                        autoClose: true,
                        buttons: [
                            // {
                            //     name: 'approve_full',
                            //     text: '<i class="fa fa-paypal"></i>&emsp;Approve full',
                            //     titleAttr: 'Approve and send a full-payment PayPal invoice',
                            //     action: function (e, dt, node, config) {
                            //         checkRequest(ACTION_APPROVE_SEND_INVOICE, 'Approve & Send Invoice');
                            //     }
                            // },
                            {
                                text: '<i class="fa fa-history"></i>&emsp;Reminder',
                                titleAttr: 'Reminder to parent(s)',
                                action: function (e, dt, node, config) {
                                    action = config.text;
                                    reminderAction(ACTION_REMINDER, action);
                                },
                            },
                            // {
                            //     name: 'mail',
                            //     text: '<i class="fa fa-envelope-o"></i>&emsp;Send mail',
                            //     titleAttr: 'Send mail to parent(s)',
                            //     action: function (e, dt, node, config) {
                            //         checkRequest(ACTION_MAIL, 'Mail');
                            //     }
                            // },
                            {
                                extend: 'excel',
                                name: 'excel',
                                text: '<i class="fa fa-file-excel-o"></i>&emsp;Export to Excel',
                                titleAttr: 'Export data to an Excel file',
                                filename: 'Registrations -' + event_name,
                                exportOptions: {
                                    columns: ':visible',
                                    modifier: {
                                        autoFilter: true,
                                        // selected: true
                                    },
                                },
                            },
                            {
                                extend: 'pdfHtml5',
                                text: '<i class="fa fa-file-pdf-o" aria-hidden="true"></i>&emsp;Export to PDF',
                                title: 'Registrations -' + event_name,
                                titileAttr: 'PDF',
                                /* download : 'open', */
                                pageSize: 'A4',
                                extension: '.pdf',
                                header: true,
                                footer: false,
                                orientation: 'landscape',
                                exportOptions: {
                                    columns: [1, 2, 3, 4, 5, 6, 7, 8, 9],
                                },
                                customize: function (doc) {
                                    var rotNo = '<%=rotaryNo%>';
                                    doc.pageMargins = [30, 50, 30, 50]; //left,top,right,bottom
                                    doc.defaultStyle.fontSize = 7;
                                    doc.styles.tableHeader.fontSize = 7;
                                    doc.styles.title.fontSize = 16;
                                    // Remove spaces around page title
                                    doc.content[0].text =
                                        doc.content[0].text.trim();
                                    var objLayout = {};
                                    // Horizontal line thickness
                                    objLayout['hLineWidth'] = function (i) {
                                        return 0.5;
                                    };
                                    // Vertical line thickness
                                    objLayout['vLineWidth'] = function (i) {
                                        return 0.5;
                                    };
                                    // Horizontal line color
                                    objLayout['hLineColor'] = function (i) {
                                        return '#aaa';
                                    };
                                    // Vertical line color
                                    objLayout['vLineColor'] = function (i) {
                                        return '#aaa';
                                    };
                                    // Left padding of the cell
                                    objLayout['paddingLeft'] = function (i) {
                                        return 4;
                                    };
                                    // Right padding of the cell
                                    objLayout['paddingRight'] = function (i) {
                                        return 4;
                                    };
                                    // Inject the object in the document
                                    doc.content[1].layout = objLayout;
                                },
                            },
                        ],
                    },
                    {
                        text: 'Validator',
                        extend: 'selectedSingle',
                        action: function (e, dt, node, config) {
                            action = config.text;
                            validate(action);
                        },
                    },
                    {
                        extend: 'edit',
                        editor: editor,
                    },
                    {
                        extend: 'selectedSingle',
                        text: 'Delete Registration',
                        action: function () {
                            deleteRegistration();
                        },
                    },
                    {
                        extend: 'selectedSingle',
                        text: '<i class="fa fa-users"></i>&emsp;Assign to Club',
                        action: function () {
                            assignPlayerToClub();
                        },
                    },
                    // {
                    //     extend: 'selectedSingle',
                    //     text: 'Manage Clubs Are Assigned',
                    //     action: function () {
                    //         manageClubs();
                    //     },
                    // },
                    {
                        extend: 'colvis',
                        text: 'Columns',
                    },
                ],
            });
        }

        $scope.goBack = function () {
            window.history.back();
        };

        function manageClubs() {
            var table_selected = table.rows({ selected: true }).data();
            var row = table_selected[0];
            var DT_RowId = row['DT_RowId'];
            var registration_id = DT_RowId.split('_')[1];
            var player = row.players;

            if (player.gender != 'Female') {
                BootstrapDialog.show({
                    title: 'Information',
                    type: BootstrapDialog.TYPE_WARNING,
                    message:
                        'This function is only supported for female players',
                });

                return;
            }

            if (player.validate_status != VALIDATE_STATUS_Validated) {
                BootstrapDialog.show({
                    title: 'Information',
                    type: BootstrapDialog.TYPE_WARNING,
                    message:
                        'This function is only supported for validated players',
                });

                return;
            }

            $scope.thisPlayerRegisteredClubs = [];
            $scope.thisSeasonClubs = [];

            jQuery.ajax({
                type: 'POST',
                url: SERVER_PATH + 'club/getClubPlayersForAssign',
                async: false,
                headers: {
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name,
                },
                data: {
                    event_id: $routeParams.id,
                    player_id: player.id,
                },
                dataType: 'json',
                complete: function (response) {
                    var jsonData = JSON.parse(response.responseText);
                    $scope.thisPlayerRegisteredClubs = jsonData.registered_club;
                    $scope.thisSeasonClubs = jsonData.clubs;
                },
            });

            // show modal #clubModal
            $('#clubModal').modal({
                backdrop: 'static',
                keyboard: false,
            });
        }

        function assignPlayerToClub() {
            // check gender is female
            var table_selected = table.rows({ selected: true }).data();
            var row = table_selected[0];
            var DT_RowId = row['DT_RowId'];
            var registration_id = DT_RowId.split('_')[1];
            var player = row.players;

            if (player.gender != 'Female') {
                BootstrapDialog.show({
                    title: 'Information',
                    type: BootstrapDialog.TYPE_WARNING,
                    message:
                        'This function is only supported for female players',
                });

                return;
            }

            if (player.validate_status != VALIDATE_STATUS_Validated) {
                BootstrapDialog.show({
                    title: 'Information',
                    type: BootstrapDialog.TYPE_WARNING,
                    message:
                        'This function is only supported for validated players',
                });

                return;
            }

            var clubs = [];

            jQuery.ajax({
                type: 'POST',
                url: SERVER_PATH + 'club/getClubsForAssign',
                async: false,
                headers: {
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name,
                },
                data: {
                    registration_id: registration_id,
                },
                dataType: 'json',
                complete: function (response) {
                    var jsonData = JSON.parse(response.responseText);
                    jsonData.data.forEach((element) => {
                        clubs.push({ label: element.name, value: element.id });
                    });
                },
            });

            // show interface for assign club
            editor = new $.fn.dataTable.Editor({
                ajax: {
                    type: 'POST',
                    url: SERVER_PATH + 'club/assignClubToPlayer',
                    headers: {
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name,
                    },
                    data: function (d) {
                        d.registration_id = registration_id;
                        d.event_id = $routeParams.id;
                    },
                    dataType: 'json',
                    complete: function (response) {
                        var jsonData = JSON.parse(response.responseText);
                        if (jsonData.status == 'OK') {
                            Swal.fire({
                                title: 'Success!',
                                text: jsonData.message,
                                icon: 'success',
                                confirmButtonClass: 'btn btn-primary',
                                buttonsStyling: false,
                            });
                            table.ajax.reload();
                        } else {
                            Swal.fire({
                                title: 'Error!',
                                text: jsonData.message,
                                icon: 'error',
                                type: 'error',
                            });
                        }
                    },
                },
                fields: [
                    {
                        label: 'Club',
                        name: 'club_id',
                        type: 'select',
                        options: clubs,
                    },
                ],
            });

            editor.create({
                title: 'Assign registration to club',
                buttons: 'Assign',
            });
        }





        function quickAssign() {
            let table_selected = table.rows({ selected: true }).data();
        
            let row = table_selected[0];

            // validated status
            if (row.players.validate_status != VALIDATE_STATUS_Validated) {
                BootstrapDialog.show({
                    title: 'Cannot assign',
                    type: BootstrapDialog.TYPE_WARNING,
                    message:
                        'This function is only supported for validated players',
                });
        
                return;
            }else if (row.registrations.approval_status != APPROVAL_STATUS_Approve) {
                BootstrapDialog.show({
                    title: 'Cannot assign',
                    type: BootstrapDialog.TYPE_WARNING,
                    message:
                        'Player is not approved!',
                });
        
                return;
            }

            let club_id = row.clubs.id;
            let registration_id = row.registrations.id;
            let player_id = row.players.id;

            // validate empty
            if (club_id == null || registration_id == null || player_id == null) {
                BootstrapDialog.show({
                    title: 'Cannot assign',
                    type: BootstrapDialog.TYPE_DANGER,
                    message:
                        'Please check Club, Registration and Player information',
                });
        
                return;
            }

            async function quickAssignPlayerToTeam(player_id, club_id, event_id){
                return new Promise((resolve, reject) => {
                    $.ajax({
                        type: 'POST',
                        url: SERVER_PATH + 'registration/quickAssignPlayerToTeam',
                        async: true, // Make it async
                        headers: {
                            'x-user-id': $rootScope.user_id,
                            'x-user-email': $rootScope.user_name,
                        },
                        data: { player_id: player_id, event_id: event_id, club_id: club_id },
                        dataType: 'json',
                        success: function (response) {
                            if(response.status == 'OK'){
                                resolve(response);
                            }else{
                                reject(response);
                            }
                        },
                        error: function (error) {
                            console.error("Error assigning to team:", error);
                            reject(error);
                        }
                    });

                });
            }

            async function showProcess(player_id, club_id, event_id) {
                const steps = [
                    { name: 'Player assigned to team', func: () => quickAssignPlayerToTeam(player_id, club_id, event_id) }
                ];
        
                let htmlContent = steps.map(step => `<p id="${step.name.replaceAll(" ", "_")}" style="font-size: 18px;">🔄 ${step.name}</p>`).join("");
        
                Swal.fire({
                    title: "Processing...",
                    html: htmlContent,
                    allowOutsideClick: false,
                    showConfirmButton: false,
                    didOpen: async () => {
                        const swalContainer = Swal.getHtmlContainer();
        
                        for (const step of steps) {
                            const element = swalContainer.querySelector(`#${step.name.replaceAll(" ", "_")}`);
        
                            try {
                                await step.func(); // Call the API function
        
                                // Strike through the step when completed
                                element.innerHTML = `✅ <s>${step.name}</s>`;
                            } catch (error) {
                                element.innerHTML = `❌ <span style="color: red;">${step.name} failed</span>`;
                                Swal.fire({
                                    icon: "error",
                                    title: "Error",
                                    text: error.message,
                                    confirmButtonText: "OK"
                                });
                                return; // Stop process on failure
                            }
                        }
        
                        Swal.fire({
                            icon: "success",
                            title: "Completed!",
                            text: "Player has been successfully added to club, group, and team.",
                            confirmButtonText: "OK"
                        });

                        table.ajax.reload();
                    }
                });
            }
        
            // Loop through selected players
            table_selected.each((index, row) => {
                showProcess(player_id, club_id, event_id);
            });
        }
        
        
        

        function deleteRegistration() {
            var table_selected = table.rows({ selected: true }).data();

            var player = table_selected[0];

            var DT_RowId = player['DT_RowId'];

            var registration_id = DT_RowId.split('_')[1];

            BootstrapDialog.confirm(
                'Delete Registration',
                'Are you sure to delete this registration?',
                function (result) {
                    if (result) {
                        jQuery.ajax({
                            type: 'POST',
                            url:
                                SERVER_PATH +
                                'registration/deleteRegistrationGoldenAge',
                            async: true,
                            headers: {
                                'x-user-id': $rootScope.user_id,
                                'x-user-email': $rootScope.user_name,
                            },
                            data: {
                                registration_id: registration_id,
                            },
                            dataType: 'json',
                            beforeSend: function () {
                                Swal.fire({
                                    title: 'Please Wait!',
                                    allowOutsideClick: false,
                                    didOpen: () => {
                                        Swal.showLoading();
                                    },
                                });
                            },
                            complete: function (response) {
                                Swal.close();
                                var jsonData = JSON.parse(
                                    response.responseText
                                );

                                table.ajax.reload();

                                if (jsonData.status == 'OK') {
                                    Swal.fire({
                                        type: 'success',
                                        icon: 'success',
                                        title: jsonData.message,
                                        confirmButtonClass: 'btn btn-primary',
                                        buttonsStyling: false,
                                    });
                                } else {
                                    Swal.fire({
                                        title: 'ERROR!',
                                        text: jsonData.message,
                                        icon: 'error',
                                        type: 'error',
                                    });
                                }
                            },
                        });
                    }
                }
            );
        }

        // Validate player
        function validate(action) {
            var rows_selected = table
                .rows({
                    selected: true,
                })
                .data()[0];
            console.log(rows_selected);
            // var validate_status = rows_selected.players.validate_status;

            if (
                rows_selected.players.validate_status ==
                    VALIDATE_STATUS_Pending ||
                rows_selected.players.validate_status == VALIDATE_STATUS_Updated
            ) {
                // set field value
                $('#registration_id').val(rows_selected.registrations.id);
                $('#player_id').val(rows_selected.players.id);
                $('#surname').val(rows_selected.players.surname);
                $('#other_name').val(rows_selected.players.other_name);
                $('#dob').val(rows_selected.players.dob);
                var radio_gender = $('input:radio[name=gender]');
                if (rows_selected.players.gender == 'Male') {
                    radio_gender.filter('[value="Male"]').prop('checked', true);
                } else {
                    radio_gender
                        .filter('[value="Female"]')
                        .prop('checked', true);
                }
                $('#player_photo').attr(
                    'src',
                    PRODUCT_IMAGE_PATH + rows_selected.players.player_photo
                );
                var radio_hkid_passport_type = $(
                    'input:radio[name=hkid_passport_type]'
                );
                console.log(radio_hkid_passport_type);
                if (rows_selected.players.hkid_passport_type == 'HKID') {
                    radio_hkid_passport_type
                        .filter('[value="HKID"]')
                        .prop('checked', true);
                    $('#field_passport_expiry_date').hide();
                } else if (
                    rows_selected.players.hkid_passport_type == 'Passport'
                ) {
                    radio_hkid_passport_type
                        .filter('[value="Passport"]')
                        .prop('checked', true);
                    $('#field_passport_expiry_date').show();
                } else {
                    radio_hkid_passport_type
                        .filter('[value="Mainland Travel Permit"]')
                        .prop('checked', true);
                    $('#field_passport_expiry_date').show();
                }
                $('#hkid_passport_photo').attr(
                    'src',
                    PRODUCT_IMAGE_PATH +
                        rows_selected.players.hkid_passport_photo
                );
                $('#passport_expiry_date').val(
                    rows_selected.players.passport_expiry_date
                );
                $('#club_id').val(rows_selected.clubs.name);

                form_fields.forEach(function (field) {
                    $('#' + field.comment_id).attr('readonly', false);
                    $('input[name="' + field.valid_id + '"]').change((e) => {
                        var valid_surname_selected = $(
                            'input[name="' + field.valid_id + '"]:checked'
                        ).val();
                        if (valid_surname_selected == 'accept') {
                            $('#' + field.comment_id).attr('readonly', true);
                            $('#' + field.comment_id).val('');
                        } else if (valid_surname_selected == 'reject') {
                            $('#' + field.comment_id).attr('readonly', false);
                        } else {
                            console.log('unknow');
                        }
                    });

                    if (
                        rows_selected.players.validated_fields.includes(
                            field.value_id
                        )
                    ) {
                        console.log(' validated');
                        $(
                            'input:radio[name=' +
                                field.valid_id +
                                '][value=accept]'
                        ).click();
                        $(
                            '#' + field.field_id + ' .col-md-4.control-label'
                        ).attr('style', 'color: #8bc34a; font-weight: bold');
                        $('#' + field.comment_id).attr('disabled', true);
                        $('#' + field.comment_id).hide();
                        $(
                            'input:radio[name=' +
                                field.valid_id +
                                '][value=reject]'
                        ).attr('disabled', true);
                    } else {
                        console.log(' invalid');
                        $(
                            'input:radio[name=' +
                                field.valid_id +
                                '][value=reject]'
                        ).click();
                        $(
                            '#' + field.field_id + ' .col-md-4.control-label'
                        ).attr('style', '');
                        $('#' + field.comment_id).attr('disabled', false);
                        $('#' + field.comment_id).show();
                        $(
                            'input:radio[name=' +
                                field.valid_id +
                                '][value=reject]'
                        ).attr('disabled', false);
                    }
                });

                showmodal();
            } else {
                BootstrapDialog.show({
                    title: 'Information - ' + action,
                    type: BootstrapDialog.TYPE_WARNING,
                    message:
                        'Only players with <b>' +
                        VALIDATE_STATUS_Pending +
                        '</b> or <b>' +
                        VALIDATE_STATUS_Updated +
                        '</b> status can be validated!',
                });
            }
        }

        // show modal
        function showmodal() {
            $('#myModal').modal({
                backdrop: 'static',
                keyboard: false,
            });
            validateAction();
        }

        function validateAction() {
            $('#btnAcceptAll').click(() => {
                form_fields.forEach(function (field) {
                    $(
                        'input:radio[name=' + field.valid_id + '][value=accept]'
                    ).click();
                    $('#' + field.comment_id).attr('readonly', true);
                });
            });

            // unset #btnSubmit click event
            $('#btnSubmit').unbind('click');

            $('#btnSubmit').click(() => {
                $('#btnSubmit').attr('disabled', true);
                var form_data = new FormData($('#validate_form')[0]);

                $.ajax({
                    url: SERVER_PATH + 'registration/validateRegistration',
                    headers: {
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name,
                    },
                    data: form_data,
                    type: 'POST',
                    processData: false,
                    contentType: false,
                    success: function (response) {
                        $('#btnSubmit').attr('disabled', false);
                        var result = JSON.parse(response);
                        $('#myModal').modal('toggle');
                        if (result.status == 'OK') {
                            BootstrapDialog.show({
                                title: 'Information',
                                type: BootstrapDialog.TYPE_DANGER,
                                message: result.message,
                                onhidden: function () {
                                    $('#registration_table')
                                        .DataTable()
                                        .ajax.reload();
                                },
                            });
                        } else {
                            BootstrapDialog.show({
                                title: 'ERROR',
                                type: BootstrapDialog.TYPE_DANGER,
                                message: result.message,
                                onhidden: function () {
                                    $('#registration_table')
                                        .DataTable()
                                        .ajax.reload();
                                },
                            });
                        }
                    },
                });
            });
        }

        $('input:radio[name=hkid_passport_type]').change(function (e) {
            if ($(this).val() == 'HKID') {
                $('#field_passport_expiry_date').hide();
            } else {
                $('#field_passport_expiry_date').show();
            }
        });

        $('#dob').datepicker({
            todayHighlight: true,
            toggleActive: true,
            format: 'dd-M-yyyy',
            autoclose: true,
            beforeShow: function (i) {
                if ($(i).attr('readonly')) {
                    return false;
                }
            },
        });
        $('#dob').on('click', function () {
            console.log($('#dob').val());
            $('#dob').datepicker('setDate', new Date($('#dob').val()));
        });

        $('#passport_expiry_date').datepicker({
            todayHighlight: true,
            toggleActive: true,
            format: 'dd-M-yyyy',
            autoclose: true,
            beforeShow: function (i) {
                if ($(i).attr('readonly')) {
                    return false;
                }
            },
        });
        $('#passport_expiry_date').on('click', function () {
            console.log($('#passport_expiry_date').val());
            $('#passport_expiry_date').datepicker(
                'setDate',
                new Date($('#passport_expiry_date').val())
            );
        });

        $('#btnUpdate').click((e) => {
            var form_data = new FormData($('#validate_form')[0]);
            if (!$form.valid()) {
                BootstrapDialog.show({
                    title: 'ERROR',
                    type: BootstrapDialog.TYPE_WARNING,
                    message: 'Please check again before update !',
                });
                return false;
            }
            $('#btnUpdate').attr('disabled', true);
            $.ajax({
                url: SERVER_PATH + 'registration/updatePlayerRegistration',
                headers: {
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name,
                },
                data: form_data,
                type: 'POST',
                processData: false,
                contentType: false,
                success: function (response) {
                    $('#btnUpdate').attr('disabled', false);
                    var result = JSON.parse(response);
                    if (result.status == 'OK') {
                        BootstrapDialog.show({
                            title: 'Information',
                            type: BootstrapDialog.TYPE_INFO,
                            message: result.message,
                            onhidden: function () {
                                $('#registration_table')
                                    .DataTable()
                                    .ajax.reload();
                            },
                        });
                    } else {
                        BootstrapDialog.show({
                            title: result.status,
                            type: BootstrapDialog.TYPE_DANGER,
                            message: result.message,
                        });
                    }
                },
            });

            return false;
        });

        $('#myModal').on('hidden.bs.modal', function () {
            document.getElementById('validate_form').reset();
            form_fields.forEach((field) => {
                $('#' + field.comment_id).attr('disabled', false);
            });
        });

        $('#hkid_passport_photo').on('click', function () {
            openHKIDphoto();
        });

        function openHKIDphoto() {
            var hkid_passport_photo = document.getElementById(
                'hkid_passport_photo'
            );
            var url = hkid_passport_photo.getAttribute('src');
            window.open(url, '_blank', 'width=720px,height=480px,resizable=1');
        }

        $('#player_photo').on('click', function () {
            openPlayerPhoto();
        });

        function openPlayerPhoto() {
            var player_photo = document.getElementById('player_photo');
            var url = player_photo.getAttribute('src');
            window.open(url, '_blank', 'width=512px,height=512px,resizable=1');
        }

        // Check request
        function checkRequest(action_id, action) {
            var table_selected = table
                .rows({
                    selected: true,
                })
                .data();
            if (DEVELOPMENT_ENVIRONMENT) console.log(table_selected);

            var players = '';
            var name_index = 0;
            var rows_selected = [];
            for (var i = 0; i < table_selected.length; i++) {
                rows_selected.push(table_selected[i].registrations.id);
                name_index = i + 1;
                players +=
                    name_index +
                    '. ' +
                    table_selected[i].players.surname +
                    ' ' +
                    table_selected[i].players.other_name +
                    '<br/>';
            }

            if (DEVELOPMENT_ENVIRONMENT) console.log(rows_selected);
            var countRows = rows_selected.length;
            rows_selected.sort();
            var selectedRows = rows_selected.join('_');

            var message =
                countRows == 1
                    ? '<strong>1 registration? </strong><br/>' + players + ''
                    : '<strong>' +
                      countRows +
                      ' registrations?</strong><br/>' +
                      players +
                      '';

            if (selectedRows == '') {
                // Not selected
                BootstrapDialog.show({
                    title: 'Information - ' + action,
                    type: BootstrapDialog.TYPE_WARNING,
                    message: 'At least one registration must be selected!',
                });
            } else {
                var title = 'Confirmation - ' + action;
                var dialog = BootstrapDialog.confirm(
                    title,
                    message,
                    function (result) {
                        if (result) {
                            dialog.close();
                            if (DEVELOPMENT_ENVIRONMENT)
                                console.log(selectedRows);
                            if (action_id == ACTION_APPROVE_SEND_INVOICE) {
                                var urlStr =
                                    SERVER_PATH + 'registration/actions';
                                var formData =
                                    'registration_ids=' +
                                    selectedRows +
                                    '&action_id=' +
                                    action_id +
                                    '&event_id=' +
                                    event_id;
                                ajaxCustomApprove(
                                    formData,
                                    urlStr,
                                    action,
                                    action_id
                                );
                            } else if (action_id == ACTION_MAIL) {
                                ajaxMailAction(
                                    selectedRows,
                                    SERVER_PATH + 'registration/actions_mail',
                                    action,
                                    action_id
                                );
                            }
                        }
                    }
                );
                if (name_index > 15) {
                    dialog.getModalBody().css('height', '330px');
                    dialog.getModalBody().css('overflow-y', 'scroll');
                }
            }
        }

        // Ajax server
        function ajaxAction(
            event_id,
            registration_ids,
            URL,
            action,
            action_id
        ) {
            var editor = new $.fn.dataTable.Editor({
                ajax: {
                    type: 'POST',
                    url: URL,
                    async: false,
                    headers: {
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name,
                    },
                    data: {
                        event_id: event_id,
                        user_id: $rootScope.user_id,
                        registration_ids: registration_ids,
                    },
                    dataType: 'json',
                    complete: function (response) {
                        var jsonData = JSON.parse(response.responseText);
                        displayActionMessage(jsonData, action, action_id);
                    },
                    error: function (xhr, status, error) {
                        alert(
                            'ajaxAction.Error - status, error = ' +
                                status +
                                ',' +
                                error +
                                ',' +
                                xhr
                        );
                    },
                },
                formOptions: {
                    main: {
                        onBlur: 'none',
                    },
                },
                fields: [
                    {
                        label: 'Subject:',
                        name: 'subject',
                        def: appName + ' Reminder ',
                    },
                    {
                        label: 'Message:',
                        name: 'message',
                        type: 'ckeditor',
                        def: 'Dear parent, </br></br>Please note that the registration for player registration is currently incomplete and pending the successful submission of the following information:</br> Please go to Registration in the app and resubmit the information. </br></br> Regards ',
                    },
                ],
            });

            editor
                .title('Reminder')
                .buttons({
                    label: 'Send',
                    fn: function () {
                        this.submit();
                    },
                })
                .edit()
                .open();
        }

        function ajaxMailAction(registration_ids, url, action, action_id) {
            var editor = new $.fn.dataTable.Editor({
                ajax: {
                    type: 'POST',
                    url: url,
                    headers: {
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name,
                    },
                    data: {
                        user_id: $rootScope.user_id,
                        registration_ids: registration_ids,
                        event_id: event_id,
                    },
                    dataType: 'json',
                    complete: function (response) {
                        var jsonData = JSON.parse(response.responseText);
                        if (jsonData.status != 'DATA_ERROR')
                            displayActionMessage(jsonData, action, action_id);
                    },
                    error: function (xhr, status, error) {},
                },
                formOptions: {
                    main: {
                        onBlur: 'none',
                    },
                },
                fields: [
                    {
                        label: 'Cc',
                        name: 'cc',
                        fieldInfo: 'Separate with semicolon (;)',
                    },
                    {
                        label: 'Bcc',
                        name: 'bcc',
                        fieldInfo: 'Separate with semicolon (;)',
                    },
                    {
                        label: 'Subject',
                        name: 'subject',
                    },
                    {
                        label: 'Content',
                        name: 'content',
                        type: 'ckeditor',
                        def: '',
                    },
                    {
                        label: 'Attachment 1:',
                        name: 'file_1',
                        type: 'upload',
                        display: function (data) {
                            return data;
                        },
                        clearText: 'Clear',
                    },
                    {
                        label: 'Attachment 2:',
                        name: 'file_2',
                        type: 'upload',
                        display: function (data) {
                            return data;
                        },
                        clearText: 'Clear',
                    },
                    {
                        label: 'Attachment 3:',
                        name: 'file_3',
                        type: 'upload',
                        display: function (data) {
                            return data;
                        },
                        clearText: 'Clear',
                    },
                ],
            });

            editor
                .title('Send Mail')
                .buttons({
                    label: 'Send',
                    fn: function () {
                        this.submit();
                    },
                })
                .edit()
                .open();
        }

        function ajaxCustomApprove(data, URL, action, action_id) {
            jQuery.ajax({
                type: 'POST',
                url: URL,
                async: false,
                headers: {
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name,
                },
                data: data,
                dataType: 'json',
                complete: function (response) {
                    var jsonData = JSON.parse(response.responseText);
                    displayActionMessage(jsonData, action, action_id);
                },
                error: function (xhr, status, error) {
                    alert(
                        'ajaxAction.Error - status, error = ' +
                            status +
                            ',' +
                            error +
                            ',' +
                            xhr
                    );
                },
            });
        }

        function displayActionMessage(jsonData, action, action_id) {
            //console.log(jsonData);
            if (jsonData.status == 'OK') {
                BootstrapDialog.show({
                    title: 'Information - ' + action,
                    type: function () {
                        if (jsonData.message.indexOf('Please try again') == -1)
                            return BootstrapDialog.TYPE_SUCCESS;
                        return BootstrapDialog.TYPE_WARNING;
                    },
                    message: jsonData.message,
                    onhidden: function (dialogRef) {
                        table.ajax.reload();
                    },
                });
            } else {
                BootstrapDialog.show({
                    title: 'Information - ' + action,
                    type: BootstrapDialog.TYPE_DANGER,
                    message: jsonData.message,
                });
            }
        }

        function reminderAction(action_id, action) {
            var table_selected = table
                .rows({
                    selected: true,
                })
                .data();
            if (DEVELOPMENT_ENVIRONMENT) console.log(table_selected);

            var countRows = table_selected.length;

            var names = '';
            var statuss = '';
            var name_index = 0;
            var rows_selected = [];
            console.log(table_selected);

            for (var i = 0; i < table_selected.length; i++) {
                10;
                if (
                    table_selected[i].players.validate_status ==
                        VALIDATE_STATUS_Invalid &&
                    action_id == ACTION_REMINDER
                ) {
                    rows_selected.push(table_selected[i].registrations.id);
                    name_index = i + 1;
                    names +=
                        name_index +
                        '. ' +
                        table_selected[i].players.surname +
                        ' ' +
                        table_selected[i].players.other_name +
                        '<br/> ';
                }
            }

            if (table_selected.length > rows_selected.length) {
                BootstrapDialog.show({
                    title: 'Information - ' + action,
                    type: BootstrapDialog.TYPE_WARNING,
                    message:
                        'Only players with <b>' +
                        VALIDATE_STATUS_Invalid +
                        '</b> </b> status can be reminded!',
                });
            } else {
                if (DEVELOPMENT_ENVIRONMENT) console.log(statuss);
                rows_selected.sort();
                var selectedRows = rows_selected.join('_');

                if (action_id == ACTION_REMINDER) {
                    message =
                        countRows == 1
                            ? '<strong>1 registration? </strong><br/>' +
                              names +
                              '  '
                            : '<strong>' +
                              countRows +
                              ' registrations?</strong><br/>' +
                              names +
                              '  ';
                }
                if (selectedRows == '') {
                    // Not selected
                    BootstrapDialog.show({
                        title: 'Information - ' + action,
                        type: BootstrapDialog.TYPE_WARNING,
                        message: 'Please select one registration!',
                    });
                } else {
                    var title = 'Confirmation - ' + action;
                    var dialog = BootstrapDialog.confirm(
                        title,
                        message,
                        function (result) {
                            if (result) {
                                dialog.close();
                                if (DEVELOPMENT_ENVIRONMENT)
                                    console.log(selectedRows);
                                if (action_id == ACTION_REMINDER) {
                                    var urlStr =
                                        SERVER_PATH +
                                        'registration/reminderRegistration';
                                    // var formData = "registration_ids=" + selectedRows + "&action_id=" + action_id +
                                    //     "&event_id=" + event_id;
                                    ajaxAction(
                                        event_id,
                                        rows_selected,
                                        urlStr,
                                        action,
                                        action_id
                                    );
                                }
                            }
                        }
                    );
                    if (name_index > 15) {
                        dialog.getModalBody().css('height', '330px');
                        dialog.getModalBody().css('overflow-y', 'scroll');
                    }
                }
            }
        }
    }
);
