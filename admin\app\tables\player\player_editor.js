app.controller('playerCtrl', [
    '$scope',
    '$rootScope',
    '$routeParams',
    '$http',
    '$q',
    function ($scope, $rootScope, $routeParams, $http, $q) {
        user_id = localStorage.getItem('hkjflApp.user_id');
        user_name = localStorage.getItem('hkjflApp.user_name');
        user = null;

        var link = document.createElement('link');
        link.rel = 'stylesheet';
        link.type = 'text/css';
        link.href = 'app/tables/player/player_editor.css';
        document.head.appendChild(link);

        // Optional: Remove CSS when changing views
        $scope.$on('$destroy', function () {
            document.head.removeChild(link);
        });

        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            'datetime-custom-pre': function (value) {
                if (!value || value.trim() === '') return 0;

                const parts = value.split(' ');
                const timePart = parts[0].split(':');

                const hours = parseInt(timePart[0], 10);
                const minutes = parseInt(timePart[1], 10);
                const day = parseInt(parts[1], 10);
                const month = new Date(
                    Date.parse(parts[2] + ' 1, 2020')
                ).getMonth();
                const year = parseInt(parts[3], 10);

                const dateObj = new Date(year, month, day, hours, minutes);

                return dateObj.getTime();
            },
            'datetime-custom-asc': function (a, b) {
                return a - b;
            },
            'datetime-custom-desc': function (a, b) {
                return b - a;
            },
        });

        $('body').unbind('click');

        $('body').on(
            'click',
            'li[class=active] a[data-toggle=tab]',
            function () {
                switch ($(this).data('target')) {
                    case '#tab-players': {
                        $scope.initTabPlayers();
                        break;
                    }
                    case '#tab-request-update': {
                        $scope.initTabRequestUpdate();
                        break;
                    }
                    case "#tab-merge-players": {
                        $scope.initTabMergePlayers();
                        break;
                    }
                }
            }
        );

        function getRoleByUserID() {
            temp_user_id = user_id.replace(/['"]+/g, '');

            return new Promise((resolve, reject) => {
                $.ajax({
                    type: 'POST',
                    url: SERVER_PATH + 'user/getInfoUser',
                    data: {
                        user_id: Number(temp_user_id),
                    },
                    async: false,
                    dataType: 'json',
                    complete: function (response) {
                        let data = response.responseJSON;
                        if (data.status == 'OK') {
                            resolve(data.info);
                        }
                    },
                    error: function (xhr, status, error) {
                        console.log(error);
                    },
                });
            });
        }

        function getFieldsNeedToUpdate(update_request_id) {
            let fields = [];

            $.ajax({
                type: 'POST',
                url:
                    SERVER_PATH +
                    'update-request/getUpdateRequestFieldsForPlayer',
                data: {
                    request_id: update_request_id,
                },
                async: false,
                dataType: 'json',
                complete: function (response) {
                    var jsonData = JSON.parse(response.responseText);
                    if (jsonData.status == 'OK') {
                        fields = jsonData.info.fields;
                    }
                },
                error: function (xhr, status, error) {
                    console.log(error);
                },
            });

            return fields;
        }

        function getcropperHTML(src) {
            var cropperHTML =
                '<div class="cropper"><img style="height: 600px" class="js-avatar-preview" src="' +
                src +
                '"><div class="overlay"></div></div>';
            return cropperHTML;
        }

        $scope.initTabPlayers = function () {
            var table;
            var editor = new $.fn.dataTable.Editor({
                ajax: {
                    type: 'POST',
                    url: SERVER_PATH + 'player/setPlayers',
                    headers: {
                        'x-user-id': user_id,
                        'x-user-email': user_name,
                    },
                    data: {
                        // "pgroup_id": pgroup_id
                    },
                    dataType: 'json',
                    complete: function (response) {
                        var jsonData = JSON.parse(response.responseText);
                        // --- may need to reload
                        if (DEVELOPMENT_ENVIRONMENT)
                            console.log('status = ' + jsonData.status);
                        if (jsonData.status == 'OK') {
                            if (DEVELOPMENT_ENVIRONMENT)
                                console.log('Before reload');
                            table.ajax.reload();
                        }
                    },
                    error: function (xhr, status, error) {},
                },
                table: '#player_table',
                formOptions: {
                    main: {
                        onBlur: 'none',
                    },
                },
                i18n: {
                    create: {
                        button: 'New',
                        title: 'Create new player',
                        submit: 'Create',
                    },
                    edit: {
                        button: 'Edit',
                        title: 'Edit player',
                        submit: 'Save',
                    },
                    remove: {
                        button: 'Delete',
                        title: 'Delete player',
                        submit: 'Delete',
                        confirm: {
                            _: 'Are you sure you want to delete these players?',
                            1: 'Are you sure you want to delete this player?',
                        },
                    },
                    error: {
                        system: 'System error, please contact player.',
                    },
                },
                fields: [
                    {
                        label: 'Surname:',
                        name: 'players.surname',
                    },
                    {
                        label: 'Other name',
                        name: 'players.other_name',
                    },
                    {
                        label: 'Chinese name',
                        name: 'players.chinese_name',
                    },
                    {
                        label: 'DOB',
                        name: 'players.dob',
                        type: 'datetime',
                        format: 'DD-MMM-YYYY',
                    },
                    {
                        label: 'Gender',
                        name: 'players.gender',
                        type: 'radio',
                        options: [
                            { label: 'Male', value: 'Male' },
                            { label: 'Female', value: 'Female' },
                        ],
                        default: 'Male',
                    },
                    {
                        label: 'Player HKID',
                        name: 'players.hkid_no',
                        type: 'text',
                    },
                    {
                        label: 'HKID/Passport photo',
                        name: 'players.hkid_passport_photo',
                        type: 'upload',
                        display: function (file_id) {
                            return (
                                '<a href="' +
                                PRODUCT_IMAGE_PATH +
                                file_id +
                                '" target="_blank">View</a>'
                            );
                        },
                        clearText: 'Clear',
                    },
                    {
                        label: 'Player Photo',
                        name: 'players.player_photo',
                        type: 'upload',
                        display: function (file_id) {
                            return (
                                '<a href="' +
                                PRODUCT_IMAGE_PATH +
                                file_id +
                                '" target="_blank">View</a>'
                            );
                        },
                        clearText: 'Clear',
                    },
                    {
                        label: 'HKID/Passport type',
                        name: 'players.hkid_passport_type',
                        type: 'radio',
                        options: ['HKID', 'Passport', 'Mainland Travel Permit'],
                        def: 'HKID',
                    },
                    {
                        label: 'Passport expiry date',
                        name: 'players.passport_expiry_date',
                        type: 'datetime',
                        format: 'DD-MMM-YYYY',
                    },
                    {
                        label: 'Parent email',
                        name: 'parens.email',
                    },
                ],
            });

            editor.on('preUpload', function (e, fieldName, file, ajaxData) {
                var aspectRatio = 2 / 1;
                var width = 600;

                if (fieldName == 'players.hkid_passport_photo') {
                    aspectRatio = 4 / 3;
                    width = 900;
                } else if (fieldName == 'players.player_photo') {
                    aspectRatio = 3 / 4;
                    width = 600;
                }

                var promise = new Promise(function (resolve, reject) {
                    const MIME_TYPE = 'image/jpeg';
                    const QUALITY = 0.75;
                    var name = file.name;
                    var validImageTypes = [
                        'image/gif',
                        'image/jpeg',
                        'image/png',
                    ];

                    if ($.inArray(file.type, validImageTypes) < 0) {
                        $('div.upload button i').html('Choose file...');
                        editor
                            .field(fieldName)
                            .error(
                                'Please upload an image file (.jpg or .png)'
                            );
                        return false;
                    }

                    const blobURL = URL.createObjectURL(file);
                    const img = new Image();
                    img.src = blobURL;
                    var bootrapDialog;

                    bootrapDialog = BootstrapDialog.show({
                        size: BootstrapDialog.SIZE_LARGE,
                        type: BootstrapDialog.TYPE_DANGER,
                        cssClass: 'modal-cropper',
                        closable: true,
                        closeByBackdrop: false,
                        closeByKeyboard: true,
                        draggable: true,
                        setDragMode: 'move',
                        title: 'Crop Photo',
                        message: getcropperHTML(blobURL),
                        buttons: [],
                        onshown: function (dialog) {
                            var image = $('.cropper img')[0];
                            cropper = new Cropper(image, {
                                viewMode: 2,
                                aspectRatio: aspectRatio,
                                minContainerWidth: width,
                                minContainerHeight: width / aspectRatio,
                                minCropBoxWidth: width,
                                minCropBoxHeight: width / aspectRatio,
                                movable: true,
                                ready: function () {
                                    if (fieldName == 'players.player_photo') {
                                        // Add div.cropper-overlay
                                        const $cropperViewBox =
                                            $('.cropper-view-box');
                                        const $div = $(
                                            '<div class="cropper-overlay"></div>'
                                        );
                                        $cropperViewBox.after($div);

                                        // Add style for .cropper-overlay
                                        const $cropperOverlay =
                                            $('.cropper-overlay');
                                        $cropperOverlay.css({
                                            top: '0',
                                            left: '0',
                                            position: 'absolute',
                                            width: '100%',
                                            height: '100%',
                                            backgroundImage:
                                                'url(./images/face-template.png)',
                                            backgroundRepeat: 'no-repeat',
                                            backgroundSize: 'contain',
                                        });
                                    }
                                },
                            });
                        },
                        onhide: function () {
                            $('div.upload button i').html('Choose file...');
                        },
                        buttons: [
                            {
                                label: 'Zoom Out',
                                action: function () {
                                    cropper.zoom(-0.1);
                                },
                            },
                            {
                                label: 'Zoom In',
                                action: function () {
                                    cropper.zoom(0.1);
                                },
                            },
                            {
                                label: 'Rotate',
                                action: function () {
                                    cropper.rotate(90);
                                },
                            },
                            {
                                label: 'Reset',
                                action: function () {
                                    cropper.reset();
                                },
                            },
                            {
                                label: 'Cancel',
                                action: function (dialog) {
                                    bootrapDialog.close();
                                    $('div.upload button i').html(
                                        'Choose file...'
                                    );
                                    reject();
                                },
                            },
                            {
                                label: 'OK',
                                action: function (dialog) {
                                    event.preventDefault();
                                    var $button = $(this);
                                    $button.text('uploading...');
                                    $button.prop('disabled', true);
                                    const canvas = cropper.getCroppedCanvas();
                                    canvas.toBlob(
                                        (blob) => {
                                            // Handle the compressed images upload or save
                                            let file_small = new File(
                                                [blob],
                                                name,
                                                {
                                                    type: 'image/jpeg',
                                                    lastModified:
                                                        new Date().getTime(),
                                                }
                                            );
                                            let container = new DataTransfer();
                                            container.items.add(file_small);
                                            x = container.files[0];

                                            var form_data = new FormData();
                                            form_data.append('file', x);

                                            var url =
                                                SERVER_PATH +
                                                'player/setUploadImg';
                                            $.ajax({
                                                url: url,
                                                type: 'POST',
                                                cache: false,
                                                data: form_data,
                                                async: false,
                                                processData: false,
                                                mimeType: 'multipart/form-data',
                                                contentType: false,
                                                timeout: 0,
                                                error: function (err) {
                                                    bootrapDialog.close();
                                                    reject();
                                                },
                                                success: function (response) {
                                                    data = JSON.parse(response);
                                                    if (data.status == 'OK') {
                                                        editor
                                                            .field(fieldName)
                                                            .val(data.info);
                                                        bootrapDialog.close();
                                                        resolve(true);
                                                    } else {
                                                        editor
                                                            .field(fieldName)
                                                            .error(
                                                                data.message
                                                            );
                                                        bootrapDialog.close();
                                                        resolve(true);
                                                    }
                                                },
                                            });
                                        },
                                        MIME_TYPE,
                                        QUALITY
                                    );
                                },
                            },
                        ],
                    });
                });
                return false;
            });

            editor.dependent('players.hkid_passport_type', function (val) {
                return val === 'HKID'
                    ? { hide: ['players.passport_expiry_date'] }
                    : { show: ['players.passport_expiry_date'] };
            });

            if ($.fn.dataTable.isDataTable('#player_table')) {
                // reset all data in the table
                $('#player_table').DataTable().destroy();
                // reset checkboxes in local storage
            }

            var table = $('#player_table').DataTable({
                dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
                stateSave: true,
                deferRender: true,
                processing: true,
                serverSide: true,
                searchDelay: 2000,
                ajax: {
                    url: SERVER_PATH + 'player/getPlayers',
                    type: 'POST',
                    data: {
                        // "pgroup_id": pgroup_id
                    },
                    headers: {
                        'x-user-id': user_id,
                        'x-user-email': user_name,
                    },
                    dataType: 'json',
                    complete: function (response) {},
                    error: function (xhr, status, error) {},
                },
                language: {
                    info: 'Showing _START_ to _END_ of _TOTAL_ player',
                    infoEmpty: 'Showing 0 to 0 of 0 players',
                    lengthMenu: 'Show _MENU_ players',
                    select: {
                        rows: {
                            _: 'You have selected %d players',
                            0: 'Click a player to select',
                            1: '1 player selected',
                        },
                    },
                    paginate: {
                        previous: '<i class="fa fa-chevron-left"></i>',
                        next: '<i class="fa fa-chevron-right"></i>',
                    },
                },
                columns: [
                    {
                        data: 'players.player_photo',
                        className: 'avatar',
                        orderable: false,
                        render: function (data) {
                            if (data !== null && data !== '') {
                                return (
                                    '<img src="' +
                                    PRODUCT_IMAGE_PATH +
                                    data +
                                    '">'
                                );
                            } else {
                                return (
                                    '<img src="' +
                                    SYSTEM_IMAGE_PATH +
                                    'favicon.png">'
                                );
                            }
                        },
                    },
                    {
                        data: 'player_name',
                    },
                    {
                        data: 'players.chinese_name',
                    },
                    {
                        data: 'players.player_id_no',
                        render: function (data, type, row) {
                            return (
                                '<span class="label label-danger player_id_no" style="cursor: pointer" data-playerId=' +
                                row.players.id +
                                '>' +
                                data +
                                '</span>'
                            );
                        },
                    },
                    {
                        data: 'players.id_creation_date',
                    },
                    {
                        data: 'parens.email',
                        render: function (data, type, row) {
                            return (
                                row.parens.surname +
                                ' ' +
                                row.parens.other_name +
                                '<br>' +
                                row.parens.email
                            );
                        },
                    },
                    { data: 'players.dob', className: 'center' },
                    { data: 'players.gender', className: 'center' },
                    { data: 'players.hkid_no', className: 'center' },
                    { data: 'players.hkid_passport_type', className: 'center' },
                    {
                        data: 'players.hkid_passport_photo',
                        render: function (data, type, full, meta) {
                            if (data != '') {
                                return '<a class="open_hkid_passport_photo">Image</a>';
                            }
                            return '';
                        },
                    },
                    {
                        data: 'players.passport_expiry_date',
                        className: 'center',
                    },

                    {
                        data: 'players.validate_status',
                        className: 'center',
                        render: function (data, type, full, meta) {
                            switch (data) {
                                case VALIDATE_STATUS_Pending:
                                    return (
                                        '<span class="label label-info">' +
                                        data +
                                        '</span>'
                                    );
                                case VALIDATE_STATUS_Invalid:
                                    return (
                                        '<span class="label label-danger">' +
                                        data +
                                        '</span>'
                                    );
                                case VALIDATE_STATUS_Updated:
                                    return (
                                        '<span class="label label-warning">' +
                                        data +
                                        '</span>'
                                    );
                                case VALIDATE_STATUS_Validated:
                                    return (
                                        '<span class="label label-success">' +
                                        data +
                                        '</span>'
                                    );
                                default:
                                    return (
                                        '<span class="label label-default">' +
                                        data +
                                        '</span>'
                                    );
                            }
                        },
                    },
                    {
                        data: 'players.manager_created',
                        className: 'center',
                        render: function (data, type, full, meta) {
                            let checked = data == 0 ? '' : 'checked';
                            return `<div >
                  <input style="width: 25px;height: 25px;" type="checkbox" class="changeState-checkbox" id="switch${full.players.id}" ${checked}>
                </div>
              `;
                        },
                    },
                    {
                        data: 'players.deleted',
                        className: 'center',
                        render: function (data, type, full, meta) {
                            let label = data == 1 ? 'Yes' : 'No';
                            return label;
                        },
                    },
                ],
                select: {
                    style: 'single',
                    selector: 'td:first-child',
                },
                order: [[1, 'asc']],
                lengthMenu: [
                    [10, 25, 50, 100, 1000],
                    [10, 25, 50, 100, 1000],
                ],
                buttons: [
                    { extend: 'edit', editor: editor },
                    { extend: 'remove', editor: editor },
                    {
                        extend: 'excel',
                        name: 'excel',
                        text: '<i class="fa fa-file-excel-o"></i>&emsp;Export to Excel',
                        titleAttr: 'Export data to an Excel file',
                        filename: appName + ' - Players',
                        title: appName + ' - Players',
                        exportOptions: {
                            columns: ':visible',
                            modifier: {
                                autoFilter: true,
                            },
                        },
                        action: exportAllToExcel,
                    },
                ],
            });

            getRoleByUserID().then((data) => {
                user = data;
                localStorage.removeItem(
                    'DataTables_player_table_/ezactivevn/hkfa/admin/index.html'
                );
                if (data.role != USER_SUPER_ADMIN) {
                    // hide collunm
                    table.column(8).visible(false);
                    table.column(9).visible(false);
                    table.column(10).visible(false);
                    table.column(11).visible(false);
                } else {
                    table.column(8).visible(true);
                    table.column(9).visible(true);
                    table.column(10).visible(true);
                    table.column(11).visible(true);
                }
            });

            editor.on('preOpen', function (e, mode, action) {
                if (user.role != USER_SUPER_ADMIN) {
                    // disable edit some field
                    editor.hide('players.hkid_no');
                    editor.hide('players.hkid_passport_type');
                    editor.hide('players.passport_expiry_date');
                    editor.hide('players.hkid_passport_photo');
                    editor.hide('players.player_photo');
                }
            });

            function getPlayerModalHTML(player_id) {
                return (
                    '<div class="table-responsive"> <table id="activity_log_each_player_table_' +
                    player_id +
                    '" class="table table-striped table-bordered table-hover" cellspacing="0" width="100%"> <thead> <th>Event name</th> <th>Event Type</th> <th>Create at</th> <th>Update at</th> <th>Status</th> </thead> </table> </div>'
                );
            }

            // off player_table click event
            $('#player_table').off('click', 'tbody td .player_id_no');

            $('#player_table').on(
                'click',
                'tbody td .player_id_no',
                async (e) => {
                    var row = $(e.target).closest('tr');
                    var data = table.row(row).data();
                    var player_id = data.players.id;
                    var player_id_no = data.players.player_id_no;
                    var player = data.players;

                    var html = getPlayerModalHTML(player_id);

                    BootstrapDialog.show({
                        title:
                            `Report ` +
                            player.surname +
                            ` ` +
                            player.other_name,
                        message: html,
                        size: BootstrapDialog.SIZE_WIDE,
                        onshown: function (dialogRef) {
                            $(
                                '#activity_log_each_player_table_' + player_id
                            ).DataTable({
                                dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
                                stateSave: true,
                                deferRender: true,
                                ajax: {
                                    url:
                                        SERVER_PATH +
                                        'player/getTableForActivityPlayer',
                                    type: 'POST',
                                    headers: {
                                        'x-user-id': user_id,
                                        'x-user-email': user_name,
                                    },
                                    data: {
                                        player_id: player_id,
                                    },
                                    dataType: 'json',
                                    complete: function (response) {},
                                    error: function (xhr, status, error) {},
                                },
                                language: {
                                    info: 'Showing _START_ to _END_ of _TOTAL_ player',
                                    infoEmpty:
                                        'Showing 0 to 0 of 0 registrations',
                                    lengthMenu: 'Show _MENU_ registrations',
                                    select: {
                                        rows: {
                                            _: 'You have selected %d registrations',
                                            0: 'Click an player to select',
                                            1: '1 player selected',
                                        },
                                    },
                                    paginate: {
                                        previous:
                                            '<i class="fa fa-chevron-left"></i>',
                                        next: '<i class="fa fa-chevron-right"></i>',
                                    },
                                },
                                columns: [
                                    {
                                        data: 'events.name',
                                        className: 'center',
                                    },
                                    {
                                        data: 'events.type',
                                        className: 'center',
                                    },
                                    {
                                        data: 'registrations.registered_date',
                                        className: 'center',
                                    },
                                    {
                                        data: 'registrations.approved_date',
                                        className: 'center',
                                    },
                                    {
                                        data: 'registrations.approval_status',
                                        className: 'center',
                                        render: function (
                                            data,
                                            type,
                                            full,
                                            meta
                                        ) {
                                            switch (data) {
                                                case APPROVAL_STATUS_Approve:
                                                    return (
                                                        '<span class="label label-success">' +
                                                        data +
                                                        '</span>'
                                                    );
                                                case APPROVAL_STATUS_Register:
                                                    return (
                                                        '<span class="label label-info">' +
                                                        data +
                                                        '</span>'
                                                    );
                                                case APPROVAL_STATUS_Reject:
                                                    return (
                                                        '<span class="label label-danger">' +
                                                        data +
                                                        '</span>'
                                                    );
                                                default:
                                                    return (
                                                        '<span class="label label-default">' +
                                                        data +
                                                        '</span>'
                                                    );
                                            }
                                        },
                                    },
                                ],
                                select: {
                                    style: 'single',
                                    selector: 'td:first-child',
                                },
                                lengthMenu: [
                                    [10, 25, 50, 100, -1],
                                    [10, 25, 50, 100, 'All'],
                                ],
                                buttons: [
                                    {
                                        extend: 'excel',
                                        name: 'excel',
                                        text: '<i class="fa fa-file-excel-o"></i>&emsp;Export to Excel',
                                        titleAttr:
                                            'Export data to an Excel file',
                                        filename: appName + ' - Players',
                                        title: appName + ' - Players',
                                        exportOptions: {
                                            columns: ':visible',
                                            modifier: {
                                                autoFilter: true,
                                            },
                                        },
                                    },
                                    { extend: 'colvis', text: 'Columns' },
                                ],
                            });
                        },
                    });
                }
            );

            $('#player_table').off('click', 'tbody td .changeState-checkbox');

            $('#player_table').on(
                'click',
                'tbody td .changeState-checkbox',
                async (e) => {
                    console.log($(e.target).prop('checked'));
                    var row = $(e.target).closest('tr');
                    var data = table.row(row).data();
                    var status = data.players.manager_created == '1' ? 0 : 1;
                    var res = await changeStatus(data.players, status);
                    console.log(res.status);
                    console.log(status);
                    if (res.status == 'OK') {
                        $(e.target).prop('checked', status);
                        table.row(row).data().players.manager_created = status;
                    } else {
                        $(e.target).prop('checked', !status);
                    }
                }
            );

            function changeStatus(player, status) {
                var data = {
                    player_id: player.id,
                    status: status,
                };
                return $.ajax({
                    type: 'POST',
                    url: SERVER_PATH + 'player/setStateCreatedByManager',
                    headers: {
                        'x-user-id': user_id,
                        'x-user-email': user_name,
                    },
                    data: data,
                    dataType: 'json',
                    complete: (response) => {
                        let jsonData = JSON.parse(response.responseText);
                        if (jsonData.status != 'OK') {
                            BootstrapDialog.show({
                                title: 'Error',
                                message: jsonData.message,
                                type: BootstrapDialog.TYPE_DANGER,
                            });
                        }
                        return jsonData;
                    },
                });
            }

            $('#player_table').off(
                'click',
                'tbody td a.open_hkid_passport_photo'
            );

            // Open passport photo
            $('#player_table').on(
                'click',
                'tbody td a.open_hkid_passport_photo',
                function (e) {
                    var $row = $(this).closest('tr');
                    var data = table.row($row).data();
                    var msg =
                        '<img src="' +
                        PRODUCT_IMAGE_PATH +
                        data.players.hkid_passport_photo +
                        '" width="100%">';
                    BootstrapDialog.show({
                        title: 'HKID/Passport photo - ' + data.players.surname,
                        message: msg,
                    });
                }
            );
        };

        function getHTMLForApprovalModal(data) {
            var html =
                '<table> <thead> <tr> <th>Field</th> <th>From</th> <th>To</th> </tr> </thead> <tbody> ';
            let temp_html = '';
            data.forEach((item) => {
                if (item.type == 'text') {
                    if (item.old_value == null) {
                        item.old_value = '';
                    }
                    temp_html +=
                        ' <tr> <td>' +
                        item.field +
                        '</td> <td>' +
                        item.old_value +
                        '</td> <td>' +
                        item.new_value +
                        '</td> </tr> ';
                } else {
                    if (item.old_value == null) {
                        item.old_value = '';
                    } else {
                        item.old_value =
                            '<img src="' +
                            PRODUCT_IMAGE_PATH +
                            item.old_value +
                            '">';
                    }

                    temp_html +=
                        ' <tr> <td>' +
                        item.field +
                        '</td> <td> ' +
                        item.old_value +
                        ' </td> <td> <img src="' +
                        PRODUCT_IMAGE_PATH +
                        item.new_value +
                        '"> </td> </tr> ';
                }
            });
            html += temp_html;
            html +=
                ' </tbody> </table> <style>th{padding:20px} td{padding:20px} td{ width:45%; border: 1px solid #CECECE; } td:first-child{ width:10% } img{ width:100%; aspect-ratio: 16/9 } th{ border: 1px solid #CECECE; } table{ border-collapse: collapse; } tbody td:first-child{ font-weight:600 }</style>';
            return html;
        }

        $scope.initTabPlayers();

        $scope.initTabRequestUpdate = function () {
            let requestUpdateTable = $('#requestTable').DataTable({
                dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
                stateSave: false,
                deferRender: true,
                bDestroy: true,
                ajax: {
                    url: SERVER_PATH + 'update-request/getSetUpdateRequest',
                    headers: {
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name,
                    },
                    type: 'POST',
                    data: {},
                    dataType: 'json',
                    beforeSend: function (xhr) {
                        Swal.fire({
                            title: 'Please Wait!',
                            allowOutsideClick: false,
                            didOpen: () => {
                                Swal.showLoading();
                            },
                        });
                    },
                    complete: function (response) {
                        Swal.close();
                    },
                    error: function (xhr, status, error) {},
                },
                language: {
                    paginate: {
                        previous: '<i class="fa fa-chevron-left"></i>',
                        next: '<i class="fa fa-chevron-right"></i>',
                    },
                },
                columns: [
                    {
                        data: 'update_requests.id',
                    },
                    {
                        data: 'full_name',
                    },
                    {
                        data: 'parent_name',
                    },
                    {
                        data: 'parens.email',
                    },
                    {
                        data: 'update_requests.reason',
                    },
                    {
                        data: 'update_requests.status',
                        render: function (data, type, full, meta) {
                            if (data == 'Pending')
                                return '<span class="label label-info">Pending</span>';
                            else if (data == 'Approved')
                                return '<span class="label label-success">Approved</span>';
                            else if (data == 'Rejected')
                                return '<span class="label label-danger">Rejected</span>';
                            else
                                return '<span class="label label-default">Unknown</span>';
                        },
                    },
                    {
                        data: 'update_requests.created_at',
                    },
                    {
                        data: 'update_requests.updated_at',
                    },
                ],
                initComplete: function () {
                    var status_column = {
                        orderColumn: 5,
                        elementId: 'status-column-content',
                        selectId: 'selType',
                        label: 'Filter by status',
                    };

                    filterColumns = [status_column];

                    filterColumns.forEach((item) => {
                        this.api()
                            .columns(item.orderColumn)
                            .every(function () {
                                var column = this;

                                // set label
                                $(`#${item.elementId}`).html(
                                    `<label>${item.label}</label>`
                                );

                                // create select element
                                var select = $(
                                    `<select id="${item.orderColumn}" class="form-control"><option value="">All</option></select>`
                                )
                                    .appendTo($(`#${item.elementId}`))
                                    .on('change', function () {
                                        var val =
                                            $.fn.dataTable.util.escapeRegex(
                                                $(this).val()
                                            );

                                        column
                                            .search(
                                                val ? '^' + val + '$' : '',
                                                true,
                                                false
                                            )
                                            .draw();
                                    })
                                    .select2();
                                var column_data = column.data();
                                var select_data = [];
                                column_data.map((item) => {
                                    if (item != null) {
                                        item.indexOf(', ') > 0
                                            ? (item = item.split(', '))
                                            : (item = [item]);
                                        item.forEach((item) => {
                                            select_data.push(item);
                                        });
                                    }
                                });

                                select_data
                                    .filter(onlyUnique)
                                    .sort(function (a, b) {
                                        a = parseInt(a.replace('U', ''));
                                        b = parseInt(b.replace('U', ''));
                                        return a - b;
                                    })
                                    .map(function (d, j) {
                                        let label = toCamelCase(d);
                                        select.append(
                                            `<option value="${d}">${label}</option>`
                                        );
                                    });
                            });
                    });
                },
                select: {
                    style: SELECT_MODE,
                },
                order: [[7, 'desc']],
                columnDefs: [
                    {
                        targets: 7,
                        type: "datetime-custom",
                    }
                ],
                buttons: [
                    {
                        extend: 'selectedSingle',
                        name: 'Review',
                        text: 'Review',
                        className: 'btn btn-review',
                        action: function () {
                            // show modal review
                            let data = requestUpdateTable
                                .row({ selected: true })
                                .data();

                            var requestedUpdateId = data.DT_RowId.split('_')[1];

                            if (data.update_requests.status != 'Pending') {
                                Swal.fire({
                                    title:
                                        'This request has been ' +
                                        data.update_requests.status,
                                    icon: 'error',
                                    confirmButtonText: 'OK',
                                });

                                return;
                            }

                            let fields =
                                getFieldsNeedToUpdate(requestedUpdateId);

                            // show modal review
                            BootstrapDialog.show({
                                title: 'Review',
                                size: BootstrapDialog.SIZE_WIDE,
                                type: BootstrapDialog.TYPE_DANGER,
                                message: getHTMLForApprovalModal(fields),
                                buttons: [
                                    {
                                        label: 'Reject',
                                        cssClass: 'btn btn-primary',
                                        action: function (dialog) {
                                            Swal.fire({
                                                title: 'Reason for rejection',
                                                input: 'text',
                                                inputPlaceholder:
                                                    'Enter reason',
                                                showCancelButton: true,
                                                confirmButtonText: 'Reject',
                                                showLoaderOnConfirm: true,
                                                allowOutsideClick: () =>
                                                    !Swal.isLoading(),
                                                confirmButtonColor: '#ed1c24',
                                                target: '.modal',
                                                customClass: {
                                                    actions: 'my-actions',
                                                    confirmButton: 'order-2',
                                                    cancelButton: 'order-1',
                                                },
                                            }).then((result) => {
                                                Swal.close();
                                                if (result.isConfirmed) {
                                                    $.ajax({
                                                        type: 'POST',
                                                        url:
                                                            SERVER_PATH +
                                                            'update-request/rejectRequest',
                                                        headers: {
                                                            'x-user-id':
                                                                user_id,
                                                            'x-user-email':
                                                                user_name,
                                                        },
                                                        data: {
                                                            request_id:
                                                                requestedUpdateId,
                                                            reason: result.value,
                                                        },
                                                        dataType: 'json',
                                                        complete: function (
                                                            response
                                                        ) {
                                                            var jsonData =
                                                                JSON.parse(
                                                                    response.responseText
                                                                );
                                                            if (
                                                                jsonData.status ==
                                                                'OK'
                                                            ) {
                                                                Swal.fire({
                                                                    title: 'Success',
                                                                    text: 'Reject request update successful',
                                                                    icon: 'success',
                                                                    confirmButtonText:
                                                                        'OK',
                                                                }).then(() => {
                                                                    dialog.close();
                                                                    requestUpdateTable.ajax.reload();
                                                                });
                                                            }
                                                        },
                                                        error: function (
                                                            xhr,
                                                            status,
                                                            error
                                                        ) {
                                                            console.log(error);
                                                        },
                                                    });
                                                }
                                            });
                                        },
                                    },
                                    {
                                        label: 'Approve',
                                        cssClass: 'btn btn-success',
                                        action: function (dialog) {
                                            // build confirm by swal
                                            Swal.fire({
                                                title: 'Are you sure?',
                                                text: 'You are about to accept this request? All fields will be updated!',
                                                icon: 'warning',
                                                iconColor: 'rgb(237, 28, 36)',
                                                showCancelButton: true,
                                                confirmButtonText: 'Approve',
                                                showLoaderOnConfirm: true,
                                                allowOutsideClick: () =>
                                                    !Swal.isLoading(),
                                                confirmButtonColor: '#ed1c24',
                                                target: '.modal',
                                                customClass: {
                                                    actions: 'my-actions',
                                                },
                                            }).then((result) => {
                                                if (result.isConfirmed) {
                                                    $.ajax({
                                                        type: 'POST',
                                                        url:
                                                            SERVER_PATH +
                                                            'update-request/approveRequest',
                                                        headers: {
                                                            'x-user-id':
                                                                user_id,
                                                            'x-user-email':
                                                                user_name,
                                                        },
                                                        data: {
                                                            request_id:
                                                                requestedUpdateId,
                                                        },
                                                        dataType: 'json',
                                                        complete: function (
                                                            response
                                                        ) {
                                                            Swal.close();
                                                            var jsonData =
                                                                JSON.parse(
                                                                    response.responseText
                                                                );
                                                            if (
                                                                jsonData.status ==
                                                                'OK'
                                                            ) {
                                                                Swal.fire({
                                                                    title: 'Success',
                                                                    text: 'Accept request update successful',
                                                                    icon: 'success',
                                                                    confirmButtonText:
                                                                        'OK',
                                                                }).then(() => {
                                                                    dialog.close();
                                                                    requestUpdateTable.ajax.reload();
                                                                });
                                                            }
                                                        },
                                                        error: function (
                                                            xhr,
                                                            status,
                                                            error
                                                        ) {
                                                            console.log(error);
                                                        },
                                                    });
                                                }
                                            });
                                        },
                                    },
                                ],
                            });
                        },
                    },
                ],
            });

            
        };
        $scope.initTabMergePlayers = function () {
            if ($.fn.dataTable.isDataTable('#merge_players_table')) {
                // reset all data in the table
                $('#merge_players_table').DataTable().destroy();
                // reset checkboxes in local storage
            }
            $('#merge_players_table').DataTable({
                dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
                stateSave: false,
                deferRender: true,
                processing: true,
                serverSide: true,
                searchDelay: 2000,
                ajax: {
                    url: SERVER_PATH + 'player/getDuplicatePlayers',
                    type: 'POST',
                    data: function (d) {
                        d.field = $('#groupByField').val(); // Get the selected field value dynamically
                    },
                    headers: {
                        'x-user-id': user_id,
                        'x-user-email': user_name,
                    },
                    dataType: 'json',
                    complete: function (response) {},
                    error: function (xhr, status, error) {},
                },
                language: {
                    info: 'Showing _START_ to _END_ of _TOTAL_ player',
                    infoEmpty: 'Showing 0 to 0 of 0 players',
                    lengthMenu: 'Show _MENU_ players',
                    select: {
                        rows: {
                            _: 'You have selected %d players',
                            0: 'Click a player to select',
                            1: '1 player selected',
                        },
                    },
                    paginate: {
                        previous: '<i class="fa fa-chevron-left"></i>',
                        next: '<i class="fa fa-chevron-right"></i>',
                    },
                },
                columns: [
                    { data: 'players.player_photo', orderable: false, visible: false },
                    { data: 'players.id', visible: false },
                    { data: 'players.chinese_name', visible: false },
     
                    { data: 'players.dob', className: 'center', visible: false, render: function (data) { return moment(data).format('YYYY-MM-DD'); } },
                    { data: 'players.gender', className: 'center', visible: false },
                  
                    { data: 'players.validate_status', className: 'center', visible: false },
                ],
                
                select: {
                    style: 'single',
                    selector: 'td:first-child',
                },
                order: [[1, 'asc']],
                lengthMenu: [
                    [12, 24, 50, 100, 1000],
                    [12, 24, 50, 100, 1000],
                ],

                rowGroup: {
                    dataSrc: function (row) {
                        let groupByFields = $('#groupByField').val(); // Get selected grouping fields (array)
                        
                        function cleanAndLowercase(str) {
                            return str ? str.trim().toLowerCase() : ''; // Trim and convert to lowercase
                        }
                
                        let groupValues = groupByFields.map(field => {
                            if (row.players.hasOwnProperty(field)) {
                                return cleanAndLowercase(row.players[field]);
                            }
                            return ''
                        });
                        
                        return groupValues.join(', '); // Join multiple field values with a separator
                    },

                    startRender: function (rows, group) {
                        if (rows.count() < 2) {
                            return `  `;
                        }
                
                        function encodeTableId(group) {
                            return btoa(encodeURIComponent(group)).replace(/[^a-zA-Z0-9]/g, '_');
                        }
                        
                        let tableId = encodeTableId(group);
                        let groupByFields = $('#groupByField').val().map(f => f.replaceAll("_", " ")).join(', ');
                        group = group.split('|').map(word => word.trim()).join(' | ');
                        group = group.replace('00:00:00.000000', '');
                        
                        return `
                        <hr>
                            <div class="table-group">
                                <div class="group-header">
                                    <div>
                                       <span class="text-secondary">Player ${groupByFields}:</span> <span class="text-primary">${group}</span> <br>
                                       <span class="text-secondary">Duplicate Count:</span> <span class="text-primary">${rows.data().length}</span>
                                    </div>
                                    <div>
                                        <button id="merge_${tableId}" class="merge-btn">Merge</button>
                                    </div>
                                </div>
                                <table id="${tableId}" class="table table-striped table-bordered" cellspacing="0" width="100%">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Player name</th>
                                            <th>Gender</th>
                                            <th>DOB</th>
                                            <th>Player Photo</th>
                                            <th>Chinese Name</th>
                                            <th>Validate Status</th>
                                            <th>Parent</th>
                                            <th>Registration</th>
                                        </tr>
                                    </thead>
                                    <tbody></tbody>
                                </table>

                            </div>
                        `;
                    },
                    endRender: function (rows, group) {
                        setTimeout(() => {
                            let tableId = btoa(encodeURIComponent(group)).replace(/[^a-zA-Z0-9]/g, '_');
                            let tableElement = $(`#${tableId}`);
                            if (tableElement.length === 0) {
                                console.warn(`⚠️ Table with ID "${tableId}" not found in DOM`);
                                return;
                            }
                            
                            if (!$.fn.DataTable.isDataTable(`#${tableId}`)) {
                                tableElement.DataTable({
                                    data: rows.data().toArray(),
                                    columns: [
                                        { data: 'players.id' },
                                        {
                                            data: null,
                                            render: (data, type, row) =>
                                                row.players.surname +
                                                ' ' +
                                                row.players.other_name,
                                        },
                                        { data: 'players.gender' },
                                        {
                                            data: 'players.dob',
                                            render: (data) =>
                                                data
                                                    ? moment(data).format(
                                                          'YYYY-MM-DD'
                                                      )
                                                    : '',
                                        },
                                        {
                                            data: 'players.player_photo',
                                            className: 'avatar',
                                            render: (data) =>
                                                data
                                                    ? `<img src="${PRODUCT_IMAGE_PATH}${data}" width="50">`
                                                    : `<img src="${SYSTEM_IMAGE_PATH}favicon.png" width="50">`,
                                        },
                                        { data: 'players.chinese_name' },
                                        {
                                            data: 'players.validate_status',
                                            render: (data) =>
                                                `<span class="label ${
                                                    data ===
                                                    VALIDATE_STATUS_Pending
                                                        ? 'label-info'
                                                        : data ===
                                                          VALIDATE_STATUS_Invalid
                                                        ? 'label-danger'
                                                        : data ===
                                                          VALIDATE_STATUS_Updated
                                                        ? 'label-warning'
                                                        : data ===
                                                          VALIDATE_STATUS_Validated
                                                        ? 'label-success'
                                                        : 'label-default'
                                                }">${data}</span>`,
                                        },
                                        {
                                            data: null,
                                            className: 'text-center',
                                            render: (data, type, row) => {
                                                const player_id =
                                                    row.players.id;
                                                // button show parent
                                                return `
                                                   <button type="button" class="btn btn-sm btn-primary" data-toggle="modal" data-target="#parentModal" data-id="${player_id}"><i class="fa fa-search"></i> Parent</button>
                                                `;
                                            },
                                        },
                                        {
                                            data: null,
                                            className: 'text-center',
                                            render: (data, type, row) => {
                                                const player_id =
                                                    row.players.id;
                                                // button show registration
                                                return `
                                                 <button type="button" class="btn btn-sm btn-primary" data-toggle="modal" data-target="#registrationModal" data-id="${player_id}"><i class="fa fa-search"></i> Registration</button>
                                                `;
                                            },
                                        },
                                    ],
                                    order: [[0, 'asc']],
                                    paging: false,
                                    info: false,
                                    searching: false,
                                });
                            } else {
                                let table = tableElement.DataTable();
                                table.clear();
                                table.rows.add(rows.data().toArray());
                                table.draw();
                            }


                            $('#merge_players_table')
                            .off('click', '[data-target="#parentModal"]')
                            .on('click', '[data-target="#parentModal"]', async function (e) {
                                e.preventDefault(); // Prevent default link behavior
                                let playerId = $(this).data('id');

                                console.log('Fetching parent details for player ID:', playerId);

                                try {
                                    let response = await $.ajax({
                                        url: SERVER_PATH + 'player/getParentByPlayerId', // Updated API endpoint
                                        type: 'POST',
                                        dataType: 'json',
                                        data: { player_id: playerId }, // Sending player_id in request body
                                    });

                                    if (response.status === 'OK' && response.data) {
                                        let parent = response.data;

                                        let tableHTML = `
                                            <div style="overflow-x:auto;">
                                                <table class="table table-bordered" style="width:100%; text-align:left;">
                                                    <thead>
                                                        <tr>
                                                            <th>Parent Name</th>
                                                            <th>Phone</th>
                                                            <th>Email</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <tr>
                                                            <td>${parent.surname || 'N/A'} ${parent.other_name || 'N/A'}</td>
                                                            <td>${parent.phone || 'N/A'}</td>
                                                            <td>${parent.email || 'N/A'}</td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                        `;

                                        Swal.fire({
                                            title: 'Parent Details',
                                            html: tableHTML,
                                            width: 'max-content',
                                            confirmButtonText: 'Close'
                                        });
                                    } else {
                                        Swal.fire({
                                            title: 'Parent Details',
                                            text: response.message || 'No parent information found.',
                                            icon: 'warning',
                                            confirmButtonText: 'Close'
                                        });
                                    }
                                } catch (error) {
                                    console.error('Error fetching parent details:', error);
                                    Swal.fire({
                                        title: 'Error',
                                        text: 'Failed to retrieve parent information.',
                                        icon: 'error',
                                        confirmButtonText: 'Close'
                                    });
                                }
                            });

                            $('#merge_players_table')
                            .off('click', '[data-target="#registrationModal"]')
                            .on('click', '[data-target="#registrationModal"]', async function (e) {
                                e.preventDefault(); // Prevent default link behavior
                                let playerId = $(this).data('id');
                        
                                console.log('Fetching registration details for player ID:', playerId);
                        
                                try {
                                    let response = await $.ajax({
                                        url: SERVER_PATH + 'player/getRegistrationsByPlayerId', // Updated API endpoint
                                        type: 'POST',
                                        dataType: 'json',
                                        data: { player_id: playerId }, // Sending player_id in request body
                                    });
                        
                                    if (response.status === 'OK' && response.data.length > 0) {
                                        let registrations = response.data;
                        
                                        let tableHTML = `
                                            <div style="overflow-x:auto; max-width: 100%;">
                                                <table class="table table-bordered" style="width:100%; text-align:left;">
                                                    <thead>
                                                        <tr>
                                                            <th>Registration ID</th>
                                                            <th>Event Name</th>
                                                            <th>Type</th>
                                                            <th>Register Date</th>
                                                            <th>Approved Date</th>
                                                            <th>Status</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                        `;
                        
                                        registrations.forEach(reg => {
                                            tableHTML += `
                                                <tr>
                                                    <td>${reg.id || 'N/A'}</td>
                                                    <td>${reg.name || 'N/A'}</td>
                                                    <td>${reg.type || 'N/A'}</td>
                                                    <td>${reg.registered_date || 'N/A'}</td>
                                                    <td>${reg.approved_date || 'N/A'}</td>
                                                    <td>
                                                        <span class="label ${reg.approval_status === 'Approved' ? 'label-success' : 'label-warning'}">
                                                            ${reg.approval_status || 'Pending'}
                                                        </span>
                                                    </td>
                                                </tr>
                                            `;
                                        });
                        
                                        tableHTML += `</tbody></table></div>`;
                        
                                        Swal.fire({
                                            title: 'Registration Details',
                                            html: tableHTML,
                                            width: 'fit-content', // Ensures content takes its natural width
                                            confirmButtonText: 'Close'
                                        });
                                    } else {
                                        Swal.fire({
                                            title: 'Registration Details',
                                            text: response.message || 'No registration information found.',
                                            width: 'fit-content',
                                            confirmButtonText: 'Close'
                                        });
                                    }
                                } catch (error) {
                                    console.error('Error fetching registration details:', error);
                                    Swal.fire({
                                        title: 'Error',
                                        text: 'Failed to retrieve registration information.',
                                        icon: 'error',
                                        width: 'fit-content',
                                        confirmButtonText: 'Close'
                                    });
                                }
                            });
                             

                            // Delegate event listener for dynamically generated elements
                            $('#merge_players_table').off('click', '.merge-btn').on('click', '.merge-btn', async function (e) {
                                e.preventDefault();
                    
                                let button = $(this);
                                if (button.prop('disabled')) return;
                    
                                button.prop('disabled', true);
                    
                                let id = this.id;
                                let tableId = id.replace('merge_', '');
                    
                                // Get player IDs
                                let table_info = $(`#${tableId}`).DataTable().rows().data().toArray();
                                let player_ids = table_info.map(row => row.players.id);
                    
                                if (player_ids.length === 0) {
                                    Swal.fire('Error', 'No players found to merge.', 'error');
                                    button.prop('disabled', false);
                                    return;
                                }

                    
                                async function mergePlayers(player_ids, chosen_player_id) {
                                    try {
                                        let response = await $.ajax({
                                            url: SERVER_PATH + 'player/mergePlayers',
                                            type: 'POST',
                                            headers: {	
                                                'x-user-id': $rootScope.user_id,
                                                'x-user-email': $rootScope.user_name
                                            },
                                            data: { player_ids, chosen_player_id, user_id: $rootScope.user_id },
                                            dataType: 'json'
                                        });
                    
                                        if (response.status === 'OK') {
                                            $('#merge_players_table').DataTable().ajax.reload(null, false);
                                            Swal.fire('Success', response.message || 'Players merged successfully.', 'success');              
                                        } else {
                                            Swal.fire('Error', response.message || 'Failed to merge players.', 'error');
                                        }
                                    } catch (error) {
                                        Swal.fire('Error', `Request failed: ${error}`, 'error');
                                    } finally {
                                        button.prop('disabled', false);
                                    }
                                }
                                                    
                                try {
                                    // Generate the HTML structure with centered radio buttons
                                    let chosen_player_id = null;

                                    if (player_ids.length < 10) {
                                        // Generate radio buttons for selection
                                        const radioOptions = player_ids.map(id => `
                                            <div style="text-align: center; margin-bottom: 8px;">
                                                <input type="radio" id="player_${id}" name="player" value="${id}" style="margin-right: 5px;">
                                                <label for="player_${id}" style="font-size: 16px;">Player ${id}</label>
                                            </div>
                                        `).join('');

                                        chosen_player_id = await Swal.fire({
                                            title: 'Choose a player to merge with',
                                            html: `<div style="display: flex; flex-direction: column; align-items: center;">${radioOptions}</div>`,
                                            showCancelButton: true,
                                            confirmButtonText: 'Merge',
                                            preConfirm: () => {
                                                const selectedPlayer = document.querySelector('input[name="player"]:checked');
                                                return selectedPlayer ? selectedPlayer.value : null;
                                            }
                                        }).then(result => result.value);
                                    } else {
                                        // Use a select dropdown if more than 10 players
                                        chosen_player_id = await Swal.fire({
                                            title: 'Select player to merge with',
                                            input: 'select',
                                            inputOptions: Object.fromEntries(player_ids.map(id => [id, `Player ${id}`])),
                                            showCancelButton: true,
                                            confirmButtonText: 'Merge'
                                        }).then(result => result.value);
                                    }

                                
                                    if (chosen_player_id) {
                                        Swal.fire({
                                            title: 'Are you sure?',
                                            text: "Only keep the selected player, the others will be deleted.",
                                            icon: 'warning',
                                            showCancelButton: true,
                                            confirmButtonText: 'Yes, merge it!',
                                            cancelButtonText: 'No, cancel!',
                                            reverseButtons: true
                                        }).then(async (result) => {
                                            if (result.isConfirmed) {
                                                await mergePlayers(player_ids, chosen_player_id);
                                            } else {
                                                button.prop('disabled', false);
                                            }
                                        });
                                    } else {
                                        button.prop('disabled', false);
                                    }
                                } catch (error) {
                                    Swal.fire({
                                        title: 'Error',
                                        text: 'Something went wrong!',
                                        icon: 'error'
                                    });
                                    button.prop('disabled', false);
                                }
                                
                            });




                            $('#merge_players_table').removeClass('dataTable');
                        }, 500);
                    }
                },
                buttons: [],
                info: false,
                searching: false
                                    
            });
            
        };

        $('#groupByField').on('change', () => {
            console.log('change');
            // can only select max 3 fields
            // if ($('#groupByField').val().length > 5) {
            //     // remove the last selected field
            //     $('#groupByField option:selected').last().prop('selected', false);
            //     $('#groupByField').trigger('change');
            //     Swal.fire('Error', 'You can only select a maximum of 5 fields', 'error');
            //     return;

            // }
         
           $('#merge_players_table').DataTable().ajax.reload(null, false);
            
        });
        const onlyUnique = (value, index, self) => self.indexOf(value) === index;
        const toCamelCase = text => text.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
    },
]);
