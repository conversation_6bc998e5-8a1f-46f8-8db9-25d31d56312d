<link rel="stylesheet"
    href="app/registrations/registration-summer-scheme/offline-registration/select-course/select-course.css" />

<!-- Modal -->
<div class="modal" id="myModal" role="dialog">
    <div class="modal-dialog" style="width: 640px;margin: auto;background-color: #ffffff;border-radius: 15px;">
        <div class="col-11 p-0 mt-3 mb-2">
            <div class="card px-0 pt-4 pb-0 mt-3 mb-3">
                <div class="row text-right" style="margin-right: 0px">
                    <button type="button" class="btn btn-close" style="border: 0px" data-dismiss="modal">
                        x
                    </button>
                </div>
                <div class="row text-center">
                    <h2><strong>Select Course</strong></h2>
                    <p>Fill all form field to go to next step</p>
                </div>
                <div class="row">
                    <div class="col-md-12 mx-0">
                        <form id="msform" name="regisForm">
                            <!-- progressbar -->
                            <ul id="progressbar">
                                <li id="course" class="active"><strong>Course</strong></li>
                                <li id="tshirt"><strong>Goods</strong></li>
                            </ul>
                            <fieldset ng-form="courseInfor">
                                <div class="form-card">
                                    <h2 class="fs-title">Course Information</h2>

                                    <!-- course -->
                                    <div ng-repeat="course in checkboxCourse">
                                        <div class="panel form-card-sm">
                                            <div class="panel-heading">
                                                <input type="checkbox" id="{{course.type}}"
                                                    ng-disabled="course.disabled" ng-model="select_course"
                                                    ng-click="toggleCourse(course.type,courseInfor)"
                                                    ng-checked="selectedCourses.indexOf(course.type) > -1"
                                                    ng-required="selectedCourses.length == 0" />
                                                <label for="{{course.type}}" class="h5"><strong
                                                        class="title">{{course.title}}</strong></label>
                                            </div>
                                            <div class="panel-body">
                                                <div class="row">
                                                    <div class="col-sm-8">
                                                        <p ng-repeat="title in course.sub_title">
                                                            {{title}}
                                                        </p>
                                                    </div>
                                                    <div class="col-sm-4">
                                                        <p>
                                                            Class selected:
                                                            <strong>{{transferCoursesToText(course.class_selected)}} </strong>
                                                        </p>
                                                    </div>
                                                </div>
                                                <div class="row"
                                                    ng-show="course.hasOwnProperty('input') && selectedCourses.indexOf(course.type) > -1">
                                                    <div ng-repeat="input in course.input">
                                                        <div class="col-sm-3">
                                                            <p>{{input.title}}:</p>
                                                        </div>
                                                        <div class="col-sm-9">
                                                            <input class="form-control text-capitalize"
                                                                type="{{input.type}}" name="{{input.name}}"
                                                                max="{{input.type=='date'?input.max :''}}"
                                                                min="{{input.type=='date'?input.min :''}}"
                                                                placeholder="{{input.placeholder}}"
                                                                ng-model="input.value"
                                                                ng-required="selectedCourses.indexOf(course.type) > -1 && input.required"
                                                                ng-pattern="selectedCourses.indexOf(course.type) > -1? input.pattern : ''" />
                                                            <div class="errorContainer">
                                                                <div ng-repeat="validation in enMsg[input.validate]">
                                                                    <div ng-show="courseInfor[input.name].$dirty && courseInfor[input.name].$invalid && courseInfor[input.name].$error.hasOwnProperty(validation.type)"
                                                                        class="text-danger">
                                                                        <i class="fa fa-times"></i>
                                                                        <span> {{ validation.message }} </span>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <hr />
                                                <div ng-show="selectedCourses.indexOf(course.type) > -1">
                                                    <div class="errorContainer">
                                                        <div ng-show="course.course.length == 0" class="text-danger">
                                                            <span>
                                                                There are no classes matching this player
                                                            </span>
                                                        </div>
                                                    </div>
                                                    <div ng-show="show">
                                                        <div class="row">
                                                            <div class="col-sm-6">
                                                                <!-- <label
                                                    >Region: <input ng-model="search.region"
                                                  /></label> -->
                                                            </div>
                                                            <div class="col-sm-6">
                                                                <label>Search: <input ng-model="search.$" /></label>
                                                            </div>
                                                        </div>
                                                        <div class="container-class">
                                                            <ul class="list-group">
                                                                <li ng-repeat="class in course.course | filter:search"
                                                                    class="list-group-item">
                                                                    <div class="row">
                                                                        <div class="col-sm-9">
                                                                            <p>
                                                                                Class code:
                                                                                <strong> {{class.class_code}}</strong>
                                                                            </p>
                                                                            <p>
                                                                                Venue:
                                                                                <strong>{{class.venue_name}}</strong>
                                                                            </p>
                                                                            <p>
                                                                                Day of the Week:
                                                                                <strong>{{class.dotw}}</strong>
                                                                            </p>
                                                                            <p>
                                                                                Birth year:
                                                                                <strong>{{class.group_year}}</strong>
                                                                            </p>
                                                                            <p>
                                                                                Region:
                                                                                <strong>{{class.region}}</strong>
                                                                            </p>
                                                                        </div>
                                                                        <div class="col-sm-3">
                                                                            <button type="button" class="btn btn-danger"
                                                                                ng-click="selectCourse(class, course.type)">
                                                                                select
                                                                            </button>
                                                                        </div>
                                                                    </div>
                                                                </li>
                                                            </ul>
                                                            <!-- {{checkboxCourse.normal}} -->
                                                        </div>
                                                    </div>
                                                    <button type="button" class="btn btn-outline-primary"
                                                        ng-init="show=false; " ng-click="show=!show; "
                                                        style="width: 100%; margin-bottom: 10px">
                                                        <i ng-class="show?'fa fa-angle-up':'fa fa-angle-down'"
                                                            aria-hidden="true"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="errorContainer">
                                            <div ng-show="selectedCourses.indexOf(course.type) > -1 && course.class_selected.id == '' "
                                                class="text-danger">
                                                <i class="fa fa-times"></i>
                                                <span> Please select the class </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="errorContainer">
                                        <div ng-show="courseInfor.$dirty && selectedCourses.length == 0"
                                            class="text-danger">
                                            <i class="fa fa-times"></i>
                                            <span> Please select the course </span>
                                        </div>
                                    </div>
                                </div>
                                <button type="button" name="next" class="next action-button" value="Next Step"
                                    ng-click="submitCourse($event, courseInfor,goodsInfo)">
                                    Next Step
                                </button>
                            </fieldset>
                            <!-- define goodInfo -->
                            <fieldset ng-form="goodsInfo">
                                <div class="form-card">
                                    <h2 class="fs-title">Course info</h2>
                                    <div class="row" ng-show="selectedCourses.indexOf('parent') > -1">
                                        <div class="col-sm-4">Parent bib size:</div>
                                        <div class="col-sm-8">
                                            <select class="form-control" ng-model="size.parent">
                                                <option ng-repeat="size in shirt_sizes" ng-value="size.id">
                                                    {{size.size_name}}
                                                </option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="errorContainer">
                                        <div ng-show="selectedCourses.indexOf('parent') > -1 && size.parent==0"
                                            class="text-danger">
                                            <i class="fa fa-times"></i>
                                            <span> Please select the bib size </span>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-sm-4">Player bib size:</div>
                                        <div class="col-sm-8">
                                            <select class="form-control" ng-model="size.player">
                                                <option ng-repeat="size in shirt_sizes" ng-value="size.id">
                                                    {{size.size_name}}
                                                </option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="errorContainer">
                                        <div ng-show="selectedCourses.length >0 && size.player==0" class="text-danger">
                                            <i class="fa fa-times"></i>
                                            <span> Please select the bib size </span>
                                        </div>
                                    </div>
                                </div>
                                <button type="button" name="previous" class="previous action-button-previous">
                                    Previous
                                </button>
                                <button type="button" name="next" class="next action-button" value="Next Step"
                                    ng-click="submitGoods($event, goodsInfo, paymentInfo)">
                                    Next Step
                                </button>
                            </fieldset>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>