# This file was auto-generated by the Firebase CLI
# https://github.com/firebase/firebase-tools

name: Deploy to Firebase Hosting on merge
'on':
  push:
    branches:
      - main
jobs:
  build_and_deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup node 14
        uses: actions/setup-node@v1
        with:
          node-version: 14.x
      - run: npm i -g @ionic/cli
      - run: cd client && npm ci && npm run build --prod
      - uses: FirebaseExtended/action-hosting-deploy@v0
        with:
          repoToken: '${{ secrets.GITHUB_TOKEN }}'
          firebaseServiceAccount: '${{ secrets.FIREBASE_SERVICE_ACCOUNT_HKFA_GRASSROOTS }}'
          channelId: live
          projectId: hkfa-grassroots
