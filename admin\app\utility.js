function showNotification(
    message,
    type = 'success',
    layout = 'attached',
    effect = 'genie',
    timeout = 3000
) {
    var notification = new NotificationFx({
        message: message,
        layout: 'attached',
        effect: 'genie',
        type: 'success', // notice, success, warning or error
        ttl: 3000,
        onClose: function () {},
    });

    notification.show();
}

function showSuccess(message, title = 'Success') {
    Swal.fire({
        title: title,
        html: message,
        icon: 'success',
        confirmButtonText: 'OK',
        confirmButtonColor: '#ed1c24',
    });
}

function showWarning(message, title = 'Warning') {
    Swal.fire({
        title: title,
        html: message,
        icon: 'warning',
        confirmButtonText: 'OK',
        confirmButtonColor: '#ed1c24',
    });
}

function showError(message, title = 'Error') {
    Swal.fire({
        title: title,
        html: message,
        icon: 'error',
        confirmButtonText: 'OK',
        confirmButtonColor: '#ed1c24',
    });
}

function showConfirm(
    message,
    title = 'Are you want to?',
    confirmButtonText = 'Yes',
    cancelButtonText = 'No'
) {
    return Swal.fire({
        title: title,
        html: message,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: confirmButtonText,
        cancelButtonText: cancelButtonText,
        confirmButtonColor: '#ed1c24',
        cancelButtonColor: '#ed1c24'
    });
}
