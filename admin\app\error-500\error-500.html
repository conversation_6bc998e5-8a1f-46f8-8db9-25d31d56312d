<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />

	<title>{{appName}} - ERROR-500</title>
	
	<!-- bootstrap -->
	<link rel="stylesheet" type="text/css" href="../../framework/node_modules/bootstrap/dist/css/bootstrap.min.css" />
	
	<!-- RTL support - for demo only -->
	<script src="app/cube/demo-rtl.js"></script>
	<!-- 
	If you need RTL support just include here RTL CSS file <link rel="stylesheet" type="text/css" href="../../framework/admin/css/libs/bootstrap-rtl.min.css" />
	And add "rtl" class to <body> element - e.g. <body class="rtl"> 
	-->
	
	<!-- libraries -->
	<link rel="stylesheet" type="text/css" href="../../framework/node_modules/font-awesome/css/font-awesome.min.css" />

	<!-- global styles -->
	<link rel="stylesheet" type="text/css" href="../../framework/node_modules/zakumi-admin/css/compiled/theme_styles.css" />

	<!-- this page specific styles -->

	<!-- google font libraries -->
	<link href='http://fonts.googleapis.com/css?family=Open+Sans:400,600,700,300|Titillium+Web:200,300,400' rel='stylesheet' type='text/css'>

	<!--[if lt IE 9]>
		<script src="../js/html5shiv.js"></script>
		<script src="../js/respond.min.js"></script>
	<![endif]-->
</head>
<body id="error-page">
	<div class="container">
		<div class="row">
			<div class="col-xs-12">
				<div id="error-box">
					<div class="row">
						<div class="col-xs-12">
							<div id="error-box-inner">
								<img src="images/error-500-v1.png" alt="Error 500"/>
							</div>
							<h1>ERROR 500</h1>
							<p>
								Something went very wrong. We are sorry for that.
							</p>
							<p>
								Go back to <a href="/">homepage</a>.
							</p>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	
	<!-- global scripts -->
	<script src="../../framework/node_modules/jquery/dist/jquery.min.js"></script>
	<script src="../../framework/node_modules/bootstrap/dist/js/bootstrap.min.js"></script>
	
	<!-- this page specific scripts -->

	
	<!-- theme scripts -->
	
	
	<!-- this page specific inline scripts -->
	
</body>
</html>