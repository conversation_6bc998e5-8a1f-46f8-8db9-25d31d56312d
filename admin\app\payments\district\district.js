app.controller(
    'paymentsDistrictCtrl',
    function ($scope, $rootScope, $routeParams, $http) {
        var event_id = $routeParams.id;
        $scope.event_id = event_id;

        jQuery.ajax({
            type: 'POST',
            url: SERVER_PATH + 'event/getEventInfo',
            async: false,
            headers: {	
                'x-user-id': $rootScope.user_id,
                'x-user-email': $rootScope.user_name
            },
            data: {
                event_id: event_id,
            },
            dataType: 'json',
            complete: function (response) {
                var jsonData = JSON.parse(response.responseText);
                var event = jsonData.info;
                $scope.event_name = event.name;
                $scope.event_type = event.type;
                $scope.normalizedType = normalizeEventType(event.type);
            },
        });

        if ($.fn.daterangepicker) {
            $('input[name="dateFilter"]').daterangepicker('destroy');
        }

        $('input[name="dateFilter"]').daterangepicker({
            startDate: moment().startOf('month'),
            endDate: moment().endOf('month'),
            locale: {
                format: 'DD/MM/YYYY',
            },
            maxSpan: {
                days: 90,
            },
            ranges: {
                Yesterday: [
                    moment().subtract(1, 'days'),
                    moment().subtract(1, 'days'),
                ],
                Today: [moment(), moment()],
                'Last week': [
                    moment().subtract(1, 'week').startOf('week').add(1, 'days'),
                    moment().subtract(1, 'week').endOf('week').add(1, 'days'),
                ],
                'This week': [
                    // start week on monday
                    moment().startOf('week').add(1, 'days'),
                    moment().endOf('week').add(1, 'days'),
                ],
                'Last Month': [
                    moment().subtract(1, 'month').startOf('month'),
                    moment().subtract(1, 'month').endOf('month'),
                ],
                'This Month': [
                    moment().startOf('month'),
                    moment().endOf('month'),
                ],
            }
        });

        $('input[name="dateFilter"]').on(
            'apply.daterangepicker',
            function (ev, picker) {
                console.log('date change');
                $scope.start_date = picker.startDate.format('YYYY-MM-DD');
                $scope.end_date = picker.endDate.format('YYYY-MM-DD');

                initPaymentTable();
            }
        );

        // Date range picker
        $scope.start_date = $('input[name="dateFilter"]')
            .data('daterangepicker')
            .startDate.format('YYYY-MM-DD');
        $scope.end_date = $('input[name="dateFilter"]')
            .data('daterangepicker')
            .endDate.format('YYYY-MM-DD');

        setTimeout(() => {
            initPaymentTable();
        }, 400);

        function normalizeEventType(eventType) {
            switch (eventType) {
                case 'Summer Scheme':
                    return 'summer-scheme';
                case 'Regional':
                    return 'regional';
                case 'PL Junior':
                    return 'pl-junior';
                case 'Golden Age':
                    return 'golden-age';
                case 'Beginner':
                    return 'beginner';
                case 'District':
                    return 'district';
                default:
                    return '';
            }
        }

        initPaymentTable = function () {
            $('#paymentTable_' + event_id)
                .DataTable()
                .destroy();

            var table = $('#paymentTable_' + event_id).DataTable({
                dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
                stateSave: true,
                deferRender: true,
                ajax: {
                    url: SERVER_PATH + 'payment/getDistrictPayments',
                    type: 'POST',
                    headers: {	
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name
                    },
                    data: {
                        event_id: event_id,
                        start_date: $scope.start_date,
                        end_date: $scope.end_date,
                    },
                    dataType: 'json',
                    complete: function (response) {},
                    error: function (xhr, status, error) {},
                },
                language: {
                    info: 'Showing _START_ to _END_ of _TOTAL_ payment',
                    infoEmpty: 'Showing 0 to 0 of 0 payments',
                    lengthMenu: 'Show _MENU_ payments',
                    select: {
                        rows: {
                            _: 'You have selected %d payments',
                            0: 'Click a payment to select',
                            1: '1 payment selected',
                        },
                    },
                    paginate: {
                        previous: '<i class="fa fa-chevron-left"></i>',
                        next: '<i class="fa fa-chevron-right"></i>',
                    },
                },
                columns: [
                    {
                        data: null,
                        render: function (data, type, row) {
                            return (
                                data.players.surname +
                                ' ' +
                                data.players.other_name
                            );
                        },
                    },
                    {
                        data: 'players.chinese_name',
                    },
                    {
                        data: 'players.dob',
                    },
                    {
                        data: 'players.group',
                    },
                    {
                        data: 'players.gender',
                    },
                    {
                        data: 'parens.phone',
                    },
                    {
                        data: 'parens.email',
                    },
                    {
                        data: 'districts.name',
                    },
                    {
                        data: 'invoices.invoice_number',
                        visible: false,
                    },
                    {
                        data: 'invoices.invoice_identification',
                    },
                    {
                        data: 'invoices.invoice_date',
                    },
                    {
                        data: 'invoices.amount',
                    },
                    {
                        data: 'invoices.status',
                        className: 'center',
                        render: function (data, type, full, meta) {
                            switch (data) {
                                case STATUS_SUCCEEDED:
                                    return (
                                        '<span class="label label-success">' +
                                        // get element
                                        DISTRICT_PAYMENT_STATUS[data] +
                                        '</span>'
                                    );
                                case STATUS_WAITING_PAYMENT:
                                    return (
                                        '<span class="label label-danger">' +
                                        DISTRICT_PAYMENT_STATUS[data] +
                                        '</span>'
                                    );
                                case STATUS_OFFLINE_PAYMENT:
                                    return (
                                        '<span class="label label-info" data-toggle="tooltip" data-placement="top" title="' +
                                        full.invoices.accepted_detail +
                                        '">' +
                                        DISTRICT_PAYMENT_STATUS[data] +
                                        '</span>'
                                    );
                                case STATUS_REQUEST_REFUND:
                                    return (
                                        '<span class="label label-info" data-toggle="tooltip" data-placement="top" title="' +
                                        full.invoices.accepted_detail +
                                        '">' +
                                        DISTRICT_PAYMENT_STATUS[data] +
                                        '</span>'
                                    );
                                case STATUS_REFUND:
                                    return (
                                        '<span class="label label-info" data-toggle="tooltip" data-placement="top" title="' +
                                        full.invoices.accepted_detail +
                                        '">' +
                                        DISTRICT_PAYMENT_STATUS[data] +
                                        '</span>'
                                    );
                                default:
                                    return (
                                        '<span class="label label-default">' +
                                        data +
                                        '</span>'
                                    );
                            }
                        },
                    },
                    {
                        data: 'invoices.payment_method',
                    },
                    {
                        data: null,
                        render: function (data, type, row) {
                            if (data.invoices.accepted_user)
                                return data.invoices.accepted_user;
                            else return '';
                        },
                    },
                    {
                        data: 'invoices.note',
                    },
                ],
                initComplete: function () {
                    var payment_status_column = {
                        orderColumn: 12,
                        elementId: 'payment_status-content',
                        selectId: 'selType',
                    };

                    var payment_method_column = {
                        orderColumn: 13,
                        elementId: 'payment_method-content',
                        selectId: 'selType',
                    };

                    filterColumns = [
                        payment_status_column,
                        payment_method_column,
                    ];

                    filterColumns.forEach((item, index) => {
                        this.api()
                            .columns(item.orderColumn)
                            .every(function () {
                                var column = this;
                                column.search('', true, false, false);

                                $(`#${item.elementId}`)
                                    .children()
                                    .not(':first')
                                    .remove();

                                var select = $(
                                    `<select id="${item.orderColumn}" class="form-control"><option value="">All</option></select>`
                                )
                                    .appendTo($(`#${item.elementId}`))
                                    .on('change', function () {
                                        var val =
                                            $.fn.dataTable.util.escapeRegex(
                                                // change '_' to ' '
                                                $(this).val()
                                            );

                                        column
                                            .search(
                                                val ? '^' + val + '$' : '',
                                                true,
                                                false,
                                                false
                                            )
                                            .draw(false);
                                    })
                                    .select2();
                                var column_data = column.data();
                                var select_data = [];
                                column_data.map((item) => {
                                    if (item != null) {
                                        item.indexOf(', ') > 0
                                            ? (item = item.split(', '))
                                            : (item = [item]);
                                        item.forEach((item) => {
                                            select_data.push(
                                                item.replace('_', ' ')
                                            );
                                        });
                                    }
                                });
                                select_data
                                    .filter(onlyUnique)
                                    .sort()
                                    .map(function (d, j) {
                                        if (index == 0) {
                                            // check if value is in DISTRICT_PAYMENT_STATUS
                                            if (
                                                DISTRICT_PAYMENT_STATUS[d] !=
                                                undefined
                                            ) {
                                                select.append(
                                                    `<option value="${DISTRICT_PAYMENT_STATUS[d]}">${DISTRICT_PAYMENT_STATUS[d]}</option>`
                                                );
                                            } else {
                                                select.append(
                                                    `<option value="${d}">${d}</option>`
                                                );
                                            }
                                        } else {
                                            select.append(
                                                // lower case and upper case first character
                                                `<option value="${d}">${d}</option>`
                                            );
                                        }
                                    });
                            });
                    });
                },
                select: {
                    style: SELECT_MODE,
                },
                order: [[12, 'desc']],
                lengthMenu: [
                    [10, 25, 50, 100, -1],
                    [10, 25, 50, 100, 'All'],
                ],
                buttons: [
                    {
                        extend: 'collection',
                        text: 'Actions <i class="fa fa-angle-down"></i>',
                        className: 'btn btn-primary',
                        autoClose: true,
                        buttons: [
                            {
                                extend: 'selectedSingle',
                                text: '<i class="fa fa-undo"></i> Refund Payment',
                                attr: {
                                    id: 'btnRefundPayment',
                                },
                                action: function () {
                                    console.log('refund payment');
                                    var rows_selected = table
                                        .rows({ selected: true })
                                        .data()[0];
                                    var payRef =
                                        rows_selected.invoices
                                            .invoice_identification;
                                    var amt = rows_selected.invoices.amount;
                                    var status = rows_selected.invoices.status;
                                    var invoice_id = rows_selected.invoices.id ;
                                    console.log('payRef: ' + payRef);
                                    console.log('amount: ' + amt);
                                    console.log('invoice_id: ' + invoice_id);

                                    // show confirm dialog before refund
                                    Swal.fire({
                                        title: 'Are you sure?',
                                        text: 'You want to refund this payment?',
                                        type: 'warning',
                                        icon: 'warning',
                                        showCancelButton: true,
                                        confirmButtonClass: 'btn btn-primary',
                                        confirmButtonText: 'Yes, refund it!',
                                        closeOnConfirm: false,
                                    }).then((result) => {
                                        if (result.value) {
                                            //call ajax to update invoice status
                                            jQuery.ajax({
                                                type: 'POST',
                                                url: `${SERVER_PATH}payment/refundDistrictPayment`,
                                                async: false,
                                                data: {
                                                    user_id: user_id,
                                                    invoice_id: invoice_id,
                                                },
                                                dataType: 'json',
                                                complete: function (response) {
                                                    var jsonData = JSON.parse(
                                                        response.responseText
                                                    );
                                                    if (jsonData.status == 'OK') {
                                                        Swal.fire({
                                                            title: 'Success',
                                                            text: 'Payment approved successfully',
                                                            type: 'success',
                                                            icon: 'success',
                                                            showCancelButton: false,
                                                            confirmButtonClass:
                                                                'btn-success',
                                                            confirmButtonText: 'OK',
                                                            closeOnConfirm: false,
                                                        });
                                                        table.ajax.reload();
                                                    } else {
                                                        Swal.fire({
                                                            title: 'Error',
                                                            text: jsonData.message,
                                                            icon: 'error',
                                                            type: 'error',
                                                            showCancelButton: false,
                                                            confirmButtonClass:
                                                                'btn-danger',
                                                            confirmButtonText: 'OK',
                                                            closeOnConfirm: false,
                                                        });
                                                    }
                                                },
                                            });
                                        }
                                    });
                                },
                            }
                        ]
                    },
                    {
                        extend: 'excel',
                        name: 'excel',
                        text: '<i class="fa fa-file-excel-o"></i>&emsp;Export to Excel',
                        titleAttr: 'Export data to an Excel file',
                        filename: `HKFA Grassroots - ${$scope.event_type} ${$scope.event_name} - Payments`,
                        exportOptions: {
                            columns: ':visible',
                            modifier: {
                                autoFilter: true,
                                // selected: true
                            },
                        },
                    },
                    {
                        extend: 'selected',
                        text: 'Approve Payment',
                        attr: {
                            id: 'btnApprovePayment',
                        },
                        action: function () {
                            // get selected row data
                            var data = table
                                .rows({
                                    selected: true,
                                })
                                .data();

                            array_data_id = [];

                            for (var i = 0; i < data.length; i++) {
                                array_data_id.push(data[i].invoices.id);
                            }

                            // get selected row id
                            var invoice_ids = array_data_id.join(',');

                            //show editor for enter note
                            Swal.fire({
                                title: 'Enter note',
                                input: 'textarea',
                                inputPlaceholder: 'Enter note',
                                showCancelButton: true,
                                inputValidator: (value) => {
                                    if (!value) {
                                        return 'You need to write something!';
                                    }
                                },
                            }).then((result) => {
                                if (result.value) {
                                    //call ajax to update invoice status
                                    jQuery.ajax({
                                        type: 'POST',
                                        url:
                                            SERVER_PATH +
                                            'payment/approveOfflinePayment',
                                        async: false,
                                        headers: {	
                                            'x-user-id': $rootScope.user_id,
                                            'x-user-email': $rootScope.user_name
                                        },
                                        data: {
                                            user_id: user_id,
                                            invoice_ids: invoice_ids,
                                            note: result.value,
                                        },
                                        dataType: 'json',
                                        complete: function (response) {
                                            var jsonData = JSON.parse(
                                                response.responseText
                                            );
                                            if (jsonData.status == 'OK') {
                                                Swal.fire({
                                                    title: 'Success',
                                                    text: 'Payment approved successfully',
                                                    type: 'success',
                                                    icon: 'success',
                                                    showCancelButton: false,
                                                    confirmButtonClass:
                                                        'btn-success',
                                                    confirmButtonText: 'OK',
                                                    closeOnConfirm: false,
                                                });
                                                table.ajax.reload();
                                            } else {
                                                Swal.fire({
                                                    title: 'Error',
                                                    text: jsonData.message,
                                                    icon: 'error',
                                                    type: 'error',
                                                    showCancelButton: false,
                                                    confirmButtonClass:
                                                        'btn-danger',
                                                    confirmButtonText: 'OK',
                                                    closeOnConfirm: false,
                                                });
                                            }
                                        },
                                    });
                                }
                            });
                        },
                    },
                    {
                        extend: 'selected',
                        extend: 'selectedSingle',
                        text: 'Update Payment Method',
                        attr: {
                            id: 'btnUpdatePaymentMethod',
                        },
                        action: function () {
                            // get selected row data
                            var data = table
                                .rows({
                                    selected: true,
                                })
                                .data()[0];

                            if (
                                data['invoices']['status'] ==
                                    STATUS_WAITING_PAYMENT ||
                                data['invoices']['status'] ==
                                    STATUS_OFFLINE_REJECTED
                            ) {
                                var editor = new $.fn.dataTable.Editor({
                                    ajax: {
                                        type: 'POST',
                                        url:
                                            SERVER_PATH +
                                            'payment/updatePaymentMethod',
                                        dataType: 'json',
                                        headers: {	
                                            'x-user-id': $rootScope.user_id,
                                            'x-user-email': $rootScope.user_name
                                        },
                                        data: {
                                            invoice_id: data['invoices']['id'],
                                        },
                                        beforeSend: function () {},
                                        complete: function (response) {
                                            if (
                                                response.responseJSON.status ==
                                                'OK'
                                            ) {
                                                Swal.fire({
                                                    title: 'SUCCESS!',
                                                    text: response.responseJSON
                                                        .message,
                                                    icon: 'success',
                                                    type: 'success',
                                                });
                                            } else {
                                                Swal.fire({
                                                    title: 'ERROR!',
                                                    text: response.responseJSON
                                                        .message,
                                                    icon: 'error',
                                                    type: 'error',
                                                });
                                            }

                                            table.ajax.reload();
                                        },
                                        error: function (xhr, status, error) {},
                                    },
                                    formOptions: {
                                        main: {
                                            onBlur: 'none',
                                        },
                                    },
                                    fields: [
                                        {
                                            label: 'Payment Method:',
                                            type: 'select2',
                                            name: 'payment_method',
                                            options: [
                                                {
                                                    label: 'AsiaPay',
                                                    value: 'AsiaPay',
                                                },
                                                {
                                                    label: 'Offline Payment',
                                                    value: 'Offline Payment',
                                                },
                                            ],
                                            default:
                                                data['invoices'][
                                                    'payment_method'
                                                ],
                                        },
                                    ],
                                });

                                editor
                                    .title('Update Payment Method')
                                    .buttons({
                                        label: 'Submit',
                                        fn: function () {
                                            this.submit();
                                        },
                                    })
                                    .edit()
                                    .open();
                            } else {
                                Swal.fire({
                                    title: 'Error',
                                    text: 'Only Outstanding payment can be updated',
                                    icon: 'error',
                                    type: 'error',
                                    showCancelButton: false,
                                    confirmButtonClass: 'btn-danger',
                                    confirmButtonText: 'OK',
                                    closeOnConfirm: false,
                                });
                            }
                        },
                    },
                    {
                        extend: 'colvis',
                        text: 'Columns',
                    },
                ],
            });
        };

        function onlyUnique(value, index, self) {
            return self.indexOf(value) === index;
        }
    }
);
