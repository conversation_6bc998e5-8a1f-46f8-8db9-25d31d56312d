app.controller(
    'selfPickUpCtrl',
    function ($scope, $rootScope, $routeParams, $http) {
        var event_id = $routeParams.id;
        $scope.event_id = event_id;
        var locker_list = [];
        $scope.product_list = [];
        var table = $('#shippingTable_' + event_id).DataTable();
        $scope.data_shipping = [];

        jQuery.ajax({
            type: 'POST',
            url: SERVER_PATH + 'event/getEventInfo',
            async: false,
            headers: {	
                'x-user-id': $rootScope.user_id,
                'x-user-email': $rootScope.user_name
            },
            data: {
                event_id: event_id,
            },
            dataType: 'json',
            complete: function (response) {
                var jsonData = JSON.parse(response.responseText);
                var event = jsonData.info;
                $scope.event_name = event.name;
                $scope.event_type = event.type;
            },
        });

        setTimeout(() => {
            table = $('#shippingTable_' + event_id).DataTable({
                dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
                stateSave: true,
                destroy: true,
                deferRender: true,
                ajax: {
                    url: SERVER_PATH + 'shipping/getSelfPickUpShipping',
                    type: 'POST',
                    headers: {	
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name
                    },
                    data: {
                        event_id: event_id,
                    },
                    dataType: 'json',
                    complete: function (response) {},
                    error: function (xhr, status, error) {},
                },
                language: {
                    info: 'Showing _START_ to _END_ of _TOTAL_ shipping',
                    infoEmpty: 'Showing 0 to 0 of 0 shippings',
                    lengthMenu: 'Show _MENU_ shippings',
                    select: {
                        rows: {
                            _: 'You have selected %d shippings',
                            0: 'Click a shipping to select',
                            1: '1 shipping selected',
                        },
                    },
                    paginate: {
                        previous: '<i class="fa fa-chevron-left"></i>',
                        next: '<i class="fa fa-chevron-right"></i>',
                    },
                },
                columns: [
                    {
                        data: 'shipping.id',
                        render: function (data, type, row) {
                            return `#${data}`;
                        },
                    },
                    {
                        data: 'shipping.shipping_type',
                        visible: false,
                    },
                    {
                        data: null,
                        render: function (data, type, row) {
                            return row.parens.surname + ' ' + row.parens.other_name;
                        }
                    },
                    {
                        data: 'parens.email',
                    },
                    {
                        data: 'parens.phone',
                    },
                    {
                        data: 'none.invoice_numbers',
                    },
                    {
                        data: 'shipping.date_pick_up',
                        className: 'text-center',
                        //    render checkbox button
                        render: function (data, type, row) {
                            if (
                                row.shipping.shipping_type == 'home' ||
                                row.shipping.shipping_type == 'locker'
                            ) {
                                return '';
                            } else {
                                let checked = (data != null) ? 'checked' : '';
                                return (
                                    '<input type="checkbox" name="' +
                                    row.shipping.shipping_type +
                                    '" id="checkbox_pickup_datetime_' +
                                    row.shipping.id +
                                    '" class="shipping_chkbox" value="' +
                                    row.shipping.id +
                                    '" ' +
                                    checked +
                                    ' />'
                                );
                            }
                        },
                    },
                    {
                        data: 'shipping.date_pick_up',
                    },
                ],
                responsive: true,
                columnDefs: [
                    { responsivePriority: 1, targets: 0 },
                    { responsivePriority: 2, targets: 4 },
                    { responsivePriority: 3, targets: -2 },
                    { responsivePriority: 4, targets: 2 },
                ],
                select: {
                    style: 'single',
                    // selector: 'td:first-child',
                },
                buttons: [
                    {
                        extend: 'collection',
                        text: 'Actions <i class="fa fa-caret-down"></i>',
                        className: 'btn btn-primary',
                        autoClose: true,
                        buttons: [
                            {
                                text: '<i class="fa fa-arrow-right"></i> Shipping detail',
                                extend: 'selectedSingle',
                                action: function () {
                                    var row_reg = table
                                        .rows({ selected: true })
                                        .data()[0];

                                    var shipping_id = row_reg.shipping.id;

                                    let products = [];

                                    jQuery.ajax({
                                        type: 'POST',
                                        url:
                                            SERVER_PATH +
                                            'product/getProductsInShipping',
                                        async: false,
                                        headers: {	
                                            'x-user-id': $rootScope.user_id,
                                            'x-user-email': $rootScope.user_name
                                        },
                                        data: {
                                            shipping_id: shipping_id,
                                        },
                                        dataType: 'json',
                                        complete: function (response) {
                                            var jsonData = JSON.parse(
                                                response.responseText
                                            );
                                            products = jsonData.info;
                                        },
                                    });

                                    let table_products_html =
                                        '<h4 style="text-align: center;">Product List</h4>' +
                                        '<table id="table_products" class="table table-striped table-bordered table-hover" cellspacing="0" width="100%">' +
                                        '<thead>' +
                                        '<tr>' +
                                        '<th>#</th>' +
                                        '<th>Type</th>' +
                                        '<th>Name</th>' +
                                        '<th>Quantity</th>' +
                                        '</tr>' +
                                        '</thead>' +
                                        '<tbody>';

                                    products.forEach(function (product, index) {
                                        console.log(product);
                                        table_products_html +=
                                            '<tr>' +
                                            '<td>' +
                                            (index + 1) +
                                            '</td>' +
                                            '<td>' +
                                            product.product_type +
                                            '</td>' +
                                            '<td>' +
                                            product.product_name +
                                            '</td>' +
                                            '<td>' +
                                            product.quantity +
                                            '</td>' +
                                            '</tr>';
                                    });

                                    table_products_html += '</tbody>' + '</table>';

                                    BootstrapDialog.show({
                                        title: 'Shipping detail',
                                        message: table_products_html,
                                        onshown: function (dialogRef) {},
                                    });
                                },
                            },
                        ],
                    },
                    { 
                        extend:'colvis',
                        text: 'Columns <i class="fa fa-eye"></i>',
                        className: 'btn btn-secondary',
                    },
                ],
            });
            table.on(
                'click',
                'input[type="checkbox"]',
                function (e, dt, type, indexes) {
                    console.log('click');
                    // table.ajax.reload();
                    // get current input value
                    var input = $(this);
                    var name = input.attr('name');
                    console.log(name);
                    if (name == 'home') {
                        this.checked = false;
                        alert('Only Type self pick up can be checked');
                        BootstrapDialog.closeAll();
                        return;
                    }
                    console.log(input.val());
                    var shipping_id = input.val();
                    // checkbox is checked
                    var check_status = this.checked;
                    if (check_status) {
                        console.log('checked');

                        showInputDatetimeSet(check_status, shipping_id);
                    } else {
                        console.log('unchecked');
                        showInputDatetimeSet(check_status, shipping_id);
                    }
                }
            );
        }, 500);

        $scope.goBack = function () {
            window.history.back();
        };

        $scope.initRegistration = function (shipping_id) {
            //tableListRegistration_
            var tableListRegistration = $(
                '#tableListRegistration_' + shipping_id
            ).DataTable({
                dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
                stateSave: true,
                deferRender: true,
                ajax: {
                    url: SERVER_PATH + 'shipping/getRegistrationByShipping',
                    type: 'POST',
                    headers: {	
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name
                    },
                    data: {
                        shipping_id: shipping_id,
                        event_id: event_id,
                    },
                    dataType: 'json',
                    complete: function (response) {
                        // response = JSON.parse(response.responseText);
                    },
                    error: function (xhr, status, error) {},
                },
                language: {
                    paginate: {
                        previous: '<i class="fa fa-chevron-left"></i>',
                        next: '<i class="fa fa-chevron-right"></i>',
                    },
                },
                columns: [
                    {
                        data: 'players.player_photo',
                        className: 'avatar',
                        orderable: false,
                        render: function (data) {
                            if (data !== null && data !== '') {
                                return (
                                    '<img src="' +
                                    PRODUCT_IMAGE_PATH +
                                    data +
                                    '">'
                                );
                            } else {
                                return (
                                    '<img src="' +
                                    SYSTEM_IMAGE_PATH +
                                    'favicon.png">'
                                );
                            }
                        },
                    },
                    {
                        data: null,
                        render: function (data, type, row, meta) {
                            return (
                                row.players.surname +
                                ' ' +
                                row.players.other_name
                            );
                        },
                    },
                    {
                        data: 'players.chinese_name',
                    },
                    {
                        data: 'players.dob',
                    },
                    {
                        data: 'players.gender',
                    },
                    {
                        data: 'groups.name',
                    },
                    {
                        data: 'courses.class_code',
                    },
                ],
                select: {
                    style: 'single',
                    // selector: 'td:first-child',
                },
                order: [[1, 'asc']],
                lengthMenu: [
                    [10, 25, 50, 100, -1],
                    [10, 25, 50, 100, 'All'],
                ],
                buttons: [
                    {
                        extend: 'selectedSingle',
                        text: 'Change sent item',
                        action: function (e, dt, node, config) {
                            var row_reg = tableListRegistration
                                .rows({ selected: true })
                                .data()[0];

                            var registration_id = row_reg.registrations.id;

                            var player_shirt_size = 0;

                            var parent_shirt_size = 0;

                            var have_parent = false;



                            jQuery.ajax({
                                type: 'POST',
                                url:
                                    SERVER_PATH +
                                    'shipping/getProductForEditing',
                                async: false,
                                headers: {	
                                    'x-user-id': $rootScope.user_id,
                                    'x-user-email': $rootScope.user_name
                                },
                                data: {
                                    // "registration_id": registration_id,
                                    registration_id: registration_id,
                                },
                                dataType: 'json',
                                complete: function (response) {
                                    var jsonData = JSON.parse(
                                        response.responseText
                                    );
                                    if (jsonData.status == 'OK') {
                                        have_parent = jsonData.info.have_parent;
                                        player_shirt_size =
                                            jsonData.info.player_shirt_size;

                                        if (have_parent) {
                                            parent_shirt_size =
                                                jsonData.info
                                                    .parent_tshirt_size;
                                        }
                                    } else {
                                        BootstrapDialog.show({
                                            title: 'Error',
                                            type: BootstrapDialog.TYPE_DANGER,
                                            message: jsonData.message,
                                        });
                                    }
                                },
                            });

                            let editor = new $.fn.dataTable.Editor({
                                ajax: {
                                    type: 'POST',
                                    url:
                                        SERVER_PATH +
                                        'shipping/changeItemShipping',
                                    headers: {	
                                        'x-user-id': $rootScope.user_id,
                                        'x-user-email': $rootScope.user_name
                                    },
                                    data: {
                                        // "registration_id": registration_id,
                                        registration_id: registration_id,
                                        event_id: event_id,
                                    },
                                    dataType: 'json',
                                    complete: function (response) {
                                        var jsonData = JSON.parse(
                                            response.responseText
                                        );
                                        if (
                                            typeof jsonData.fieldErrors !=
                                            'undefined'
                                        ) {
                                            return;
                                        }
                                        if (jsonData.status == 'OK') {
                                            Swal.fire({
                                                title: 'SUCCESS',
                                                type: 'success',
                                                icon: 'success',
                                                text: jsonData.message,
                                                showCancelButton: false,
                                                confirmButtonText:'OK'
                                            });
                                            table.ajax.reload();
                                        } else {
                                            Swal.fire({
                                                title: 'ERROR',
                                                type: 'error',
                                                icon: 'error',
                                                text: jsonData.message,
                                                showConfirmButton: false,
                                            });
                                        }
                                    },
                                    error: function (xhr, status, error) {},
                                },
                                table: '#shippingTable_' + event_id,
                                formOptions: {
                                    main: {
                                        onBlur: 'none',
                                    },
                                },
                                i18n: {
                                    create: {
                                        button: 'Change',
                                        title: 'Change shipping item',
                                        submit: 'Save',
                                    },
                                },
                                fields: [
                                    {
                                        label: 'Player shirt size:',
                                        name: 'player_shirt_size',
                                        type: 'select2',
                                        options: $scope.product_list,
                                        def: player_shirt_size,
                                    },
                                    {
                                        label: 'Parent shirt size:',
                                        name: 'parent_shirt_size',
                                        type: have_parent
                                            ? 'select2'
                                            : 'hidden',
                                        options: $scope.product_list,
                                        def: parent_shirt_size,
                                    },
                                ],
                            });

                            editor
                                .title('Change shipping item')
                                .buttons({
                                    label: 'Save',
                                    fn: function () {
                                        this.submit();
                                    },
                                })
                                .create()
                                .open();
                        },
                    },
                ],
            });
        };

        function showInputDatetimeSet(check_status, shipping_id) {
            if (check_status) {
                return BootstrapDialog.show({
                    title: 'Set pickup date time',
                    type: BootstrapDialog.TYPE_DANGER,
                    size: BootstrapDialog.SIZE_WIDE,
                    message: function (dialog) {
                        var $message = $('<div></div>');
                        var $validator = $(
                            '<div class="validator-errors"></div>'
                        );
                        var $form = $(
                            '<form id="form-set-pickup-datetime"></form>'
                        );
                        var currently_selected_datetime =
                            new Date().toISOString();
                        // convert new Date() to timestamp
                        var timestamp = Date.parse(currently_selected_datetime);
                        // get current GMT
                        var gmt = new Date().getTimezoneOffset() / 60;
                        // convert timestamp to ISOString format
                        currently_selected_datetime = new Date(
                            timestamp + -gmt * 60 * 60 * 1000
                        ).toISOString();
                        currently_selected_datetime =
                            currently_selected_datetime.substring(
                                0,
                                currently_selected_datetime.indexOf('T') + 6
                            );

                        var $datetime = $(
                            '<div class="row"><label>Pickup date time:</label><input  type="datetime-local" min="2010-06-01T08:30" max="2099-06-30T16:30" value=' +
                                currently_selected_datetime +
                                '  class="form-control input-normal" id="pickup_datetime" name="pickup_datetime" placeholder="Pickup date time" style="line-height: initial;"/></div><br>'
                        );
                        var $submit = $(
                            '<div class="row"><button type="submit" class="btn btn-primary pull-right">Submit</button> </div>'
                        );

                        $form.append($datetime);
                        $form.append($submit);
                        $message.append($form);

                        let products = [];

                        jQuery.ajax({
                            type: 'POST',
                            url: SERVER_PATH + 'product/getProductsInShipping',
                            async: false,
                            headers: {	
                                'x-user-id': $rootScope.user_id,
                                'x-user-email': $rootScope.user_name
                            },
                            data: {
                                shipping_id: shipping_id,
                            },
                            dataType: 'json',
                            complete: function (response) {
                                var jsonData = JSON.parse(
                                    response.responseText
                                );
                                products = jsonData.info;
                                console.warn(products);
                            },
                        });

                        let table_products_html =
                            '<h4 style="text-align: center; text-transform: uppercase;">Product List</h4>' +
                            '<table id="table_products" class="table table-striped table-bordered table-hover" cellspacing="0" width="100%">' +
                            '<thead>' +
                            '<tr>' +
                            '<th>#</th>' +
                            '<th>Type</th>' +
                            '<th>Name</th>' +
                            '<th>Quantity</th>' +
                            '<th>Picked up</th>' +
                            '</tr>' +
                            '</thead>' +
                            '<tbody>';

                        products.forEach(function (product, index) {
                            table_products_html +=
                                '<tr>' +
                                '<td>' +
                                (index + 1) +
                                '</td>' +
                                '<td>' +
                                product.product_type +
                                '</td>' +
                                '<td>' +
                                product.product_name +
                                '</td>' +
                                '<td>' +
                                product.quantity +
                                '</td>' +
                                '<td>' +
                                (product.Picked_up == '1' ? 'Yes' : 'No') +
                                '</td>' +
                                '</tr>';
                        });

                        table_products_html += '</tbody>' + '</table>';

                        console.log(table_products_html);

                        $message.append(table_products_html);

                        // prevent default action
                        $form.on('submit', function (e) {
                            // get input value
                            var pickup_datetime = $('#pickup_datetime').val();

                            console.log(pickup_datetime);
                            if (pickup_datetime == '') {
                                console.log('empty');
                                alert('Please input pickup date time');
                                table.ajax.reload();

                                return false;
                            }
                            // convert to timestamp
                            var pickup_datetime_timestamp =
                                new Date(pickup_datetime).getTime() / 1000;
                            console.log(pickup_datetime_timestamp);
                            e.preventDefault();

                            // send ajax request to server
                            setShippingDatePickup(
                                check_status,
                                pickup_datetime_timestamp,
                                shipping_id
                            );

                            // close form dialog
                            dialog.close();
                        });
                        return $message;
                    },
                });
            } else {
                Swal.fire({
                    title: 'Are you sure?',
                    text: "You won't be able to revert this!",
                    type: 'warning',
                    showCancelButton: true,
                    customClass: 'swal-wide',
                    confirmButtonColor: '#3085d6',
                    inputAttributes: {
                        id: 'swal_remove_pickup_datetime',
                    },
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'Yes, delete it!',
                    cancelButtonText: 'No, cancel!',
                    allowOutsideClick: false,
                    allowEscapeKey: false,
                }).then((result) => {
                    if (result.value) {
                        Swal.fire(
                            'Deleted!',
                            'Your file has been deleted.',
                            'success'
                        );
                        setShippingDatePickup(check_status, 0, shipping_id);
                    } else {
                        console.log('cancel');
                        // reset checkbox
                        $('#checkbox_pickup_datetime_' + shipping_id).prop(
                            'checked',
                            true
                        );
                    }
                });
            }
        }

        function setShippingDatePickup(
            check_status,
            pickup_datetime,
            shipping_id,
        ) {
            jQuery.ajax({
                type: 'POST',
                url: SERVER_PATH + 'shipping/set-shipping-date-pickup',
                async: false,
                headers: {	
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name
                },
                data: {
                    user_id: $rootScope.user_id,
                    shipping_id: shipping_id,
                    check_status: check_status.toString(),
                    pickup_datetime: pickup_datetime,
                },
                dataType: 'json',
                complete: function (response) {
                    var jsonData = JSON.parse(response.responseText);

                    if (jsonData.status == 'OK') {
                        table.ajax.reload();

                        Swal.fire({
                            title: 'Update successfully',
                            text: 'Data has been updated',
                            icon: 'success',
                            type: 'success',
                            showCancelButton: false, // There won't be any cancel button
                            showConfirmButton: false, // There won't be any confirm button
                            allowOutsideClick: false,
                            allowEscapeKey: false,
                            timer: 2000,
                        });
                    } else {
                        table.ajax.reload();

                        Swal.fire({
                            title: 'ERROR !',
                            text: jsonData.message,
                            icon: 'error',
                            type: 'error',
                        });
                    }
                    setTimeout(() => {
                        BootstrapDialog.closeAll();
                    }, 3000);
                },
            });
        }

        // on click close BootstrapDialog
        $(document).on('click', '.bootstrap-dialog-close-button', function () {
            console.log('close');
            table.ajax.reload();
        });

        // on shipping table change value
        $(document).on('change', '#shipping-table', function () {
            Swal.close();
        });

        function onlyUnique(value, index, self) {
            return self.indexOf(value) === index;
        }
    }
);
