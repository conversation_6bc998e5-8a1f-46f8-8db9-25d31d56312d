app.controller('cgroupsCtrl', function($scope, $rootScope, $routeParams, $http) {
	$('#page-wrapper').removeClass('nav-small');
	
	var CLUBS_ACTION_ASSIGN_PLAYERS_TO_TEAM = 1;
	var TEAMS_ACTION_SUBMMIT_TEAM_SHEET = 2;
	var ASSIGN_PLAYERS_TO_TEAM = 3;

    var event_id = $routeParams.id;
    jQuery.ajax({
    	type: 'POST',
  		url: SERVER_PATH + "event/getEventInfo",
  		async: false,
		headers: {	
			'x-user-id': $rootScope.user_id,
			'x-user-email': $rootScope.user_name
		},
  		data: {
    		"event_id": event_id
    	},
  		dataType: 'json',
  		complete: function (response) {
  			var jsonData = JSON.parse(response.responseText);
  			var event = jsonData.info;
  			event_name = event.name;
  			event_type = event.type;
  		}
    });
	console.log('cgroupsCtrl - event_id, name, type  = ' + event_id + ', ' + event_name + ', ' + event_type);
	$scope.showSelectGroups = event_type == 'Season' ? true : false;
    $scope.event_id = event_id;
	$rootScope.event_name = event_name;
 	
    $.fn.dataTable.moment( 'D-MMM-YYYY HH:mm:ss' );
    $.fn.dataTable.moment( 'D-MMM-YYYY' );

    jQuery.ajax({
    	type: 'POST',
  		url: SERVER_PATH + "group/getCgroupsByUser",
  		async: false,
		headers: {	
			'x-user-id': $rootScope.user_id,
			'x-user-email': $rootScope.user_name
		},
  		data: {
    		"event_id": event_id,
    		"user_id": $rootScope.user_id,
    	},
  		dataType: 'json',
  		complete: function (response) {
  			var jsonData = JSON.parse(response.responseText);
  			if (jsonData.status == "OK") {
				  $scope.groups = jsonData.info;
				  $scope.role =$scope.groups.role;
  				$scope.selectedGroup = $scope.groups[0];
  				// getGroup();
  			}
  		}
    });

    $('button').click( function (e) {
		getGroup();
    });
    
    function getGroup() {
		var role = $scope.role;
		var cgroup_id = $scope.selectedGroup.id;
		var cgroup_name = $scope.selectedGroup.name;
        
        // initialize html
        CgroupPlayersTableHtml = getCgroupPlayersTableHtml(cgroup_id);
		TeamsTableHtml = getTeamsTableHtml(cgroup_id);
		
		var html = '<div class="tabs-wrapper cgroups-tabs">' +
			'<ul class="nav nav-tabs">' +
				'<li class="active"><a data-target="#tab-CgroupPlayers" data-toggle="tab"><i class="fa fa-fw fa-users"></i> Players</a></li>' +
				'<li><a data-target="#tab-Teams" data-toggle="tab"><i class="fa fa-fw fa-list"></i> Teams</a></li>' +
			'</ul>' +
			'<div class="tab-content">' +
				'<div class="tab-pane fade in active" id="tab-CgroupPlayers">' +
					CgroupPlayersTableHtml +
				'</div>' +
				'<div class="tab-pane fade" id="tab-Teams">' +
					TeamsTableHtml +
				'</div>' +
			'</div>' +
		'</div>';

        $('#cgroupsPageContent').html(html);

        // initialize data	
        initCgroupPlayersTable(cgroup_id, cgroup_name);
		initTeamsTable(cgroup_id, cgroup_name);
    }
    
    function getCgroupPlayersTableHtml(cgroup_id) {
		var str = ''+
			'<div class="table-responsive">' +
				'<table id="tblCgroupPlayers_' + cgroup_id + '" class="table table-striped table-bordered table-hover" cellspacing="0" width="100%">' +
					'<thead>' +
						'<tr>' +
							'<th></th>' +
							'<th>Photo</th>' +
							'<th>Name</th>' +
							'<th>Teams</th>' +
							'<th>Year</th>' +
							'<th>Gender</th>' +
							'<th class="text-center">Status</th>' +
							
						'</tr>' +
					'</thead>' +
				'</table>' +
			'</div>';
		return str;
    }

    function getTeamsTableHtml(cgroup_id) {
		var str = ''+
			'<div class="table-responsive">' +
				'<table id="tblTeams_' + cgroup_id + '" class="table table-striped table-bordered table-hover" cellspacing="0" width="100%">' +
					'<thead>' +
						'<tr>' +
							'<th></th>' +
							'<th>Name</th>' +
						'</tr>' +
					'</thead>' +
				'</table>' +
			'</div>';
		return str;
	}

	function getTeamPlayersTableHtml(team_id) {
		var str = ''+
			'<div class="table-responsive">' +
				'<table id="tblTeamPlayers_' + team_id + '" class="table table-striped table-bordered table-hover" cellspacing="0" width="100%">' +
					'<thead>' +
						'<tr>' +
							'<th>Photo</th>' +
							'<th>Name</th>' +
							'<th>Year</th>' +
						'</tr>' +
					'</thead>' +
				'</table>' +
			'</div>';
		return str;
	}

    function initCgroupPlayersTable(cgroup_id, cgroup_name) {

		var editorCgroupPlayers = new $.fn.dataTable.Editor( {
            ajax: {
                type: 'POST',
                url: SERVER_PATH + "group/setCgroupPlayers",
				headers: {	
					'x-user-id': $rootScope.user_id,
					'x-user-email': $rootScope.user_name
				},
                data: {
        			"cgroup_id": cgroup_id,
        			"event_id": event_id
    			},
                dataType: 'json',
                complete: function (response) {
                    tableCgroupPlayers.ajax.reload();
                },
                error: function(xhr, status, error) {
                },
            },
            table: '#tblCgroupPlayers_' + cgroup_id,
            formOptions: {
                main: {
                    onBlur: 'none'
                }
            },
            i18n: {
                remove: {
                    button: "Delete",
                    title:  "Delete player",
                    submit: "Delete"
                },
                error: {
                    system: "System error, please contact administrator."
                },
            },
            fields: [ 
                {
                    label: "Player:",
                    name: "cgroup_players.club_player_id",  
					type:  "select2",
					opts: {
						placeholder: "Select a player"
					}
                }
            ]
        } );

		tableCgroupPlayers = $('#tblCgroupPlayers_' + cgroup_id).DataTable( {
			dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
			stateSave: true,
			deferRender: true,
			ajax: {
				url: SERVER_PATH + "group/getCgroupPlayers",
				type: 'POST',
				headers: {	
					'x-user-id': $rootScope.user_id,
					'x-user-email': $rootScope.user_name
				},
  				data: {
        			"cgroup_id": cgroup_id,
        			"event_id": event_id
    			},
  				dataType: 'json',
  				complete: function (response) {
					// response = JSON.parse(response.responseText);
				},
		  		error: function(xhr, status, error) {
  				},
			},
			language: {
				paginate: {
					previous: '<i class="fa fa-chevron-left"></i>',
					next: '<i class="fa fa-chevron-right"></i>'
				}
			},
  			columns: [
				{
                    data: 'DT_RowId',
                    // defaultContent: '',
                    // className: 'select-checkbox',
                    // orderable: false
                    targets: 0,
                    render: function (data, type, row, meta) {
                        if (type === 'display') {
                            data = '<div class="checkbox"><input type="checkbox" class="dt-checkboxes"><label></label></div>';
                        }
                        return data;
                    },
                    checkboxes: {
                        selectRow: true,
                        selectAllRender: '<div class="checkbox"><input type="checkbox" class="dt-checkboxes"><label></label></div>'
                    }
                },
                { 
                    data: "players.player_photo",
                    className: "avatar",
                    orderable: false,
                    render: function(data) {
                        if(data !== null && data !== ''){
                            return '<img src="' + PRODUCT_IMAGE_PATH + data + '">';
                        }else{
                            return '<img src="' + SYSTEM_IMAGE_PATH + 'favicon.png">';
                        }
                    } 
                },
                {
                    data: null, 
                    render: function ( data, type, row ) {
                        return data.players.surname + ' ' + data.players.other_name;
                    } 
                },
                {
                    data: "teams.name",
                    className: "center"
				},
                {
                    data: "players.dob",
                    className: "center"
				},
				{
                    data: "players.gender",
                    className: "center"
				},
				{
                    data: "players.validate_status",
                    className: "center",
                    render: function (data, type, full, meta) {
                        switch (data) {
                            case VALIDATE_STATUS_Pending:
                                return '<span class="label label-info">' + data + '</span>';
                            case VALIDATE_STATUS_Invalid:
                                return '<span class="label label-danger">' + data + '</span>';
                            case VALIDATE_STATUS_Updated:
                                return '<span class="label label-warning">' + data + '</span>';
                            case VALIDATE_STATUS_Validated:
                                return '<span class="label label-success">' + data + '</span>';
                            default:
                                return '<span class="label label-default">' + data + '</span>';
                        }
                    }
                },
          
			],
			select: {
				style: SELECT_MODE,
				// selector: 'td:first-child'
			},
			buttons: [
				{
					extend: 'collection',
                    text: 'Actions',
                    autoClose: true,
                    buttons: [
						{
							extend: 'excel',
							name: 'excel',
							text: '<i class="fa fa-file-excel-o"></i>&emsp;Export to Excel',
							titleAttr: 'Export data to an Excel file',
							filename: cgroup_name + ' - Players',
							title: cgroup_name,
							exportOptions: {
								columns: ':visible',
								modifier: {
									autoFilter: true,
									// selected: true
								}
							}
						},
						{
                            text: '<i class="fa fa-share-square"></i>&emsp;Assign player to Team...',
                            titleAttr: 'Assign players to team',
                            cgroup_id: cgroup_id,
                            action: function ( e, dt, node, config ) {
                                action = config.text;
                                cgroup_id = config.cgroup_id;
                                checkRequest(cgroup_id, CLUBS_ACTION_ASSIGN_PLAYERS_TO_TEAM, action);
                            }
                        },
					]
				},
				{ extend: 'remove', editor: editorCgroupPlayers, text: 'Delete' },
				{ extend: 'colvis', text: 'Columns' }
			],
			order: [[2, 'asc']],
			"lengthMenu": [[10, 25, 50, 100, -1], [10, 25, 50, 100, "All"]]
		} );
    }
    
    function initTeamsTable(cgroup_id, cgroup_name) {

		tableTeams = $('#tblTeams_' + cgroup_id).DataTable( {
			dom: 'rt',
			stateSave: true,
			deferRender: true,
			ajax: {
				url: SERVER_PATH + "team/getTeams",
				type: 'POST',
				headers: {	
					'x-user-id': $rootScope.user_id,
					'x-user-email': $rootScope.user_name
				},
  				data: {
        			"cgroup_id": cgroup_id,
        			"event_id": event_id
    			},
  				dataType: 'json',
  				complete: function (response) {
					// response = JSON.parse(response.responseText);
				},
		  		error: function(xhr, status, error) {
  				},
			},
			language: {
				paginate: {
					previous: '<i class="fa fa-chevron-left"></i>',
					next: '<i class="fa fa-chevron-right"></i>'
				}
			},
  			columns: [
				{
                    "className": 'details-control',
                    "orderable": false,
                    "data": null,
                    "defaultContent": ''
                },
                {
                    data: "teams.name",
                    className: "center"
				}
			],
			select: {
				style: SELECT_MODE,
				selector: 'td:first-child'
			},
			order: [[1, 'asc']],
			displayLength: -1,
		} );

		// Add event listener for opening and closing details
        $('#tblTeams_' + cgroup_id + ' tbody').on('click', 'td.details-control', function () {
            var tr = $(this).closest('tr');
			var row = $('#tblTeams_' + cgroup_id).DataTable().row( tr );
			var team = row.data().teams;

            if ( row.child.isShown() ) {
                // This row is already open - close it
                row.child.hide();
                tr.removeClass('shown');
            } else {
                // Open this row
                row.child( getTeamPlayersTableHtml(team.id) ).show();
				tr.addClass('shown');
                
                // add class for next tr (child row)
                $(this).closest('tr').next().addClass('child-row-detail');
                
                initTeamPlayersTable(team.id, team.name);
            }
        } );
	}
	
	function initTeamPlayersTable(team_id, team_name) {

		var editorTeamPlayers = new $.fn.dataTable.Editor( {
            ajax: {
                type: 'POST',
                url: SERVER_PATH + "team/setTeamPlayers",
				headers: {	
					'x-user-id': $rootScope.user_id,
					'x-user-email': $rootScope.user_name
				},
                data: {
        			"team_id": team_id
    			},
                dataType: 'json',
                complete: function (response) {
                    tableTeamPlayers.ajax.reload();
                },
                error: function(xhr, status, error) {
                },
            },
            table: '#tblTeamPlayers_' + team_id,
            formOptions: {
                main: {
                    onBlur: 'none'
                }
            },
            i18n: {
                remove: {
                    button: "Delete",
                    title:  "Delete player",
                    submit: "Delete"
                },
                error: {
                    system: "System error, please contact administrator."
                },
            },
            fields: [ 
                {
                    name: "team_players.team_id",  
					type:  "hidden",
					def: team_id
				},
                {
                    label: "Coach:",
                    name: "team_players.cgroup_player_id",  
					type:  "select2",
					opts: {
						placeholder: "Select a player"
					}
                }
            ]
        } );

		tableTeamPlayers = $('#tblTeamPlayers_' + team_id).DataTable( {
			dom: '<"row"<"col-sm-6"B><"col-sm-6"f>>rt<"row"i>',
			stateSave: true,
			deferRender: true,
			ajax: {
				url: SERVER_PATH + "team/getTeamPlayers",
				type: 'POST',
				headers: {	
					'x-user-id': $rootScope.user_id,
					'x-user-email': $rootScope.user_name
				},
  				data: {
        			"team_id": team_id
    			},
  				dataType: 'json',
  				complete: function (response) {
					// response = JSON.parse(response.responseText);
				},
		  		error: function(xhr, status, error) {
  				},
			},
			language: {
				paginate: {
					previous: '<i class="fa fa-chevron-left"></i>',
					next: '<i class="fa fa-chevron-right"></i>'
				}
			},
  			columns: [
				{ 
                    data: "players.player_photo",
                    className: "avatar",
                    orderable: false,
                    render: function(data) {
                        if(data !== null && data !== ''){
                            return '<img src="' + PRODUCT_IMAGE_PATH + data + '">';
                        }else{
                            return '<img src="' + SYSTEM_IMAGE_PATH + 'favicon.png">';
                        }
                    } 
                },
                {
                    data: null, 
                    render: function ( data, type, row ) {
                        return data.players.surname + ' ' + data.players.other_name;
                    } 
                },
				{
                    data: "players.dob",
                    className: "center"
				},
			],
			select: {
				style: SELECT_MODE,
				// selector: 'td:first-child'
			},
			buttons: [
				{
					extend: 'collection',
                    text: 'Actions',
                    autoClose: true,
                    buttons: [
						{
                            text: '<i class="fa fa-share-square"></i>&emsp;Assign',
                            titleAttr: 'Assign players to team',
                            team_id: team_id,
                            action: function ( e, dt, node, config ) {
                                action = config.text;
                                team_id = config.team_id;
                                checkRequestTeam(team_id, ASSIGN_PLAYERS_TO_TEAM, action);
                            }
                        },
						{
							name: 'submit-team-sheet',
							text: '<i class="fa fa-list-alt"></i>&emsp;Submit Team Sheet',
							titleAttr: 'Submit Team Sheet',
							team_name: team_name,
							action: function ( e, dt, node, config ) {
								// var urlStr = SERVER_PATH + "team/submitTeamSheet";
								// var formData = "team_id=" + team_id + '&user_id=' + $rootScope.user_id;
								// submitTeamSheet(formData, urlStr, config.text, TEAMS_ACTION_SUBMMIT_TEAM_SHEET);
								action = config.text;
								var formData = "team_id=" + team_id + '&user_id=' + $rootScope.user_id;
								checkRequestSubmitTeam(formData,TEAMS_ACTION_SUBMMIT_TEAM_SHEET,action);
                            }
						},
						{
							extend: 'excel',
							name: 'excel',
							text: '<i class="fa fa-file-excel-o"></i>&emsp;Export to Excel',
							titleAttr: 'Export data to an Excel file',
							filename: 'Team ' + team_name,
							title: team_name,
							exportOptions: {
								columns: ':visible',
								modifier: {
									autoFilter: true,
									// selected: true
								}
							}
						}
					]
				},
				{ extend: 'remove', editor: editorTeamPlayers, text: 'Delete' },
				{ extend: 'colvis', text: 'Columns' }
			],
			order: [[1, 'asc']],
			displayLength: -1,
		} );
	}
	
	function checkRequest(cgroup_id, action_id, action) {
        if (action_id == CLUBS_ACTION_ASSIGN_PLAYERS_TO_TEAM) {
 			table = $('#tblCgroupPlayers_' + cgroup_id).DataTable();
        }

        if (DEVELOPMENT_ENVIRONMENT) console.log('checkRequest - action: ' + action);

        var table_selected = table.rows( { selected: true } ).data();
        var countRows = table_selected.length;

        var MAX_NAMES_TO_DISPLAY = 10;

        var names = ''; 
        var name_index = 0;
        var rows_selected = [];
        for (var i = 0; i < table_selected.length; i++) {
        	
			if (action_id == CLUBS_ACTION_ASSIGN_PLAYERS_TO_TEAM) {
 				id = table_selected[i].cgroup_players.id;
 				name = table_selected[i].players.surname + ' ' + table_selected[i].players.other_name;
 			}				
            rows_selected.push(id);
            name_index = i + 1;
			names += name_index +'. ' + name +'<br/>';
        }

        if (DEVELOPMENT_ENVIRONMENT) console.log('checkRequest - rows_selected: ' + countRows);
        
        rows_selected.sort();
        var selectedRows = (rows_selected.join("_"));
        var data = null;

        if (action_id == CLUBS_ACTION_ASSIGN_PLAYERS_TO_TEAM) {
 			message = (countRows == 0) ? 'At least one player must be selected!' :
			((countRows == 1) ? '<strong>1 player?</strong></br>' + names :
            	'<strong>' + countRows + ' players?</strong></br>' + names); 
        } 

        if (selectedRows == "") {
            // Not selected
            BootstrapDialog.show({
                title: 'Information - ' + action,
                type: BootstrapDialog.TYPE_WARNING,
                message: message
            });
        } else {
            if (data == null) {
				// for select multiple rows
                title = 'Confirmation - ' + action;
                var dialog = BootstrapDialog.confirm( title, message, function(result){
                    if(result) {
                        dialog.close();
                        if (DEVELOPMENT_ENVIRONMENT) console.log('checkRequest - action_id: ' + action_id);
                        var urlStr = "";
                        if (action_id == CLUBS_ACTION_ASSIGN_PLAYERS_TO_TEAM) {
                            urlStr = SERVER_PATH + "team/assignPlayersToTeam";
                            admin_assignPlayersToTeam(cgroup_id, rows_selected, urlStr, action, action_id);
                        }
                    } 
                }); 
                if (name_index > 15 ) {
                    dialog.getModalBody().css('height', '330px');
                    dialog.getModalBody().css('overflow-y', 'scroll');
                }
            } else {
                // for select one row
            }
        }
	}
	
	// Assign player to year group
    function admin_assignPlayersToTeam(cgroup_id, cgroup_player_ids, URL, action, action_id) {

		jQuery.ajax({
			type: 'POST',
			url: SERVER_PATH + "team/getTeamsByCgroup",
			async: false,
			headers: {	
					'x-user-id': $rootScope.user_id,
					'x-user-email': $rootScope.user_name
			},
			data: {
				"event_id": $scope.event_id,
				"cgroup_id": cgroup_id
			},
			dataType: 'json',
			complete: function (response) {
				response = JSON.parse(response.responseText);
				teamSelection = response.teamSelection;

				var editor = new $.fn.dataTable.Editor( {
					ajax: {
						type: 'POST',
						url: URL,
						async: false,
						data: {
							user_id: $rootScope.user_id,
							event_id: $scope.event_id,
							cgroup_id: cgroup_id,
							cgroup_player_ids: cgroup_player_ids
						},
						dataType: 'json',
						complete: function (response) {
							var jsonData = JSON.parse(response.responseText);
							displayActionMessage(jsonData, action, action_id);
							tableCgroupPlayers.ajax.reload();
						},
						error: function(xhr, status, error) {
							alert('admin_assignPlayersToTeam.Error - status, error = ' + status + ',' + error + ',' + xhr);
						},
					},
					formOptions: {
						main: {
							onBlur: 'none'
						}
					},
					fields: [ 
						{
							label: "Team:",
							name: "team_id",
							type: "select2",
							opts: {
								placeholder: "Select a team"
							},
							options: teamSelection
						}
					]
				} );
				editor
					.title( 'Assign player to team' )
					.buttons(
						{
							label: "Submit",
							fn: function () { this.submit(); }
						}
					)
					.edit()
					.open();
			}
		});

	}

	function checkRequestTeam(team_id, action_id, action) {
        if (action_id == ASSIGN_PLAYERS_TO_TEAM) {
 			table = $('#tblTeamPlayers_' + team_id).DataTable();
        }

        if (DEVELOPMENT_ENVIRONMENT) console.log('checkRequestTeam - action: ' + action);

        var table_selected = table.rows( { selected: true } ).data();
		var countRows = table_selected.length;

        var MAX_NAMES_TO_DISPLAY = 10;

        var names = ''; 
        var name_index = 0;
        var rows_selected = [];
        for (var i = 0; i < table_selected.length; i++) {
        	
			if (action_id == ASSIGN_PLAYERS_TO_TEAM) {
				 id = table_selected[i].team_players.id;
 				name = table_selected[i].players.surname + ' ' + table_selected[i].players.other_name;
 			}				
            rows_selected.push(id);
            name_index = i + 1;
			names += name_index +'. ' + name +'<br/>';
        }

        if (DEVELOPMENT_ENVIRONMENT) console.log('checkRequest - rows_selected: ' + countRows);
        
        rows_selected.sort();
        var selectedRows = (rows_selected.join("_"));
        var data = null;

        if (action_id == ASSIGN_PLAYERS_TO_TEAM) {
 			message = (countRows == 0) ? 'At least one player must be selected!' :
			((countRows == 1) ? '<strong>1 player?</strong></br>' + names :
            	'<strong>' + countRows + ' players?</strong></br>' + names); 
        } 

        if (selectedRows == "") {
            // Not selected
            BootstrapDialog.show({
                title: 'Information - ' + action,
                type: BootstrapDialog.TYPE_WARNING,
                message: message
            });
        } else {
            if (data == null) {
				// for select multiple rows
                title = 'Confirmation - ' + action;
                var dialog = BootstrapDialog.confirm( title, message, function(result){
                    if(result) {
                        dialog.close();
                        if (DEVELOPMENT_ENVIRONMENT) console.log('checkRequest - action_id: ' + action_id);
                        var urlStr = "";
                        if (action_id == ASSIGN_PLAYERS_TO_TEAM) {
                            urlStr = SERVER_PATH + "team/assignPlayers";
                            ajaxAssignPlayerToTeam(team_id, rows_selected, urlStr, action, action_id);
                        }
                    } 
                }); 
                if (name_index > 15 ) {
                    dialog.getModalBody().css('height', '330px');
                    dialog.getModalBody().css('overflow-y', 'scroll');
                }
            } else {
                // for select one row
            }
        }
	}
	
	// Assign player to team
    function ajaxAssignPlayerToTeam(team_id, team_players_ids, URL, action, action_id) {
		
		jQuery.ajax({
			type: 'POST',
			url: SERVER_PATH + "team/getTeamsByTeamPlayer",
			async: false,
			headers: {	
					'x-user-id': $rootScope.user_id,
					'x-user-email': $rootScope.user_name
			},
			data: {
				"event_id": $scope.event_id,
				"team_id": team_id
			},
			dataType: 'json',
			complete: function (response) {
				response = JSON.parse(response.responseText);
				teamSelection = response.teamSelection;
				var editor = new $.fn.dataTable.Editor( {
					ajax: {
						type: 'POST',
						url: URL,
						async: false,
						data: {
							team_players_ids: team_players_ids
						},
						dataType: 'json',
						complete: function (response) {
							tableTeamPlayers.ajax.reload();
							var jsonData = JSON.parse(response.responseText);
							displayActionMessage(jsonData, action, action_id);
						},
						error: function(xhr, status, error) {
							alert('ajaxAssignPlayerToTeam.Error - status, error = ' + status + ',' + error + ',' + xhr);
						},
					},
					formOptions: {
						main: {
							onBlur: 'none'
						}
					},
					fields: [ 
						{
							label: "Team:",
							name: "team_id",
							type: "select2",
							opts: {
								placeholder: "Select a team"
							},
							options: teamSelection
						}
					]
				} );
				editor
					.title( 'Assign player to team' )
					.buttons(
						{
							label: "Submit",
							fn: function () { this.submit(); }
						}
					)
					.edit()
					.open();
			}
		});

	}

	function displayActionMessage(jsonData, action, action_id) {
        if (jsonData.status == "OK") {                    
            BootstrapDialog.show({
                title: 'Information - ' + action,
                type: function() {
                    if (jsonData.message.indexOf("Please try again") == -1)
                        return BootstrapDialog.TYPE_SUCCESS;
                    return BootstrapDialog.TYPE_WARNING;
                },
                message: jsonData.message,
                onhidden: function(dialogRef) {
                    // table.ajax.reload();
                }
            });
        } else if (jsonData.status == "ERROR") {
            BootstrapDialog.show({
                title: 'Information - ' + action,
                type: BootstrapDialog.TYPE_DANGER,
                message: jsonData.message,                        
            });
        }  
	}

	function checkRequestSubmitTeam(data,action_id, action) {
		// for select multiple rows
		title = 'Confirmation - ' + action;
		message = 'Are you sure that are you want to submit the team sheet?' ;
		var dialog = BootstrapDialog.confirm( title, message, function(result){
			dialog.enableButtons(false);
			if(result) {
				dialog.close();
				if (DEVELOPMENT_ENVIRONMENT) console.log('checkRequest - action_id: ' + action_id);
				var urlStr = "";
				if (action_id == TEAMS_ACTION_SUBMMIT_TEAM_SHEET) {
					var urlStr = SERVER_PATH + "team/submitTeamSheet";
					submitTeamSheet(data, urlStr, action, action_id);
				}
			} 
		}); 
	}

	function submitTeamSheet(data, URL, action, action_id) {
		jQuery.ajax({
            type: 'POST',
            url: URL,
            async: false,
            data: data,
			dataType: 'json',
            complete: function (response) {
                var jsonData = JSON.parse(response.responseText);
                displayActionMessage(jsonData, action, action_id);
            },
            error: function(xhr, status, error) {
                alert('ajaxAction.Error - status, error = ' + status + ',' + error +
                        ',' + xhr);
            },
        });
	}

});
