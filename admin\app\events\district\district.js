app.controller('districtCtrl', function (user, $scope, $rootScope, $http, seasonService) {
    $scope.user = user;

    $scope.events = [];
    $scope.seasons = [];
    $scope.selectedSeasonId = null;

    $scope.items = [];

    seasonService.loadSeasons().then(function(seasons) {
        $scope.seasons = seasons;
        $scope.selectedSeasonId = seasonService.getSelectedSeasonId();
        if ($scope.selectedSeasonId) {
            getEventDistrict();
        }
    });

    function getEventDistrict() {
        $http({
            method: 'POST',
            url: SERVER_PATH + 'event/getEventDistricts',
            headers: {
                'x-user-id': $rootScope.user_id,
                'x-user-email': $rootScope.user_name,
            },
            data: {
                season_id: $scope.selectedSeasonId
            }
        }).success(function (response) {
            if (response.status == 'OK') {
                $scope.events = response.data;

                $scope.items = [
                    {
                        name: 'Registration',
                        icon: 'fa-user-plus',
                        href: '#/registration-district/{event.id}',
                        image: 'images/adminpanel-icon/Registrations.png'
                    },
                    {
                        name: 'Payment',
                        icon: 'fa-credit-card',
                        href: '#/payments/district/{event.id}',
                        image: 'images/adminpanel-icon/Payments.png'
                    },
                    {
                        name: 'Team',
                        icon: 'fa-list-ul',
                        href: '#/event/{event.id}/team_district',
                        image: 'images/adminpanel-icon/Teams.png'
                    },
                    {
                        name: 'Messages',
                        icon: 'fa-envelope-o',
                        href: '#/messages/{event.id}',
                        image: 'images/adminpanel-icon/Message.png'
                    },
                    {
                        name: 'Report',
                        icon: 'fa-file-text-o',
                        href: '#/reports/district/{event.id}',
                        image: 'images/adminpanel-icon/Report.png'
                    },
                ];
            }
        });
    }

    $scope.filterEventsBySeason = function () {
        seasonService.setSelectedSeasonId($scope.selectedSeasonId);
        if ($scope.selectedSeasonId) {
            getEventDistrict();
        }
    };
});
