app.controller(
    'registrationsTrainingSchemeCtrl',
    function ($scope, $rootScope, $routeParams, $http) {
        $('#page-wrapper').removeClass('nav-small');

        var types = ['Normal', 'Parent'];

        $scope.selectedType = types[0];
        $scope.types = types;
        var event_id = $routeParams.id;
        $scope.courses = [];

        $scope.updateEvents = function () {
            jQuery.ajax({
                type: 'POST',
                url: SERVER_PATH + 'training-scheme/getCoursesInEvent',
                async: false,
                headers: {	
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name
                },
                data: {
                    train_type: $scope.selectedType.toLowerCase(),
                    event_id: event_id,
                },
                dataType: 'json',
                complete: function (response) {
                    var jsonData = JSON.parse(response.responseText);
                    var courses = [];

                    if (jsonData.status == 'OK') {
                        for (var i = 0; i < jsonData.info.length; i++) {
                            let value = jsonData['info'][i];
                            courses.push(value);
                        }
                        $scope.courses = courses;

                        if ($scope.courses != '' && $scope.courses != null) {
                            $scope.selectedCourses = $scope.courses[0];
                        } else {
                            BootstrapDialog.show({
                                title: 'ERROR',
                                type: BootstrapDialog.TYPE_WARNING,
                                message:
                                    'Cannot find any courses. Please create in Courses ',
                            });
                            return;
                        }
                        console.log($scope.courses);
                    }
                },
            });
        };

        $scope.updateEvents();

        jQuery.ajax({
            type: 'POST',
            url: SERVER_PATH + 'event/getEventInfo',
            async: false,
            headers: {	
                'x-user-id': $rootScope.user_id,
                'x-user-email': $rootScope.user_name
            },
            data: {
                event_id: event_id
            },
            dataType: 'json',
            complete: function (response) {
                var jsonData = JSON.parse(response.responseText);
                var event = jsonData.info;
                event_name = event.name;
                event_type = event.type;
            }
        });

        $rootScope.event_name = event_name;
        $rootScope.event_type = event_type;

        $('button').click(function (e) {
            if ($.fn.dataTable.isDataTable('#registration_table')) {
                $('#registration_table').DataTable().destroy();
            }

            initRegistrationtable('registration_table', event_id, false);

            function initRegistrationtable(table_id, event_id, flag) {
                table = $('#' + table_id).DataTable({
                    dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
                    stateSave: true,
                    deferRender: true,
                    ajax: {
                        url:SERVER_PATH +'registration/getRegistrationsTrainingSchemes',
                        type: 'POST',
                        headers: {	
                            'x-user-id': $rootScope.user_id,
                            'x-user-email': $rootScope.user_name
                        },
                        data: {
                            event_id: event_id,
                            course_id: $scope.selectedCourses.id,
                        },
                        dataType: 'json',
                        complete: function (response) {
                            if (flag)
                                initRegistrationtable(
                                    'registration_table',
                                    event_id,
                                    false
                                );
                        },
                        error: function (xhr, status, error) {},
                    },
                    language: {
                        info: 'Showing _START_ to _END_ of _TOTAL_ registration',
                        infoEmpty: 'Showing 0 to 0 of 0 registrations',
                        lengthMenu: 'Show _MENU_ registrations',
                        select: {
                            rows: {
                                _: 'You have selected %d registrations',
                                0: 'Click a registration to select',
                                1: '1 registration selected',
                            },
                        },
                        paginate: {
                            previous: '<i class="fa fa-chevron-left"></i>',
                            next: '<i class="fa fa-chevron-right"></i>',
                        },
                    },
                    columns: [
                        {
                            data: 'players.player_photo',
                            className: 'avatar',
                            orderable: false,
                            render: function (data) {
                                if (data !== null && data !== '') {
                                    return (
                                        '<img src="' +
                                        PRODUCT_IMAGE_PATH +
                                        data +
                                        '">'
                                    );
                                } else {
                                    return (
                                        '<img src="' +
                                        SYSTEM_IMAGE_PATH +
                                        'favicon.png">'
                                    );
                                }
                            },
                        },
                        {
                            data: null,
                            render: function (data, type, row) {
                                return (
                                    data.players.surname +
                                    ' ' +
                                    data.players.other_name
                                );
                            },
                        },
                        {
                            data: 'players.dob',
                            className: 'center',
                        },
                        {
                            data: 'players.gender',
                            className: 'center',
                        },
                        {
                            data: null,
                            render: function (data, type, row) {
                                return (
                                    data.parens.surname +
                                    ' ' +
                                    data.parens.other_name
                                );
                            },
                            visible: false,
                        },
                        {
                            data: 'parens.email',
                            className: 'center',
                            visible: false,
                        },
                        // {
                        //     data: 'players.hkid_no',
                        //     className: 'center',
                        // },
                        {
                            data: 'players.res_address',
                            className: 'center',
                        },
                        {
                            data: 'parens.phone',
                            className: 'center',
                        },
                        {
                            data: 'courses.class_code',
                            className: 'center',
                        },
                        {
                            data: 'registrations.approval_status',
                            className: 'center',
                            render: function (data, type, full, meta) {
                                switch (data) {
                                    case APPROVAL_STATUS_Approve:
                                        return (
                                            '<span class="label label-success">' +
                                            data +
                                            '</span>'
                                        );
                                    case APPROVAL_STATUS_Register:
                                        return (
                                            '<span class="label label-info">' +
                                            data +
                                            '</span>'
                                        );
                                    case APPROVAL_STATUS_Reject:
                                        return (
                                            '<span class="label label-danger">' +
                                            data +
                                            '</span>'
                                        );
                                    default:
                                        return (
                                            '<span class="label label-default">' +
                                            data +
                                            '</span>'
                                        );
                                }
                            },
                        },
                    ],
                    select: {
                        style: SELECT_MODE,
                        // selector: 'td:first-child',
                    },
                    order: [[1, 'asc']],
                    lengthMenu: [
                        [10, 25, 50, 100, -1],
                        [10, 25, 50, 100, 'All'],
                    ],
                    buttons: [
                        {
                            extend: 'collection',
                            text: 'Actions',
                            // background: false,
                            autoClose: true,
                            buttons: [
                                {
                                    extend: 'excel',
                                    name: 'excel',
                                    text: '<i class="fa fa-file-excel-o"></i>&emsp;Export to Excel',
                                    titleAttr: 'Export data to an Excel file',
                                    filename: 'Registrations -' + event_name,
                                    exportOptions: {
                                        columns: ':visible',
                                        modifier: {
                                            autoFilter: true,
                                            // selected: true
                                        },
                                    },
                                },
                                {
                                    extend: 'pdfHtml5',
                                    text: '<i class="fa fa-file-pdf-o" aria-hidden="true"></i>&emsp;Export to PDF',
                                    title: 'Registrations -' + event_name,
                                    titileAttr: 'PDF',
                                    /* download : 'open', */
                                    pageSize: 'A4',
                                    extension: '.pdf',
                                    header: true,
                                    footer: false,
                                    orientation: 'portrait',
                                    exportOptions: {
                                        columns: [
                                            1, 2, 3, 4, 5, 6, 7, 8, 9, 10,
                                        ],
                                    },
                                    customize: function (doc) {
                                        var rotNo = '<%=rotaryNo%>';
                                        doc.pageMargins = [30, 50, 30, 50]; //left,top,right,bottom
                                        doc.defaultStyle.fontSize = 7;
                                        doc.styles.tableHeader.fontSize = 7;
                                        doc.styles.title.fontSize = 16;
                                        // Remove spaces around page title
                                        doc.content[0].text =
                                            doc.content[0].text.trim();
                                        var objLayout = {};
                                        // Horizontal line thickness
                                        objLayout['hLineWidth'] = function (i) {
                                            return 0.5;
                                        };
                                        // Vertical line thickness
                                        objLayout['vLineWidth'] = function (i) {
                                            return 0.5;
                                        };
                                        // Horizontal line color
                                        objLayout['hLineColor'] = function (i) {
                                            return '#aaa';
                                        };
                                        // Vertical line color
                                        objLayout['vLineColor'] = function (i) {
                                            return '#aaa';
                                        };
                                        // Left padding of the cell
                                        objLayout['paddingLeft'] = function (
                                            i
                                        ) {
                                            return 4;
                                        };
                                        // Right padding of the cell
                                        objLayout['paddingRight'] = function (
                                            i
                                        ) {
                                            return 4;
                                        };
                                        // Inject the object in the document
                                        doc.content[1].layout = objLayout;
                                    },
                                },
                            ],
                        },
                        {
                            text: 'Edit',
                            extend: 'selectedSingle',
                            action: function (e, dt, node, config) {
                                let data = table
                                    .rows({ selected: true })
                                    .data()[0];

                                let editor = new $.fn.dataTable.Editor({
                                    ajax: {
                                        type: 'POST',
                                        url:
                                            SERVER_PATH +
                                            'registration/editRegistration',
                                        headers: {	
                                            'x-user-id': $rootScope.user_id,
                                            'x-user-email': $rootScope.user_name
                                        },
                                        data: {
                                            player_id: data.players.id,
                                        },

                                        dataType: 'json',
                                        complete: function (response) {
                                            response = JSON.parse(
                                                response.responseText
                                            );
                                            if (response.status == 'OK') {
                                                table.ajax.reload();
                                            }
                                        },
                                        error: function (xhr, status, error) {
                                            console.log(error);
                                        },
                                    },

                                    formOptions: {
                                        main: {
                                            onBlur: 'none',
                                        },
                                    },
                                    fields: [
                                        {
                                            label: 'Surname',
                                            name: 'surname',
                                            def: data.players.surname,
                                        },
                                        {
                                            label: 'Other name',
                                            name: 'other_name',
                                            def: data.players.other_name,
                                        },
                                        {
                                            label: 'Date of birth',
                                            name: 'dob',
                                            type: 'datetime',
                                            format: 'DD-MMM-YYYY',
                                            fieldInfo: 'US style d-m-y format',
                                            def: data.players.dob
                                        },
                                        {
                                            label: 'Gender',
                                            name: 'gender',
                                            type: 'radio',
                                            options: [
                                                {
                                                    label: 'Male',
                                                    value: 'Male',
                                                },
                                                {
                                                    label: 'Female',
                                                    value: 'Female',
                                                },
                                            ],
                                            def: data.players.gender,
                                        },
                                        {
                                            label: 'Player Photo',
                                            name: 'player_photo',
                                            type: 'upload',
                                            display: function (data) {
                                                return (
                                                    '<img src="' +
                                                    PRODUCT_IMAGE_PATH +
                                                    data +
                                                    '">'
                                                );
                                            },
                                            noImageText: 'No image',
                                            def: data.players.player_photo,
                                        },
                                        {
                                            label: 'HKID Photo',
                                            name: 'hkid_img',
                                            type: 'upload',
                                            display: function (data) {
                                                return (
                                                    '<img src="' +
                                                    PRODUCT_IMAGE_PATH +
                                                    data +
                                                    '">'
                                                );
                                            },
                                            noImageText: 'No image',
                                            def: data.players
                                                .hkid_passport_photo,
                                        },
                                        {
                                            label: 'HKID No',
                                            name: 'hkid_no',
                                            def: data.players.hkid_no,
                                        },
                                        {
                                            label: 'Res Address',
                                            name: 'res_address',
                                            def: data.players.res_address,
                                        },
                                    ],
                                });

                                editor.on('initEdit', function (e, type) {
                                    editor.disable('hkid_img');
                                    editor.disable('player_photo');
                                });

                                editor
                                    .title('Edit Player')
                                    .buttons({
                                        label: 'Save',
                                        fn: function () {
                                            this.submit();
                                        },
                                    })
                                    .edit()
                                    .open();
                            },
                        },
                        {
                            extend: 'colvis',
                            text: 'Columns',
                        },
                    ],
                });
            }
        });
    }
);
