app.controller('summerSchemeCtrl', function (user, $scope, $rootScope, $http, seasonService) {
    $scope.user = user;

    $scope.events = [];
    $scope.seasons = [];
    $scope.selectedSeasonId = null;

    $scope.items = [];

    seasonService.loadSeasons().then(function(seasons) {
        $scope.seasons = seasons;
        $scope.selectedSeasonId = seasonService.getSelectedSeasonId();
        if ($scope.selectedSeasonId) {
            getEventSummerSchemes();
        }
    });

    function getEventSummerSchemes() {
        $http({
            method: 'POST',
            url: SERVER_PATH + 'event/getEventSummerScheme',
            headers: {
                'x-user-id': $rootScope.user_id,
                'x-user-email': $rootScope.user_name,
            },
            data: {
                season_id: $scope.selectedSeasonId
            }
        }).success(function (response) {
            if (response.status === 'OK') {
                $scope.events = response.data;

                $scope.items = [
                    {
                        name: 'Registration',
                        icon: 'fa-solid fa-address-card fa-lg',
                        href: '#/registration-summer-scheme/{event.id}',
                        image: 'images/adminpanel-icon/Registrations.png'
                    },
                    {
                        name: 'Payment',
                        icon: 'fa-solid fa-money-check-dollar fa-lg',
                        href: '#/payments/summer-scheme/{event.id}',
                        image: 'images/adminpanel-icon/Payments.png'
                    },
                    {
                        name: 'Course',
                        icon: 'fa-list-ul',
                        href: '#/event/{event.id}/course',
                        image: 'images/adminpanel-icon/Course.png'
                    },
                    {
                        name: 'Shipping',
                        icon: 'fa-solid fa-cart-shopping fa-lg',
                        href: '#/shipping/{event.id}',
                        image: 'images/adminpanel-icon/Shipping.png'
                    },
                    {
                        name: 'Self Pick Up',
                        icon: 'fa-hand-o-right',
                        href: '#/self-pick-up/{event.id}',
                        image: 'images/adminpanel-icon/SelfPickUp.png'
                    },
                    {
                        name: 'Messages',
                        icon: 'fa-solid fa-message fa-lg',
                        href: '#/messages/{event.id}',
                        image: 'images/adminpanel-icon/Message.png'
                    },
                    {
                        name: 'Report',
                        icon: 'fa-solid fa-server fa-lg',
                        href: '#/reports/summer_scheme/{event.id}',
                        image: 'images/adminpanel-icon/Report.png'
                    },
                ];
            }
        });
    }

    $scope.filterEventsBySeason = function () {
        seasonService.setSelectedSeasonId($scope.selectedSeasonId);
        if ($scope.selectedSeasonId) {
            getEventSummerSchemes();
        }
    };
});
