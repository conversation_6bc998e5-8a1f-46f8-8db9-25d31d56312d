app.controller('TournamentKnockoutStageCtrl', function ($scope, $rootScope, $routeParams, $http) {
    $('#page-wrapper').removeClass('nav-small');
    console.log('TournamentKnockoutStageCtrl');
    var leagueSelected = [];
    $scope.event_id = $routeParams.id;
    var leagueId = $routeParams.leaguesid;
    var eventName = $routeParams.eventName;
    var groupName = $routeParams.groupName;
    var leagueName = $routeParams.leagueName;
    var groupIdSelected = $routeParams.groupIdSelected;
    $scope.eventName = eventName;
    $scope.leagueId = leagueId;
    $scope.league_name = leagueName;
    $scope.groupName = groupName;
    $scope.groupIdSelected = groupIdSelected;
    jQuery.ajax({
        type: 'POST',
        url: SERVER_PATH + "league/getleagueinfo",
        async: false,
        headers: {	
            'x-user-id': $rootScope.user_id,
            'x-user-email': $rootScope.user_name
        },
        data: {
            "league_id": leagueId,
        },
        dataType: 'json',
        complete: function (response) {
            var jsonData = JSON.parse(response.responseText);
            console.log('league');
            console.log(jsonData);
            if (jsonData.status == "OK") {
                leagueSelected = jsonData.info;
            }
        }
    });
    function getLeagueMatchesDetailHtml(match_id) {
        var str = '' +
            '<div class="table-responsive">' +
            '<table id="tblLeagueMatchesEvents_' + match_id + '" class="table table-striped table-bordered table-hover" cellspacing="0" width="100%">' +
            '<thead>' +
            '<tr>' +
            '<th >Player</th>' +
            '<th >Team</th>' +
            '<th>Type</th>' +
            '<th >Time</th>' +
            '<th  class="center">User Create</th>' +
            '<th  class="center">Note</th>' +
            '</tr>' +
            '</thead>' +
            '</table>' +
            '</div>';
        return str;
    }
    $('body').unbind('click');
    initLeagueMatchesTable(leagueId, leagueName, groupName);
    $('body').on('click', 'li[class=active] a[data-toggle=tab]', function () {
        switch ($(this).data('target')) {
            case '#tab-matches': {
                initLeagueMatchesTable(leagueId, leagueName, groupName);
                break;
            }
        }
    })
    function initLeagueMatchesTable(league_id, league_name, group_name) {
        if ($.fn.dataTable.isDataTable('#tblLeagueMatches')) {
            $('#tblLeagueMatches').DataTable().destroy();
        }
        var editorLeagueMatches = new $.fn.dataTable.Editor({
            ajax: {
                type: 'POST',
                url: SERVER_PATH + "tournament/setKnockoutStageMatch",
                headers: {	
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name
                },
                data: {
                    "league_id": league_id,
                    "type_of_match": 0,
                    "user_id": $rootScope.user_id
                },
                dataType: 'json',
                complete: function (response) {
                    let data = JSON.parse(response.responseText);
                    if ((typeof (data.fieldErrors) == "undefined") || (data.fieldErrors.length == 0)) {
                        tableLeagueMatches.ajax.reload();
                        tableLeagueMatches.rows({ selected: true }).deselect();
                    }
                },
                error: function (xhr, status, error) { },
            },
            table: '#tblLeagueMatches',
            formOptions: {
                main: {
                    onBlur: 'none'
                }
            },
            i18n: {
                edit: {
                    button: "Edit",
                    title: "Edit match",
                    submit: "Save"
                },
                error: {
                    system: "System error, please contact administrator."
                },
            },
            fields: [{
                label: "League ID:",
                name: "matches.league_id",
                type: "hidden",
                def: league_id
            },
            {
                label: "Date:",
                name: "matches.date",
                type: "datetime",
                format: 'DD-MMM-YYYY',
                default: moment().format("DD-MMM-YYYY"),
            },
            {
                label: "Start Time:",
                name: "matches.start_time",
                type: "datetime",
                format: 'HH:mm',
                def: '00:00'
            },
            {
                label: "End Time:",
                name: "matches.end_time",
                type: "datetime",
                format: 'HH:mm',
                def: '00:00'
            },
            {
                label: "Location:",
                name: "matches.location",
                type: "select2",
                opts: {
                    placeholder: "Select location",
                    tags: true,
                    closeOnSelect: true
                },
            },
            {
                label: "Home Team:",
                name: "matches.home_team_id",
                type: "select2",
                opts: {
                    placeholder: "Select a team"
                },
                def: 0

            },
            {
                label: "Away Team:",
                name: "matches.away_team_id",
                type: "select2",
                opts: {
                    placeholder: "Select a team"
                },
                def: 0
            },
            {
                label: "group_id",
                name: "matches.LKR_id",
                type: "hidden"
            }
            ]
        });
        editorLeagueMatches.on('preOpen', function (e, data, action) {
            if (action == 'create') {
                editorLeagueMatches.dependent('matches.start_time', function (val) {
                    var start_time = new Date();
                    start_time_arr = val.split(":");
                    start_time.setHours(start_time_arr[0]);
                    start_time.setMinutes(start_time_arr[1]);
                    var end_time = new Date(start_time.getTime() + parseInt(leagueSelected.match_duration) * 60000);
                    editorLeagueMatches.val('matches.end_time', end_time);
                })
            }
            $('.modal-backdrop').hide();
            $('.bootstrap-dialog').hide();
        });
        editorLeagueMatches.on('initEdit', function () {
            var dataSelectedRow = tableLeagueMatches.rows({ selected: true }).data()[0];
            if ((dataSelectedRow.matches.home_team_id != null) && (dataSelectedRow.matches.away_team_id != null)) {
                editorLeagueMatches.field('matches.home_team_id').hide();
                editorLeagueMatches.field('matches.away_team_id').hide();
            } else {
                editorLeagueMatches.field('matches.home_team_id').show();
                editorLeagueMatches.field('matches.away_team_id').show();
            }
        })
        editorLeagueMatches.on('close', function () {
            editorLeagueMatches.undependent('matches.start_time')
            $('.modal-backdrop').show();
            $('.bootstrap-dialog').show();
        });
        tableLeagueMatches = $('#tblLeagueMatches').DataTable({
            dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
            stateSave: false,
            deferRender: true,
            ajax: {
                url: SERVER_PATH + "tournament/getKnockoutStageMatch",
                type: 'POST',
                headers: {	
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name
                },
                data: {
                    "league_id": league_id,
                    "type_of_match": 0
                },
                dataType: 'json',
                complete: function (response) {

                },
                error: function (xhr, status, error) {

                },
            },
            language: {
                paginate: {
                    previous: '<i class="fa fa-chevron-left"></i>',
                    next: '<i class="fa fa-chevron-right"></i>'
                }
            },
            columns: [
                {
                    "data": null,
                    "render": function (data, type, full, meta) {
                        return full.matches.date == '' ? 'TBD' : full.matches.date;
                    }
                },
                {
                    "data": null,
                    "render": function (data, type, full, meta) {
                        return full.matches.start_time == '' ? 'TBD' : full.matches.start_time;
                    }
                },
                {
                    "data": null,
                    "render": function (data, type, full, meta) {
                        return full.matches.end_time == '' ? 'TBD' : full.matches.end_time;
                    }
                },
                {
                    "data": null,
                    "render": function (data, type, full, meta) {
                        return full.matches.location == 0 ? 'TBD' : full.venues.name;
                    }
                },
                {
                    data: "match_cancellations.cancel_type",
                    render: function (data, type, row) {
                        if (row.match_cancellations.cancel_type == null) {
                            return '<span class="label label-success">Can Play</span>';
                        }
                        else {
                            switch (parseInt(row.match_cancellations.cancel_type)) {
                                case (MATCH_POSTPONED):
                                    {
                                        return '<span class="label label-danger">Postponed</span>';
                                        break;
                                    }
                                case (MATCH_CANCELLED):
                                    {
                                        return '<span class="label label-danger">Cancelled</span>';
                                        break;
                                    }
                                case (MATCH_ABANDONED):
                                    {
                                        return '<span class="label label-danger">Abandoned</span>';
                                        break;
                                    }
                            }
                        }
                    }
                },
                {
                    data: "matches.round_name",
                    className: "center",
                    sortable: false,
                },
                {
                    data: "matches.home_name",
                    className: "center",
                    sortable: false,
                },
                {
                    data: null,
                    className: "center",
                    sortable: false,
                    className: "center",
                    render: function () {
                        return 'VS'
                    }
                },
                {
                    data: "matches.away_name",
                    className: "center",
                    sortable: false,
                },
                {
                    data: "matches.home_team_score",
                    className: "center",
                    sortable: false,
                },
                {
                    data: null,
                    className: "center",
                    sortable: false,
                    className: "center",
                    render: function () {
                        return '-'
                    }
                },
                {
                    data: "matches.away_team_score",
                    sortable: false,
                },
                {
                    data: "match_penalty.home_pen_score",
                    className: "center",
                    sortable: false,
                },
                {
                    data: null,
                    className: "center",
                    sortable: false,
                    className: "center",
                    render: function () {
                        return '-'
                    }
                },
                {
                    data: "match_penalty.away_pen_score",
                    sortable: false,
                },
                {
                    data: "matches.home_respect_score",
                    className: "center",
                    sortable: false,
                },
                {
                    data: null,
                    className: "center",
                    sortable: false,
                    render: function () {
                        return '-'
                    }
                },
                {
                    data: "matches.away_respect_score",
                    className: "center",
                    sortable: false,
                },
                {
                    data: "matches.round_level",
                    sortable: false,
                    visible: false
                }
            ],
            "createdRow": function (row, data, dataIndex) {
                if (data.matches.highlight == 1) {
                    $(row).css("background-color", "#FFFACD");
                }
            },
            rowGroup: {
                order: [['matches.round_name', 'asc']],
                dataSrc: function (row) {
                    return row.matches.round_name;
                }
            },
            order: [
                ['18', 'asc']
            ],
            select: {
                style: SELECT_MODE,
            },
            buttons: [{
                extend: 'collection',
                text: 'Actions',
                autoClose: true,
                buttons: [
                    {
                        name: 'generate_matches',
                        text: '<i class="fa fa-magic"></i>&emsp;Generate Matches',
                        titleAttr: 'Auto generate match in league',
                        action: function (e, dt, node, config) {
                            jQuery.ajax({
                                type: 'POST',
                                url: SERVER_PATH + "tournament/generateMatchKnockoutRound",
                                async: false,
                                headers: {	
                                    'x-user-id': $rootScope.user_id,
                                    'x-user-email': $rootScope.user_name
                                },
                                data: {
                                    "league_id": league_id
                                },
                                dataType: 'json',
                                complete: function (response) {
                                    var jsonData = JSON.parse(response.responseText);
                                    if (jsonData.status == 'OK') {
                                        BootstrapDialog.show({
                                            size: BootstrapDialog.SIZE_WIDE,
                                            type: BootstrapDialog.TYPE_DANGER,
                                            title: league_name,
                                            message: jsonData.message
                                        });
                                        tableLeagueMatches.ajax.reload();
                                    }
                                    else {
                                        BootstrapDialog.show({
                                            size: BootstrapDialog.SIZE_WIDE,
                                            type: BootstrapDialog.TYPE_DANGER,
                                            title: league_name,
                                            message: jsonData.message
                                        });
                                    }
                                }
                            });
                        }
                    },
                    {
                        extend: 'excel',
                        name: 'excel',
                        text: '<i class="fa fa-file-excel-o"></i>&emsp;Export to Excel',
                        titleAttr: 'Export data to an Excel file',
                        filename: group_name + ' - ' + league_name,
                        exportOptions: {
                            columns: ':visible',
                            modifier: {
                                autoFilter: true,
                                // selected: true
                            }
                        }
                    }
                ]
            },
            {
                extend: 'selectedSingle',
                text: 'Add Replace Match',
                action: function () {
                    var dataSelectedRow = tableLeagueMatches.rows({ selected: true }).data()[0];
                    if ((dataSelectedRow.matches.home_team_id != null) && (dataSelectedRow.matches.away_team_id != null)) {
                        editorLeagueMatches.field('matches.home_team_id').hide();
                        editorLeagueMatches.field('matches.away_team_id').hide();
                    } else {
                        editorLeagueMatches.field('matches.home_team_id').show();
                        editorLeagueMatches.field('matches.away_team_id').show();
                    }
                    editorLeagueMatches.edit(tableLeagueMatches.rows({ selected: true }).indexes(), {
                        title: 'Add Replace Match',
                        buttons: 'Create'
                    })
                        .mode('create');
                    editorLeagueMatches.on('preSubmit', function (e, data, action) {
                        data.old_match_id = dataSelectedRow.matches.id;
                    })
                }
            },
            {
                extend: 'edit',
                editor: editorLeagueMatches,
                text: 'Edit'
            },
            {
                extend: 'selectedSingle',
                text: 'Match detail',
                action: function () {
                    var match = tableLeagueMatches.rows({
                        selected: true
                    }).data()[0].matches;
                    var msg = getLeagueMatchesDetailHtml(match.id);
                    // Show dialog
                    BootstrapDialog.show({
                        size: BootstrapDialog.SIZE_WIDE,
                        type: BootstrapDialog.TYPE_DANGER,
                        closable: true,
                        closeByBackdrop: false,
                        closeByKeyboard: true,
                        title: match.home_name + ' VS ' + match.away_name,
                        id: "leaguematch",
                        message: msg,
                        onshown: function (dialog) {
                            initLeagueMatchesEventTable(match.id, match.home_team_id, match.away_team_id);
                        }, onhide: function (dialog) {
                            $('body').removeClass('modal-open-custom');
                        },
                        onhidden: function (dialogRef) {
                            $('body').removeClass('modal-open-custom');
                        }
                    });
                }
            },
            {
                extend: 'selectedSingle',
                text: 'Update Match Result',
                action: function () {
                    var match = tableLeagueMatches.rows({
                        selected: true
                    }).data()[0];
                    console.log(match);
                    var editor = new $.fn.dataTable.Editor({
                        ajax: {
                            type: 'POST',
                            url: SERVER_PATH + "tournament/setLeagueMatchResult",
                            headers: {	
                                'x-user-id': $rootScope.user_id,
                                'x-user-email': $rootScope.user_name
                            },
                            data: {
                                "user_id": $rootScope.user_id,
                                "match_id": match.matches.id
                            },
                            dataType: 'json',
                            complete: function (response) {
                                var jsonData = JSON.parse(response.responseText);
                                if (jsonData.status == 'OK') {
                                    tableLeagueMatches.ajax.reload();
                                } else if (jsonData.status == 'WARNING') {
                                    BootstrapDialog.show({
                                        title: jsonData.status,
                                        message: jsonData.data
                                    })
                                }
                            },
                            error: function (xhr, status, error) {
                                console.log(error);
                            },
                        },
                        formOptions: {
                            main: {
                                onBlur: 'none'
                            }
                        },
                        fields: [{
                            label: match.matches.home_name,
                            name: 'home_score',
                            attr: {
                                type: "number"
                            },
                            def: parseInt(match.matches.home_team_score)
                        }, {
                            label: match.matches.away_name,
                            name: 'away_score',
                            attr: {
                                type: "number"
                            },
                            def: parseInt(match.matches.away_team_score)
                        },
                        {
                            label: "Penalty",
                            name: "penalty",
                            type: "radio",
                            options: [
                                {
                                    label: 'Yes',
                                    value: 1
                                },
                                {
                                    label: 'No',
                                    value: 0
                                }
                            ],
                            def: parseInt(match.matches.penalty)
                        },
                        {
                            label: match.matches.home_name + " (penalty score)",
                            name: 'homePenScore',
                            attr: {
                                type: "number"
                            },
                            def: parseInt(match.match_penalty.home_pen_score)
                        },
                        {
                            label: match.matches.away_name + " (penalty score)",
                            name: 'awayPenScore',
                            attr: {
                                type: "number"
                            },
                            def: parseInt(match.match_penalty.away_pen_score)
                        },
                        {
                            label: match.matches.home_name + " (respect score)",
                            name: 'homeResScore',
                            attr: {
                                type: "number"
                            },
                            def: parseInt(match.match_penalty.home_respect_score)
                        },
                        {
                            label: match.matches.away_name + " (respect score)",
                            name: 'awayResScore',
                            attr: {
                                type: "number"
                            },
                            def: parseInt(match.match_penalty.away_respect_score)
                        }
                        ]
                    });
                    editor.dependent('penalty', function (val) {
                        if (val == 0) {
                            editor.field('homePenScore').hide();
                            editor.field('awayPenScore').hide();
                        } else {
                            editor.field('homePenScore').show();
                            editor.field('awayPenScore').show();
                        }
                    })
                    editor.title(match.matches.home_name + ' VS ' + match.matches.away_name).buttons({
                        label: "Save",
                        fn: function () {
                            this.submit();
                        }
                    })
                        .edit()
                        .open();
                }
            },
            {
                extend: 'selectedSingle',
                text: 'Cancel Match',
                action: function () {
                    var matchID = tableLeagueMatches.rows({ selected: true }).data()[0].matches.id;
                    var editor = new $.fn.dataTable.Editor({
                        ajax: {
                            type: 'POST',
                            url: SERVER_PATH + "league/setMatchCancellations",
                            headers: {	
                                'x-user-id': $rootScope.user_id,
                                'x-user-email': $rootScope.user_name
                            },
                            data: {
                                user_id: $rootScope.user_id
                            },
                            dataType: 'json',
                            complete: function (response) {
                                tableLeagueMatches.rows({ selected: true }).deselect();
                            },
                            error: function (xhr, status, error) {
                                console.log(error);
                            },
                        },
                        formOptions: {
                            main: {
                                onBlur: 'none'
                            }
                        },
                        fields: [{
                            label: 'Cancel type',
                            name: 'match_cancellations.cancel_type',
                            type: "radio",
                            options: [
                                { label: "Postponed", value: 1 },
                                { label: "Cancelled", value: 2 },
                                { label: "Abandoned", value: 3 }
                            ],
                            def: 1
                        }, {
                            label: 'Reason',
                            name: 'match_cancellations.reason',
                        }, {
                            label: 'Match Id',
                            name: 'match_cancellations.match_id',
                            def: matchID
                        }]
                    })
                    editor.on('initCreate', function () {
                        editor.hide('match_cancellations.match_id');
                    });
                    editor.on('initSubmit', function () {
                        editor.close();
                    });
                    editor.on('close', function () {
                        tableLeagueMatches.ajax.reload();
                    });
                    editor.on('preSubmit', function () {
                        return confirm('Ask again, are you sure?');
                    })
                    editor.title('Cancel Match').buttons({
                        label: "Save",
                        fn: function () {
                            this.submit();
                        }
                    })
                        .create()
                        .open();
                }
            }
            ],
            lengthMenu: [
                [10, 25, 50, 100, -1],
                [10, 25, 50, 100, "All"]
            ],
        });
        tableLeagueMatches.on('select deselect', function () {
            var selectedRows = tableLeagueMatches.rows({ selected: true });
            var flat = true;
            if (selectedRows.count() == 1) {
                var dataSelectedRow = selectedRows.data()[0];
                if (dataSelectedRow.match_cancellations.cancel_type != null) {
                    flat = false;
                }
            } else {
                flat = false;
            }
            tableLeagueMatches.button(5).enable(flat);
        })
        tableLeagueMatches.on('select deselect', function () {
            var selectedRows = tableLeagueMatches.rows({ selected: true });
            var flat = false;
            if (selectedRows.count() == 1) {
                var dataSelectedRow = selectedRows.data()[0];
                if ((dataSelectedRow.match_cancellations.cancel_type != null) && (dataSelectedRow.match_cancellations.created_replace_match == 0)) {
                    flat = true;
                }
            } else {
                flat = false;
            }
            tableLeagueMatches.button(1).enable(flat);
        })
    }

    function getUpdateMatchesResultHtml(home_name, away_name, message) {
        var str = `<form method='get' id='updateScore' action=''>
            <div style="margin-bottom: -26px;" > `+ message + ` </div>
            <h3>The Penalty Score</h3>
                <div style="margin-bottom: -15px;" class='form-group'><label class='control-label'>`+ home_name + `</label>
                   <input type='number' class='form-control input-sm' id='home_team' name='home_team'>
                </div>
                <div style="margin-bottom: -53px;" class='form-group'><label class='control-label'>`+ away_name + `</label>
                    <input type='number' class='form-control input-sm' id='away_team' name='away_team'>
                </div>

            </form>`;
        return str;
    }

    function initLeagueMatchesEventTable(match_id, home_team_id, away_team_id) {

        var editorLeagueMatchesEvent = new $.fn.dataTable.Editor({
            ajax: {
                type: 'POST',
                url: SERVER_PATH + "tournament/setMatchEvents",
                headers: {	
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name
                },
                data: {
                    'match_id': match_id
                },
                dataType: 'json',
                complete: function (response) {
                    // console.log(response.responseText.data);
                    var jsonData = JSON.parse(response.responseText);

                    // --- may need to reload
                    if (DEVELOPMENT_ENVIRONMENT) console.log('status = ' + response.status);
                    if (jsonData.status == 'OK') {
                        tableLeagueMatchesEvent.ajax.reload();
                    } else if (jsonData.status == 'WARNING') {
                        tableLeagueMatchesEvent.ajax.reload();

                        var match = tableLeagueMatches.rows({
                            selected: true
                        }).data()[0].matches;

                        var msg = getUpdateMatchesResultHtml(match.home_name, match.away_name, jsonData.message);
                        BootstrapDialog.show({
                            type: BootstrapDialog.TYPE_WARNING,
                            size: BootstrapDialog.SIZE_SMALL,

                            closable: true,
                            closeByBackdrop: false,
                            closeByKeyboard: true,
                            title: match.home_name + ' VS ' + match.away_name,
                            message: msg,
                            buttons: [{
                                label: 'Close',
                                action: function (dialogRef) {
                                    dialogRef.close();
                                }
                            }, {
                                label: 'Save',
                                action: function (dialogRef) {
                                    var $form = $("#updateScore");
                                    $form.validate({
                                        onfocusout: false,
                                        onkeyup: false,
                                        onclick: false,
                                        rules: {
                                            "home_team": {
                                                required: true,
                                                digits: true
                                            },
                                            "away_team": {
                                                required: true,
                                                digits: true
                                            },
                                        },
                                        messages: {
                                            "home_team": {
                                                required: "this field is required",
                                                digits: "please enter number"
                                            },
                                            "away_team": {
                                                required: "this field is required",
                                                minlength: "please enter number"
                                            },
                                        }
                                    });
                                    if ($form.valid()) {
                                        var home_score = dialogRef.getModalBody().find('#home_team').val();
                                        var away_score = dialogRef.getModalBody().find('#away_team').val();
                                        var url = SERVER_PATH + "tournament/updatePenaltyScore";
                                        var form = new FormData();
                                        form.append("home_penalty", home_score);
                                        form.append("away_penalty", away_score);
                                        form.append("match_id", match.id);
                                        // form.append("user_id", $rootScope.user_id);
                                        $.ajax({
                                            url: url,
                                            type: 'POST',
                                            cache: false,
                                            data: form,
                                            async: false,
                                            processData: false,
                                            mimeType: "multipart/form-data",
                                            contentType: false,
                                            headers: {	
                                                'x-user-id': $rootScope.user_id,
                                                'x-user-email': $rootScope.user_name
                                            },
                                            timeout: 0,
                                            error: function (err) { },
                                            success: function (response) {
                                                // console.log("reposnse" + response.status);
                                                if (response.status == 'OK') {
                                                    tableLeagueMatches.ajax.reload();
                                                    dialogRef.close();
                                                }else{
                                                    response = JSON.parse(response);
                                                    BootstrapDialog.show({
                                                        title: response.status,
                                                        type: BootstrapDialog.TYPE_WARNING,
                                                        message: response.message
                                                    });
                                                }

                                            }
                                        });

                                    }
                                }
                            }
                            ]
                        });
                    }

                },
                error: function (xhr, status, error) { },
            },
            table: '#tblLeagueMatchesEvents_' + match_id,
            formOptions: {
                main: {
                    onBlur: 'none'
                }
            },
            i18n: {
                create: {
                    button: "New",
                    title: "Add new event",
                    submit: "Create",
                },
                edit: {
                    button: "Edit",
                    title: "Edit event",
                    submit: "Save"
                },
                remove: {
                    button: "Delete",
                    title: "Delete event",
                    submit: "Delete"
                },
                error: {
                    system: "System error, please contact administrator."
                },
            },
            fields: [{
                label: "Player Name",
                name: "matches_detail.player_id",
                type: "select2"
            },
            {
                label: "Team",
                name: "matches_detail.team_id",
                type: "select",
            },
            {
                label: "Type",
                name: "matches_detail.type",
                type: "select"
            },
            {
                label: "Time",
                name: "matches_detail.time",
                attr: {
                    type: 'number'
                }
            },
            {
                label: "Note",
                name: "matches_detail.note",
            },
            {
                label: "match_id",
                name: "matches_detail.match_id",
                def: match_id,
                type: 'hidden',
            },
            {
                label: "User ID",
                name: "matches_detail.user_create_id",
                def: $rootScope.user_id,
                type: 'hidden',
            }
            ]
        })
        var tableLeagueMatchesEvent = $('#tblLeagueMatchesEvents_' + match_id).DataTable({
            dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
            stateSave: false,
            deferRender: true,
            ajax: {
                url: SERVER_PATH + "league/getmatchevents",
                type: 'POST',
                data: {
                    'match_id': match_id,
                    'home_team_id': home_team_id,
                    'away_team_id': away_team_id
                },
                headers: {	
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name
                },
                dataType: 'json',
                complete: function (response) {
                    console.log(response);
                },
                error: function (xhr, status, error) { },
            },
            language: {
                paginate: {
                    previous: '<i class="fa fa-chevron-left"></i>',
                    next: '<i class="fa fa-chevron-right"></i>'
                }
            },
            columnDefs: [{
                "visible": false
            }],
            columns: [{
                data: null,
                render: function (data, type, row) {
                    var other_name = row.players.other_name == null ? '_' : row.players.other_name;
                    var surname = row.players.surname == null ? '_' : row.players.surname;
                    return surname + ' ' + other_name;
                }
            },
            {
                data: 'teams.name'
            },
            {
                data: 'matches_detail.type'
            },
            {
                data: 'matches_detail.time'
            },
            {
                data: null,
                render: function (data, type, row) {
                    return row.parens.surname + ' ' + row.parens.other_name
                }
            }, {
                data: 'matches_detail.note'
            },
            ],
            select: {
                style: 'single',
            },
            buttons: [{
                extend: "create",
                editor: editorLeagueMatchesEvent
            },
            {
                extend: "edit",
                editor: editorLeagueMatchesEvent
            },
            {
                extend: 'remove',
                editor: editorLeagueMatchesEvent,
                text: 'Delete'
            },
            {
                extend: 'colvis',
                text: 'Columns'
            }
            ]
        });
        editorLeagueMatchesEvent.on('preOpen', function () {
            // alert('preOpen');
            $('.modal-backdrop').hide();
            $('.bootstrap-dialog').hide();
            $('body').removeClass('modal-open-custom');
        });
        editorLeagueMatchesEvent.on('close', function () {
            // alert('preOpen');
            tableLeagueMatchesEvent.ajax.reload();
            tableLeagueMatches.ajax.reload();
            $('.modal-backdrop').show();
            $('.bootstrap-dialog').show();
            $('body').addClass('modal-open-custom');
        });
        editorLeagueMatchesEvent.on('initCreate', function () {
            var value = editorLeagueMatchesEvent.field('matches_detail.team_id').val();
            editorLeagueMatchesEvent.enable('matches_detail.user_create_id');
            editorLeagueMatchesEvent.field('matches_detail.type').show();
            editorLeagueMatchesEvent.field('matches_detail.team_id').show();
            $.ajax({
                url: SERVER_PATH + "league/getallplayerinteam",
                headers: {	
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name
                },
                data: {
                    team_id: value
                },
                error: function () {

                },
                dataType: 'jsonp',
                complete: function (ajax) {
                    var data = JSON.parse(ajax.responseText);
                    editorLeagueMatchesEvent.field('matches_detail.player_id').update(data.info);
                },
                error: function (data) { },
                type: 'POST'
            });
        });
        editorLeagueMatchesEvent.on('initEdit', function () {
            editorLeagueMatchesEvent.disable('matches_detail.user_create_id');
            editorLeagueMatchesEvent.field('matches_detail.type').hide();
            editorLeagueMatchesEvent.field('matches_detail.team_id').hide();
            var value = editorLeagueMatchesEvent.field('matches_detail.team_id').val();
            $.ajax({
                url: SERVER_PATH + "league/getallplayerinteam",
                headers: {	
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name
                },
                data: {
                    team_id: value
                },
                error: function () {

                },
                dataType: 'jsonp',
                complete: function (ajax) {
                    var data = JSON.parse(ajax.responseText);
                    editorLeagueMatchesEvent.field('matches_detail.player_id').update(data.info);
                },
                error: function (data) { },
                type: 'POST'
            });
        });
        $(editorLeagueMatchesEvent.field('matches_detail.team_id').input()).on('change', function (e, d) {
            if (!d || !d.editor) {
                var value = editorLeagueMatchesEvent.field('matches_detail.team_id').val();
                $.ajax({
                    url: SERVER_PATH + "league/getallplayerinteam",
                    headers: {	
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name
                    },
                    data: {
                        team_id: value
                    },
                    error: function () { },
                    dataType: 'jsonp',
                    complete: function (ajax) {
                        var data = JSON.parse(ajax.responseText);
                        editorLeagueMatchesEvent.field('matches_detail.player_id').update(data.info);
                    },
                    error: function (data) { },
                    type: 'POST'
                });
                editorLeagueMatchesEvent.field('matches_detail.player_id').val('');
            }
        });
    }
})