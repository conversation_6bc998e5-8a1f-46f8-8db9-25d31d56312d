<style>
  #refundModal h1,
  #refundModal h2,
  #refundModal h3,
  #refundModal h4,
  #refundModal h5,
  #refundModal h6,
  #refundModal label,
  #refundModal p,
  #refundModal input {
    margin: 0;
    padding: 0;
    font-family: "Poppins", serif;
    line-height: 1;
  }

  /* Show error messages on invalid fields */
  #refundModal input:invalid~.invalid-feedback,
  #refundModal textarea:invalid~.invalid-feedback,
  #refundModal select:invalid~.invalid-feedback {
    display: block;
  }

  #refundModal input:valid~.valid-feedback,
  #refundModal textarea:valid~.valid-feedback,
  #refundModal select:valid~.valid-feedback {
    display: block;
  }

  #refundModal .sub-title {
    color: #6F6F6F;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 18px;
  }

  .form-check-input {
    border-radius: 4px;
    border: 1.5px solid #4B465C;
    width: 18px;
    height: 18px;
  }

  .form-check {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  #userSelection .form-check:not(:last-child) {
    margin-bottom: 12px;
  }

  #userSelection input[type="checkbox"] {
    accent-color: #F83A42 !important;
  }

  .tab-content.active {
    display: block;
  }

  .tab-content {
    display: none;
  }

  .modal-tab.active {
    background-color: #fff !important;
    border-top-left-radius: 6px !important;
    border-bottom-left-radius: 6px !important;
    font-weight: 600 !important;
  }
</style>

<div class="row" id="tab-page">
  <div class="row">
    <div class="col-lg-12">
      <ol class="breadcrumb">
        <li>Events</li>
        <li><a data-match-route="/events/{{normalizedType}}" href="#/events/{{normalizedType}}">{{event_type}}</a></li>
        <li>{{event_name}}</li>
        <li class="active"><span>Payments</span></li>
      </ol>
    </div>
  </div>
</div>

<div class="row">
  <div class="col-lg-12">
    <div class="clearfix">
      <h1 class="pull-left">Payments</h1>
      <hr />
    </div>
  </div>
</div>
<form role="form">
  <div class="row">
    <div class="form-group col-lg-3">
      <label>Date range</label>
      <div class="input-group">
        <span class="input-group-addon"><i class="fa fa-calendar-o"></i></span>
        <input type="text" name="dateFilter" class="form-control" />
      </div>
    </div>
    <div class="form-group col-lg-3">
      <label>Filter by type of payment</label>
      <div id="payment-type_filter"></div>
    </div>
  </div>
</form>

<div class="row">
  <div class="col-lg-12">
    <div class="main-box clearfix">
      <div class="main-box-body clearfix">
        <div class="table-responsive">
          <table id="paymentTable_{{event_id}}" class="table table-striped table-bordered table-hover" cellspacing="0"
            width="100%">
            <thead>
              <tr>
                <th>Order ID</th>
                <th>Parent name</th>
                <th>Parent phone</th>
                <th>Parent email</th>
                <th>Transaction ID (AsiaPay)</th>
                <th>Transaction date/time</th>
                <th>Total paid (HKD)</th>
                <th>Status</th>
                <th>Payment type</th>
                <th>Details</th>
              </tr>
            </thead>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- <div class="modal fade" id="refundModal" tabindex="-1" aria-labelledby="refundModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header" style="display: flex; justify-content: flex-start; width: 100%; align-items: center;">
        <div class="modal-title">
          <h5 id="refundModalLabel">Refund</h5>
          <span class="sub-title">Select user to refund and edit shipping fee (if desired):</span>
        </div>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"
          style="width: 24px; height: 24px; padding: 0; margin: 0; margin-left: auto; background: inherit; border: 0px;" ng-click="closeModal()">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
            stroke="currentColor" class="size-6">
            <path stroke-linecap="round" stroke-linejoin="round"
              d="m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"></path>
          </svg>
        </button>
      </div>
      <div class="modal-body" style="padding: 12px;">
        <form id="refundForm" novalidate class="ng-pristine" data-invoice-id="40351">
          <div id="userSelection">
          </div>
          <div class="text-danger ng-binding ng-hide" ng-if="validationMessages.selectedUsers">
            {{validationMessages.selectedUsers}}</div>
          <div class="mb-3">
            <label for="reason" class="form-label fw-bold">Reason for Refund *</label>
            <textarea class="form-control" id="reason" rows="3" required ng-model="formData.reason" value="Please enter reason"></textarea>
            <div class="text-danger ng-binding" ng-if="validationMessages.reason">{{validationMessages.reason}}</div>
          </div>
          <div class="mb-3">
            <label class="form-label fw-bold">Do you want to cancel registration? *</label>
            <div class="form-check">
              <input class="form-check-input" type="radio" name="cancelRegistration" id="cancelRegYes" value="Yes"
                required ng-model="formData.cancelRegistration">
              <label class="form-check-label" for="cancelRegYes">Yes</label>
            </div>
            <div class="form-check">
              <input class="form-check-input" type="radio" name="cancelRegistration" id="cancelRegNo" value="No"
                required ng-model="formData.cancelRegistration">
              <label class="form-check-label" for="cancelRegNo">No</label>
            </div>
            <div class="text-danger ng-binding" ng-if="validationMessages.cancelRegistration">
              {{validationMessages.cancelRegistration}}</div>
          </div>
          <div class="mb-3">
            <label class="form-label fw-bold">Do you want to cancel shipping? *</label>
            <div class="form-check">
              <input class="form-check-input" type="radio" name="cancelShipping" id="cancelShipYes" value="Yes" required
                ng-model="formData.cancelShipping">
              <label class="form-check-label" for="cancelShipYes">Yes</label>
            </div>
            <div class="form-check">
              <input class="form-check-input" type="radio" name="cancelShipping" id="cancelShipNo" value="No" required
                ng-model="formData.cancelShipping">
              <label class="form-check-label" for="cancelShipNo">No</label>
            </div>
            <div class="text-danger ng-binding" ng-if="validationMessages.cancelShipping">
              {{validationMessages.cancelShipping}}</div>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" ng-click="closeModal()">Cancel</button>
        <button type="button" class="btn btn-danger" ng-click="submitRefund()">Submit</button>
      </div>
    </div>
  </div>
</div> -->

<div class="modal fade" id="refundModal" tabindex="-1" aria-labelledby="refundModalLabel"
  style="font-family: Arial, sans-serif; color: #333;">
  <div class="modal-dialog">
    <div class="modal-content"
      style="border-radius: 10px; overflow: hidden; box-shadow: 0px 4px 15px rgba(0, 0, 0, 0.2);">
      <div class="modal-header"
        style="display: flex; justify-content: space-between; align-items: center; background-color: #f5f5f5; padding: 15px;">
        <div class="modal-title" style="flex-grow: 1;">
          <h5 id="refundModalLabel" style="margin: 0; font-size: 18px; font-weight: bold;">Refund</h5>
          <span class="sub-title"
            style="font-size: 12px; font-style: normal; font-weight: 400; line-height: 18px;color: #6F6F6F; font-weight: 600;">Select
            user to refund and edit shipping fee (if
            desired):</span>
        </div>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"
          style="width: 24px; height: 24px; background: inherit; border: none; padding: 0; display: flex; align-items: center; justify-content: center;"
          ng-click="closeModal()">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
            stroke="currentColor" style="width: 20px; height: 20px; color: #333;">
            <path stroke-linecap="round" stroke-linejoin="round"
              d="m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
          </svg>
        </button>
      </div>
      <div class="modal-body" style="padding: 20px;">
        <form id="refundForm" novalidate class="ng-pristine" data-invoice-id="40351">
          <div id="userSelection"></div>
          <div class="text-danger ng-binding" ng-if="checkValidation('selectedUsers')"
            style="font-size: 14px; margin-bottom: 10px;">
            {{validationMessages.selectedUsers}}
          </div>
          <div class="mb-3" style="margin-top: 18px;">
            <label for="reason" class="form-label fw-bold"
              style="font-size: 13px; font-weight: bold;margin-bottom: 8px;">Reason for Refund
              *</label>
            <textarea class="form-control" id="reason" rows="3" required ng-model="formData.reason"
              style="border-radius: 5px; padding: 10px; font-size: 14px;"></textarea>
            <div class="text-danger ng-binding" ng-if="validationMessages.reason"
              style="font-size: 14px; margin-top: 5px;">{{validationMessages.reason}}</div>
          </div>
          <div class="mb-3" style="margin-top: 18px;">
            <label class="form-label fw-bold" style="font-size: 13px; font-weight: bold;">Do you want to cancel
              registration? *</label>
            <div style="display: flex;flex-direction: column;gap: 10px;align-items: flex-start;margin-top: 8px;">
              <div class="form-check" style="display: flex; gap: 5px;">
                <input class="form-check-input" type="radio" name="cancelRegistration" id="cancelRegYes" value="Yes"
                  ng-model="formData.cancelRegistration" style=" width: 16px; height: 16px; ">
                <label class="form-check-label" for="cancelRegYes" style="font-size: 12x;">Yes</label>
              </div>
              <div class="form-check" style="display: flex; gap: 5px;">
                <input class="form-check-input" type="radio" name="cancelRegistration" id="cancelRegNo" value="No"
                  ng-model="formData.cancelRegistration" style=" width: 16px; height: 16px; " checked>
                <label class="form-check-label" for="cancelRegNo" style="font-size: 12px;">No</label>
              </div>
            </div>
            <div class="text-danger ng-binding" ng-if="validationMessages.cancelRegistration"
              style="font-size: 14px; margin-top: 5px;">{{validationMessages.cancelRegistration}}</div>
          </div>
          <div class="mb-3" style="margin-top: 18px;">
            <label class="form-label fw-bold" style="font-size: 13px; font-weight: bold;">Do you want to cancel
              shipping? *</label>
            <div style="display: flex;flex-direction: column;gap: 10px;align-items: flex-start;margin-top: 8px;">
              <div class="form-check" style="display: flex; gap: 5px;">
                <input class="form-check-input" type="radio" name="cancelShipping" id="cancelShipYes" value="Yes"
                  ng-model="formData.cancelShipping" style=" width: 16px; height: 16px; ">
                <label class="form-check-label" for="cancelShipYes" style="font-size: 12px;">Yes</label>
              </div>
              <div class="form-check" style="display: flex; gap: 5px;">
                <input class="form-check-input" type="radio" name="cancelShipping" id="cancelShipNo" value="No"
                  ng-model="formData.cancelShipping" style=" width: 16px; height: 16px; " checked>
                <label class="form-check-label" for="cancelShipNo" style="font-size: 12px;">No</label>
              </div>
            </div>
          </div>
        </form>
      </div>
      <div class="modal-footer" style="padding: 15px; background-color: #f9f9f9; border-top: 1px solid #ddd;">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" ng-click="closeModal()"
          style="padding: 8px 16px; font-size: 14px;">Cancel</button>
        <button type="button" class="btn btn-danger" ng-click="submitRefund()"
          style="padding: 8px 16px; font-size: 14px;">Submit</button>
      </div>
    </div>
  </div>
</div>

<div class="modal fade" id="paymentDetail"
  style="position: fixed;top: 50%;left: 50%;transform: translate(-50%, -50%);background-color: rgb(255, 255, 255);border-radius: 10px;box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 10px;min-height: 100vh;display: none;">

  <div class="modal-header"
    style="display: flex;/* justify-content: space-between; */padding: 18px;border-bottom: 1px solid rgba(170, 170, 170, 0.67);background: #F9FAFB;/* width: 100%; */">
    <h2 style="font-family: 'Public Sans'; font-size: 15px; font-weight: 500; margin: 0;">Payment Details
    </h2>
    <button class="close-button" style="background: none;border: none;cursor: pointer;margin-left: auto;"
      ng-click="closePaymentDetail()">
      <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18" fill="none">
        <path
          d="M14.6594 13.4655C14.8179 13.624 14.907 13.839 14.907 14.0631C14.907 14.2873 14.8179 14.5023 14.6594 14.6608C14.5009 14.8193 14.2859 14.9084 14.0618 14.9084C13.8376 14.9084 13.6226 14.8193 13.4641 14.6608L8.99997 10.1953L4.53442 14.6594C4.37591 14.8179 4.16093 14.907 3.93677 14.907C3.7126 14.907 3.49762 14.8179 3.33911 14.6594C3.1806 14.5009 3.09155 14.2859 3.09155 14.0617C3.09155 13.8376 3.1806 13.6226 3.33911 13.4641L7.80466 8.99994L3.34052 4.53439C3.18201 4.37588 3.09296 4.1609 3.09296 3.93674C3.09296 3.71257 3.18201 3.49759 3.34052 3.33908C3.49902 3.18057 3.71401 3.09152 3.93817 3.09152C4.16234 3.09152 4.37732 3.18057 4.53583 3.33908L8.99997 7.80463L13.4655 3.33838C13.624 3.17987 13.839 3.09082 14.0632 3.09082C14.2873 3.09082 14.5023 3.17987 14.6608 3.33838C14.8193 3.49689 14.9084 3.71187 14.9084 3.93603C14.9084 4.1602 14.8193 4.37518 14.6608 4.53369L10.1953 8.99994L14.6594 13.4655Z"
          fill="black"></path>
      </svg>
    </button>
  </div>

  <div class="modal-body" style="padding: 24px 12px; font-family: 'Inter', serif; min-height: 100vh;">
    <div class="invoice-detail" style="display: flex; gap: 36px;" id="invoiceDetail">

    </div>
    <div class="modal-tabs"
      style="width: 100%; background-color: #eee; height: 30px; margin-top: 32px; border-radius: 6px; display: flex; padding: 2px;">
      <div class="modal-tab active" ng-click="switchTab('payment-details')" data-tabid="payment-details"
        style="flex: 1; display: flex; justify-content: center; align-items: center; font-size: 14px; font-weight: 400; color: black; border-radius: 6px; padding: 5px; cursor: pointer;">
        Payment Details
      </div>
      <div class="modal-tab" ng-click="switchTab('refund-details')" data-tabid="refund-details"
        style="flex: 1; display: flex; justify-content: center; align-items: center; font-size: 14px; font-weight: 400; color: black; border-radius: 6px; padding: 5px; cursor: pointer;">
        Refund Details
      </div>
    </div>
    <div class="tab-content active" data-tab="payment-details">
      <div class="invoice-detail-items" style="margin-top: 32px;" id="invoiceDetailItems">

      </div>
    </div>
    <div class="tab-content" data-tab="refund-details" id="refundDetails">
    </div>
  </div>
</div>

<script>
  // onclose event for modal
  $('.modal').on('hidden.bs.modal', function () {
      // remove inline style for body element
      $('body').removeAttr('style');
  });
</script>