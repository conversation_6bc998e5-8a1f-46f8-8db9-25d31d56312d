app.controller('plJuniorCtrl', function (user, $scope, $rootScope, $http, seasonService) {
    $scope.user = user;

    $scope.events = [];
    $scope.seasons = [];
    $scope.selectedSeasonId = null;

    $scope.items = [];

    seasonService.loadSeasons().then(function(seasons) {
        $scope.seasons = seasons;
        $scope.selectedSeasonId = seasonService.getSelectedSeasonId();
        if ($scope.selectedSeasonId) {
            getEventPLJs();
        }
    });

    function getEventPLJs() {
        $http({
            method: 'POST',
            url: SERVER_PATH + 'event/getEventPLJ',
            headers: {
                'x-user-id': $rootScope.user_id,
                'x-user-email': $rootScope.user_name,
            },
            data: {
                season_id: $scope.selectedSeasonId
            }
        }).success(function (response) {
            if (response.status == 'OK') {
                $scope.events = response.data;

                $scope.items = [
                    {
                        name: 'Registration',
                        icon: 'fa-user-plus',
                        href: '#/registration-pl-junior/{event.id}',
                        image: 'images/adminpanel-icon/Registrations.png'
                    },
                    {
                        name: 'Payment',
                        icon: 'fa-credit-card',
                        href: '#/payments/pl-junior/{event.id}',
                        image: 'images/adminpanel-icon/Payments.png'
                    },
                    {
                        name: 'Course',
                        icon: 'fa-list-ul',
                        href: '#/event/{event.id}/course',
                        image: 'images/adminpanel-icon/Course.png'
                    },
                    {
                        name: 'Shipping',
                        icon: 'fa-truck',
                        href: '#/shipping-plj/{event.id}',
                        image: 'images/adminpanel-icon/Shipping.png'
                    },
                    {
                        name: 'Messages',
                        icon: 'fa-envelope-o',
                        href: '#/messages/{event.id}',
                        image: 'images/adminpanel-icon/Message.png'
                    },
                    {
                        name: 'Report',
                        icon: 'fa-file-text-o',
                        href: '#/reports/pl_junior/{event.id}',
                        image: 'images/adminpanel-icon/Report.png'
                    },
                ];
            }
        });
    }

    $scope.filterEventsBySeason = function () {
        seasonService.setSelectedSeasonId($scope.selectedSeasonId);
        if ($scope.selectedSeasonId) {
            getEventPLJs();
        }
    };
});
