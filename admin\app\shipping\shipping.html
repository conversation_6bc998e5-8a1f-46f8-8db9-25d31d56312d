<style>
    .shipping_chkbox {
        width: 20px;
        height: 20px;
        border: 1px solid #ccc;
        border-radius: 5px;
        margin: auto;
    }

    .shipping_chkbox :checked {
        width: 20px;
        height: 20px;
        border: 1px solid #ccc;
        border-radius: 5px;
        margin: auto;
    }

    #form-submit-time>button {
        margin: auto;
        text-align: right;
    }

    .modal-header {
        padding: 9px 15px;
        border-bottom: 1px solid #eee;
        background-color: #0480be;
        -webkit-border-top-left-radius: 5px;
        -webkit-border-top-right-radius: 5px;
        -moz-border-radius-topleft: 5px;
        -moz-border-radius-topright: 5px;
        border-top-left-radius: 5px;
        border-top-right-radius: 5px;
        color: white;
    }
</style>
<div class="row" id="tab-page">
    <div class="row">
        <div class="col-lg-12">
            <ol class="breadcrumb">
                <li>Events</li>
                <li><a href="" ng-click="goBack()">Summer Scheme</a></li>
                <li>{{event_name}}</li>
                <li class="active"><span>Shipping</span></li>
            </ol>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-12">
        <div class="clearfix">
            <h1 class="pull-left">Shipping</h1>
            <hr>
        </div>
    </div>
</div>
<form role="form">
    <div class="row">
        <div class="form-group col-lg-3">
            <label >Date range</label>
            <div class="input-group">
                <span class="input-group-addon"><i class="fa fa-calendar-o"></i></span>
                <input type="text" name="dateFilter" class="form-control"  />
            </div>
        </div>
        <div class="form-group col-lg-3" id="shipping-status_filter">
            <label>Filter by status</label>
        </div>
    </div>
</form>
<div class="row">
    <div class="col-lg-12">
        <div class="main-box clearfix">
            <div class="main-box-body clearfix">
                <div class="table-responsive">
                    <table id="shippingTable_{{event_id}}"
                        class="table table-striped table-bordered dt-responsive table-hover" cellspacing="0"
                        width="100%">
                        <thead>
                            <tr>

                                <th>
                                    Parent name
                                </th>
                                <th>
                                    Parent email
                                </th>
                                <th>
                                    Address
                                </th>
                                <th>
                                    City
                                </th>
                                <th>
                                    Province
                                </th>
                                <th>
                                    Postcode
                                </th>
                                <th>
                                    Locker code
                                </th>
                                <th>
                                    Receive phone
                                </th>
                                <th>
                                    Order number
                                </th>
                                <th>
                                    Order id
                                </th>
                                <th>
                                    Order status
                                </th>
                                <th>
                                    Courier tracking number
                                </th>
                                <th>
                                    Invoice number
                                </th>
                                <th>
                                    Shipping Type
                                </th>
                                <th>
                                    Actions
                                </th>
                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal -->
<div class="modal fade" id="changeItemShipping" role="dialog">
    <div class="modal-dialog modal-lg" style="width: 840px; margin: auto">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title">Change Shipping Item</h4>
            </div>
            <div class="modal-body">
                <table id="shipping_item_table" class="table table-striped table-bordered" cellspacing="0" width="100%">
                    <thead>
                        <tr>
                            <th>Product from</th>
                            <th>Product to</th>
                            <th>Product type</th>
                            <th>Send to</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr ng-repeat="item in data_shipping">
                            <td>{{item.product_name}}</td>
                            <td ng-if="item.product_type=='BIB'">
                                <select class="form-control" ng-model="item.product_code"
                                    ng-options="size.value as size.label for size in product_list"
                                    ng-change="changeItem(item)">
                                </select>
                            </td>
                            <td ng-if="item.product_type!='BIB'">{{item.product_code}}</td>
                            <td>{{item.product_type}}</td>
                            <td>{{item.send_to}}</td>
                        </tr>
                    </tbody>
                </table>

                <!-- Add button submit -->
                <button type="button" class="btn btn-primary ml-auto btn-block" name="Submit" ng-click="submitGoods()">
                    Submit
                </button>
            </div>
        </div>
    </div>
</div>