app.controller(
    'productsCtrl',
    function ($scope, $rootScope, $routeParams, $http) {
        $('#page-wrapper').removeClass('nav-small');

        $scope.mainEvents = [];

        $scope.selectedMainEvent = null;

        $scope.childEvents = [];

        $scope.selectedChildEvent = null;

        $scope.isSelected = false;

        jQuery.ajax({
            type: 'POST',
            url: SERVER_PATH + 'product/getAllMainSeasion',
            async: false,
            headers: {	
                'x-user-id': $rootScope.user_id,
                'x-user-email': $rootScope.user_name
            },
            data: {},
            dataType: 'json',
            complete: function (response) {
                var jsonData = JSON.parse(response.responseText);
                $scope.mainEvents = jsonData.info;
                $scope.selectedMainEvent = $scope.mainEvents[0].id;
                getAllChildSeasions();
            },
        });

        $('body').unbind('click');

        $('body').on(
            'click',
            'li[class=active] a[data-toggle=tab]',
            function () {
                console.log($(this).data('target'));
                switch ($(this).data('target')) {
                    case '#tab-tshirt': {
                        initTShirt();
                        break;
                    }
                    case '#tab-ball': {
                        initBall();
                        break;
                    }
                    case '#tab-product': {
                        initProductTable();
                        break;
                    }
                }
            }
        );

        $scope.changeMainEvent = () => {
            getAllChildSeasions();
        };

        $scope.selectEvent = () => {
            $scope.isSelected = true;

            setTimeout(function () {
                initTShirt();

                initBall();
                initProductTable();
            }, 500);
        };

        function getAllChildSeasions() {
            jQuery.ajax({
                type: 'POST',
                url: SERVER_PATH + 'product/getAllChildSeasion',
                async: false,
                headers: {	
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name
                },
                data: {
                    event_id: $scope.selectedMainEvent,
                },
                dataType: 'json',
                complete: function (response) {
                    var jsonData = JSON.parse(response.responseText);

                    $scope.childEvents = jsonData.info;

                    $scope.selectedChildEvent = $scope.childEvents[0].id;
                },
            });
        }

        function initTShirt() {
            if ($.fn.dataTable.isDataTable('#tbTShirt')) {
                $('#tbTShirt').DataTable().destroy();
            }

            productTShirtEditor = new $.fn.dataTable.Editor({
                ajax: {
                    type: 'POST',
                    url: SERVER_PATH + 'product/setTShirt',
                    headers: {	
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name
                    },
                    data: {
                        event_id: $scope.selectedChildEvent,
                    },
                    dataType: 'json',
                    complete: function (response) {},
                    error: function (xhr, status, error) {},
                },
                table: '#tbTShirt',
                formOptions: {
                    main: {
                        onBlur: 'none',
                    },
                },
                i18n: {
                    create: {
                        button: 'Add',
                        title: 'Add Tshirt type',
                        submit: 'Add',
                    },
                    edit: {
                        button: 'Edit',
                        title: 'Edit Tshirt type',
                        submit: 'Save',
                    },
                    remove: {
                        button: 'Delete',
                        title: 'Delete Tshirt type',
                        submit: 'Delete',
                    },
                    error: {
                        system: 'System error, please contact administrator.',
                    },
                },
                fields: [
                    {
                        label: 'Size Name',
                        name: 't_shirts.size_name',
                    },
                    {
                        label: 'Chest Width',
                        name: 't_shirts.chest_width',
                        default: 0,
                    },
                    {
                        label: 'Shirt Length',
                        name: 't_shirts.shirt_length',
                        default: 0,
                    },
                    {
                        label: 'Group Suggestion',
                        name: 't_shirts.group_suggestion',
                    },
                    {
                        label: 't_shirts.event_id',
                        name: 't_shirts.event_id',
                        def: $scope.selectedChildEvent,
                        type: 'hidden',
                    },
                    {
                        label: 'Product Code',
                        name: 't_shirts.product_code',
                        type: 'select2',
                        opts: {
                            placeholder: 'Select a product code',
                        },
                    },
                ],
            });

            productTShirtEditor.on('preOpen', function (e, mode, action) {
                if ($scope.selectedChildEvent.type == EVENT_PL_JUNIOR) {
                    productTShirtEditor
                        .field('t_shirts.group_suggestion')
                        .hide();
                    productTShirtEditor.field('t_shirts.product_code').hide();
                } else {
                    productTShirtEditor
                        .field('t_shirts.group_suggestion')
                        .show();
                    productTShirtEditor.field('t_shirts.product_code').show();
                }
            });

            productTShirt = $('#tbTShirt').DataTable({
                dom: '<"row"<"col-sm-6"B><"col-sm-6"f>>rt<"row"i>',
                stateSave: false,
                deferRender: true,
                ajax: {
                    url: SERVER_PATH + 'product/getTShirt',
                    type: 'POST',
                    headers: {	
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name
                    },
                    data: {
                        event_id: $scope.selectedChildEvent,
                    },
                    dataType: 'json',
                    complete: function (response) {
                        // response = JSON.parse(response.responseText);
                    },
                    error: function (xhr, status, error) {},
                },
                language: {
                    paginate: {
                        previous: '<i class="fa fa-chevron-left"></i>',
                        next: '<i class="fa fa-chevron-right"></i>',
                    },
                },
                columns: [
                    {
                        data: 't_shirts.size_name',
                        sortable: false,
                    },
                    {
                        data: 't_shirts.chest_width',
                        sortable: false,
                    },
                    {
                        data: 't_shirts.shirt_length',
                        sortable: false,
                    },
                    {
                        data: 't_shirts.group_suggestion',
                        sortable: false,
                    },
                    {
                        data: 't_shirts.product_code',
                        sortable: false,
                    },
                ],
                select: {
                    style: 'single',
                    selector: 'td:first-child',
                },
                order: [[1, 'asc']],
                buttons: [
                    {
                        extend: 'create',
                        editor: productTShirtEditor,
                    },
                    {
                        extend: 'edit',
                        editor: productTShirtEditor,
                    },
                    {
                        extend: 'remove',
                        editor: productTShirtEditor,
                        text: 'Delete',
                    },
                    {
                        extend: 'colvis',
                        text: 'Columns',
                    },
                ],
                displayLength: -1,
            });
        }

        function initBall() {
            if ($.fn.dataTable.isDataTable('#tbBall')) {
                $('#tbBall').DataTable().destroy();
            }

            productBallEditor = new $.fn.dataTable.Editor({
                ajax: {
                    type: 'POST',
                    url: SERVER_PATH + 'product/setBallSize',
                    headers: {	
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name
                    },
                    data: {
                        event_id: $scope.selectedChildEvent,
                    },
                    dataType: 'json',
                    complete: function (response) {},
                    error: function (xhr, status, error) {},
                },
                table: '#tbBall',
                formOptions: {
                    main: {
                        onBlur: 'none',
                    },
                },
                i18n: {
                    create: {
                        button: 'Add',
                        title: 'Add Ball type',
                        submit: 'Add',
                    },
                    edit: {
                        button: 'Edit',
                        title: 'Edit Ball type',
                        submit: 'Save',
                    },
                    remove: {
                        button: 'Delete',
                        title: 'Delete Ball type',
                        submit: 'Delete',
                    },
                    error: {
                        system: 'System error, please contact administrator.',
                    },
                },
                fields: [
                    {
                        label: 'Ball size',
                        name: 'size_ball.size',
                        attr: {
                            type: 'number',
                        },
                        default: 0,
                    },
                    {
                        label: 'Group Suggestion',
                        name: 'size_ball.group_suggestion',
                        name: 'groups[].id',
                        type: 'select2',
                        opts: {
                            multiple: true,
                            placeholder: 'Search and select groups...',
                        },
                    },
                    {
                        label: 'Product Code',
                        name: 'size_ball.product_code',
                        type: 'select2',
                        opts: {
                            placeholder: 'Search and select product code...',
                        },
                    },
                ],
            });

            productBallEditor.on('initEdit', function () {
                productBallEditor.field('groups[].id').hide();
            });

            productBallEditor.on('initCreate', function () {
                productBallEditor.field('groups[].id').show();
            });

            productBall = $('#tbBall').DataTable({
                dom: '<"row"<"col-sm-6"B><"col-sm-6"f>>rt<"row"i>',
                stateSave: false,
                deferRender: true,
                ajax: {
                    url: SERVER_PATH + 'product/getBallSize',
                    type: 'POST',
                    headers: {	
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name
                    },
                    data: {
                        event_id: $scope.selectedChildEvent,
                    },
                    dataType: 'json',
                    complete: function (response) {
                        // response = JSON.parse(response.responseText);
                    },
                    error: function (xhr, status, error) {},
                },
                language: {
                    paginate: {
                        previous: '<i class="fa fa-chevron-left"></i>',
                        next: '<i class="fa fa-chevron-right"></i>',
                    },
                },
                columns: [
                    { data: 'size_ball.size' },
                    { data: 'size_ball.group_names' },
                    { data: 'size_ball.product_code' },
                ],
                select: {
                    style: 'single',
                    selector: 'td:first-child',
                },
                order: [[0, 'asc']],
                buttons: [
                    {
                        extend: 'create',
                        editor: productBallEditor,
                    },
                    {
                        extend: 'edit',
                        editor: productBallEditor,
                    },
                    {
                        extend: 'remove',
                        editor: productBallEditor,
                        text: 'Delete',
                    },
                    {
                        extend: 'colvis',
                        text: 'Columns',
                    },
                ],
                displayLength: -1,
            });
        }

        function initProductTable() {
            if ($.fn.dataTable.isDataTable('#tableProduct')) {
                $('#tableProduct').DataTable().destroy();
            }

            productEditor = new $.fn.dataTable.Editor({
                ajax: {
                    type: 'POST',
                    url: SERVER_PATH + 'product/setProduct',
                    headers: {	
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name
                    },
                    data: {
                        event_id: $scope.selectedChildEvent,
                    },
                    dataType: 'json',
                    complete: function (response) {},
                    error: function (xhr, status, error) {},
                },
                table: '#tableProduct',
                formOptions: {
                    main: {
                        onBlur: 'none',
                    },
                },
                i18n: {
                    create: {
                        button: 'Add',
                        title: 'Add product',
                        submit: 'Add',
                    },
                    edit: {
                        button: 'Edit',
                        title: 'Edit product',
                        submit: 'Save',
                    },
                    remove: {
                        button: 'Delete',
                        title: 'Delete product',
                        submit: 'Delete',
                    },
                    error: {
                        system: 'System error, please contact administrator.',
                    },
                },
                fields: [
                    {
                        label: 'SKU',
                        name: 'products.sku_no',
                        type: 'select2',
                        opts: {
                            placeholder: 'Select a SKU number',
                        },
                    },
                    {
                        label: 'Name',
                        name: 'products.name',
                    },
                    {
                        label: 'Category',
                        name: 'products.category',
                        type: 'select2',
                        opts: {
                            placeholder: 'Select category'
                        },
                    },
                    {
                        label: 'Group',
                        name: 'products.group_ids',
                        type: 'select2',
                        opts: {
                            placeholder: 'Select group',
                            multiple: true,
                        },
                    },
                    {
                        label: 'Ordered',
                        name: 'products.ordered_qty',
                    },
                    {
                        label: 'products.event_id',
                        name: 'products.event_id',
                        def: $scope.selectedChildEvent,
                        type: 'hidden',
                    },
                ],
            });

            productEditor.dependent('products.category', function (val) {
                return val != 'Book'
                    ? { hide: ['products.group_ids'] }
                    : { show: ['products.group_ids'] };
            });

            productProduct = $('#tableProduct').DataTable({
                dom: '<"row"<"col-sm-6"B><"col-sm-6"f>>rt<"row"i>',
                stateSave: false,
                deferRender: true,
                ajax: {
                    url: SERVER_PATH + 'product/getProduct',
                    type: 'POST',
                    headers: {	
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name
                    },
                    data: {
                        event_id: $scope.selectedChildEvent,
                    },
                    dataType: 'json',
                    complete: function (response) {
                        // response = JSON.parse(response.responseText);
                    },
                    error: function (xhr, status, error) {},
                },
                language: {
                    paginate: {
                        previous: '<i class="fa fa-chevron-left"></i>',
                        next: '<i class="fa fa-chevron-right"></i>',
                    },
                },
                columns: [
                    {
                        data: 'products.sku_no',
                    },
                    {
                        data: 'products.name',
                    },
                    {
                        data: 'products.group_ids',
                    },
                    {
                        data: 'products.category',
                    },
                    {
                        data: 'products.ordered_qty',
                    },
                ],
                select: {
                    style: 'single',
                    selector: 'td:first-child',
                },
                order: [[2, 'asc']],
                buttons: [
                    {
                        extend: 'create',
                        editor: productEditor,
                    },
                    {
                        extend: 'edit',
                        editor: productEditor,
                    },
                    {
                        extend: 'remove',
                        editor: productEditor,
                        text: 'Delete',
                    },
                    {
                        extend: 'colvis',
                        text: 'Columns',
                    },
                ],
                displayLength: -1,
            });
        }
    }
);
