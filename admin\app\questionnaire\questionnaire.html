<div class="row">
    <div class="row">
        <div class="col-lg-12">
            <ol class="breadcrumb">
                <li>Others</li>
                <li class="active">Questionaire</li>
            </ol>
        </div>
    </div>
</div>
<div class="row" ng-init="init()">
    <div class="col-lg-12">
        <div class="clearfix">
            <h1 class="pull-left">Questionaire</h1>
        </div>
    </div>
</div>

<form role="form">
    <div class="row">
        <div class="form-group col-lg-3">
            <h3 style="margin-top: 0px !important;"><span>Filter by season</span></h3>
            <select id="selectedSession" style="width:300px" id="sel2" ng-model="selectedSession"
                ng-options="session.name for session in sessionLists" ng-change="changeSession()">
            </select>
        </div>
        <div class="form-group col-lg-3" style="margin-left: 20px;">
            <h3 style="margin-top: 0px !important"><span>Filter by event</span></h3>
            <select id="selectedEvent" style="width:300px" id="sel2" ng-model="selectedEvent"
                ng-options="event.name for event in eventLists | filterBySeason : selectedSession"
                ng-change="changeEvent()">
            </select>
        </div>
    </div>
</form>
<div class="row">
    <div class="col-lg-12" id="table-content">
    </div>
</div>
<script>

    $("#selectedSession").select2({
        width: 'resolve' 
    });

    $("#selectedEvent").select2({
        width: 'resolve' 
    });
</script>

