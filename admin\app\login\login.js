var app = angular.module('hkjflApp', []);
app.controller('loginCtrl', function($scope, $rootScope, $http) {
	
	// Le Quyet Tien - 22/06/2016	
	// login
	$scope.loginData = {};
	$scope.login = false;
	$scope.login = function(form) {
		
		if (typeof $scope.loginData.email === 'undefined' || typeof $scope.loginData.password === 'undefined') {
			BootstrapDialog.show({
              	title: 'ERROR',
                type: BootstrapDialog.TYPE_WARNING,
                message: "Email or password can not be empty!"
            });
			    // alert("Email or password can not be empty!");
		
		} else if ($scope.loginData.email != '' && $scope.loginData.password != '' 
							&& $scope.loginData.email != 'undefined') {
			$http({
				method  : 'POST',
				url     : SERVER_PATH + "user/login",
				data    : $.param($scope.loginData),
				headers : { 'Content-Type': 'application/x-www-form-urlencoded' }
			})
			.success(function(response) {				
			    if (response.status == "OK") {
					if (response.info.two_factor_auth === '0' || DEVELOPMENT_ENVIRONMENT) {
						location.href='index.html?id=' + response.info.base64;
					} else {
						verify2FA(response.info.id, response.info.base64);
					}
			    } else {
                	// alert(response.message);
                	BootstrapDialog.show({
                		title: 'ERROR',
                		type: BootstrapDialog.TYPE_WARNING,
                		message: response.message
            		});
			    }
            });
		} else {
			
			BootstrapDialog.show({
              	title: 'ERROR',
                type: BootstrapDialog.TYPE_WARNING,
                message: "Email or password can not be empty!"
            });
			// alert("Email or password can not be empty!");
		}
	};

	function verify2FA(user_id, base64) {
		var $textAndPic = $('<div></div>');
		$textAndPic.append('Please enter the code in the <b>Google Authenticator</b> App into the below input box to complete login process. <br />');
		$textAndPic.append('<br />');
		$textAndPic.append('<input type="text" id="txtCode" placeholder="Enter Code" class="form-control">');

		BootstrapDialog.show({
			title: 'Two Factor Authentication',
			message: $textAndPic,
			size: BootstrapDialog.SIZE_NORMAL,
			cssClass: 'verify-modal',
			closable: true,
			closeByBackdrop: false,
			closeByKeyboard: false,
			onshown: function(dialog) {
				// Get the input field
				var input = document.getElementById("txtCode");

				// Execute a function when the user releases a key on the keyboard
				input.addEventListener("keyup", function(event) {
				// Number 13 is the "Enter" key on the keyboard
				if (event.keyCode === 13) {
					// Cancel the default action, if needed
					event.preventDefault();
					// Trigger the button element with a click
					document.getElementById("btnSubmit").click();
				}
				});
			},
			buttons: [{
				label: 'OK',
				id: 'btnSubmit',
				action: function (dialogRef) {
					$scope.one_code = dialogRef.getModalBody().find('input').val();
					$http({
							method: 'POST',
							url: SERVER_PATH + "user/verify2FA",
							data: 'user_id=' + user_id + '&one_code=' + $scope.one_code,
							headers: {
								'Content-Type': 'application/x-www-form-urlencoded'
							}
						})
						.success(function (response) {
							console.log(response);
							if (response.status === 'OK') {
								location.href='index.html?id=' + base64;
								dialogRef.close();
							} else {
								BootstrapDialog.show({
									title: 'ERROR',
									type: BootstrapDialog.TYPE_WARNING,
									message: response.message
								});
							}
						});
				}
			}]
		});
	}
})
