<div class="row" id="tab-page">
    <div class="row">
        <div class="col-lg-12">
            <ol class="breadcrumb">
                <li>Events</li>
                <li><a data-match-route="/events/{{normalizedType}}" href="#/events/{{normalizedType}}">{{event_type}}</a></li>
                <li>{{event_name}}</li>
                <li class="active"><span>Teams</span></li>
            </ol>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-12">
        <div class="clearfix">
            <!-- <h1 class="pull-left" ng-if="selectedEventType!='District'">Course</h1> -->
            <h1 class="pull-left">Team</h1>
        </div>
    </div>
</div>
<form role="form">
    <div class="row">
        <!-- Location -->
        <div class="form-group col-lg-4" id="age-group-content"></div>
    </div>
</form>
<div>
    <div class="row">
        <div class="col-lg-12">
            <div class="main-box clearfix">
                <div class="main-box-body clearfix">
                    <div class="table-responsive">
                        <table class="table table-striped table-bordered table-hover team_district_tbl" cellspacing="0"
                            width="100%">
                            <thead>
                                <!-- ExtractText() from framework\admin\readpdf\readpdf.js -->
                                <tr>
                                    <th>Name</th>
                                    <th>Age group</th>
                                    <th>District</th>
                                    <th>Supervisors</th>
                                    <th>Quota</th>
                                    <th>Is main team</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<style>
    .onoffswitch-inner:before {
        content: "Yes";
    }

    .onoffswitch-inner:after {
        content: "No";
    }
</style>