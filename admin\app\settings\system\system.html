<div class="row" id="tab-page">
    <div class="row">
        <div class="col-lg-12">
            <ol class="breadcrumb">
                <li>Setup</li>
                <li class="active"><span>System</span></li>
            </ol>
        </div>
    </div>
</div>
<div class="row">
	<div class="col-lg-12">
		<div class="clearfix">
			<h1 class="pull-left">System</h1>
		</div>
	</div>
</div>

<div class="row">
	<div class="col-lg-12">
		<div class="main-box clearfix">
			<div class="main-box-body clearfix">
				<div class="table-responsive">
					<table id="table" class="table table-striped table-bordered table-hover" cellspacing="0"
						width="100%">
						<thead>
							<tr>
								<th>ID</th>
								<th>Name</th>
								<th>Value</th>
								<th>Description</th>
								<th>Type</th>
							</tr>
						</thead>
					</table>
				</div>
			</div>
		</div>
	</div>
</div>

<script type="text/javascript" language="javascript" class="init">
	$(document).ready(function () {

		// datatable & editor master
		var table;

		user_id = localStorage.getItem('hkjflApp.user_id');
		user_name = localStorage.getItem('hkjflApp.user_name');

		var editor = new $.fn.dataTable.Editor({
			ajax: {
				type: 'POST',
				url: SERVER_PATH + "setting/getSettingsSystem",
				headers: {	
                    'x-user-id': user_id,
                    'x-user-email': user_name
                },
				data: {},
				dataType: 'json',
				complete: function (response) {
					var jsonData = JSON.parse(response.responseText);
					// --- may need to reload
					if (DEVELOPMENT_ENVIRONMENT) console.log('status = ' + jsonData.status);
					if (jsonData.status == 'OK') {
						table.ajax.reload();
					}
				},
				error: function (xhr, status, error) { },
			},
			table: "#table",
			formOptions: {
				main: {
					onBlur: 'none',
					focus: 'settings.value'
				}
			},
			i18n: {
				edit: {
					button: "Edit",
					title: "Edit",
					submit: "Update"
				},
				error: {
					system: "System error, please contact administrator."
				},
			},
			fields: [
				{
					label: "Name",
					name: "settings.name",
					type: "readonly"
				},
				{
					label: "Value",
					name: "settings.value",
				},
				{
					label: "Description",
					name: "settings.description",
				},
			]
		});

		var table = $('#table').DataTable({
			dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
			stateSave: false,
			deferRender: true,
			ajax: {
				url: SERVER_PATH + "setting/setSettingsSystem",
				type: 'POST',
				headers: {	
                    'x-user-id': user_id,
                    'x-user-email': user_name
                },
				data: {},
				dataType: 'json',
				complete: function (response) { },
				error: function (xhr, status, error) { },
			},
			language: {
				paginate: {
					previous: '<i class="fa fa-chevron-left"></i>',
					next: '<i class="fa fa-chevron-right"></i>'
				}
			},
			rowGroup: {
				order: [['settings.type', 'asc']],
				dataSrc: function (row) {
					return "Type: " + row.settings.type;
				}
			},
			columnDefs: [
				{
					"targets": [0],
					"visible": false
				}
			],
			columns: [
				{
					data: 'settings.id',
					sortable: false
				},
				{
					data: 'settings.name',
					sortable: false
				},
				{
					data: 'settings.value',
					className: "center",
					sortable: false
				},
				{
					data: 'settings.description',
					sortable: false
				},
				{
					data: 'settings.type',
					sortable: false
				},
			],
			select: {
				style: 'single',
				selector: 'td:first-child'
			},
			order: [
				[4, 'DESC']
			],
			lengthMenu: [
				[10, 25, 50, 100, -1],
				[10, 25, 50, 100, "All"]
			],
			buttons: [
				{
					extend: "edit",
					editor: editor
				},
				{
					extend: 'colvis',
					text: 'Columns'
				}
			]
		});

		$('button.buttons-edit').on('click', function () {
			editor.clear('settings.value');
			var data = table.rows({ selected: true }).data()[0]
			var name = data.settings.name;

			if (name != 'Show self-pickup option' && name != 'Allow substitute coach' && name != 'Allow change to shipping' && name != 'Show home option') {
				if (name == 'Extra class registration days') {
					editor.add({
						label: "Value",
						name: "settings.value",
						type: 'select',
						options: ['1', '2', '3', '4', '5', '6', '7']
					});
				} else
					editor.add({
						label: "Value",
						name: "settings.value",
					});
			}
			else {
				editor.add({
					label: "Value",
					name: "settings.value",
					type: 'select',
					options: ['Yes', 'No']
				});
			}
		});

		$('#table').on('click', 'tbody td:not(:first-child)', function (e) {
			if (editor.display() == 'inline' && editor.displayed() == 'settings.value')
				return;
			var row = $(this).closest('tr');
			var data = $('#table').DataTable().row(row).data();
			var name = data.settings.name;
			editor.clear('settings.value');

			if (name != 'Show self-pickup option' && name != 'Allow substitute coach' && name != 'Allow change to shipping' && name != 'Show home option') {
				if (name == 'Extra class registration days') {
					editor.add({
						label: "Value",
						name: "settings.value",
						type: 'select',
						options: ['1', '2', '3', '4', '5', '6', '7']
					});
				} else
					editor.add({
						label: "Value",
						name: "settings.value",
					});
			}
			else {
				editor.add({
					label: "Value",
					name: "settings.value",
					type: 'select',
					options: ['Yes', 'No']
				});
			}
			editor.inline(this);
		});
	});
</script>