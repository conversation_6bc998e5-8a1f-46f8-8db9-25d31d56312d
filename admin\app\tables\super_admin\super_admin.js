app.controller(
    'superAdminCtrl',
    function ($scope, $rootScope, $routeParams, $http) {
        $('#page-wrapper').removeClass('nav-small');

        var editor = new $.fn.dataTable.Editor({
            ajax: {
                type: 'POST',
                url: SERVER_PATH + 'paren/setSuperAdmins',
                data: {
                    user_id: $rootScope.user_id,
                },
                headers: {
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name,
                },
                dataType: 'json',
                complete: function (response) {
                    var jsonData = JSON.parse(response.responseText);
                    // --- may need to reload
                    if (DEVELOPMENT_ENVIRONMENT)
                        console.log('status = ' + jsonData.status);
                    if (jsonData.status == 'OK') {
                        if (DEVELOPMENT_ENVIRONMENT)
                            console.log('Before reload');
                        table.ajax.reload();
                    }
                },
                error: function (xhr, status, error) {},
            },
            table: '#tableSuperAdmin',
            formOptions: {
                main: {
                    onBlur: 'none',
                },
            },
            i18n: {
                create: {
                    button: 'New',
                    title: 'Create new super admin',
                    submit: 'Create',
                },
                edit: {
                    button: 'Edit',
                    title: 'Edit super admin',
                    submit: 'Save',
                },
                remove: {
                    button: 'Delete',
                    title: 'Delete super admin',
                    submit: 'Delete',
                    confirm: {
                        _: 'Are you sure you want to delete these super admins?',
                        1: 'Are you sure you want to delete this super admin?',
                    },
                },
                error: {
                    system: 'System error, please contact super admin.',
                },
            },
            fields: [
                {
                    label: 'Surname:',
                    name: 'parens.surname',
                },
                {
                    label: 'Other name',
                    name: 'parens.other_name',
                },
                {
                    label: 'Email',
                    name: 'parens.email',
                },
                {
                    name: 'parens.type',
                    type: 'hidden',
                    def: TYPE_SUPER_ADMIN,
                },
                {
                    label: 'Create user:',
                    name: 'create_user',
                    type: 'checkbox',
                    separator: '|',
                    options: [{ label: '', value: 1 }],
                },
            ],
        });

        // disable email field for edit
        editor.on('initEdit', function (e, type) {
            editor.disable('parens.email');
        });
        // enable email field for create
        editor.on('initCreate', function (e, type) {
            editor.enable('parens.email');
        });

        var table = $('#tableSuperAdmin').DataTable({
            dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
            stateSave: true,
            deferRender: true,
            ajax: {
                url: SERVER_PATH + 'paren/getSuperAdmins',
                type: 'POST',
                headers: {
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name,
                },
                data: {
                    // "pgroup_id": pgroup_id
                },
                dataType: 'json',
                complete: function (response) {},
                error: function (xhr, status, error) {},
            },
            language: {
                info: 'Showing _START_ to _END_ of _TOTAL_ super admin',
                infoEmpty: 'Showing 0 to 0 of 0 super admins',
                lengthMenu: 'Show _MENU_ super admins',
                select: {
                    rows: {
                        _: 'You have selected %d super admins',
                        0: 'Click an super admin to select',
                        1: '1 super admin selected',
                    },
                },
                paginate: {
                    previous: '<i class="fa fa-chevron-left"></i>',
                    next: '<i class="fa fa-chevron-right"></i>',
                },
            },
            columns: [
                { data: 'parens.surname' },
                { data: 'parens.other_name' },
                { data: 'parens.email' },
            ],
            select: {
                style: 'single',
                selector: 'td:first-child',
            },
            order: [[1, 'asc']],
            lengthMenu: [
                [10, 25, 50, 100, -1],
                [10, 25, 50, 100, 'All'],
            ],
            buttons: [
                { extend: 'create', editor: editor },
                { extend: 'edit', editor: editor },
                { extend: 'remove', editor: editor },
                {
                    extend: 'selectedSingle',
                    text: 'Transform to parent',
                    action: function (e, dt, button, config) {
                        const data = dt.row({ selected: true }).data();
                        const { id } = data.parens;

                        Swal.fire({
                            title: 'Transform to parent',
                            text: 'Are you sure you want to transform this super admin to parent?',
                            icon: 'warning',
                            showCancelButton: true,
                            confirmButtonColor: '#ed1c24',
                            confirmButtonText: 'Yes, transform it!',
                            cancelButtonText: 'No, cancel!',
                            customClass: {
                                actions: 'my-actions',
                            },
                        }).then(async (result) => {
                            if (result.isConfirmed) {
                                try {
                                    const response = await $http({
                                        method: 'POST',
                                        url:
                                            SERVER_PATH +
                                            'paren/transformToParent',
                                        headers: {
                                            'Content-Type': 'application/json',
                                            'x-user-id': $rootScope.user_id,
                                            'x-user-email':
                                                $rootScope.user_name,
                                        },
                                        data: {
                                            parent_id: id,
                                        },
                                    });
                                    const { status, message } = response.data;

                                    if (status === 'OK') {
                                        Swal.fire({
                                            title: 'Success',
                                            text: message,
                                            icon: 'success',
                                        });

                                        table.ajax.reload();
                                    } else {
                                        Swal.fire({
                                            title: 'Error',
                                            text: message,
                                            icon: 'error',
                                        });
                                    }
                                } catch (error) {
                                    Swal.fire({
                                        title: 'Error',
                                        text: 'System error, please contact league admin.',
                                        icon: 'error',
                                    });
                                    console.error(error);
                                }
                            }
                        });
                    },
                },
                { extend: 'colvis', text: 'Columns' },
            ],
        });
        editor.on('initEdit', function (e, type) {
            editor.disable('create_user');
            editor.hide('create_user');
        });
        editor.on('initCreate', function (e, type) {
            editor.enable('create_user');
            editor.show();
        });
    }
);
