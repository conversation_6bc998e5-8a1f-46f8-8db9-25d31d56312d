app.controller(
    'activityLogEachPlayerCtrl',
    function ($scope, $rootScope, $routeParams, $http) {
        
        var playerId = $routeParams.playerId;

        $scope.player = null;

        // ajax call to get player data
        jQuery.ajax({
            type: 'POST',
            url: SERVER_PATH + "player/getPlayerById",
            async: false,
            headers: {	
                'x-user-id': $rootScope.user_id,
                'x-user-email': $rootScope.user_name
            },
            data: {
                "player_id": playerId
            },
            dataType: 'json',
            complete: function (response) {
                var jsonData = JSON.parse(response.responseText);
                if (jsonData.status == "OK") {
                    $scope.player = jsonData.data;
                    console.warn($scope.player);
                }
            }
        });
        
        // define datatable for activity
        $('#activity_log_each_player_table').DataTable({
            dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
            stateSave: true,
            deferRender: true,
            ajax: {
                url: SERVER_PATH + 'player/getTableForActivityPlayer',
                type: 'POST',
                headers: {	
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name
                },
                data: {
                    player_id: playerId,
                },
                dataType: 'json',
                complete: function (response) {},
                error: function (xhr, status, error) {},
            },
            language: {
                info: 'Showing _START_ to _END_ of _TOTAL_ player',
                infoEmpty: 'Showing 0 to 0 of 0 registrations',
                lengthMenu: 'Show _MENU_ registrations',
                select: {
                    rows: {
                        _: 'You have selected %d registrations',
                        0: 'Click a registration to select',
                        1: '1 registrations selected',
                    },
                },
                paginate: {
                    previous: '<i class="fa fa-chevron-left"></i>',
                    next: '<i class="fa fa-chevron-right"></i>',
                },
            },
            columns: [
                { data: 'events.name', className: 'center' },
                { data: 'events.type', className: 'center' },
                { data: 'registrations.registered_date', className: 'center' },
                { data: 'registrations.approved_date', className: 'center' },
                {
                    data: 'registrations.approval_status',
                    className: 'center',
                    render: function (data, type, full, meta) {
                        switch (data) {
                            case APPROVAL_STATUS_Approve:
                                return (
                                    '<span class="label label-success">' +
                                    data +
                                    '</span>'
                                );
                            case APPROVAL_STATUS_Register:
                                return (
                                    '<span class="label label-info">' +
                                    data +
                                    '</span>'
                                );
                            case APPROVAL_STATUS_Reject:
                                return (
                                    '<span class="label label-danger">' +
                                    data +
                                    '</span>'
                                );
                            default:
                                return (
                                    '<span class="label label-default">' +
                                    data +
                                    '</span>'
                                );
                        }
                    },
                },
            ],
            select: {
                style: 'single',
                selector: 'td:first-child',
            },
            order: [[1, 'asc']],
            lengthMenu: [
                [10, 25, 50, 100, -1],
                [10, 25, 50, 100, 'All'],
            ],
            buttons: [
                {
                    extend: 'excel',
                    name: 'excel',
                    text: '<i class="fa fa-file-excel-o"></i>&emsp;Export to Excel',
                    titleAttr: 'Export data to an Excel file',
                    filename: appName + ' - Players',
                    title: appName + ' - Players',
                    exportOptions: {
                        columns: ':visible',
                        modifier: {
                            autoFilter: true,
                        },
                    },
                },
                { extend: 'colvis', text: 'Columns' },
            ],
        });
    }
);
