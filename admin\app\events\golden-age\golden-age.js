app.controller('goldenAgeCtrl', function (user, $scope, $rootScope, $http, seasonService) {
    $scope.user = user;

    $scope.events = [];
    $scope.seasons = [];
    $scope.selectedSeasonId = null;
    $scope.items = [];

    seasonService.loadSeasons().then(function(seasons) {
        $scope.seasons = seasons;
        $scope.selectedSeasonId = seasonService.getSelectedSeasonId();
        if ($scope.selectedSeasonId) {
            getEventGoldenAge();
        }
    });

    function getEventGoldenAge() {
        $http({
            method: 'POST',
            url: SERVER_PATH + 'event/getEventGoldenAge',
            headers: {
                'x-user-id': $rootScope.user_id,
                'x-user-email': $rootScope.user_name,
            },
             data: {
                season_id: $scope.selectedSeasonId
            }
        }).success(function (response) {
            if (response.status == 'OK') {
                $scope.events = response.data;

                $scope.items = [
                    {
                        name: 'Registration',
                        icon: 'fa-solid fa-address-card fa-lg',
                        href: '#/registrations/{event.id}',
                        image: 'images/adminpanel-icon/Registrations.png'
                    },
                    {
                        name: 'League',
                        icon: 'fa-trophy',
                        href: '#/leagues/{event.id}',
                        image: 'images/adminpanel-icon/League.png'
                    },
                    {
                        name: 'Season',
                        icon: 'fa-users',
                        href: '#/seasons/{event.id}',
                        image: 'images/adminpanel-icon/Season.png'
                    },
                    {
                        name: 'Club',
                        icon: 'fa-shield',
                        href: '#/admin_club_manager/{event.id}',
                        image: 'images/adminpanel-icon/Club.png'
                    },
                    {
                        name: 'Year Group',
                        icon: 'fa-th-large',
                        href: '#/cgroups_admin/{event.id}',
                        image: 'images/adminpanel-icon/YearGroup.png'
                    },
                    {
                        name: 'Team',
                        icon: 'fa-list-ul',
                        href: '#/event/{event.id}/team_golden_age',
                        image: 'images/adminpanel-icon/Teams.png'
                    },
                    {
                        name: 'Team Sheet',
                        icon: 'fa-list',
                        href: '#/team_sheet/{event.id}',
                        image: 'images/adminpanel-icon/TeamSheet.png'
                    },
                    {
                        name: 'Messages',
                        icon: 'fa-envelope-o',
                        href: '#/messages/{event.id}',
                        image: 'images/adminpanel-icon/Message.png'
                    },
                    {
                        name: 'Report',
                        icon: 'fa-file-text-o',
                        href: '#/reports/registration',
                        image: 'images/adminpanel-icon/Report.png'
                    },
                ];
            }
        });
    }

    $scope.filterEventsBySeason = function () {
        seasonService.setSelectedSeasonId($scope.selectedSeasonId);
        if ($scope.selectedSeasonId) {
            getEventGoldenAge();
        }
    };
});
