app.controller('paymentTeamsCtrl', function($scope, $rootScope, $routeParams, $http) {
    $('#page-wrapper').removeClass('nav-small');
    
    $.fn.dataTable.moment( 'D-MMM-YYYY HH:mm:ss' );
    $.fn.dataTable.moment( 'D-MMM-YYYY' );
    
    var event_id = $routeParams.id;
    jQuery.ajax({
    	type: 'POST',
  		url: SERVER_PATH + "event/getEventInfo",
  		async: false,
        headers: {	
            'x-user-id': $rootScope.user_id,
            'x-user-email': $rootScope.user_name
        },
  		data: {
    		"event_id": event_id
    	},
  		dataType: 'json',
  		complete: function (response) {
  			var jsonData = JSON.parse(response.responseText);
  			var event = jsonData.info;
  			event_name = event.name;
  			event_type = event.type;
  		}
    });
    console.log('cgroupsCtrl - event_id, name, type  = ' + event_id + ', ' + event_name + ', ' + event_type);
	$scope.showSelectGroups = event_type == 'Season' ? true : false;
    $scope.event_id = event_id;
	$rootScope.event_name = event_name;
 	
    jQuery.ajax({
    	type: 'POST',
  		url: SERVER_PATH + "group/getCgroupsByUser",
  		async: false,
        headers: {	
            'x-user-id': $rootScope.user_id,
            'x-user-email': $rootScope.user_name
        },
  		data: {
    		"event_id": event_id,
    		"user_id": $rootScope.user_id,
    	},
  		dataType: 'json',
  		complete: function (response) {
  			var jsonData = JSON.parse(response.responseText);
  			if (jsonData.status == "OK") {
				  $scope.groups = jsonData.info;
				  $scope.role =$scope.groups.role;
  				$scope.selectedGroup = $scope.groups[0];
  				// getGroup();
  			}
  		}
    });

    $('button').click( function (e) {
		getGroup();
    });

    function getGroup() {
		var cgroup_id = $scope.selectedGroup.id;
		var cgroup_name = $scope.selectedGroup.name;
		
		// initialize html
		var html = '';
		html += getPaymentTableHtml(cgroup_id);
        $('#paymentCgroupsPageContent').html(html);

        // initialize data	
        initPaymentTable(cgroup_id, cgroup_name);
    }
        
    function getPaymentTableHtml(cgroup_id) {
        var str = ''+
        '<div class="form-group">' +
            '<label>Filter by Status</label>' +
            '<br/>' +
        '</div>' +
          '<div class="table-responsive">' +
              '<table id="tblPaymentGroupPlayers_' + cgroup_id + '" class="table table-striped table-bordered table-hover" cellspacing="0" width="100%">' +
                  '<thead>' +
                      '<tr>' +
                        '<th></th>' +
                        '<th>Player</th>' +
                        '<th>Year</th>' +
                        '<th>Group</th>' +
                        '<th>Team</th>' +
                        '<th>Parent</th>' +
                        '<th>Email</th>' +
                        '<th>App.date</th>' +
                        '<th>App.status</th>' +
                        '<th>Inv.number</th>' +
                        '<th>Amount</th>' +
                        '<th>Status</th>' +
                      '</tr>' +
                  '</thead>' +
              '</table>' +
          '</div>';
      return str;
      }

      function initPaymentTable(cgroup_id) {
        function cbDropdown(column) {
            return $('<ul>', {
              'class': 'cb-dropdown'
            }).appendTo($('<div>', {
              'class': 'cb-dropdown-wrap'
            }).appendTo(column));
          }
        tablePaymentTeamPlayers_ = $('#tblPaymentGroupPlayers_' + cgroup_id).DataTable( {
            dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
            stateSave: true,
            deferRender: true,
            ajax: {
                url: SERVER_PATH + "invoice/getPaymentTeamPlayers",
                headers: {	
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name
                },
                type: 'POST',
                  data: {
                    "cgroup_id": cgroup_id,
                    "event_id": event_id
                },
                  dataType: 'json',
                  complete: function (response) {
                },
                  error: function(xhr, status, error) {
                  },
            },
            language: {
                paginate: {
                    previous: '<i class="fa fa-chevron-left"></i>',
                    next: '<i class="fa fa-chevron-right"></i>'
                }
            },
              columns: [
                {
                    data: 'DT_RowId',
                    // defaultContent: '',
                    // className: 'select-checkbox',
                    // orderable: false
                    targets: 0,
                    render: function (data, type, row, meta) {
                        if (type === 'display') {
                            data = '<div class="checkbox"><input type="checkbox" class="dt-checkboxes"><label></label></div>';
                        }

                        return data;
                    },
                    checkboxes: {
                        selectRow: true,
                        selectAllRender: '<div class="checkbox"><input type="checkbox" class="dt-checkboxes"><label></label></div>'
                    }
                },
                {
                    data: null,
                    render: function (data, type, row) {
                        return data.players.surname + ' ' + data.players.other_name;
                    }
                },
                { data: "players.dob", className: "center" },
                // { data: "pgroups.name" },
                { data: "groups.name", className: "center" },
                { data: "teams.name", className: "center" },
                {
                    data: null,
                    render: function (data, type, row) {
                        return data.parens.surname + ' ' + data.parens.other_name;
                    }
                },
                { data: "parens.email" },
                // { data: "parens.hkfc_account", className: "center" },
                { data: "registrations.approved_date", className: "center" },
                {
                    data: "registrations.approval_status",
                    className: "center",
                    render: function (data, type, full, meta) {
                        switch (data) {
                            case APPROVAL_STATUS_Approve: return '<span class="label label-success">' + data + '</span>';
                           
                            default: return '<span class="label label-default">' + data + '</span>';
                        }
                    }
                },
                { data: "invoices.invoice_number", className: "center" },
                { data: "registrations.amount", className: "center" },
                { 
                    data: "invoices.status",
                    name: "name",
                    name_index: "name_index",
                    className: "center"
                }
            ],
            select: {
                style: 'multi',
            },
            buttons: [
                { extend: 'colvis', text: 'Columns' }
            ],
            initComplete: function () {
                this.api().columns('11').every(function () {
                    var column = this;
                    //added class "mymsel"
                    var ddmenu = cbDropdown($(column.header()))
                        .on('change',':checkbox', function () {
                            var vals = $(':checked', ddmenu).map(function (index, element) {
                                return $.fn.dataTable.util.escapeRegex($(element).val());
                            }).toArray().join('|');

                            column
                                .search(vals.length > 0 ? '^(' + vals + ')$' : '', true, false)
                                .draw();
                        });

                    column.data().unique().sort().each(function (d, j) {
                        var // wrapped
                            $label = $('<label>'),
                            $text = $('<span>', {
                                style: 'margin: 0px 15px 10px 0px',
                                text: d
                            }),
                            $cb = $('<input>', {
                                type: 'checkbox',
                                style: 'height: 17px; width: 17px; vertical-align: bottom; position: relative; top: -1px; *overflow: hidden; margin-right: 2px;',
                                value: d
                            });
                        
                        $cb.appendTo($label);
                        $text.appendTo($label);

                        ddmenu.append($label);
                        ddmenu.add().css('margin','0px 0px -20px -35px').appendTo(".form-group")
                    });
                });

                $(".cb-dropdown-wrap").each(function () {
                    console.log($(this).parent().width());
                    $(this).width($(this).parent().width());
                });
            },
            order: [[1, 'asc']],
            "lengthMenu": [[10, 25, 50, 100, -1], [10, 25, 50, 100, "All"]]
        } );
    }

});