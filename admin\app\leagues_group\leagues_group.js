app.controller(
    'leaguesGroupsCtrl',
    function ($scope, $rootScope, $routeParams, $http, socket) {

        $scope.$on('$viewContentLoaded',function(){
            socket.connect();

            socket.joinChannel(CHANNEL_ENVIRONMENT+'-formb-channel');
        })

        $scope.$on('$destroy', function () {
            socket.disconnect();
        });

        $('#page-wrapper').removeClass('nav-small');
        // get info event
        var event_id = $routeParams.id;
        $scope.event_id = event_id;
        $scope.selectedDate = null;
        $scope.clubs = null;
        $scope.selectedClub = null;

        jQuery.ajax({
            type: 'POST',
            url: SERVER_PATH + 'event/getEventInfo',
            async: false,
            headers: {
                'x-user-id': $rootScope.user_id,
                'x-user-email': $rootScope.user_name,
            },
            data: {
                event_id: event_id,
            },
            dataType: 'json',
            complete: function (response) {
                console.log('response', response);
                var jsonData = JSON.parse(response.responseText);
                var event = jsonData.info;
                event_name = event.name;
                event_type = event.type;
            },
        });
        jQuery.ajax({
            type: 'POST',
            url: SERVER_PATH + 'league/getAllClub',
            async: false,
            headers: {
                'x-user-id': $rootScope.user_id,
                'x-user-email': $rootScope.user_name,
            },
            data: {},
            dataType: 'json',
            complete: function (response) {
                var jsonData = JSON.parse(response.responseText);
                $scope.clubs = jsonData.info;
                $scope.clubs.unshift({
                    name: 'All',
                    id: 0,
                });
            },
        });
        initTabGroups();
        $scope.event_name = event_name;
        $('body').unbind('click');
        $('body').on(
            'click',
            'li[class=active] a[data-toggle=tab]',
            function () {
                switch ($(this).data('target')) {
                    case '#tab-groups': {
                        initTabGroups();
                        break;
                    }
                    case '#tab-matches': {
                        $scope.initMatchesTable();
                        break;
                    }

                    case '#tab-formb': {
                        $scope.initFormBTable();
                        break;
                    }
                }
            }
        );

        $scope.goBack = function () {
            window.history.back();
        };
        
        function initTabGroups() {
            jQuery.ajax({
                type: 'POST',
                url: SERVER_PATH + 'league/getGroupsByEvent',
                async: false,
                headers: {
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name,
                },
                data: {
                    event_id: event_id,
                    user_id: $rootScope.user_id,
                },
                dataType: 'json',
                complete: function (response) {
                    var jsonData = JSON.parse(response.responseText);
                    if (jsonData.status == 'OK') {
                        $scope.mixedGroups = jsonData.info.mixed;
                        $scope.girlGroups = jsonData.info.girl;
                    }
                },
            });
        }
        $scope.initMatchesTable = function () {
            if ($.fn.dataTable.isDataTable('#tbReportMatches')) {
                $('#tbReportMatches').DataTable().destroy();
            }
            tableLeagueMatches = $('#tbReportMatches').DataTable({
                dom: '<"row"<"col-sm-6"B><"col-sm-6"f>>rt<"row"i>',
                stateSave: true,
                deferRender: true,
                ajax: {
                    url: SERVER_PATH + 'league/getMatchesReport',
                    type: 'POST',
                    headers: {
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name,
                    },
                    data: {
                        event_id: event_id,
                        date: $scope.selectedDate,
                        club_id: $scope.selectedClub,
                    },
                    dataType: 'json',
                    complete: function (response) {},
                    error: function (xhr, status, error) {},
                },
                language: {
                    paginate: {
                        previous: '<i class="fa fa-chevron-left"></i>',
                        next: '<i class="fa fa-chevron-right"></i>',
                    },
                },
                aoColumns: [
                    {
                        data: 'leagues.name',
                        className: 'center',
                        sortable: false,
                    },
                    {
                        data: null,
                        render: function (data, type, full, meta) {
                            return full.matches.date == ''
                                ? 'TBD'
                                : full.matches.date;
                        },
                        sType: 'date',
                        bSortable: true,
                        sortable: false,
                    },
                    {
                        data: null,
                        render: function (data, type, full, meta) {
                            return full.matches.start_time == ''
                                ? 'TBD'
                                : full.matches.start_time;
                        },
                        sType: 'startTime',
                        bSortable: true,
                        sortable: false,
                    },
                    {
                        data: null,
                        render: function (data, type, full, meta) {
                            return full.matches.end_time == ''
                                ? 'TBD'
                                : full.matches.end_time;
                        },
                        sortable: false,
                    },
                    {
                        data: null,
                        render: function (data, type, full, meta) {
                            return full.matches.location == 0
                                ? 'TBD'
                                : full.venues.name;
                        },
                    },
                    {
                        data: 'matches.home_name',
                        className: 'center',
                        sortable: false,
                    },

                    {
                        data: null,
                        className: 'center',
                        sortable: false,
                        className: 'center',
                        render: function () {
                            return 'VS';
                        },
                    },
                    {
                        data: 'matches.away_name',
                        className: 'center',
                        sortable: false,
                    },
                    {
                        data: null,
                        className: 'center',
                        sortable: false,
                        render: function (data, type, full, meta) {
                            return full.matches.penalty == 0
                                ? full.matches.home_team_score
                                : full.matches.home_team_score +
                                      '(' +
                                      full.match_penalty.home_pen_score +
                                      ')';
                        },
                    },
                    {
                        data: null,
                        className: 'center',
                        sortable: false,
                        className: 'center',
                        render: function () {
                            return '-';
                        },
                    },
                    {
                        data: null,
                        className: 'center',
                        sortable: false,
                        render: function (data, type, full, meta) {
                            return full.matches.penalty == 0
                                ? full.matches.away_team_score
                                : full.matches.away_team_score +
                                      '(' +
                                      full.match_penalty.away_pen_score +
                                      ')';
                        },
                    },
                ],
                createdRow: function (row, data, dataIndex) {
                    if (data.matches.highlight == 1) {
                        $(row).css('background-color', '#FFFACD');
                    }
                },
                rowGroup: {
                    order: [['venues.name', 'asc']],
                    dataSrc: function (row) {
                        return row.venues.name == null
                            ? 'TBD'
                            : row.venues.name;
                    },
                },
                columnDefs: [
                    { orderable: true, targets: 4, orderData: [4, 1, 2] },
                ],
                order: [['4', 'desc']],
                select: {
                    // style: SELECT_MODE,
                },
                buttons: [
                    {
                        extend: 'collection',
                        text: 'Actions',
                        autoClose: true,
                        buttons: [
                            {
                                extend: 'excel',
                                name: 'excel',
                                text: '<i class="fa fa-file-excel-o"></i>&emsp;Export to Excel',
                                titleAttr: 'Export data to an Excel file',
                                filename: 'Match Report',
                                exportOptions: {
                                    columns: ':visible',
                                    modifier: {
                                        autoFilter: true,
                                        // selected: true
                                    },
                                },
                            },
                            {
                                titleAttr: 'Export data to an PDF file',
                                filename: 'Match Report',
                                text: '<i class="fa fa-file-pdf-o"></i>&emsp;Generate Form B',
                                extend: 'selectedSingle',
                                action: function (e, dt, node, config) {
                                    var allMatch_id = [];
                                    var table_selected = tableLeagueMatches
                                        .rows({ selected: true })
                                        .data();
                                    console.log('selected');

                                    var match_id = -1;
                                    var home_name = '';
                                    var away_name = '';
                                    var datetimeconvert = '';
                                    // console.log(table_selected[0].matches.id);

                                    for (
                                        var i = 0;
                                        i < table_selected.length;
                                        i++
                                    ) {
                                        console.log('selected');
                                        console.log(i);
                                        console.log(table_selected[i].matches);

                                        let match_date_time =
                                            table_selected[i].matches.date +
                                            '-' +
                                            table_selected[i].matches
                                                .start_time;
                                        match_date_time = new Date(
                                            match_date_time
                                        );
                                        datetimeconvert =
                                            match_date_time.getFullYear() +
                                            '-' +
                                            (match_date_time.getMonth() + 1) +
                                            '-' +
                                            match_date_time.getDate() +
                                            '-' +
                                            (
                                                '0' + match_date_time.getHours()
                                            ).slice(-2) +
                                            '_' +
                                            (
                                                '0' +
                                                match_date_time.getMinutes()
                                            ).slice(-2);
                                        console.log(
                                            'time : ' + datetimeconvert
                                        );
                                        match_id = table_selected[i].matches.id;
                                        home_name =
                                            table_selected[i].matches.home_name;
                                        away_name =
                                            table_selected[i].matches.away_name;
                                        console.log(datetimeconvert);
                                    }

                                    var editor = new $.fn.dataTable.Editor({
                                        ajax: {
                                            type: 'POST',
                                            url:
                                                SERVER_PATH +
                                                'league/exportPDFfile',
                                            headers: {
                                                'x-user-id': $rootScope.user_id,
                                                'x-user-email':
                                                    $rootScope.user_name,
                                            },
                                            data: {
                                                allMatch_id: match_id,
                                            },
                                            dataType: 'json',
                                            beforeSend: function () {
                                                Swal.fire({
                                                    icon: 'info',
                                                    text: 'Export in process, please wait…',
                                                    allowOutsideClick: false,
                                                    confirmButtonColor:
                                                        '#3085d6',
                                                });
                                            },
                                            complete: function (response) {
                                                // hide loading with SweetAlert
                                                Swal.close();

                                                var jsonData = JSON.parse(
                                                    response.responseText
                                                );

                                                console.log(jsonData);
                                                if (
                                                    jsonData.info == undefined
                                                ) {
                                                    BootstrapDialog.show({
                                                        size: BootstrapDialog.SIZE_NORMAL,
                                                        type: BootstrapDialog.TYPE_WARNING,
                                                        message:
                                                            jsonData.message,
                                                    });
                                                } else {
                                                    if (match_id != -1) {
                                                        jsonData.info.forEach(
                                                            (file_info) => {
                                                                downloadFile(
                                                                    file_info.link,
                                                                    file_info.filename
                                                                );
                                                            }
                                                        );
                                                    }

                                                    if (
                                                        jsonData.status == 'OK'
                                                    ) {
                                                        // show success message with sweet alert
                                                        Swal.fire({
                                                            type: 'success',
                                                            icon: 'success',
                                                            title: jsonData.message,
                                                            confirmButtonClass:
                                                                'btn btn-primary',
                                                            buttonsStyling: false,
                                                        });
                                                    }
                                                }
                                            },
                                            error: function (
                                                xhr,
                                                status,
                                                error
                                            ) {
                                                console.log(error);
                                            },
                                        },
                                        formOptions: {
                                            main: {
                                                onBlur: 'none',
                                            },
                                        },
                                        fields: [],
                                    });

                                    editor.create(false).submit();
                                },
                            },
                        ],
                    },
                    {
                        extend: 'colvis',
                        text: 'Columns',
                    },
                ],
                displayLength: -1,
            });

            function downloadFile(urlToSend, fileName) {
                var req = new XMLHttpRequest();
                req.open('GET', urlToSend, true);
                req.responseType = 'blob';
                req.onload = function (event) {
                    var blob = req.response;
                    //if you have the fileName header available
                    var link = document.createElement('a');
                    link.href = window.URL.createObjectURL(blob);
                    link.download = fileName;
                    link.click();
                };

                req.send();
            }

            function getRankDate(date) {
                var milliseconds = moment(date).format('DD-M-Y');
                var parts = milliseconds.split('-');
                return new Date(parts[2], parts[1] - 1, parts[0]);
            }
            $.fn.dataTable.ext.type.order['date-desc'] = function (x, y) {
                if (getRankDate(x).getTime() == getRankDate(y).getTime()) {
                    return 0;
                }
                return getRankDate(x).getTime() > getRankDate(y).getTime()
                    ? 1
                    : -1;
            };

            $.fn.dataTable.ext.type.order['date-asc'] = function (x, y) {
                if (getRankDate(x).getTime() == getRankDate(y).getTime()) {
                    return 0;
                }
                return getRankDate(x).getTime() > getRankDate(y).getTime()
                    ? 1
                    : -1;
            };
            function getRankStartTime(time) {
                var start_time = new Date();
                start_time_arr = time.split(':');
                start_time.setHours(start_time_arr[0]);
                start_time.setMinutes(start_time_arr[1]);
                return start_time;
            }
            $.fn.dataTable.ext.type.order['startTime-desc'] = function (x, y) {
                return getRankStartTime(x).getTime() >
                    getRankStartTime(y).getTime()
                    ? 1
                    : -1;
            };
            $.fn.dataTable.ext.type.order['startTime-asc'] = function (x, y) {
                return getRankStartTime(x).getTime() >
                    getRankStartTime(y).getTime()
                    ? 1
                    : -1;
            };
        };
        $scope.initFormBTable = function () {
            if ($.fn.dataTable.isDataTable('#tbFormB')) {
                $('#tbFormB').DataTable().destroy();
            }

            let formBEditor = new $.fn.dataTable.Editor({
                ajax: {
                    type: 'POST',
                    url: SERVER_PATH + 'league/getFormBDayReport',
                    data: {},
                    headers: {
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name,
                    },
                    dataType: 'json',
                    complete: function (response) {
                        table.ajax.reload();
                    },
                    error: function (xhr, status, error) {},
                },
                table: '#tbFormB',
                formOptions: {
                    main: {
                        onBlur: 'none',
                    },
                },
                i18n: {
                    create: {
                        button: 'New',
                        title: 'Create new form B',
                        submit: 'Create',
                    },
                    remove: {
                        button: 'Delete',
                        title: 'Delete form B',
                        submit: 'Delete',
                    },
                    error: {
                        system: 'System error, please contact administrator.',
                    },
                },
                fields: [
                    {
                        label: 'Date:',
                        name: 'form_b_day_reports.date',
                        type: 'select',
                    },
                ],
            });

            table = $('#tbFormB').DataTable({
                dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
                ajax: {
                    url: SERVER_PATH + 'league/getFormBDayReport',
                    type: 'POST',
                    dataType: 'json',
                    headers: {
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name,
                    },
                    data: {},
                    complete: function (response) {},
                    error: function (xhr, status, error) {},
                },
                language: {
                    paginate: {
                        previous: '<i class="fa fa-chevron-left"></i>',
                        next: '<i class="fa fa-chevron-right"></i>',
                    },
                },
                columns: [
                    {
                        data: 'form_b_day_reports.date',
                    },
                    {
                        data: 'form_b_day_reports.status',
                        render: function (data, type, row) {
                            return data == 0
                                ? '<span class="badge badge-info">Didn\'t generate</span>'
                                : data == 1
                                ? '<span class="badge badge-success">Generated</span>'
                                : '<span class="badge badge-danger">Processing</span>';
                        },
                    },
                    {
                        data: 'form_b_day_reports.updated_at',
                    },
                    {
                        data: 'form_b_day_reports.percentage',
                        render: function (data, type, row) {
                            return data + '%';
                        },
                    },
                    {
                        data: 'form_b_day_reports.date',
                        render: function (data, type, row) {
                            var button = `<button class="btn btn-primary btn-sm" data-toggle="modal" data-target="#paymentDetailModal" onclick="downLoadFormB('${row.form_b_day_reports.id}')">Download</button>`;

                            return button;
                        },
                    },
                ],
                select: {
                    style: 'single',
                },
                buttons: [
                    { extend: 'create', editor: formBEditor },
                    { extend: 'remove', editor: formBEditor },
                    {
                        name: 'reGenerate',
                        text: 'Re-Generate',
                        titleAttr: 'Re-Generate Form B for selected date',
                        action: function (e, dt, node, config) {
                            var dataSelectedRow = table
                                .rows({ selected: true })
                                .data()[0];

                            let formBId = dataSelectedRow.form_b_day_reports.id;

                            jQuery.ajax({
                                type: 'POST',
                                url: SERVER_PATH + 'league/reGenerateFormB',
                                async: false,
                                data: {
                                    form_b_day_report_id: formBId,
                                },
                                headers: {
                                    'x-user-id': $rootScope.user_id,
                                    'x-user-email': $rootScope.user_name,
                                },
                                dataType: 'json',
                                beforeSend: function (xhr) {
                                    Swal.fire({
                                        title: 'Please Wait!',
                                        allowOutsideClick: false,
                                        didOpen: () => {
                                            Swal.showLoading();
                                        },
                                    });
                                },
                                complete: function (response) {
                                    // hide loading with SweetAlert
                                    setTimeout(function () {
                                        Swal.close();
                                    }, 1000);

                                    var jsonData = JSON.parse(
                                        response.responseText
                                    );
                                    if (jsonData.status == 'OK') {
                                        table.ajax.reload();

                                        Swal.fire({
                                            icon: 'success',
                                            title: 'Success',
                                            text: jsonData.message,
                                        });
                                    }
                                },
                                error: function (xhr, status, error) {
                                    Swal.close();
                                },
                            });
                        },
                    },
                ],
            });


            $('#tbFormB').on( 'init.dt', function () { 
                socket.isOn('form-b-percent', function (data) {
                    socket.off('form-b-percent');
                });
                
                socket.on('form-b-percent', function (socketData) {
                   socketData = JSON.parse(socketData);
                    // check tab is active
                     if ($('#tab-formb').hasClass('active')) {
                        // find the row with the same date
                        var row = table.row(function (idx, data, node) {
                            return data.form_b_day_reports.date == socketData.date;
                        });
                        
                        if (row.length) {
                            var rowData = row.data();
                            rowData.form_b_day_reports.percentage = socketData.percentage;
                            rowData.form_b_day_reports.status = 2;
                            table.row(row.index()).data(rowData).draw();
                        }

                        if (socketData.percentage == 100) {
                            rowData.form_b_day_reports.status = 1;
                            table.ajax.reload();
                        }
                     }
                 });
            });

            downLoadFormB = function (id) {
                jQuery.ajax({
                    type: 'POST',
                    url: SERVER_PATH + 'league/zipFileAndDownload',
                    async: true,
                    headers: {
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name,
                    },
                    data: {
                        id: id,
                    },
                    dataType: 'json',
                    beforeSend: function () {
                        Swal.fire({
                            title: 'Please Wait!',
                            allowOutsideClick: false,
                            didOpen: () => {
                                Swal.showLoading();
                            },
                        });
                    },
                    complete: function (response) {
                        Swal.close();
                        var jsonData = JSON.parse(response.responseText);

                        if (jsonData.status == 'OK') {
                            downloadFile(jsonData.link, jsonData.name);
                        } else {
                            Swal.fire({
                                icon: 'error',
                                title: 'Oops...',
                                text: jsonData.message,
                            });
                        }
                    },
                });
            };

            function downloadFile(urlToSend, fileName) {
                var req = new XMLHttpRequest();
                req.open('GET', urlToSend, true);
                req.responseType = 'blob';
                req.onload = function (event) {
                    var blob = req.response;
                    //if you have the fileName header available
                    var link = document.createElement('a');
                    link.href = window.URL.createObjectURL(blob);
                    link.download = fileName;
                    link.click();
                };

                req.send();
            }
        };
    }
);
