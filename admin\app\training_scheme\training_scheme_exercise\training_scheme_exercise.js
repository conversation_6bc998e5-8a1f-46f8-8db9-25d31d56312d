app.controller(
    'trainingSchemeExerciseCtrl',
    function ($scope, $rootScope, $routeParams, $http) {
        $('#page-wrapper').removeClass('nav-small');

        var trainingSchemeSkillId = $routeParams.id;
        var trainingSchemeGroupName = $routeParams.name;

        $scope.trainingSchemeGroupName = trainingSchemeGroupName;

        var trainingSchemeEditor = new $.fn.dataTable.Editor({
            ajax: {
                type: 'POST',
                url: SERVER_PATH + 'training-scheme/setTrainingSchemeExercises',
                headers: {	
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name
                },
                data: {
                    training_scheme_skill_id: trainingSchemeSkillId,
                },
                dataType: 'json',
                complete: function (response) {
                    var jsonData = JSON.parse(response.responseText);
                    table.ajax.reload();
                },
                error: function (xhr, status, error) {},
            },
            table: '#trainingSchemeExercises',
            formOptions: {
                main: {
                    onBlur: 'none',
                },
            },
            i18n: {
                create: {
                    button: 'New',
                    title: 'Create new exercise',
                    submit: 'Create',
                },
                edit: {
                    button: 'Edit',
                    title: 'Edit exercise',
                    submit: 'Update',
                },
                remove: {
                    button: 'Delete',
                    title: 'Delete exercise',
                    submit: 'Delete',
                    confirm: {
                        _: 'Are you sure you want to delete these exercise?',
                        1: 'Are you sure you want to delete this exercise?',
                    },
                },
                error: {
                    system: 'System error, please contact administrator.',
                },
            },
            fields: [
                {
                    label: 'Drill:',
                    name: 'training_scheme_exercises.drill',
                },
                {
                    label: 'Chinese drill',
                    name: 'training_scheme_exercises.drill_cn',
                },
                {
                    label: 'Time',
                    name: 'training_scheme_exercises.time',
                },
                {
                    label: 'Chinese time',
                    name: 'training_scheme_exercises.time_cn',
                },
                {
                    label: 'Set',
                    name: 'training_scheme_exercises.set',
                },
                {
                    label: 'Chinese set',
                    name: 'training_scheme_exercises.set_cn',
                },
                {
                    label: 'Demonstration',
                    name: 'training_scheme_exercises.demonstration',
                },
                {
                    label: 'Objective',
                    name: 'training_scheme_exercises.objective',
                },
                {
                    label: 'Chinese objective',
                    name: 'training_scheme_exercises.objective_cn',
                },
                {
                    label: 'Precaution',
                    name: 'training_scheme_exercises.precaution',
                },
                {
                    label: 'Chinese precaution',
                    name: 'training_scheme_exercises.precaution_cn',
                },
                {
                    type: 'hidden',
                    label: 'training_scheme_skill_id',
                    name: 'training_scheme_exercises.training_scheme_skill_id',
                    def: trainingSchemeSkillId,
                },
                {
                    label: 'order',
                    name: 'training_scheme_exercises.order',
                    type: 'hidden',
                },
            ],
        });

        var uploadEditor = new $.fn.dataTable.Editor({
            fields: [
                {
                    label: 'CSV file:',
                    name: 'csv',
                    type: 'upload',
                    ajax: function (files, done) {
                        var form = new FormData();
                        form.append('file', files[0]);
                        form.append(
                            'training_scheme_skill_id',
                            trainingSchemeSkillId
                        );
                        var settings = {
                            url:
                                SERVER_PATH +
                                'training-scheme/importExercisesFromFile',
                            method: 'POST',
                            timeout: 0,
                            processData: false,
                            headers: {	
                                'x-user-id': $rootScope.user_id,
                                'x-user-email': $rootScope.user_name
                            },
                            mimeType: 'multipart/form-data',
                            contentType: false,
                            data: form,
                            beforeSend: function () {
                                Swal.fire({
                                    title: 'Uploading file...',
                                    allowOutsideClick: false,
                                    onBeforeOpen: () => {
                                        Swal.showLoading(Swal.getDenyButton());
                                    },
                                });
                            },
                            complete: function () {
                                Swal.close();
                            },
                        };

                        $.ajax(settings).done(function (response) {
                            var results = JSON.parse(response);
                            if (results.status == 'ERROR') {
                                uploadEditor
                                    .field('csv')
                                    .error(
                                        ' Import was failed: ' + results.message
                                    );
                            } else {
                                uploadEditor.close();
                                BootstrapDialog.show({
                                    title: 'SUCCESS',
                                    type: BootstrapDialog.TYPE_SUCCESS,
                                    message: results.message,
                                    onhidden: function (dialogRef) {
                                        table.ajax.reload();
                                    },
                                });
                            }
                        });
                    },
                    fieldInfo:
                        'You can download the sample file <a href="' +
                        SYSTEM_IMAGE_PATH +
                        'exercises.xlsx" target="_blank">here</a>',
                },
            ],
        });

        var table = $('#trainingSchemeExercises').DataTable({
            dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
            stateSave: true,
            deferRender: true,
            ajax: {
                url: SERVER_PATH + 'training-scheme/getTrainingSchemeExercises',
                type: 'POST',
                headers: {	
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name
                },
                data: {
                    training_scheme_skill_id: trainingSchemeSkillId,
                },
                dataType: 'json',
                complete: function (response) {},
                error: function (xhr, status, error) {},
            },
            columns: [
                { data: 'training_scheme_exercises.drill', orderable: false },
                {
                    data: 'training_scheme_exercises.drill_cn',
                    orderable: false,
                },
                { data: 'training_scheme_exercises.time', orderable: false },
                { data: 'training_scheme_exercises.time_cn', orderable: false },
                { data: 'training_scheme_exercises.set', orderable: false },
                { data: 'training_scheme_exercises.set_cn', orderable: false },
                {
                    data: 'training_scheme_exercises.demonstration',
                    orderable: false,
                },
                {
                    data: 'training_scheme_exercises.objective',
                    orderable: false,
                },
                {
                    data: 'training_scheme_exercises.objective_cn',
                    orderable: false,
                },
                {
                    data: 'training_scheme_exercises.precaution',
                    orderable: false,
                },
                {
                    data: 'training_scheme_exercises.precaution_cn',
                    orderable: false,
                },
                { data: 'training_scheme_exercises.order', visible: false },
            ],
            select: {
                style: 'single',
            },
            order: [[11, 'asc']],
            displayLength: -1,
            buttons: [
                {
                    extend: 'create',
                    editor: trainingSchemeEditor,
                },
                {
                    extend: 'edit',
                    editor: trainingSchemeEditor,
                },
                {
                    extend: 'remove',
                    editor: trainingSchemeEditor,
                },
                {
                    text: 'Import exercises from file',
                    action: function () {
                        uploadEditor.create({
                            title: 'Import file exercises',
                        });
                    },
                },
                {
                    extend: 'colvis',
                    text: 'Columns',
                },
            ],
            rowReorder: {
                dataSrc: 'training_scheme_exercises.order',
                editor: trainingSchemeEditor,
            },
        });
    }
);
