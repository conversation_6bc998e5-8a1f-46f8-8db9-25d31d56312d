app.controller('logsCtrl', function ($scope, $rootScope, $routeParams, $http) {
    $('#page-wrapper').removeClass('nav-small');

    if ($.fn.daterangepicker) {
        $('input[name="dateFilter"]').daterangepicker('destroy');
    }

    // Define custom sorting function for Euro date time format
    $.fn.dataTable.ext.type.order['euro-date-pre'] = function (date) {
        var dateTimeParts = date.split(' ');
        var dateParts = dateTimeParts[0].split('/');
        var timeParts = dateTimeParts[1]
            ? dateTimeParts[1].split(':')
            : [0, 0, 0];

        // Create a Date object from the parts
        var day = parseInt(dateParts[0], 10);
        var month = parseInt(dateParts[1], 10) - 1; // Months are zero-based
        var year = parseInt(dateParts[2], 10);
        var hours = parseInt(timeParts[0], 10);
        var minutes = parseInt(timeParts[1], 10);
        var seconds = parseInt(timeParts[2], 10);

        var tempDate = new Date(
            year,
            month,
            day,
            hours,
            minutes,
            seconds
        ).getTime();

        console.warn('tempDate', tempDate); 

        return tempDate;
    };

    $.fn.dataTable.ext.type.order['euro-date-asc'] = function (a, b) {
        return a > b ? 1 : a < b ? -1 : 0;
    }


    $.fn.dataTable.ext.type.order['euro-date-desc'] = function (a, b) {
        return a < b ? 1 : a > b ? -1 : 0;
    }

    $('input[name="dateFilter"]').daterangepicker({
        startDate: moment().startOf('month'),
        endDate: moment().endOf('month'),
        locale: {
            format: 'DD/MM/YYYY',
        },
        maxSpan: {
            days: 90,
        },
        ranges: {
            Yesterday: [
                moment().subtract(1, 'days'),
                moment().subtract(1, 'days'),
            ],
            Today: [moment(), moment()],
            'Last week': [
                moment().subtract(1, 'week').startOf('week').add(1, 'days'),
                moment().subtract(1, 'week').endOf('week').add(1, 'days'),
            ],
            'This week': [
                // start week on monday
                moment().startOf('week').add(1, 'days'),
                moment().endOf('week').add(1, 'days'),
            ],
            'Last Month': [
                moment().subtract(1, 'month').startOf('month'),
                moment().subtract(1, 'month').endOf('month'),
            ],
            'This Month': [moment().startOf('month'), moment().endOf('month')],
        },
    });

    $('input[name="dateFilter"]').on(
        'apply.daterangepicker',
        function (ev, picker) {
            console.log('date change');
            $scope.start_date = picker.startDate.format('YYYY-MM-DD');
            $scope.end_date = picker.endDate.format('YYYY-MM-DD');

            setTimeout(() => {
                initLogTable();
            }, 400);
        }
    );

    // Date range picker
    $scope.start_date = $('input[name="dateFilter"]')
        .data('daterangepicker')
        .startDate.format('YYYY-MM-DD');
    $scope.end_date = $('input[name="dateFilter"]')
        .data('daterangepicker')
        .endDate.format('YYYY-MM-DD');

    setTimeout(() => {
        initLogTable();
    }, 400);

    initLogTable = function () {
        $('#log_table').DataTable().destroy();

        table = $('#log_table').DataTable({
            dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
            deferRender: true,
            searchDelay: 500,
            processing: true,
            serverSide: true,
            bDestroy: true,
            ajax: {
                url: SERVER_PATH + 'log/getDataForTable',
                type: 'POST',
                headers: {
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name,
                },
                data: {
                    event_id: $scope.event_id,
                    start_date: $scope.start_date,
                    end_date: $scope.end_date,
                },
                dataType: 'json',
                complete: function (response) {},
                error: function (xhr, status, error) {},
            },
            language: {
                paginate: {
                    previous: '<i class="fa fa-chevron-left"></i>',
                    next: '<i class="fa fa-chevron-right"></i>',
                },
            },
            columnDefs: [{ type: 'euro-date', targets: 0 }],
            columns: [
                {
                    data: 'time',
                    render: function (data, type, row, meta) {
                        // set time zone
                        return moment(data).utcOffset(UTC_OFFSET).format('DD/MM/YYYY HH:mm:ss');
                    }
                },
                {
                    data: 'url',
                },
                {
                    data: 'post_data',
                    render: function (data, type, row) {
                        return (
                            '<div class="text-trucate">' +
                            JSON.stringify(data) +
                            '</div>'
                        );
                    },
                },
                {
                    data: 'host',
                },
                {
                    data: 'browser',
                },
                {
                    data: 'status',
                    visible: false,
                },
                {
                    data: 'platform',
                    visible: false,
                },
                {
                    data: 'referer',
                    visible: false,
                },
                {
                    data: 'headers',
                    render: function (data, type, row) {
                        return JSON.stringify(data);
                    },
                },
            ],
            select: {
                style: 'single',
            },
            order: [[0, 'desc']],
            lengthMenu: [
                [10, 25, 50, 100],
                [10, 25, 50, 100],
            ],
            buttons: [
                {
                    text: 'Delete all logs',
                    className: 'btn btn-danger',
                    action: function (e, dt, node, config) {
                        if (
                            confirm('Are you sure you want to delete all logs?')
                        ) {
                            $http({
                                url: SERVER_PATH + 'log/deleteAllDataFromLog',
                                method: 'POST',
                                headers: {
                                    'x-user-id': $rootScope.user_id,
                                    'x-user-email': $rootScope.user_name,
                                },
                            }).then(
                                function (response) {
                                    table.ajax.reload();
                                    swal(
                                        'Success',
                                        'Delete all logs successfully',
                                        'success'
                                    );
                                    table.ajax.reload();
                                },
                                function (response) {
                                    swal(
                                        'Error',
                                        'Delete all logs failed',
                                        'error'
                                    );
                                }
                            );
                        }
                    },
                },
                { extend: 'colvis', text: 'Columns' },
            ],
        });
    };
});
