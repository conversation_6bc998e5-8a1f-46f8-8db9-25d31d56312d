var ACTION_APPROVE = 1;
var ACTION_UNAPPROVE = 2;

app.controller(
    'registrationsDistrictCtrl',
    function ($scope, $rootScope, $routeParams, $http) {
        $('#page-wrapper').removeClass('nav-small');

        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            'justNum-pre': (a) => parseFloat(a.replace(/\D/g, '')),
            'justNum-asc': (a, b) => a - b,
            'justNum-desc': (a, b) => b - a,
        });

        var event_id = $routeParams.id;
        $scope.groups = [];
        $scope.districts = [];

        jQuery.ajax({
            type: 'POST',
            url: SERVER_PATH + 'event/getEventInfo',
            async: false,
            headers: {	
                'x-user-id': $rootScope.user_id,
                'x-user-email': $rootScope.user_name
            },
            data: {
                event_id: event_id,
            },
            dataType: 'json',
            complete: function (response) {
                var jsonData = JSON.parse(response.responseText);
                var event = jsonData.info;
                $scope.event_name = event.name;
                $scope.event_type = event.type;
            },
        });

        $.fn.dataTable.moment('D-MMM-YYYY HH:mm:ss');
        $.fn.dataTable.moment('D-MMM-YYYY');

        $scope.goBack = function () {
            window.history.back();
        };

        function initRegistrationsTable() {
            setTimeout(() => {
                tableRegistration = $('#district_table').DataTable({
                    dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
                    deferRender: true,
                    processing: true,
                    serverSide: true,
                    bDestroy: true,
                    search: {
                        caseInsensitive: true,
                        regex: true,
                    },
                    ajax: {
                        url: SERVER_PATH + 'district/getRegistrationOfDistrict',
                        type: 'POST',
                        headers: {	
                            'x-user-id': $rootScope.user_id,
                            'x-user-email': $rootScope.user_name
                        },
                        data: {
                            event_id: event_id,
                            user_id: $rootScope.user_id,
                        },
                        dataType: 'json',
                        complete: function (response) {},
                        error: function (xhr, status, error) {},
                    },
                    language: {
                        paginate: {
                            previous: '<i class="fa fa-chevron-left"></i>',
                            next: '<i class="fa fa-chevron-right"></i>',
                        },
                    },
                    columns: [
                        {
                            data: 'DT_RowId',
                            targets: 0,
                            render: function (data, type, row, meta) {
                                if (type === 'display') {
                                    data =
                                        '<div class="checkbox"><input type="checkbox" class="dt-checkboxes"><label></label></div>';
                                }
                                return data;
                            },
                            checkboxes: {
                                selectRow: true,
                                selectAllRender:
                                    '<div class="checkbox"><input type="checkbox" class="dt-checkboxes"><label></label></div>',
                            },
                        },
                        {
                            data: 'players.player_photo',
                            className: 'avatar',
                            orderable: false,
                            render: function (data) {
                                if (data !== null && data !== '') {
                                    return (
                                        '<img src="' +
                                        PRODUCT_IMAGE_PATH +
                                        data +
                                        '">'
                                    );
                                } else {
                                    return (
                                        '<img src="' +
                                        SYSTEM_IMAGE_PATH +
                                        'favicon.png">'
                                    );
                                }
                            },
                        },
                        {
                            data: 'full_name',
                            render: function (data, type, row) {
                                return (
                                    row.players.surname +
                                    ' ' +
                                    row.players.other_name
                                );
                            },
                        },
                        {
                            data: 'players.chinese_name',
                        },
                        {
                            data: 'players.dob',
                            className: 'center',
                            // visible: false
                        },
                        {
                            data: 'players.gender',
                            className: 'center',
                            // visible: false
                        },
                        {
                            data: 'parens.surname',
                            render: function (data, type, row) {
                                return (
                                    row.parens.surname +
                                    ' ' +
                                    row.parens.other_name
                                );
                            },
                            visible: false,
                        },
                        {
                            data: 'parens.email',
                            className: 'center',
                            visible: false,
                        },
                        {
                            data: 'parens.phone',
                            className: 'center',
                            visible: false,
                        },
                        {
                            data: 'groups.id',
                            className: 'center',
                            render: function (data, type, row) {
                                return row.groups.name;
                            },
                        },
                        {
                            data: 'districts.id',
                            render: function (data, type, row) {
                                return row.districts.name;
                            },
                        },
                        {
                            data: 'registrations.goalkeeper',
                            render: function (data, type, row) {
                                let show_checkbox = '';
                                if (data == 1) {
                                    show_checkbox = 'checked';
                                }
                                return (
                                    '<input type="checkbox" class="shipping_chkbox" ' +
                                    ' name="' +
                                    row.registrations.id +
                                    '"' +
                                    show_checkbox +
                                    '>'
                                );
                            },
                        },
                        {
                            data: 'registrations.registered_date',
                        },
                        {
                            data: 'registrations.approved_date',
                        },
                        {
                            data: 'registrations.approval_status',
                            className: 'center',
                        },
                        {
                            data: 'registrations.emailed',
                            render: function (data, type, row, meta) {
                                // show badge
                                if (data == 0) {
                                    return '<span class="badge badge-info">Didn\'t send</span>';
                                } else if (data == 1 || data == 2) {
                                    return (
                                        '<span class="badge badge-danger">Can\'t send(' +
                                        data +
                                        ')</span>'
                                    );
                                } else if (data == 'Done') {
                                    return '<span class="badge badge-success"> Sent</span>';
                                }
                            },
                        },
                        {
                            data: 'registrations.sent_invoice_email',
                            render: function (data, type, row, meta) {
                                // show badge
                                if (data == 0) {
                                    return '<span class="badge badge-info">Didn\'t send</span>';
                                } else if (data == 1 || data == 2) {
                                    return (
                                        '<span class="badge badge-danger">Can\'t send(' +
                                        data +
                                        ')</span>'
                                    );
                                } else if (data == 'Done') {
                                    return '<span class="badge badge-success"> Sent</span>';
                                }
                            },
                        },
                        {
                            data: 'registrations.sent_approval_email',
                            render: function (data, type, row, meta) {
                                // show badge
                                if (data == 0) {
                                    return '<span class="badge badge-info">Didn\'t send</span>';
                                } else if (data == 1 || data == 2) {
                                    return (
                                        '<span class="badge badge-danger">Can\'t send(' +
                                        data +
                                        ')</span>'
                                    );
                                } else if (data == 'Done') {
                                    return '<span class="badge badge-success"> Sent</span>';
                                }
                            },
                        },
                    ],
                    initComplete: function (settings, json) {
                        $scope.groups = json.options['groups.id'];
                        $scope.districts = json.options['districts.id'];

                        // build the select list
                        var age_group_column = {
                            orderColumn: 9,
                            elementId: 'age-group-content',
                            options: json.options['groups.id'],
                            selectId: 'selType',
                        };
                        var district_column = {
                            orderColumn: 10,
                            elementId: 'district-content',
                            options: json.options['districts.id'],
                            selectId: 'selType',
                        };

                        var approval_column = {
                            orderColumn: 14,
                            elementId: 'approval-content',
                            options:
                                json.options['registrations.approval_status'],
                            selectId: 'selType',
                        };

                        var goalkeeper_column = {
                            orderColumn: 11,
                            elementId: 'goalkeeper-content',
                            options: json.options['registrations.goalkeeper'],
                            selectId: 'selType',
                        };

                        filterColumns = [
                            age_group_column,
                            district_column,
                            approval_column,
                            goalkeeper_column,
                        ];

                        filterColumns.forEach((item) => {
                            this.api()
                                .columns(item.orderColumn)
                                .every(function () {
                                    var column = this;
                                    var select = $(
                                        `<select id="${item.orderColumn}" class="form-control"><option value="">All</option></select>`
                                    )
                                        .appendTo($(`#${item.elementId}`))
                                        .on('change', function () {
                                            if ($(this).val()) {
                                                var val =
                                                    $.fn.dataTable.util.escapeRegex(
                                                        $(this).val()
                                                    );
                                                column
                                                    .search(
                                                        val ? val : '',
                                                        true,
                                                        false
                                                    )
                                                    .draw();
                                            } else {
                                                column
                                                    .search('', true, false)
                                                    .draw();
                                            }
                                        })
                                        .select2();

                                    item.options.forEach(function (d) {
                                        select.append(
                                            `<option value="${d.value}">${d.label}</option>`
                                        );
                                    });
                                });
                        });
                    },
                    select: {
                        style: SELECT_MODE,
                        selector: 'td:not(:last-child)',
                    },
                    columnDefs: [
                        {
                            type: 'justNum',
                            targets: 8,
                        },
                    ],
                    order: [[9, 'asc']],
                    buttons: [
                        {
                            extend: 'collection',
                            text: 'Actions <i class="fa fa-angle-down"></i>',
                            className: 'btn btn-primary',
                            autoClose: true,
                            buttons: [
                                {
                                    extend: 'selectedSingle',
                                    text: '<i class="fa fa-undo"></i> Cancel Registration',
                                    attr: {
                                        id: 'btnCancelRegistration',
                                    },
                                    action: function () {
                                        console.log('Cancel registration');
                                        var rows_selected = tableRegistration
                                            .rows({ selected: true })
                                            .data()[0];
                                        console.log(rows_selected);
                                        var registration_id =
                                            rows_selected.registrations.id;
                                        console.log(
                                            'registration_id: ' +
                                                registration_id
                                        );

                                        // show confirm dialog before refund
                                        Swal.fire({
                                            title: 'Are you sure?',
                                            text: 'You want to cancel this payment?',
                                            type: 'warning',
                                            icon: 'warning',
                                            showCancelButton: true,
                                            confirmButtonClass:
                                                'btn btn-primary',
                                            confirmButtonText:
                                                'Yes, cancel it!',
                                            closeOnConfirm: false,
                                        }).then((result) => {
                                            if (result.value) {
                                                //call ajax to update invoice status
                                                jQuery.ajax({
                                                    type: 'POST',
                                                    url: `${SERVER_PATH}district/cancelRegistration`,
                                                    async: false,
                                                    headers: {	
                                                        'x-user-id': $rootScope.user_id,
                                                        'x-user-email': $rootScope.user_name
                                                    },
                                                    data: {
                                                        user_id: user_id,
                                                        registration_id:
                                                            registration_id,
                                                    },
                                                    dataType: 'json',
                                                    complete: function (
                                                        response
                                                    ) {
                                                        var jsonData =
                                                            JSON.parse(
                                                                response.responseText
                                                            );
                                                        if (
                                                            jsonData.status ==
                                                            'OK'
                                                        ) {
                                                            Swal.fire({
                                                                title: 'Success',
                                                                text: 'Registration has been cancelled successfully!',
                                                                type: 'success',
                                                                icon: 'success',
                                                                showCancelButton: false,
                                                                confirmButtonClass:
                                                                    'btn-success',
                                                                confirmButtonText:
                                                                    'OK',
                                                                closeOnConfirm: false,
                                                            });
                                                            tableRegistration.ajax.reload();
                                                        } else {
                                                            Swal.fire({
                                                                title: 'Error',
                                                                text: jsonData.message,
                                                                icon: 'error',
                                                                type: 'error',
                                                                showCancelButton: false,
                                                                confirmButtonClass:
                                                                    'btn-danger',
                                                                confirmButtonText:
                                                                    'OK',
                                                                closeOnConfirm: false,
                                                            });
                                                        }
                                                    },
                                                });
                                            }
                                        });
                                    },
                                },
                            ],
                        },
                        {
                            extend: 'excel',
                            name: 'excel',
                            text: '<i class="fa fa-file-excel-o"></i>&emsp;Export to Excel',
                            titleAttr: 'Export data to an Excel file',
                            filename: 'District - Registration',
                            title:
                                'District - Registration - ' +
                                $scope.event_name,
                            exportOptions: {
                                columns: ':visible',
                                modifier: {
                                    autoFilter: true,
                                    // selected: true
                                },
                            },
                            action: exportAllToExcel,
                        },
                        {
                            text: 'Approve',
                            extend: 'selected',
                            action: function () {
                                checkRequest(ACTION_APPROVE, 'Approve');
                            },
                        },
                        {
                            text: 'Unapprove',
                            extend: 'selected',
                            action: function () {
                                checkRequest(ACTION_UNAPPROVE, 'Unapprove');
                            },
                        },
                        {
                            extend: 'selectedSingle',
                            text: 'Delete Registration',
                            action: function () {
                                deleteRegistration();
                            },
                        },
                        {
                            extend: 'selectedSingle',
                            text: 'Edit Registration',
                            action: function () {
                                editRegistration();
                            },
                        },
                        {
                            extend: 'selectedSingle',
                            text: '<i class="fa fa-envelope"></i> Resend Confirmation Email',
                            action: function () {
                                resendConfirmationEmail();
                            },
                        },
                        {
                            extend: 'selectedSingle',
                            text: '<i class="fa fa-envelope"></i> Send Approval Email',
                            action: function () {
                                sendApprovalEmail();
                            },
                        },
                        {
                            extend: 'colvis',
                            text: 'Columns',
                        },
                    ],
                });

                tableRegistration.on(
                    'click',
                    'input[type="checkbox"][class="shipping_chkbox"]',
                    function (e, dt, type, indexes) {
                        var input = $(this);
                        var name = input.attr('name');

                        var checked = input.prop('checked');

                        $scope.checkGoalkeeper(name, checked);
                    }
                );
            }, 400);
        }

        $scope.checkGoalkeeper = function (registration_id, status) {
            $.ajax({
                type: 'POST',
                url: SERVER_PATH + 'district/checkGoalkeeper',
                headers: {	
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name
                },
                data: {
                    registration_id: registration_id,
                    status: status,
                },
                async: false,
                dataType: 'json',
                complete: function (response) {
                    var jsonData = JSON.parse(response.responseText);
                    if (jsonData.status == 'OK') {
                        Swal.fire({
                            toast: true,
                            icon: 'success',
                            title: jsonData.message,
                            animation: false,
                            position: 'top-right',
                            showConfirmButton: false,
                            timer: 2000,
                            timerProgressBar: true,
                            didOpen: (toast) => {
                                toast.addEventListener(
                                    'mouseenter',
                                    Swal.stopTimer
                                );
                                toast.addEventListener(
                                    'mouseleave',
                                    Swal.resumeTimer
                                );
                            },
                        });
                    } else {
                        Swal.fire({
                            toast: true,
                            icon: 'error',
                            title: jsonData.message,
                            animation: false,
                            position: 'top-right',
                            showConfirmButton: false,
                            timer: 2000,
                            timerProgressBar: true,
                            didOpen: (toast) => {
                                toast.addEventListener(
                                    'mouseenter',
                                    Swal.stopTimer
                                );
                                toast.addEventListener(
                                    'mouseleave',
                                    Swal.resumeTimer
                                );
                            },
                        });

                        tableRegistration.ajax.reload();
                    }
                },
                error: function (xhr, status, error) {
                    console.log(error);
                },
            });
        };

        // Check request
        function checkRequest(action_id, action) {
            var table_selected = tableRegistration
                .rows({ selected: true })
                .data();
            if (DEVELOPMENT_ENVIRONMENT) console.log(table_selected);

            var players = '';
            var name_index = 0;
            var rows_selected = [];
            for (var i = 0; i < table_selected.length; i++) {
                rows_selected.push(table_selected[i].registrations.id);
                name_index = i + 1;
                players +=
                    name_index +
                    '. ' +
                    table_selected[i].players.surname +
                    ' ' +
                    table_selected[i].players.other_name +
                    '<br/>';
            }

            if (DEVELOPMENT_ENVIRONMENT) console.log(rows_selected);
            var countRows = rows_selected.length;
            rows_selected.sort();
            var selectedRows = rows_selected.join('_');

            var message =
                countRows == 1
                    ? '<strong>1 registration? </strong><br/>' + players + ''
                    : '<strong>' +
                      countRows +
                      ' registrations?</strong><br/>' +
                      players +
                      '';

            if (selectedRows == '') {
                // Not selected
                BootstrapDialog.show({
                    title: 'Information - ' + action,
                    type: BootstrapDialog.TYPE_WARNING,
                    message: 'At least one registration must be selected!',
                });
            } else {
                var title = 'Confirmation - ' + action;
                var dialog = BootstrapDialog.confirm(
                    title,
                    message,
                    function (result) {
                        if (result) {
                            dialog.close();
                            if (DEVELOPMENT_ENVIRONMENT)
                                console.log(selectedRows);
                            if (action_id == ACTION_APPROVE) {
                                ajaxApproveAction(
                                    selectedRows,
                                    SERVER_PATH +
                                        'district/approveCoursesFromWaiting',
                                    action,
                                    action_id
                                );
                            } else if (action_id == ACTION_UNAPPROVE) {
                                ajaxUnApproveAction(selectedRows);
                            } else {
                                Swal.fire({
                                    title: 'ERROR!',
                                    text: 'Invalid action',
                                    icon: 'error',
                                    type: 'error',
                                });
                            }
                        }
                    }
                );
                if (name_index > 15) {
                    dialog.getModalBody().css('height', '330px');
                    dialog.getModalBody().css('overflow-y', 'scroll');
                }
            }
        }

        function resendConfirmationEmail() {
            var table_selected = tableRegistration
                .rows({ selected: true })
                .data();

            var player = table_selected[0];

            var DT_RowId = player['DT_RowId'];

            var registration_id = DT_RowId.split('_')[1];
            BootstrapDialog.confirm(
                'Resend Confirmation Email',
                'Are you sure to resend confirmation email?',
                function (result) {
                    if (result) {
                        jQuery.ajax({
                            type: 'POST',
                            url:
                                SERVER_PATH +
                                'district/resendConfirmationEmail',
                            async: true,
                            headers: {	
                                'x-user-id': $rootScope.user_id,
                                'x-user-email': $rootScope.user_name
                            },
                            data: {
                                registration_id: registration_id,
                            },
                            dataType: 'json',
                            beforeSend: function () {
                                Swal.fire({
                                    title: 'Please Wait!',
                                    allowOutsideClick: false,
                                    didOpen: () => {
                                        Swal.showLoading();
                                    },
                                });
                            },
                            complete: function (response) {
                                Swal.close();
                                var jsonData = JSON.parse(
                                    response.responseText
                                );

                                tableRegistration.ajax.reload();

                                if (jsonData.status == 'OK') {
                                    Swal.fire({
                                        type: 'success',
                                        icon: 'success',
                                        title: jsonData.message,
                                        confirmButtonClass: 'btn btn-primary',
                                        buttonsStyling: false,
                                    });
                                } else {
                                    Swal.fire({
                                        title: 'ERROR!',
                                        text: jsonData.message,
                                        icon: 'error',
                                        type: 'error',
                                    });
                                }
                            },
                        });
                    }
                }
            );
        }

        function sendApprovalEmail() {
            var table_selected = tableRegistration
                .rows({ selected: true })
                .data();

            var player = table_selected[0];

            var DT_RowId = player['DT_RowId'];

            var registration_id = DT_RowId.split('_')[1];
            BootstrapDialog.confirm(
                'Send Approval Email',
                'Are you sure to send approval email?',
                function (result) {
                    if (result) {
                        jQuery.ajax({
                            type: 'POST',
                            url:
                                SERVER_PATH +
                                'district/sendApproveMessageForRegistration',
                            async: true,
                            data: {
                                registration_id: registration_id,
                            },
                            dataType: 'json',
                            beforeSend: function () {
                                Swal.fire({
                                    title: 'Please Wait!',
                                    allowOutsideClick: false,
                                    didOpen: () => {
                                        Swal.showLoading();
                                    },
                                });
                            },
                            complete: function (response) {
                                Swal.close();
                                var jsonData = JSON.parse(
                                    response.responseText
                                );

                                tableRegistration.ajax.reload();

                                if (jsonData.status == 'OK') {
                                    Swal.fire({
                                        type: 'success',
                                        icon: 'success',
                                        title: jsonData.message,
                                        confirmButtonClass: 'btn btn-primary',
                                        buttonsStyling: false,
                                    });
                                } else {
                                    Swal.fire({
                                        title: 'ERROR!',
                                        text: jsonData.message,
                                        icon: 'error',
                                        type: 'error',
                                    });
                                }
                            },
                        });
                    }
                }
            );
        }

        function deleteRegistration() {
            var table_selected = tableRegistration
                .rows({ selected: true })
                .data();

            var player = table_selected[0];

            var DT_RowId = player['DT_RowId'];

            var registration_id = DT_RowId.split('_')[1];
            BootstrapDialog.confirm(
                'Delete Registration',
                'Are you sure to delete this registration?',
                function (result) {
                    if (result) {
                        jQuery.ajax({
                            type: 'POST',
                            url: SERVER_PATH + 'district/deleteRegistraion',
                            async: true,
                            headers: {	
                                'x-user-id': $rootScope.user_id,
                                'x-user-email': $rootScope.user_name
                            },
                            data: {
                                registration_id: registration_id,
                            },
                            dataType: 'json',
                            beforeSend: function () {
                                Swal.fire({
                                    title: 'Please Wait!',
                                    allowOutsideClick: false,
                                    didOpen: () => {
                                        Swal.showLoading();
                                    },
                                });
                            },
                            complete: function (response) {
                                Swal.close();
                                var jsonData = JSON.parse(
                                    response.responseText
                                );

                                tableRegistration.ajax.reload();

                                if (jsonData.status == 'OK') {
                                    Swal.fire({
                                        type: 'success',
                                        icon: 'success',
                                        title: jsonData.message,
                                        confirmButtonClass: 'btn btn-primary',
                                        buttonsStyling: false,
                                    });
                                } else {
                                    Swal.fire({
                                        title: 'ERROR!',
                                        text: jsonData.message,
                                        icon: 'error',
                                        type: 'error',
                                    });
                                }
                            },
                        });
                    }
                }
            );
        }

        function editRegistration() {
            var table_selected = tableRegistration
                .rows({ selected: true })
                .data();

            var player = table_selected[0];

            var team_district_player_id = player['team_district_players']['id'];

            var player_selected_district =
                player['team_districts']['district_id'];

            var player_age_group = player['team_districts']['group_id'];

            jQuery.ajax({
                type: 'POST',
                url: SERVER_PATH + 'district/getAllAgeGroupsAtSelectedPlayer',
                async: false,
                headers: {	
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name
                },
                data: {
                    team_district_player_id: team_district_player_id,
                },
                dataType: 'json',
                beforeSend: function () {
                    Swal.fire({
                        title: 'Please Wait!',
                        allowOutsideClick: false,
                        didOpen: () => {
                            Swal.showLoading();
                        },
                    });
                },
                complete: function (response) {
                    Swal.close();
                    var jsonData = JSON.parse(response.responseText);
                    if (jsonData.status == 'OK') {
                        groups = jsonData.data.groups;

                        selectedPlayerGroup = jsonData.data.selected_group;
                    } else {
                        Swal.fire({
                            title: 'ERROR!',
                            text: jsonData.message,
                            icon: 'error',
                            type: 'error',
                        });
                    }
                },
            });

            jQuery.ajax({
                type: 'POST',
                url: SERVER_PATH + 'district/getAllDistrictOfAgeGroup',
                async: false,
                headers: {	
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name
                },
                data: {
                    age_group_id: selectedPlayerGroup.id,
                },
                dataType: 'json',
                beforeSend: function () {
                    Swal.fire({
                        title: 'Please Wait!',
                        allowOutsideClick: false,
                        didOpen: () => {
                            Swal.showLoading();
                        },
                    });
                },
                complete: function (response) {
                    Swal.close();
                    var jsonData = JSON.parse(response.responseText);

                    if (jsonData.status == 'OK') {
                        districts = jsonData.data;
                    } else {
                        Swal.fire({
                            title: 'ERROR!',
                            text: jsonData.message,
                            icon: 'error',
                            type: 'error',
                        });
                    }
                },
            });

            var editor_edit_registration = new $.fn.dataTable.Editor({
                ajax: {
                    type: 'POST',
                    url: SERVER_PATH + 'district/editRegistration',
                    headers: {	
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name
                    },
                    data: {
                        event_id: event_id,
                        user_id: $rootScope.user_id,
                        team_district_player_id: team_district_player_id,
                    },
                    dataType: 'json',
                    complete: function (response) {
                        var jsonData = JSON.parse(response.responseText);
                        if (jsonData.status == 'OK') {
                            Swal.fire({
                                toast: true,
                                icon: 'success',
                                title: jsonData.message,
                                animation: false,
                                position: 'top-right',
                                showConfirmButton: false,
                                timer: 2000,
                                timerProgressBar: true,
                                didOpen: (toast) => {
                                    toast.addEventListener(
                                        'mouseenter',
                                        Swal.stopTimer
                                    );
                                    toast.addEventListener(
                                        'mouseleave',
                                        Swal.resumeTimer
                                    );
                                },
                            });

                            tableRegistration.ajax.reload();
                        } else {
                            Swal.fire({
                                title: 'ERROR!',
                                text: jsonData.message,
                                icon: 'error',
                                type: 'error',
                            });
                        }
                    },
                    error: function (xhr, status, error) {
                        console.log(error);
                    },
                },
                formOptions: {
                    main: {
                        onBlur: 'none',
                    },
                },
                fields: [
                    {
                        label: 'Age Group:',
                        name: 'age_group_id',
                        type: 'select2',
                        opts: {
                            placeholder: 'Select Age Group',
                        },
                        options: $scope.groups,
                        default: player_age_group,
                    },
                    {
                        label: 'Team District:',
                        name: 'districs_target',
                        type: 'select2',
                        opts: {
                            placeholder: 'Select a District',
                        },
                        options: $scope.districts,
                        default: player_selected_district,
                    },
                ],
            });

            editor_edit_registration
                .title('Edit Registration')
                .buttons({
                    label: 'Save',
                    fn: function () {
                        this.submit();
                    },
                })
                .edit()
                .open();

            editor_edit_registration.dependent(
                'age_group_id',
                function (val, data, callback) {
                    // get selected row
                    var table_selected = tableRegistration
                        .rows({ selected: true })
                        .data()[0];
                    var player_selected_district =
                        table_selected['team_districts']['district_id'];
                    jQuery.ajax({
                        type: 'POST',
                        url: SERVER_PATH + 'district/getAllDistrictOfAgeGroup',
                        headers: {	
                            'x-user-id': $rootScope.user_id,
                            'x-user-email': $rootScope.user_name
                        },
                        async: false,
                        data: {
                            age_group_id: val,
                        },
                        dataType: 'json',
                        beforeSend: function () {
                            Swal.fire({
                                title: 'Please Wait!',
                                allowOutsideClick: false,
                                didOpen: () => {
                                    Swal.showLoading();
                                },
                            });
                        },
                        complete: function (response) {
                            Swal.close();
                            var jsonData = JSON.parse(response.responseText);

                            if (jsonData.status == 'OK') {
                                districts = jsonData.data;
                                // update the options list for second select box
                                editor_edit_registration
                                    .field('districs_target')
                                    .update(
                                        districts.map(function (district) {
                                            return {
                                                label: district.name,
                                                value: district.id,
                                            };
                                        })
                                    );

                                // check player_registration['district_region'] is in the new options list
                                var isDistrictExist = false;
                                for (var i = 0; i < districts.length; i++) {
                                    if (
                                        districts[i].id ==
                                        player_selected_district
                                    ) {
                                        isDistrictExist = true;
                                        break;
                                    }
                                }

                                if (isDistrictExist) {
                                    editor_edit_registration
                                        .field('districs_target')
                                        .set(player_selected_district);
                                } else {
                                    // check districts[0] is not null
                                    if (districts[0] != null) {
                                        editor_edit_registration
                                            .field('districs_target')
                                            .set(districts[0].id);
                                    }
                                }
                            } else {
                                Swal.fire({
                                    title: 'ERROR!',
                                    text: jsonData.message,
                                    icon: 'error',
                                    type: 'error',
                                });
                            }
                        },
                    });

                    return true;
                }
            );
        }

        function ajaxApproveAction(registration_ids, url, action, action_id) {
            console.log(`ajaxApproveAction: ${registration_ids}`);
            jQuery.ajax({
                type: 'POST',
                url: SERVER_PATH + 'district/approveCoursesFromWaiting',
                async: true,
                headers: {	
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name
                },
                data: {
                    registration_ids: registration_ids,
                },
                dataType: 'json',
                beforeSend: function () {
                    Swal.fire({
                        title: 'Please Wait!',
                        html: 'Server is processing!', // add html attribute if you want or remove
                        allowOutsideClick: false,
                        didOpen: () => {
                            Swal.showLoading();
                        },
                    });
                },
                complete: function (response) {
                    Swal.close();
                    var jsonData = JSON.parse(response.responseText);

                    console.log(jsonData);
                    tableRegistration.ajax.reload();

                    if (jsonData.status == 'OK') {
                        Swal.fire({
                            type: 'success',
                            icon: 'success',
                            title: jsonData.message,
                            confirmButtonClass: 'btn btn-primary',
                            buttonsStyling: false,
                        });
                    } else {
                        Swal.fire({
                            title: 'ERROR!',
                            text: jsonData.message,
                            icon: 'error',
                            type: 'error',
                        });
                    }
                },
            });
        }

        function ajaxUnApproveAction(registration_ids) {
            console.log(`ajaxApproveAction: ${registration_ids}`);

            console.log(`ajaxApproveAction: ${registration_ids}`);
            jQuery.ajax({
                type: 'POST',
                url: SERVER_PATH + 'district/unapproveCoursesFromWaiting',
                async: true,
                headers: {	
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name
                },
                data: {
                    registration_ids: registration_ids,
                },
                dataType: 'json',
                beforeSend: function () {
                    Swal.fire({
                        title: 'Please Wait!',
                        html: 'Mail is sending!', // add html attribute if you want or remove
                        allowOutsideClick: false,
                        didOpen: () => {
                            Swal.showLoading();
                        },
                    });
                },
                complete: function (response) {
                    Swal.close();
                    var jsonData = JSON.parse(response.responseText);

                    console.log(jsonData);
                    tableRegistration.ajax.reload();

                    if (jsonData.status == 'OK') {
                        Swal.fire({
                            type: 'success',
                            icon: 'success',
                            title: jsonData.message,
                            confirmButtonClass: 'btn btn-primary',
                            buttonsStyling: false,
                        });
                    } else {
                        Swal.fire({
                            title: 'ERROR!',
                            text: jsonData.message,
                            icon: 'error',
                            type: 'error',
                        });
                    }
                },
            });
        }

        function onlyUnique(value, index, self) {
            return self.indexOf(value) === index;
        }

        initRegistrationsTable();
    }
);
