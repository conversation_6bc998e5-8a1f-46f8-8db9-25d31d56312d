app.controller('certificateCtrl', function ($scope, $location, $rootScope, $routeParams, $http) {
	$('#page-wrapper').removeClass('nav-small');

	$scope.eventId = $routeParams.eventId;

	if ($.fn.dataTable.isDataTable('#certificateTable')) {
		$('#certificateTable').DataTable().destroy();
	}

	jQuery.ajax({
		type: 'POST',
		url: SERVER_PATH + 'event/getEventInfo',
		async: false,
		data: {
			event_id: $scope.eventId,
		},
		headers: {	
			'x-user-id': $rootScope.user_id,
			'x-user-email': $rootScope.user_name
		},
		dataType: 'json',
		complete: function (response) {
			var jsonData = JSON.parse(response.responseText);
			var event = jsonData.info;
			$scope.event_name = event.name;
			$scope.event_type = event.type;
		},
	});

	var table;

	var editor = new $.fn.dataTable.Editor({
		ajax: {
			type: 'POST',
			url: SERVER_PATH + "certificate/setCertificates",
			headers: {	
				'x-user-id': $rootScope.user_id,
				'x-user-email': $rootScope.user_name
			},
			data: {},
			dataType: 'json',
			complete: function (response) {
				table.ajax.reload();
			},
			error: function (xhr, status, error) { },
		},
		table: "#certificateTable",
		formOptions: {
			main: {
				onBlur: 'none',
			}
		},
		i18n: {
			remove: {
				button: "Delete",
				title: "Delete Certificate",
				submit: "Delete",
				confirm: {
					_: "Are you sure you want to delete these certificate?",
					1: "Are you sure you want to delete this certificate?"
				}
			},
			error: {
				system: "System error, please contact administrator."
			},
		},
		fields: [
			{
				label: "Name:",
				name: "certificates.name",
			}
		]
	});

	table = $('#certificateTable').DataTable({
		dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
		ajax: {
			url: SERVER_PATH + "certificate/getCertificates",
			type: 'POST',
			dataType: 'json',
			headers: {	
				'x-user-id': $rootScope.user_id,
				'x-user-email': $rootScope.user_name
			},
			data: {
				event_id: $scope.eventId
			},
			complete: function (response) {
			},
			error: function (xhr, status, error) { },
		}, language: {
			paginate: {
				previous: '<i class="fa fa-chevron-left"></i>',
				next: '<i class="fa fa-chevron-right"></i>'
			}
		},
		columns: [
			{
				data: 'certificates.img',
				className: "trainingSchemeGroups",
				render: function (data, type, row) {
					return '<img src="' + data + '"/>';
				}
			},
			{
				data: 'certificates.name'
			},
			{
				data: null,
				render: function (data, type, row) {
					return '<a class="btn btn-primary" data-match-route="/certificates/print/' + row.certificates.id + '/'+$scope.eventId+'?certificateName=' + row.certificates.name + '" href="#/certificates/print/' + row.certificates.id +'/'+$scope.eventId+'?certificateName=' + row.certificates.name + '"> Manage Players</a>';
				}
			}
		],
		select: {
			style: 'single'
		},
		buttons: [
			{
				extend: 'collection',
				text: 'Create New Certificate',
				autoClose: true,
				buttons: [
					{
						text: 'Create portrait certificate',
						action: function () {
							$rootScope.$evalAsync(function () {
								$location.path('/certificates/create/portrait/'+$scope.eventId);
							});
						}
					},
					{
						text: 'Create landscape certificate',
						action: function () {
							$rootScope.$evalAsync(function () {
								$location.path('/certificates/create/landscape/'+$scope.eventId);
							});
						}
					}
				]
			},
			{
				extend: "remove",
				editor: editor
			}]
	})
})