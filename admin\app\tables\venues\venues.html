<div class="row" id="tab-page">
    <div class="row">
        <div class="col-lg-12">
            <ol class="breadcrumb">
                <li>Setup</li>
                <li class="active"><span>Venues</span></li>
            </ol>
        </div>
    </div>
</div>
<div class="row">
    <div class="col-lg-12">
        <div class="clearfix">
            <h1 class="pull-left">Venues</h1>
        </div>
    </div>
</div>
<div class="row">
    <div class="col-lg-12">
        <div class="main-box clearfix">
            <div class="main-box-body clearfix">
                <div class="table-responsive">
                    <table id="venue_table" class="table table-striped table-bordered table-hover" cellspacing="0"
                        width="100%">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Chinese Name</th>
                                <th>Address</th>
                                <th>Chinese Address</th>
                                <th>Region</th>
                                <th>Chinese Region</th>
                                <th>Enquiry No.</th>
                                <th>Latitude</th>
                                <th>Longitude</th>
                                <th>Surface</th>
                                <th>Parking</th>
                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript" language="javascript" class="init">

    $(document).ready(function () {

        user_id = localStorage.getItem('hkjflApp.user_id');
		user_name = localStorage.getItem('hkjflApp.user_name');

        var editor = new $.fn.dataTable.Editor({
            ajax: {
                type: 'POST',
                url: SERVER_PATH + "venues/setVenues",
                data: {
                    // "pgroup_id": pgroup_id
                },
                headers: {
                    'x-user-id': user_id,
                    'x-user-email': user_name
                },
                dataType: 'json',
                complete: function (response) {
                },
                error: function (xhr, status, error) {
                },
            },
            table: "#venue_table",
            formOptions: {
                main: {
                    onBlur: 'none'
                }
            },
            i18n: {
                create: {
                    button: "New",
                    title: "Create new venue",
                    submit: "Create"
                },
                edit: {
                    button: "Edit",
                    title: "Edit venue",
                    submit: "Save"
                },
                remove: {
                    button: "Delete",
                    title: "Delete venue",
                    submit: "Delete",
                    confirm: {
                        _: "Are you sure you want to delete these venues?",
                        1: "Are you sure you want to delete this venue?"
                    }
                },
                error: {
                    system: "System error, please contact administrator."
                },
            },
            fields: [{
                label: "Name:",
                name: "name"
            }, {
                label: "Chinese Name:",
                name: "chinese_name",
            }, {
                label: "Address:",
                name: "address"
            }, {
                label: "Chinese Address:",
                name: "chinese_address",
            }, {
                label: "Region:",
                name: "region"
            }, {
                label: "Chinese Region:",
                name: "chinese_region",
            }, {
                label: "Enquiry No.:",
                name: "enquiry_no",
            }, {
                label: "Latitude:",
                name: "latitude"
            }, {
                label: "Longitude:",
                name: "longitude"
            }, {
                label: "Surface:",
                name: "surface"
            }, {
                label: "Parking:",
                name: "parking"
            }
            ]
        });

        var table = $('#venue_table').DataTable({
            dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
            stateSave: true,
            deferRender: true,
            ajax: {
                url: SERVER_PATH + "venues/getVenues",
                type: 'POST',
                data: {
                    // "pgroup_id": pgroup_id
                },
                headers: {
                    'x-user-id': user_id,
                    'x-user-email': user_name
                },
                dataType: 'json',
                complete: function (response) {
                },
                error: function (xhr, status, error) {
                },
            },
            language: {
                info: "Showing _START_ to _END_ of _TOTAL_ venue",
                infoEmpty: "Showing 0 to 0 of 0 venues",
                lengthMenu: "Show _MENU_ venues",
                select: {
                    rows: {
                        "_": "You have selected %d venues",
                        "0": "Click a venue to select",
                        "1": "1 venue selected"
                    },
                },
                paginate: {
                    previous: '<i class="fa fa-chevron-left"></i>',
                    next: '<i class="fa fa-chevron-right"></i>'
                }
            },
            columns: [
                {
                    data: "name"
                },
                {
                    data: "chinese_name",
                },
                {
                    data: "address"
                },
                {
                    data: "chinese_address",
                    visible: false
                },
                {
                    data: "region"
                },
                {
                    data: "chinese_region",
                    visible: false
                },
                {
                    data: "enquiry_no",
                    visible: false
                },
                {
                    data: "latitude"
                },
                {
                    data: "longitude"
                },
                {
                    data: "surface"
                },
                {
                    data: "parking"
                }
            ],
            select: {
                style: SELECT_MODE,
            },
            order: [[0, 'asc']],
            lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "All"]],
            buttons: [
                { extend: "create", editor: editor },
                { extend: "edit", editor: editor },
                { extend: "remove", editor: editor },
                {
                    text: '<i class="fa fa-compress"></i> Merge by Chinese Name',
                    action: function (e, dt, node, config) {
                        $.ajax({
                            type: 'POST',
                            url: SERVER_PATH + "venues/mergeDuplicatedVenues",
                            dataType: 'json',
                            headers: {
                                'x-user-id': user_id,
                                'x-user-email': user_name
                            },
                            complete: function (response) {
                                var result = response.responseJSON;
                                console.log(result);
                                if (result.status == "OK") {
                                    // show alert
                                    Swal.fire({
                                        title: 'Venues are merged successfully',
                                        html: result.message,
                                        icon: 'success',
                                        confirmButtonText: 'OK',
                                        confirmButtonColor: '#ed1c24',
                                    });
                                    table.ajax.reload();
                                } else {
                                    // show alert
                                    Swal.fire({
                                        title: 'Error',
                                        html: result.message,
                                        icon: 'error',
                                        confirmButtonText: 'OK',
                                        confirmButtonColor: '#ed1c24',
                                    });
                                }
                            },
                            error: function (xhr, status, error) {
                            },
                        });
                    }
                },
                { extend: 'colvis', text: 'Columns' }
            ]
        });

    });

</script>