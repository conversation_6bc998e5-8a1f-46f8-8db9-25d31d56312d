<style>
	
</style>
<div class="row" id="tab-page">
    <div class="row">
        <div class="col-lg-12">
            <ol class="breadcrumb">
                <li>Events</li>
                <li><a href="" ng-click="goBack()">Golden Age</a></li>
                <li>{{event_name}}</li>
                <li class="active"><span>Year Group</span></li>
            </ol>
        </div>
    </div>
</div>
<div class="row">
	<div class="col-lg-12">
		<div class="clearfix">
			<h1 class="pull-left">Year Group</h1>
			<hr>
        </div>
        <div>

			<h3 ng-if="showSelectGroups" style="margin-top: 0px !important;"><span>Select Club and Year group</span></h3>

			<select class="selectedGroup"  style="width: 300px;" id="sel" ng-model="selectedGroup" ng-options="group.name for group in groups" ng-change="updateGroups()">
			</select>
			<span>&nbsp;&nbsp;</span>

			<select class="selectedClub"   style="width: 300px;" id="sel2" ng-model="selectedClub" ng-options="club.name for club in clubs">
			</select>

		


			
			<button id="btn-yeargroup" type="button" class="btn btn-primary">Select</button>
			<hr>
		</div>
	</div>
</div>

<div class="row">
	<div class="col-lg-12">
		<div class="main-box clearfix">
			<div id="cgroupsPageContent"></div>
		</div>
	</div>
</div>

<!-- this page specific scripts -->
<script type="text/javascript">

$(".selectedClub").select2({
    width: 'resolve' // need to override the changed default
});

$(".selectedGroup").select2({
    width: 'resolve' // need to override the changed default
});
</script>
