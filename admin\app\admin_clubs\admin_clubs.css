	/* Custom Swal modal */
	.swal-custom-popup {
		width: 600px !important;
		border-radius: 10px;
		padding: 20px;
	}
	
	/* Tab container */
	.swal-tabs-container {
		width: 100%;
		display: flex;
		flex-direction: column;
	}
	
	/* Tab buttons */
	.swal-tabs {
		display: flex;
		border-radius: 20px;
		background: #f0f0f0;
		padding: 5px;
		width: 100%;
		margin: 0 auto;
		overflow: hidden;
	}
	
	.swal-tab {
		flex: 1;
		border: none;
		background: transparent;
		padding: 10px 20px;
		text-align: center;
		font-weight: bold;
		cursor: pointer;
		transition: all 0.3s ease-in-out;
		border-radius: 20px;
	}
	
	.swal-tab.active {
		background: white;
		box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1);
	}
	
	/* Tab content */
	.swal-tab-content {
		padding: 15px;
		background: white;
		border-radius: 8px;
		max-height: 300px;
		overflow-y: auto;
	}
	
	/* Hide inactive tab content */
	.swal-tab-pane {
		display: none;
	}
	
	.swal-tab-pane.active {
		display: block;
	}
	
	/* List styling */
	.swal-list {
		list-style: none;
		padding: 0;
		margin: 0;
	}
	
	.swal-list-item {
		border-bottom: 1px solid #eee;
		padding: 10px 0;
		font-size: 14px;
	}
	
	.swal-list-item:last-child {
		border-bottom: none;
	}
	
	/* Successful and failed styles */
	.swal-club-team {
		color: #999;
		font-size: 12px;
	}
	
	.swal-failed {
		color: #dc3545 !important;
	}
	