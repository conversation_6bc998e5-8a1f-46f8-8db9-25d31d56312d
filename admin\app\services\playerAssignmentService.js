var app = angular.module('hkjflApp');

app.service('PlayerAssignmentService', ['$http', '$q', '$rootScope', function($http, $q, $rootScope) {


    this.quickAssignPlayerToTeam = function(player_id, club_id, event_id) {
        return new Promise((resolve, reject) => {
            $.ajax({
                type: 'POST',
                url: SERVER_PATH + 'registration/quickAssignPlayerToTeam',
                async: true,
                headers: {
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name,
                },
                data: { player_id: player_id, event_id: event_id, club_id: club_id },
                dataType: 'json',
                success: function (response) {
                    if (response.status === 'OK') {
                        resolve(response.data);
                    } else {
                        reject(response);
                    }
                },
                error: function (error) {
                    console.error("Error assigning to team:", error);
                    reject(error);
                }
            });
        });
    };

    // 🔹 Show warning using BootstrapDialog
    this.showWarning = function (title, message) {
        Swal.fire({
            title: title,
            text: message,
            icon: 'warning',
            confirmButtonText: 'OK'
        })
    };

    // 🔹 Validate selected players before processing
    this.quickAssign = function ($scope, tableClubPlayers, event_id) {
        let selectedPlayers = tableClubPlayers.rows({ selected: true }).data();

        if (selectedPlayers.length === 0) {
            this.showWarning('Cannot Assign', 'Please select at least one player.');
            return;
        }

        let validPlayers = selectedPlayers.filter(row =>
            row.players.validate_status === VALIDATE_STATUS_Validated &&
            row.registrations.approval_status === APPROVAL_STATUS_Approve &&
            $scope.club_id && row.registrations.id && row.players.id
        );

        const invalidPlayers = selectedPlayers.filter(row =>
            row.players.validate_status !== VALIDATE_STATUS_Validated ||
            row.registrations.approval_status !== APPROVAL_STATUS_Approve
        );

        if (invalidPlayers.length > 0) {
            // skip this player and continue 
            Swal.fire({
                title: 'Warning',
                text: `${invalidPlayers.length} player(s) are not validated or approved. Do you want to skip them and assign the others?`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'Yes',
                cancelButtonText: 'No',
                confirmButtonColor: '#ed1c24',
                customClass: {
                    actions: 'my-actions',
                },
            }).then((result) => {
                if (result.isConfirmed) {
                    this.showProcess(validPlayers, $scope, tableClubPlayers, event_id);
                }else{
                    return;
                }
            });
        }

        if (validPlayers.length > 0) {
            this.showProcess(validPlayers, $scope, tableClubPlayers, event_id);
        }

    };  


    this.showProcess = async function (players = [], $scope, tableClubPlayers, event_id) {
        let htmlContent = `
            <div class="swal-tabs-container">
                <div class="swal-tabs">
                    <button class="swal-tab active" data-target="successfulTab">Successful</button>
                    <button class="swal-tab" data-target="failedTab">Failed</button>
                </div>
                <div class="swal-tab-content">
                    <div id="successfulTab" class="swal-tab-pane active">
                        <ul class="swal-list" id="successList"></ul>
                    </div>
                    <div id="failedTab" class="swal-tab-pane">
                        <ul class="swal-list" id="failedList"></ul>
                    </div>
                </div>
            </div>
        `;
    
        Swal.fire({
            title: 'Assign Player',
            html: htmlContent,
            allowOutsideClick: true,
            showConfirmButton: true,
            confirmButtonText: 'OK',
            customClass: {
                popup: 'swal-custom-popup',
                actions: 'my-actions',
            },
            didOpen: async () => {
                const swalContainer = Swal.getHtmlContainer();
                const successList = swalContainer.querySelector("#successList");
                const failedList = swalContainer.querySelector("#failedList");
                const tabButtons = swalContainer.querySelectorAll(".swal-tab");
                const tabPanes = swalContainer.querySelectorAll(".swal-tab-pane");
    
                let hasSuccess = false;
                let hasFailed = false;
    
                // Tab switching function
                function switchTab(target) {
                    tabButtons.forEach(t => t.classList.remove("active"));
                    tabPanes.forEach(p => p.classList.remove("active"));
                    swalContainer.querySelector(`[data-target="${target}"]`).classList.add("active");
                    swalContainer.querySelector(`#${target}`).classList.add("active");
                }
    
                // Tab switching logic
                tabButtons.forEach(tab => {
                    tab.addEventListener("click", function () {
                        switchTab(this.dataset.target);
                    });
                });
    
                for (let i = 0; i < players.length; i++) {
                    const player = players[i];
                    const playerName = `${player.players.surname} ${player.players.other_name}`;
    
                    try {
                        const result = await this.quickAssignPlayerToTeam(player.players.id, $scope.club_id, event_id);
                        const club_and_team = `Club: ${result.club_name}   Team: ${result.team_name}`;
                        successList.innerHTML += `
                            <li class="swal-list-item text-left">
                                <strong>${playerName}</strong>
                                <br><span class="swal-club-team">${club_and_team}</span>
                            </li>
                        `;
                        hasSuccess = true;
                    } catch (error) {
                        failedList.innerHTML += `
                            <li class="swal-list-item text-left">
                                <strong>${playerName}</strong>
                                <br><span class="swal-club-team swal-failed">${error.message || "Failed to assign"}</span>
                            </li>
                        `;
                        hasFailed = true;
                    }
                }
    
                if (!hasSuccess) {
                    successList.innerHTML += `<li class="swal-list-item">No player assigned</li>`;
                }
                if (!hasFailed) {
                    failedList.innerHTML += `<li class="swal-list-item">No assignment failed</li>`;
                } else {
                    // Auto-switch to "Failed" tab if there are failed assignments
                    switchTab("failedTab");
                }
            }
        }).then(() => {
            tableClubPlayers.ajax.reload();
        });
    };
    
    
    
    

}]);

