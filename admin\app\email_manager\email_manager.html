<style>
	.ck-content .image {
		clear: both;
		display: table;
		margin: 0 auto;
		min-width: 50px;
		text-align: center;
	}

	.container-ckeditor {
		/* box-shadow: 2px 2px 0px hsla( 0, 0%, 0%, 0.1 ); */
		margin: 1.5em 0;
		/* padding: 1em; */
	}

	.container-ckeditor h3 {
		font-size: 18px;
		font-weight: bold;
		margin: 0 0 .5em;
		padding: 0;
	}

	.container-ckeditor .ck.ck-editor__editable_inline {
		border: 1px solid hsla(0, 0%, 0%, 0.15);
		transition: background .5s ease-out;
		min-height: 6em;
		margin-bottom: 1em;
	}

	.controls {
		display: flex;
		flex-direction: row;
		align-items: center;
	}

	.chart {
		margin-right: 1em;
	}

	.chart__circle {
		transform: rotate(-90deg);
		transform-origin: center;
	}

	.chart__characters {
		font-size: 13px;
		font-weight: bold;
	}

	.words {
		flex-grow: 1;
		opacity: .5;
	}

	.limit-close .chart__circle {
		stroke: hsl(30, 100%, 52%);
	}

	.limit-close .chart__circle_chinese {
		stroke: hsl(30, 100%, 52%);
	}


	.limit-exceeded .ck.ck-editor__editable_inline {
		background: hsl(0, 100%, 97%);
	}

	.limit-exceeded .chart__circle {
		stroke: hsl(0, 100%, 52%);
	}

	.limit-exceeded .chart__circle_chinese {
		stroke: hsl(0, 100%, 52%);
	}

	.limit-exceeded .chart__characters {
		fill: hsl(0, 100%, 52%);
	}

	.upload-file {
		padding: 24px 16px;
		display: flex;
		flex-direction: column;
		gap: 14px;
		border-radius: 12px;
		box-shadow: 0px 0px 15px rgba(149, 157, 165, 0.2);
	}

	.upload-file h1 {
		font-size: 20px;
		font-weight: 700;
	}

	.upload-subtitle {
		color: #6b6d73;
	}

	/* Header Image */
	.upload-file-top {
		position: relative;
	}

	.upload-header-img {
		position: absolute;
		top: 50%;
		right: 0;
		transform: translateY(-50%);
		width: 50px;
		height: 50px;
	}

	.upload-header-img img {
		width: 100%;
		height: 100%;
		border-radius: 50%;
		object-fit: cover;
	}

	/* Drag and Drop Section */
	.upload-file-drag-drop {
		border: 2px dashed black;
		border-radius: 16px;
		min-height: 200px;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		gap: 10px;
	}

	.upload-drag-drop-img {
		width: 100px;
		height: 100px;
	}

	.upload-drag-drop-img img {
		width: 100%;
		height: 100%;
		object-fit: cover;
	}

	.upload-file a {
		color: #000;
		text-decoration: underline;
		font-weight: 700;
	}

	/* File List Section */
	.upload-file-item {
		display: flex;
		gap: 10px;
		padding: 16px 24px;
		border: 2px solid black;
		border-radius: 8px;
		position: relative;
	}

	.upload-close-button {
		width: 20px;
		height: 20px;
		position: absolute;
		top: 8px;
		right: 8px;
	}

	.upload-close-button img {
		width: 100%;
		height: 100%;
		object-fit: cover;
	}

	.upload-file-icon {
		width: 40px;
		height: 40px;
		padding: 6px;
		border: 3px solid #eee;
		border-radius: 8px;
	}

	.upload-file-icon img {
		width: 100%;
		height: 100%;
		object-fit: cover;
	}

	.upload-file-info {
		flex: 1;
		display: flex;
		flex-direction: column;
		gap: 4px;
	}

	.upload-file-name {
		font-weight: 600;
	}

	.upload-file-progress {
		display: flex;
		align-items: center;
		gap: 4px;
	}

	.upload-file-progress-bar {
		flex: 1;
		height: 8px;
		border-radius: 4px;
		background: #eee;
		position: relative;
		overflow: hidden;
	}

	.upload-file-progress-bar::before {
		content: '';
		position: absolute;
		width: var(--progress);
		height: 100%;
		background: #000;
		border-radius: 4px;
	}

	.upload-file-item:not(:last-child) {
		margin-bottom: 10px;
	}

	.upload-btn {
		background-color: inherit;
		border: none;
		outline: none;
	}

	.upload-remove-button {
		width: 20px;
		height: 20px;
		position: absolute;
		top: 8px;
		right: 8px;
		outline: none;
		cursor: pointer;
		border: none;
		background-color: inherit;
	}

	.upload-remove-button img {
		width: 100%;
		height: 100%;
		object-fit: cover;
	}

	.drag-over {
		border-color: red;
	}

	.ck-splitbutton__arrow{
		max-width: 15px;
		margin-right: 10px;
	}
</style>
<div class="row" id="tab-page">
    <div class="row">
        <div class="col-lg-12">
            <ol class="breadcrumb">
                <li>Others</li>
                <li class="active"><span>Email Editor</span></li>
            </ol>
        </div>
    </div>
</div>
<div class="row">
	<div class="col-lg-9">
		<div class="clearfix">
			<h1 class="pull-left">Email Editor</h1>
			<hr>
		</div>
		<hr>
	</div>
</div>
<div class="row">
	<div class="{{template.listVariable.length > 0 ? 'col-lg-9': 'col-lg-12'}}"
		style="background: #fff; padding-bottom: 10px; margin-bottom: 10px;">
		<div class="row-lg-12" style="padding-top: 10px;display: flex;align-items: center;">
			<select style="width:300px" class="form-control" ng-model="selectedTemplate"
				ng-options="template.name for template in template.listTemplate track by template.id"
				ng-change="changeTemplate(selectedTemplate)">
				<option value="" disabled selected>Select a template to edit</option>
			</select>
			<label ng-if="selectedTemplate" style="margin-left: 20px;justify-self: end;">Last updated by:
				{{selectedTemplate.updater_email}} at {{selectedTemplate.updated_at}}</label>
		</div>

		<div class="container-ckeditor" ng-click="setCurrentEditor('email-subject')">
			<h3>Subject</h3>
			<textarea id="subject-editor" rows="1"></textarea>
			<div class="controls">
				<span class="words"></span>
				<svg style="width: fit-content!important;" class="chart" viewbox="0 0 40 40" width="40" height="40"
					xmlns="http://www.w3.org/2000/svg">
					<circle stroke="hsl(0, 0%, 93%)" stroke-width="3" fill="none" cx="20" cy="20" r="17" />
					<circle class="chart__circle" stroke="hsl(202, 92%, 59%)" stroke-width="3"
						stroke-dasharray="134,534" stroke-linecap="round" fill="none" cx="20" cy="20" r="17" />
					<text class="chart__characters" x="50%" y="50%" dominant-baseline="central"
						text-anchor="middle"></text>
				</svg>
			</div>
		</div>

		<div class="container-ckeditor-chinese" ng-click="setCurrentEditor('email-chinese-subject')">
			<h3>Chinese Subject</h3>
			<textarea id="chinese-subject-editor" rows="1"></textarea>
			<div class="controls">
				<span class="words"></span>
				<svg style="width: fit-content!important;" class="chart" viewbox="0 0 40 40" width="40" height="40"
					xmlns="http://www.w3.org/2000/svg">
					<circle stroke="hsl(0, 0%, 93%)" stroke-width="3" fill="none" cx="20" cy="20" r="17" />
					<circle class="chart__circle_chinese" stroke="hsl(202, 92%, 59%)" stroke-width="3" transform="rotate(-90 20 20)"
						stroke-dasharray="134,534" stroke-linecap="round" fill="none" cx="20" cy="20" r="17" />
					<text class="chart__characters_chinese" x="50%" y="50%" dominant-baseline="central"
						text-anchor="middle"></text>
				</svg>
			</div>
		</div>

		<div class="container-ckeditor">
			<div ng-click="setCurrentEditor('email')">
				<h3>Content</h3>
				<textarea id="email-editor"></textarea>
			</div>
			<div ng-click="setCurrentEditor('email-chinese')">
				<h3>Chinese Content</h3>
				<textarea id="email-chinese-editor"></textarea>
			</div>
		</div>

		<div class="upload-file" ng-if="templateSelected">
			<div class="upload-file-top">
				<h1 style="margin: 0; margin-bottom: 0px; padding: 0px;">Upload and attach files</h1>
				<span class="upload-subtitle">Upload and attach file to this email template</span>
			</div>
			<div class="upload-file-drag-drop">
				<div class="upload-drag-drop-img">
					<img src="{{SystemImage}}upload.png" alt="Upload">
				</div>
				<p><button class="upload-btn">Click to upload</a> or drag and drop </button>
					<span>Maximum file size 50MB</span>
			</div>
			<div class="upload-file-list">
			</div>
		</div>

		<div class="row" style="margin-top: 10px;">
			<div class="col-lg-12">
				<div class="row-lg-12">
					<button class="btn btn-primary btn_save" ng-click="saveTemplate()">Save</button>
					<!-- <button class="btn btn-primary" ng-click="getData()">Get Data</button> -->
				</div>

			</div>
		</div>
	</div>
	<div class="col-lg-3" ng-if="template.listVariable.length > 0">
		<!-- list vars -->
		<div class="panel panel-default">
			<div class="panel-heading">
				<h4 class="panel-title">All Variables</h4>
			</div>
			<div class="panel-body">
				<div class="list-group">
					<a class="list-group-item" ng-repeat="variable in template.listVariable"
						ng-click="insertContent(variable)">
						{{variable}}
					</a>
				</div>
			</div>
		</div>
	</div>

</div>
<script type="text/javascript" language="javascript" class="init">
	$('select').select2();
</script>