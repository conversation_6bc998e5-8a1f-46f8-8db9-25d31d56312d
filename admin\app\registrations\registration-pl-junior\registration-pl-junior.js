var ACTION_APPROVE = 1;

app.controller(
    'registrationsPLJuniorCtrl',
    function ($scope, $rootScope, $http, $routeParams) {
        $('#page-wrapper').removeClass('nav-small');

        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            'justNum-pre': (a) => parseFloat(a.replace(/\D/g, '')),
            'justNum-asc': (a, b) => a - b,
            'justNum-desc': (a, b) => b - a,
        });

        var event_id = $routeParams.id;

        jQuery.ajax({
            type: 'POST',
            url: SERVER_PATH + 'event/getEventInfo',
            async: false,
            headers: {
                'x-user-id': $rootScope.user_id,
                'x-user-email': $rootScope.user_name,
            },
            data: {
                event_id: event_id,
            },
            dataType: 'json',
            complete: function (response) {
                var jsonData = JSON.parse(response.responseText);
                var event = jsonData.info;
                $scope.event_name = event.name;
                $scope.event_type = event.type;
            },
        });

        $scope.goBack = function () {
            window.history.back();
        };

        function getcancelRegistrationByID(registration_id) {
            var data = new FormData();
            data.append('registration_id', registration_id);
            $http({
                url: SERVER_PATH + 'registration/getcancelRegistrationByID',
                method: 'POST',
                data: data,
                headers: {
                    'Content-Type': undefined,
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name,
                },
            }).then(
                function (response) {
                    console.log(response);
                    if (response.statusText == 'OK') {
                        console.log(response.data.data);
                        let data = response.data.data;
                        if (!data.reason) {
                            data.reason = 'Cancel';
                        }
                        if (!data.descriptions) {
                            data.descriptions = '';
                        } else {
                            //parse to json
                            data.descriptions = JSON.parse(data.descriptions);
                            data.descriptions =
                                data.descriptions.status +
                                ' - ' +
                                data.descriptions.message;
                            console.log(data.descriptions);
                        }

                        let user_name = data.other_name
                            ? data.other_name +
                              ' ' +
                              data.surname +
                              ' (' +
                              data.email +
                              ')'
                            : 'This user is not exist or deleted';

                        BootstrapDialog.show({
                            title: 'Cancel details',
                            type: BootstrapDialog.TYPE_INFO,
                            message:
                                'Canceler: <strong>' +
                                user_name +
                                '</strong><br><br>Reason: <strong>' +
                                data.reason +
                                '</strong><br><br>Cancel at: <strong>' +
                                data.cancel_date +
                                '</strong>',
                        });
                    } else {
                        BootstrapDialog.show({
                            title: 'Error',
                            type: BootstrapDialog.TYPE_DANGER,
                            message: jsonData.message,
                        });
                    }
                },
                function (response) {
                    // this function handles error
                    console.log(response);
                }
            );
        }

        $html_content_table_plj = [
            'Player name',
            'Chinese name',
            // 'HKID',
            'DOB',
            'Gender',
            'Parent phone',
            'Parent email',
            'Group',
            'Course code',
            'Registration status',
            'Is girl promotion',
            'Registered date',
            'Invoice number',
            'Payment status',
            'Products',
            'Emailed',
        ];

        $html_content_table_elderly = [
            'Player name',
            'Chinese name',
            // 'HKID',
            'DOB',
            'Gender',
            'Parent phone',
            'Parent email',
            'Group',
            'Course code',
            'Emergency contact name',
            'Emergency contact number',
            'Emergency contact relationship',
            'Registered date',
            'Invoice number',
            'Payment status',
            'Products',
            'Emailed',
        ];

        $scope.html_content_table = [];

        if ($scope.event_type == EVENT_ELDERLY) {
            $scope.html_content_table = $html_content_table_elderly;
        } else {
            $scope.html_content_table = $html_content_table_plj;
        }

        $.fn.dataTable.moment('D-MMM-YYYY HH:mm:ss');
        $.fn.dataTable.moment('D-MMM-YYYY');

        var columns = [
            {
                data: 'players.surname',
                render: function (data, type, row, meta) {
                    return row.players.surname + ' ' + row.players.other_name;
                },
            },
            {
                data: 'players.chinese_name',
            },
            // { data: 'players.hkid_no', visible: false },
            { data: 'players.dob' },
            { data: 'players.gender' },
            { data: 'parens.phone' },
            { data: 'parens.email' },
            {
                data: 'groups.name',
                visible: false,
            },
            {
                data: 'courses.class_code',
                render: function (data, type, row, meta) {
                    return data;
                },
            },
            ...($scope.event_type == 'PL Junior'
                ? [
                      {
                          data: 'registrations.approval_status',
                          className: 'center',
                          render: function (data, type, row) {
                              if (data == 'Canceled')
                                  return (
                                      '<span class="label label-danger">' +
                                      data +
                                      '</span><br><br> <a class="open_cancel_info">Details</a>'
                                  );
                              else if (data == 'Approved')
                                  return (
                                      '<span class="label label-success">' +
                                      data +
                                      '</span>'
                                  );
                              else
                                  return (
                                      '<span class="label label-warning">' +
                                      data +
                                      '</span>'
                                  );
                          },
                      },
                      {
                          data: 'registrations.girl_offer',
                          render: function (data, type, row) {
                              return data == 1 ? 'Yes' : 'No';
                          },
                      },
                  ]
                : [
                      {
                          data: 'players.emergency_contact_name',
                          visible: false,
                      },
                      {
                          data: null,
                          visible: false,
                          render: function (data, type, row) {
                              return (
                                  row.players.emergency_contact_country_code +
                                  ' ' +
                                  row.players.emergency_contact_phone
                              );
                          },
                      },
                      {
                          data: 'players.emergency_contact_relationship',
                          visible: false,
                      },
                  ]),
            { data: 'registrations.registered_date' },
            { data: 'invoices.invoice_number' },
            {
                data: 'invoices.status',
                className: 'center',
                render: function (data, type, full, meta) {
                    switch (data) {
                        case STATUS_SUCCEEDED:
                            return (
                                '<span class="label label-success">' +
                                // get element
                                DISTRICT_PAYMENT_STATUS[data] +
                                '</span>'
                            );
                        case STATUS_PROCESSING:
                            return (
                                '<span class="label label-danger">' +
                                DISTRICT_PAYMENT_STATUS[data] +
                                '</span>'
                            );
                        case STATUS_WAITING_PAYMENT:
                            return (
                                '<span class="label label-warning">' +
                                DISTRICT_PAYMENT_STATUS[data] +
                                '</span>'
                            );
                        case STATUS_OFFLINE_PAYMENT:
                            return (
                                '<span class="label label-info" data-toggle="tooltip" data-placement="top" title="' +
                                full.invoices.accepted_detail +
                                '">' +
                                DISTRICT_PAYMENT_STATUS[data] +
                                '</span>'
                            );
                        default:
                            return (
                                '<span class="label label-default">' +
                                data +
                                '</span>'
                            );
                    }
                },
            },
            { data: 'registrations.product' },
            {
                data: 'registrations.emailed',
                render: function (data, type, row, meta) {
                    // show badge
                    if (data == 0) {
                        return '<span class="badge badge-warning">Didn\'t send</span>';
                    } else if (data == 1 || data == 2) {
                        return (
                            '<span class="badge badge-warning">Can\'t send(' +
                            data +
                            ')</span>'
                        );
                    } else if (data == 'Done') {
                        return '<span class="badge badge-primary"> Sent</span>';
                    }
                },
            },
        ];

        function initRegistrationsTable() {
            setTimeout(() => {
                tableRegistration = $('#pl-junior-table').DataTable({
                    dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
                    stateSave: false,
                    deferRender: true,
                    bDestroy: true,
                    ajax: {
                        url:
                            SERVER_PATH +
                            'registration/getRegistrationsForPLJunior',
                        type: 'POST',
                        headers: {
                            'x-user-id': $rootScope.user_id,
                            'x-user-email': $rootScope.user_name,
                        },
                        data: {
                            event_id: event_id,
                            user_id: $rootScope.user_id,
                        },
                        dataType: 'json',
                        complete: function (response) {},
                        error: function (xhr, status, error) {},
                    },
                    language: {
                        paginate: {
                            previous: '<i class="fa fa-chevron-left"></i>',
                            next: '<i class="fa fa-chevron-right"></i>',
                        },
                    },
                    columns: columns,
                    initComplete: function () {
                        // build the select list
                        var age_group_column = {
                            orderColumn: 6,
                            elementId: 'age-group-content',
                            selectId: 'selType',
                        };

                        var invoice_status_column = {
                            orderColumn: columns.length - 3,
                            elementId: 'approval-content',
                            selectId: 'selType',
                        };

                        filterColumns = [
                            age_group_column,
                            invoice_status_column,
                        ];

                        filterColumns.forEach((item, index) => {
                            this.api()
                                .columns(item.orderColumn)
                                .every(function () {
                                    var column = this;
                                    var select = $(
                                        `<select id="${item.orderColumn}" class="form-control"><option value="">All</option></select>`
                                    )
                                        .appendTo($(`#${item.elementId}`))
                                        .on('change', function () {
                                            var val =
                                                $.fn.dataTable.util.escapeRegex(
                                                    $(this).val()
                                                );

                                            column
                                                .search(
                                                    val ? '^' + val + '$' : '',
                                                    true,
                                                    false
                                                )
                                                .draw();
                                        })
                                        .select2();
                                    var column_data = column.data();
                                    var select_data = [];
                                    column_data.map((item) => {
                                        if (item != null) {
                                            item.indexOf(', ') > 0
                                                ? (item = item.split(', '))
                                                : (item = [item]);
                                            item.forEach((item) => {
                                                select_data.push(item);
                                            });
                                        }
                                    });
                                    select_data
                                        .filter(onlyUnique)
                                        .sort()
                                        .map(function (d, j) {
                                            if (index == 1) {
                                                select.append(
                                                    `<option value="${DISTRICT_PAYMENT_STATUS[d]}">${DISTRICT_PAYMENT_STATUS[d]}</option>`
                                                );
                                            } else {
                                                select.append(
                                                    // lower case and upper case first character
                                                    `<option value="${d}">${d}</option>`
                                                );
                                            }
                                        });
                                });
                        });
                    },
                    select: {
                        style: SELECT_MODE,
                        selector: 'td:not(:last-child)',
                    },
                    columnDefs: [
                        {
                            type: 'justNum',
                            targets: 8,
                        },
                    ],
                    order: [[9, 'asc']],
                    buttons: [
                        {
                            extend: 'collection',
                            text: 'Actions <span class="caret"></span>',
                            className: 'btn btn-primary',
                            dataToggle: 'dropdown',
                            autoClose: true,
                            buttons: [
                                {
                                    text: '<i class="fa fa-envelope"></i> Resend confirmation email',
                                    extend: 'selectedSingle',
                                    action: function () {
                                        var rows_selected = tableRegistration
                                            .rows({ selected: true })
                                            .data()[0];
                                        if (
                                            rows_selected.invoices.status ==
                                                STATUS_SUCCEEDED ||
                                            rows_selected.invoices.status ==
                                                STATUS_OFFLINE_PAYMENT
                                        ) {
                                            let registration_id =
                                                rows_selected.registrations.id;

                                            jQuery.ajax({
                                                type: 'POST',
                                                url:
                                                    SERVER_PATH +
                                                    'registration/resendRegistrationEmailPLJ',
                                                async: true,
                                                headers: {
                                                    'x-user-id':
                                                        $rootScope.user_id,
                                                    'x-user-email':
                                                        $rootScope.user_name,
                                                },
                                                data: {
                                                    registration_id:
                                                        registration_id,
                                                },
                                                dataType: 'json',
                                                beforeSend: function () {
                                                    Swal.fire({
                                                        title: 'Please Wait!',
                                                        allowOutsideClick: false,
                                                        didOpen: () => {
                                                            Swal.showLoading();
                                                        },
                                                    });
                                                },
                                                complete: function (response) {
                                                    var jsonData = JSON.parse(
                                                        response.responseText
                                                    );
                                                    if (
                                                        jsonData.status == 'OK'
                                                    ) {
                                                        Swal.fire({
                                                            type: 'success',
                                                            icon: 'success',
                                                            title: jsonData.message,
                                                            confirmButtonClass:
                                                                'btn btn-primary',
                                                            buttonsStyling: false,
                                                        });
                                                        tableRegistration.ajax.reload();
                                                    } else {
                                                        Swal.fire({
                                                            title: 'ERROR!',
                                                            text: jsonData.message,
                                                            icon: 'error',
                                                            type: 'error',
                                                        });
                                                    }
                                                },
                                            });
                                        } else {
                                            Swal.fire({
                                                title: 'ERROR!',
                                                text: 'This registration is not paid yet!',
                                                icon: 'error',
                                                type: 'error',
                                            });
                                        }
                                    },
                                },
                                {
                                    extend: 'excel',
                                    name: 'excel',
                                    text: '<i class="fa fa-file-excel-o"></i>&emsp;Export to Excel',
                                    titleAttr: 'Export data to an Excel file',
                                    filename:
                                        'Registrations -' + $scope.event_name,
                                    exportOptions: {
                                        columns: ':visible',
                                        modifier: {
                                            autoFilter: true,
                                            // selected: true
                                        },
                                    },
                                },
                            ],
                        },
                        {
                            extend: 'selectedSingle',
                            text: 'Cancel Registration',
                            action: function () {
                                cancelRegistration();
                            },
                        },
                        {
                            extend: 'selectedSingle',
                            text: 'Delete Registration',
                            action: function () {
                                deleteRegistration();
                            },
                        },
                        {
                            extend: 'selectedSingle',
                            text: 'Edit Registration',
                            action: function () {
                                editRegistration();
                            },
                        },
                        {
                            extend: 'colvis',
                            text: 'Columns',
                        },
                    ],
                });

                $('#girl-offer').unbind();
                $('#girl-offer').change(function () {
                    // check if checkbox is checked
                    if ($(this).is(':checked')) {
                        tableRegistration.column(9).search('Yes').draw();
                    } else {
                        tableRegistration.column(9).search('No').draw();
                    }
                });

                // unbind click event
                $('#pl-junior-table').off(
                    'click',
                    'tbody td a.open_cancel_info'
                );

                $('#pl-junior-table').on(
                    'click',
                    'tbody td a.open_cancel_info',
                    function (e) {
                        e.preventDefault();
                        var $row = $(this).closest('tr');
                        // Get row data
                        var data = tableRegistration.row($row).data();

                        getcancelRegistrationByID(data.registrations.id);
                    }
                );
            }, 400);
        }

        function editRegistration() {
            var table_selected = tableRegistration
                .rows({ selected: true })
                .data();

            var player = table_selected[0];

            var registration_id = player.registrations.id;

            var courses = [];

            //  get Data of registration
            jQuery
                .ajax({
                    type: 'POST',
                    url:
                        SERVER_PATH +
                        'registration/getAllCoursePLJBySelectedPlayer',
                    async: false,
                    headers: {
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name,
                    },
                    data: {
                        event_id: event_id,
                        res_id: registration_id,
                    },
                    dataType: 'json',
                    complete: function (response) {
                        jsonData = JSON.parse(response.responseText);
                        courses = jsonData.data;
                    },
                })
                .done(function () {
                    var editCourseEditor = new $.fn.dataTable.Editor({
                        ajax: {
                            type: 'POST',
                            url:
                                SERVER_PATH +
                                'registration/updateCourseRegistrationPLJ',
                            headers: {
                                'x-user-id': $rootScope.user_id,
                                'x-user-email': $rootScope.user_name,
                            },
                            data: {
                                event_id: event_id,
                                user_id: $rootScope.user_id,
                                player_id: player.players.id,
                            },
                            async: false,
                            dataType: 'json',
                            complete: function (response) {
                                var jsonData = JSON.parse(
                                    response.responseText
                                );
                                if (jsonData.status == 'OK') {
                                    Swal.fire({
                                        type: 'success',
                                        icon: 'success',
                                        title: jsonData.message,
                                        confirmButtonClass: 'btn btn-primary',
                                        buttonsStyling: false,
                                    });

                                    tableRegistration.ajax.reload();
                                } else if (
                                    typeof jsonData.fieldErrors == 'undefined'
                                ) {
                                    Swal.fire({
                                        title: 'ERROR!',
                                        text: jsonData.message,
                                        icon: 'error',
                                        type: 'error',
                                    });
                                }
                            },
                        },
                        formOptions: {
                            main: {
                                onBlur: 'none',
                            },
                        },
                        fields: [
                            ...courses.map((item) => {
                                return {
                                    label:
                                        'Change course from ' +
                                        item.courses.class_code +
                                        ' to',
                                    name: item.rcourse.course_id,
                                    type: 'select',
                                    options: item.options,
                                    default: item.rcourse.course_id,
                                };
                            }),
                        ],
                    });

                    editCourseEditor
                        .title('Edit Registration')
                        .buttons({
                            label: 'Submit',
                            fn: function () {
                                this.submit();
                            },
                        })
                        .edit()
                        .open();
                });
        }

        function deleteRegistration() {
            var table_selected = tableRegistration
                .rows({ selected: true })
                .data();

            var player = table_selected[0];

            var DT_RowId = player['DT_RowId'];

            var registration_id = DT_RowId.split('_')[1];
            BootstrapDialog.confirm(
                'Delete Registration',
                'Are you sure to delete this registration?',
                function (result) {
                    if (result) {
                        jQuery.ajax({
                            type: 'POST',
                            url:
                                SERVER_PATH +
                                'registration/deleteRegistrationForPLJ',
                            async: true,
                            headers: {
                                'x-user-id': $rootScope.user_id,
                                'x-user-email': $rootScope.user_name,
                            },
                            data: {
                                registration_id: registration_id,
                            },
                            dataType: 'json',
                            beforeSend: function () {
                                Swal.fire({
                                    title: 'Please Wait!',
                                    allowOutsideClick: false,
                                    didOpen: () => {
                                        Swal.showLoading();
                                    },
                                });
                            },
                            complete: function (response) {
                                Swal.close();
                                var jsonData = JSON.parse(
                                    response.responseText
                                );

                                tableRegistration.ajax.reload();

                                if (jsonData.status == 'OK') {
                                    Swal.fire({
                                        type: 'success',
                                        icon: 'success',
                                        title: jsonData.message,
                                        confirmButtonClass: 'btn btn-primary',
                                        buttonsStyling: false,
                                    });
                                } else {
                                    Swal.fire({
                                        title: 'ERROR!',
                                        text: jsonData.message,
                                        icon: 'error',
                                        type: 'error',
                                    });
                                }
                            },
                        });
                    }
                }
            );
        }

        function onlyUnique(value, index, self) {
            return self.indexOf(value) === index;
        }

        function cancelRegistration() {
            var table_selected = tableRegistration
                .rows({ selected: true })
                .data()[0];

            

            const {
                registrations: { id: registration_id },
                invoices: { status },
            } = table_selected;

            if (
                status == STATUS_SUCCEEDED ||
                status == STATUS_OFFLINE_PAYMENT ||
                status == STATUS_REQUEST_PARTIAL_REFUND ||
                status == STATUS_REQUEST_REFUND||
                status == STATUS_FREE
            ) {
                BootstrapDialog.confirm(
                    'Cancel Registration',
                    'Are you sure to cancel this registration?',
                    function (result) {
                        BootstrapDialog.show({
                            title: 'Enter Reason',
                            message:
                                '<textarea id="reasonField" class="form-control" rows="3" placeholder="Enter your reason here..."></textarea>',
                            buttons: [
                                {
                                    label: 'Submit',
                                    cssClass: 'btn-primary',
                                    action: function (dialog) {
                                        var reason = $('#reasonField').val();
                                        dialog.close();

                                        jQuery.ajax({
                                            type: 'POST',
                                            url:
                                                SERVER_PATH +
                                                'registration/cancelRegistrationForPLJ',
                                            async: true,
                                            headers: {
                                                'x-user-id': $rootScope.user_id,
                                                'x-user-email':
                                                    $rootScope.user_name,
                                            },
                                            data: {
                                                registration_id:
                                                    registration_id,
                                                user_id: $rootScope.user_id,
                                                reason: reason,
                                            },
                                            dataType: 'json',
                                            beforeSend: function () {
                                                Swal.fire({
                                                    title: 'Please Wait!',
                                                    allowOutsideClick: false,
                                                    didOpen: () => {
                                                        Swal.showLoading();
                                                    },
                                                });
                                            },
                                            complete: function (response) {
                                                Swal.close();
                                                var jsonData = JSON.parse(
                                                    response.responseText
                                                );

                                                tableRegistration.ajax.reload();

                                                if (jsonData.status == 'OK') {
                                                    Swal.fire({
                                                        type: 'success',
                                                        icon: 'success',
                                                        title: jsonData.message,
                                                        confirmButtonClass:
                                                            'btn btn-primary',
                                                        buttonsStyling: false,
                                                    });
                                                } else {
                                                    Swal.fire({
                                                        title: 'ERROR!',
                                                        text: jsonData.message,
                                                        icon: 'error',
                                                        type: 'error',
                                                    });
                                                }
                                            },
                                        });
                                    },
                                },
                                {
                                    label: 'Cancel',
                                    action: function (dialog) {
                                        dialog.close();
                                    },
                                },
                            ],
                        });
                    }
                );
            }else
            {
                // show error message sorry you can't cancel this registration
                Swal.fire({
                    title: 'ERROR!',
                    text: 'Sorry, you can\'t cancel this registration!',
                    icon: 'error',
                    type: 'error',
                });

                return;
            }
        }
        initRegistrationsTable();
    }
);
