app.controller('messageTeamCoachCtrl', function (
    $scope,
    $rootScope,
    $routeParams,
    $http
) {
    $scope.eventId = $routeParams.id;

    $scope.event_name = '';

    $scope.event_type = '';

    var sender_id = 0;

    var teamsInDistrict = [];
    initTable();
    // get event info
    jQuery.ajax({
        type: 'POST',
        url: SERVER_PATH + 'event/getEventInfo',
        async: false,
        headers: {	
            'x-user-id': $rootScope.user_id,
            'x-user-email': $rootScope.user_name
        },
        data: {
            event_id: $scope.eventId,
        },
        dataType: 'json',
        complete: function (response) {
            var jsonData = JSON.parse(response.responseText);
            var event = jsonData.info;
            $scope.event_name = event.name;
            $scope.event_type = event.type;
        },
    });

    // get all team of coach
    jQuery.ajax({
        type: 'POST',
        url: SERVER_PATH + 'district/getAllTeamCoachManage',
        async: false,
        headers: {	
            'x-user-id': $rootScope.user_id,
            'x-user-email': $rootScope.user_name
        },
        data: {
            event_id: $scope.eventId,
            user_id: $rootScope.user_id
        },
        dataType: 'json',
        complete: function (response) {
            var jsonData = JSON.parse(response.responseText);
            teamsInDistrict = jsonData.data;
            console.warn(teamsInDistrict);
        },
    });

    $scope.delete = function (id, deleted = false) {
        BootstrapDialog.show({
            title: 'Delete',
            message: 'Are you sure you want to delete?',
            type: BootstrapDialog.TYPE_DANGER,
            closable: false,
            buttons: [
                {
                    label: 'No',
                    action: function (dialogRef) {
                        dialogRef.close();
                    },
                },
                {
                    label: 'Yes',
                    cssClass: 'btn-primary',
                    action: function (dialogRef) {
                        // call to server
                        $http({
                            method: 'POST',
                            url: SERVER_PATH + 'message/deleteMessage',
                            headers: {	
                                'x-user-id': $rootScope.user_id,
                                'x-user-email': $rootScope.user_name
                            },
                            data: 'id=' + id + '&deleted=' + deleted,
                            headers: {
                                'Content-Type': 'application/x-www-form-urlencoded',
                            },
                        }).success(function (response) {
                            console.log(response);
                            BootstrapDialog.show({
                                title: response.status,
                                type:
                                    response.status == 'OK'
                                        ? BootstrapDialog.TYPE_SUCCESS
                                        : BootstrapDialog.TYPE_WARNING,
                                message: response.message,
                                onhide: function (dialogRef) {
                                    table.ajax.reload();
                                    if (response.status == 'OK') {
                                        sender_id = 0;
                                    }
                                },
                            });
                        });
                        dialogRef.close();
                    },
                },
            ],
        });
    };

    editor = new $.fn.dataTable.Editor({
        ajax: {
            type: 'POST',
            url: SERVER_PATH + 'district/getAllMessageCoach',
            headers: {	
                'x-user-id': $rootScope.user_id,
                'x-user-email': $rootScope.user_name
            },
            data: function (d) {
                d.event_id = $scope.eventId;
                d.user_id = $scope.user_id
            },
            dataType: 'application/json',
            complete: function (response) {
                var jsonData = JSON.parse(response.responseText);
                if (jsonData.status == 'OK') {
                    table.ajax.reload();
                }
            },
            error: function (xhr, status, error) { },
        },
        table: `#table_send_message_${$scope.eventId}`,
        formOptions: {
            main: {
                onBlur: 'none',
            },
        },
        i18n: {
            create: {
                button: 'New',
                title: 'New message',
                submit: 'Send',
            },
            remove: {
                button: 'Delete',
                title: 'Delete',
                submit: 'Delete',
                confirm: {
                    _: 'Are you sure you want to delete?',
                    1: 'Are you sure you want to delete?',
                },
            },
            error: {
                system: 'System error, please contact administrator.',
            },
        },
        fields: [
            {
                label: 'Event ID',
                name: 'send_message.event_id',
                def: $scope.eventId,
                type: 'hidden',
            },
            {
                label: 'Cc',
                name: 'send_message.cc',
                attr: {
                    placeholder: 'Ex: <EMAIL>; <EMAIL>; ...',
                },
            },
            {
                label: 'Bcc',
                name: 'send_message.bcc',
                attr: {
                    placeholder: 'Ex: <EMAIL>; <EMAIL>; ...',
                },
            },
            {
                label: 'Options',
                name: 'send_message.type',
                options: [
                    { label: 'Push', value: SEND_VIA_NOTIFICAION },
                    { label: 'Email & Push', value: SEND_VIA_EMAIL_AND_NOTIFICAION },
                ],
                type: 'select2',
                def: 1,
            },
            {
                label: 'Send to',
                name: 'send_message.send_to',
                def: SEND_FROM_COACH_TO_PLAYER,
                type: 'hidden'
            },
            {
                label: 'Select team',
                name: 'team',
                type: 'select2',
                options: $.map(teamsInDistrict, function (team) {
                    return { label: team.name, value: team.id };
                })
            },
            {
                label: 'Select players',
                name: 'players[].id',
                type: 'select2',
                options: [],
                opts: {
                    multiple: 'multiple',
                    // closeOnSelect: false,
                    placeholder: "Select a players"
                }
            },
            {
                label: 'Title',
                name: 'send_message.title',
            },
            {
                label: 'Content',
                name: 'send_message.content',
                type: 'ckeditor',
                className: 'block',
            },
            {
                label: 'Sender',
                name: 'send_message.user_id',
                def: $rootScope.user_id,
                type: 'hidden',
            },
            {
                label: 'Date',
                name: 'send_message.created_at',
                type: 'hidden',
                def: function () {
                    return moment().format('YYYY-MM-DD HH:mm:ss');
                },
            },
            {
                label: 'Attachments',
                name: 'files[].id',
                type: 'uploadMany',
                display: function (fileId, counter) {
                    return editor.file('files', fileId).filename;
                },
                noFileText: 'No files',
            },
        ]
    })

    // dependencies teams to get players
    editor.dependent('team', function (val, data, callback) {
        if (val) {
            $http({
                method: 'POST',
                url: SERVER_PATH + 'district/getAllPlayersInTeam',
                headers: {	
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name
                },
                data: 'team_id=' + val,
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
            }).success(function (response) {
                var players = [];
                $.each(response.data, function (index, player) {
                    players.push({ label: player.name, value: player.id });
                });
                // Update the options list for the player field
                editor.field('players[].id').update(players);
            });

            return {};
        }
    });

    table = $().DataTable({});

    function initTable() {
        // remove datatable when it exists
        if ($.fn.DataTable.isDataTable(`#table_send_message_${$scope.eventId}`)) {
            $(`#table_send_message_${$scope.eventId}`).DataTable().destroy();
            console.log('destroy table');
        }
        table_html = $(`<table id="table_send_message_${$scope.eventId}" class="table table-striped table-bordered table-hover"
        cellspacing="0" width="100%">
        <thead>
            <tr>
                <th>Id</th>
                <th>Title</th>
                <th>Content</th>
                <th>Sender</th>
                <th>Send to</th>
                <th>Send by</th>
                <th>Date</th>
            </tr>
        </thead>
        </table>`);

        $('.table-responsive').append(table_html);
        console.log('init table');
    }

    setTimeout(function () {
        table = $(`#table_send_message_${$scope.eventId}`).DataTable({
            dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
            stateSave: false,
            deferRender: true,
            ajax: {
                url: SERVER_PATH + 'district/getAllMessageCoach',
                headers: {	
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name
                },
                type: 'POST',
                data: {
                    "event_id": $scope.eventId,
                    "user_id": $rootScope.user_id
                },
                dataType: 'json',
                complete: function (response) {
                },
                error: function (xhr, status, error) { },
            },
            language: {
                paginate: {
                    previous: '<i class="fa fa-chevron-left"></i>',
                    next: '<i class="fa fa-chevron-right"></i>',
                },
            },
            columnDefs: [
                {
                    targets: [0],
                    visible: false,
                },
            ],
            columns: [
                {
                    data: 'send_message.id',
                },
                {
                    data: 'send_message.title',
                    render: function (data, type, row) {
                        return '<a data-match-route="/message-coach/course_coach_message_detail/' + row.send_message.id + '?title=' + row.send_message.title + '&eventId=' + $scope.eventId + '&previous=message-team-coach" href="#/message-coach/course_coach_message_detail/' + row.send_message.id + '?title=' + row.send_message.title + '&eventId=' + $scope.eventId + '&previous=message-team-coach">' + row.send_message.title + '</a>';
                    }
                },
                {
                    data: 'send_message.content',
                },
                {
                    data: null,
                    render: function (data, type, row) {
                        return data.parens.surname + ' ' + data.parens.other_name;
                    },
                },
                {
                    data: null,
                    render: function (data, type, row) {
                        switch (+data.send_message.send_to) {
                            case SEND_TO_ALL:
                                return 'All';
                            case SEND_FROM_COACH_TO_PLAYER:
                                {
                                    var player_names = data.players.map((user) => {
                                        return user.surname + ' ' + user.other_name;
                                    })
                                    return player_names.join(',<br>');
                                }
                            default:
                                return 'N/A';
                        }
                    },
                },
                {
                    data: null,
                    render: function (data, type, row) {
                        if (data.send_message.type == SEND_VIA_EMAIL) return 'Email';
                        if (data.send_message.type == SEND_VIA_NOTIFICAION) return 'Push';
                        if (data.send_message.type == SEND_VIA_EMAIL_AND_NOTIFICAION)
                            return 'Email and Push';
                        return '';
                    },
                },
                {
                    data: 'send_message.created_at',
                },
            ],
            select: {
                style: 'single',
            },
            order: [[0, 'desc']],
            lengthMenu: [
                [10, 25, 50, 100, -1],
                [10, 25, 50, 100, 'All'],
            ],
            buttons: [
                {
                    extend: 'create',
                    editor: editor,
                },
                {
                    extend: 'collection',
                    text: 'Delete',
                    autoClose: true,
                    enabled: false,
                    buttons: [
                        {
                            text: 'Delete (Admin & Push)',
                            action: function (e, dt, node, config) {
                                var rowData = table.rows({ selected: true }).data()[0];
                                $scope.delete(rowData.send_message.id, true);
                            },
                        },
                        {
                            text: 'Delete (Admin)',
                            action: function (e, dt, node, config) {
                                var rowData = table.rows({ selected: true }).data()[0];
                                $scope.delete(rowData.send_message.id);
                            },
                        },
                    ],
                },
                {
                    extend: 'colvis',
                    text: 'Columns',
                }
            ]
        });
    }, 200);

    editor.on('create', function (e, json, data) {
        sender_id = data.send_message.id;
        $scope.send(sender_id);
    });

    $scope.send = function (id) {
        // $scope.loadingElement.style.display = 'block';
        $http({
            method: 'POST',
            url: SERVER_PATH + 'message/sendMessage',
            headers: {	
                'x-user-id': $rootScope.user_id,
                'x-user-email': $rootScope.user_name
            },
            data: 'id=' + id,
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
        }).success(function (response) {
            console.log(response);
            // $scope.loadingElement.style.display = 'none';

            BootstrapDialog.show({
                title: response.status,
                type:
                    response.status == 'OK'
                        ? BootstrapDialog.TYPE_SUCCESS
                        : BootstrapDialog.TYPE_WARNING,
                message: response.message,
            });
        });
    };

    table
        .on('select', function (e, dt, type, indexes) {
            console.log('select');
            table.button(1).enable(); // enable Delete button
            var rowData = table.rows(indexes).data().toArray();
            sender_id = rowData['0'].send_message.id;
        })
        .on('deselect', function (e, dt, type, indexes) {
            console.log('deselect');
            table.button(1).disable(); // disable Delete button
            sender_id = 0;
        });
});