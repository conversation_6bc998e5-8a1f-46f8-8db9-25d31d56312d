.avatar > img {
    position: relative;
    max-width: 96px;
    float: left;
    margin: auto;
    border-radius: 18%;
    background-clip: padding-box;
}

input[type='radio']:checked + label {
    font-weight: bold;
}

.editor-datetime {
    margin-top: 10vh;
}

.cropper {
    overflow-y: auto;
    position: relative;
}

.colorSquare {
    width: 20px;
    height: 20px;
    margin: auto;
    border: 1px solid rgba(0, 0, 0, 0.2);
}

.modal-cropper .modal-dialog {
    width: 900px !important;
}

.modal-cropper .modal-dialog {
    zoom: 0.7;
}

.label-pending {
    background-color: rgba(153, 102, 255, 0.16);
    color: rgba(153, 102, 255, 1);
}

.label-approved {
    background-color: rgba(40, 167, 69, 0.16);
    color: #28a745;
}

.label-rejected {
    background-color: rgba(234, 84, 85, 0.16);
    color: #ea5455;
}

.btn-review {
    font-size: 15px;
    color: black;
    padding: 10px;
    border-radius: 2px;
}

.btn-review span {
    margin: 0;
}

#status-column-content label {
    flex: 108px 0 0;
}

#status-column-content span.select2 {
    flex: 1 0 0;
}

.my-actions {
    flex-direction: row-reverse;
}

.group-table-container {
    border: 1px solid #ddd;
    padding: 15px;
    margin-bottom: 15px;
    border-radius: 5px;
    background: #fff;
}

.group-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    border-radius: 5px;
    margin-bottom: 10px;
    width: 100%;
}

.merge-btn {
    background: red;
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 5px;
    cursor: pointer;
}

.text-secondary {
    color: #A8AAAE !important;
}

.text-primary {
    color: #000000 !important;
    font-weight: bold;
}

.swal-title {
    font-size: 18px !important; /* Change to your preferred size */
}

.swal-popup {
    font-size: 14px !important; /* Change the overall popup text size */
}

.swal-confirm-button,
.swal-cancel-button {
    font-size: 12px !important; /* Adjust button text size */
}


  /* Custom styling for Select2 */
  .select2-container--default .select2-selection--multiple {
    min-height: 40px;
    border: 1px solid #ccc;
    border-radius: 5px;
}