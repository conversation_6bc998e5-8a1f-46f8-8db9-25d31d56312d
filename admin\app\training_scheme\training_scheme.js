app.controller('trainingSchemeCtrl', function ($scope, $rootScope, $routeParams, $http) {
	$('#page-wrapper').removeClass('nav-small');

	function format(d) {
		id = d.training_scheme_groups.id;
		// `d` is the original data object for the row
		$content = '<h2 class="pull-left">Skill</h2><br/>';
		$content += '<div class="table-responsive">' +
			'<table id="training_scheme_group_' + id + '" class="table table-striped table-bordered table-hover" cellspacing="0" width="100%">' +
			'<thead><tr>' +
			'<th>Name</th>' +
			'<th>Chinese name</th>' +
			'<th>Order</th>' +
			'</tr></thead>' +
			'</table>';
		'</div>';

		return $content;
	}

	jQuery.extend(jQuery.fn.dataTableExt.oSort, {
		"justNum-pre": a => {
			var matches = a.match(/(\d+)/);

			if (matches == null || matches.length == 0) {
				return 900;

			}
			else {
				return parseInt(matches[0]);

			}
		},
		"justNum-asc": (a, b) => a - b,
		"justNum-desc": (a, b) => b - a
	});

	var editorTrainingScheme = new $.fn.dataTable.Editor({
		ajax: {
			type: 'POST',
			url: SERVER_PATH + "training-scheme/setTrainingSchemeGroups",
			headers: {	
				'x-user-id': $rootScope.user_id,
				'x-user-email': $rootScope.user_name
			},
			data: {},
			async: false,
			dataType: 'json',
			complete: function (response) {
				var jsonData = JSON.parse(response.responseText);
				// --- may need to reload
				if (DEVELOPMENT_ENVIRONMENT) console.log('status = ' + jsonData.status);
				if (jsonData.status == 'OK') {
					table.ajax.reload();
				}
			},
			error: function (xhr, status, error) { },
		},
		table: '#trainingSchemeTable',
		formOptions: {
			main: {
				onBlur: 'none',
			}
		},
		i18n: {
			create: {
				button: "New",
				title: "Create training scheme groups",
				submit: "Create"
			},
			edit: {
				button: "Edit",
				title: "Edit training scheme groups",
				submit: "Update"
			},
			remove: {
				button: "Delete",
				title: "Delete training scheme groups",
				submit: "Delete",
				confirm: {
					_: "Are you sure you want to delete these respects?",
					1: "Are you sure you want to delete this respects?"
				}
			},
			error: {
				system: "System error, please contact administrator."
			},
		},
		fields: [
			{
				label: "Name",
				name: "training_scheme_groups.name",
			},
			{
				label: "Chinese name",
				name: "training_scheme_groups.chinese_name",
			},
			{
				label: "Content",
				name: "training_scheme_groups.content",
				type: "textarea"
			},
			{
				label: "Image",
				name: "training_scheme_groups.image",
				type: "upload",
				display: function (data) {
					return '<img src="' + UPLOAD_FILE_PATH + data + '" width="100%">';
				},
				clearText: "Clear",
				noImageText: 'No image'
			},
			{
				label: 'Updated date:',
				name: 'training_scheme_groups.updated_at',
				def: function () {
					var d = new Date($.now());
					return d.getDate() + "-" + (d.getMonth() + 1) + "-" + d.getFullYear() + " " + d.getHours() + ":" + d.getMinutes() + ":" + d.getSeconds()
				},
				format: 'MM-DD-YYYY h:mm A',
				type: "hidden"
			}
		]
	});

	function addDetailEvent() {
		$('#trainingSchemeTable tbody').on('click', 'td.details-control', function () {
			var tr = $(this).closest('tr');
			var row = tableTrainingScheme.row(tr);

			if (row.child.isShown()) {
				// This row is already open - close it
				row.child.hide();
				tr.removeClass('shown');
			} else {
				// Open this row
				row.child(format(row.data())).show();
				tr.addClass('shown');
				var id = row.data().training_scheme_groups.id;

				// add class for next tr (child row)
				$(this).closest('tr').next().addClass('child-row-detail');

				var editor = new $.fn.dataTable.Editor({
					"ajax": {
						url: SERVER_PATH + "training-scheme/setTrainingSchemeSkills",
						type: 'POST',
						headers: {	
							'x-user-id': $rootScope.user_id,
							'x-user-email': $rootScope.user_name
						},
						data: {
							"training_scheme_group_id": id
						},
						dataType: 'json',
						complete: function (response) {
						},
						error: function (xhr, status, error) {
						},
					},
					"table": '#training_scheme_group_' + id,
					formOptions: {
						main: {
							onBlur: 'none'
						}
					},
					fields: [
						{
							"label": "Name",
							"name": "training_scheme_skills.name"
						}, {
							"label": "Chinese name",
							"name": "training_scheme_skills.chinese_name"
						},
						{
							"label": "Group id",
							"name": "training_scheme_skills.training_scheme_group_id",
							"type": "hidden",
							"default": id
						},
						{
							label: 'order',
							name: 'training_scheme_skills.order',
							type: "hidden"
						}
					],
					i18n: {
						create: {
							button: "New",
							title: "Add new skill",
							submit: "Create"
						},
						edit: {
							button: "Edit",
							title: "Edit skill",
							submit: "Save"
						},
						remove: {
							button: "Delete",
							title: "Delete skill",
							submit: "Delete",
							confirm: {
								_: "Are you sure you want to delete these skill?",
								1: "Are you sure you want to delete this skill?"
							}
						},
						error: {
							system: "System error, please contact administrator."
						},
					},
				})

				$('#training_scheme_group_' + id).DataTable({
					dom: '<"row"B>rt<"row"i>',
					stateSave: true,
					deferRender: true,
					ajax: {
						url: SERVER_PATH + "training-scheme/getTrainingSchemeSkills",
						type: 'POST',
						headers: {	
							'x-user-id': $rootScope.user_id,
							'x-user-email': $rootScope.user_name
						},
						data: {
							"training_scheme_group_id": id
						},
						dataType: 'json',
						complete: function (response) {
						},
						error: function (xhr, status, error) {
						},
					},
					columns: [
						{
							data: "training_scheme_skills.name",
							render: function (data, type, row) {
								return '<a data-match-route="/training_scheme_exercise/' + row.training_scheme_skills.id + '?name=' + row.training_scheme_skills.name + '" href="#/training_scheme_exercise/' + row.training_scheme_skills.id + '?name=' + row.training_scheme_skills.name + '">' + row.training_scheme_skills.name + '</a>';
							}
						},
						{ data: "training_scheme_skills.chinese_name" },
						{ data: "training_scheme_skills.order", "visible": false }
					],
					select: {
						style: 'single',
						// selector: 'td:first-child',
					},
					order: [[2, 'asc']],
					displayLength: -1,
					buttons: [
						{ extend: "create", editor: editor },
						{ extend: "edit", editor: editor },
						{ extend: "remove", editor: editor }
					],
					rowReorder: {
						dataSrc: 'training_scheme_skills.order',
						editor: editor
					},
				});
			}
		})
	}

	tableTrainingScheme = $('#trainingSchemeTable').DataTable({
		dom: '<"row"<"col-sm-6"B><"col-sm-6"f>>rt<"row"i>',
		stateSave: false,
		deferRender: true,
		ajax: {
			url: SERVER_PATH + "training-scheme/getTrainingSchemeGroups",
			type: 'POST',
			headers: {	
				'x-user-id': $rootScope.user_id,
				'x-user-email': $rootScope.user_name
			},
			data: {},
			dataType: 'json',
			complete: function (response) {
				addDetailEvent();
			},
			error: function (xhr, status, error) { },
		},
		language: {
			paginate: {
				previous: '<i class="fa fa-chevron-left"></i>',
				next: '<i class="fa fa-chevron-right"></i>'
			}
		},
		columns: [
			{
				"className": 'details-control',
				"orderable": false,
				"data": null,
				"defaultContent": ''
			}
			,
			{
				data: 'training_scheme_groups.image',
				className: "trainingSchemeGroups center",
				render: function (data) {
					if (data !== null && data !== '') {
						return '<img src="' + UPLOAD_FILE_PATH + data + '">';
					} else {
						return '<img src="' + SYSTEM_IMAGE_PATH + 'favicon.png">';
					}
				}
			},
			{
				data: 'training_scheme_groups.name'
			},
			{
				data: 'training_scheme_groups.chinese_name'
			},
			{
				data: 'training_scheme_groups.content'
			},
			{
				data: 'training_scheme_groups.views'
			},
			{
				data: 'training_scheme_groups.updated_at'
			}
		],
		select: {
			style: 'single',
			selector: 'td:not(:last-child)'
		},
		order: [
			[2, 'asc']
		],
		buttons: [{
			extend: "create",
			editor: editorTrainingScheme
		},
		{
			extend: "edit",
			editor: editorTrainingScheme
		},
		{
			extend: "remove",
			editor: editorTrainingScheme
		},
		{
			extend: 'colvis',
			text: 'Columns'
		}
		],
		columnDefs: [{
			type: 'justNum',
			targets: 2
		}]
	});


});

