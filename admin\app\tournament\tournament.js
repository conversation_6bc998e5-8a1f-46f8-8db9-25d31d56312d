app.controller('leagueTournamentCtrl', function ($scope, $rootScope, $routeParams, $http) {
  $('#page-wrapper').removeClass('nav-small');
  console.log('leagueTournamentCtrl');
  $scope.event_id = $routeParams.id;
  var leagueId = $routeParams.leaguesid;
  var eventName = $routeParams.eventName;
  var groupName = $routeParams.groupName;
  var leagueName = $routeParams.leagueName;
  var groupIdSelected = $routeParams.groupIdSelected;
  $rootScope.groupIdSelected = groupIdSelected;
  //--get start data--//
  $scope.eventName = eventName;
  $scope.leagueId = leagueId;
  $scope.leagueName = leagueName;
  $scope.groupName = groupName;
  $scope.groupIdSelected = groupIdSelected;
  jQuery.ajax({
    type: 'POST',
    url: SERVER_PATH + "tournament/getFistRoundID",
    async: false,
    headers: {	
      'x-user-id': $rootScope.user_id,
      'x-user-email': $rootScope.user_name
    },
    data: {
      "league_id": leagueId,
    },
    dataType: 'json',
    complete: function (response) {
      var jsonData = JSON.parse(response.responseText);
      if (jsonData.status == "OK") {
        var tournamentFirstRound = jsonData.info;
        $scope.tournamentFirstRoundID=tournamentFirstRound['id'];
      }
    }
  });
});