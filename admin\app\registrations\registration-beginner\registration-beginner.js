var ACTION_APPROVE = 1;
var ACTION_UNAPPROVE = 2;

app.controller(
    'registrationsBeginnerCtrl',
    function ($scope, $rootScope, $routeParams, $http) {
        $('#page-wrapper').removeClass('nav-small');

        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            'justNum-pre': (a) => parseFloat(a.replace(/\D/g, '')),
            'justNum-asc': (a, b) => a - b,
            'justNum-desc': (a, b) => b - a,
        });

        var event_id = $routeParams.id;

        jQuery.ajax({
            type: 'POST',
            url: SERVER_PATH + 'event/getEventInfo',
            async: false,
            headers: {	
                'x-user-id': $rootScope.user_id,
                'x-user-email': $rootScope.user_name
            },
            data: {
                event_id: event_id,
            },
            dataType: 'json',
            complete: function (response) {
                var jsonData = JSON.parse(response.responseText);
                var event = jsonData.info;
                $scope.event_name = event.name;
                $scope.event_type = event.type;
            },
        });

        $.fn.dataTable.moment('D-MMM-YYYY HH:mm:ss');
        $.fn.dataTable.moment('D-MMM-YYYY');

        function initRegistrationsTable() {
            setTimeout(() => {
                tableRegistration = $('#table_beginner').DataTable({
                    dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
                    stateSave: false,
                    deferRender: true,
                    bDestroy: true,
                    ajax: {
                        url: SERVER_PATH + 'district/getRegistrationOfDistrict',
                        type: 'POST',
                        headers: {	
                            'x-user-id': $rootScope.user_id,
                            'x-user-email': $rootScope.user_name
                        },
                        data: {
                            event_id: event_id,
                            user_id: $rootScope.user_id,
                        },
                        dataType: 'json',
                        complete: function (response) {},
                        error: function (xhr, status, error) {},
                    },
                    language: {
                        paginate: {
                            previous: '<i class="fa fa-chevron-left"></i>',
                            next: '<i class="fa fa-chevron-right"></i>',
                        },
                    },
                    columns: [
                        {
                            data: 'DT_RowId',
                            targets: 0,
                            render: function (data, type, row, meta) {
                                if (type === 'display') {
                                    data =
                                        '<div class="checkbox"><input type="checkbox" class="dt-checkboxes"><label></label></div>';
                                }
                                return data;
                            },
                            checkboxes: {
                                selectRow: true,
                                selectAllRender:
                                    '<div class="checkbox"><input type="checkbox" class="dt-checkboxes"><label></label></div>',
                            },
                        },
                        {
                            data: 'players.player_photo',
                            className: 'avatar',
                            orderable: false,
                            render: function (data) {
                                if (data !== null && data !== '') {
                                    return (
                                        '<img src="' +
                                        PRODUCT_IMAGE_PATH +
                                        data +
                                        '">'
                                    );
                                } else {
                                    return (
                                        '<img src="' +
                                        SYSTEM_IMAGE_PATH +
                                        'favicon.png">'
                                    );
                                }
                            },
                        },
                        {
                            data: null,
                            render: function (data, type, row) {
                                return (
                                    data.players.surname +
                                    ' ' +
                                    data.players.other_name
                                );
                            },
                        },
                        {
                            data: 'players.chinese_name',
                        },
                        {
                            data: 'players.dob',
                            className: 'center',
                            // visible: false
                        },
                        {
                            data: 'players.gender',
                            className: 'center',
                            // visible: false
                        },
                        {
                            data: null,
                            render: function (data, type, row) {
                                return (
                                    data.parens.surname +
                                    ' ' +
                                    data.parens.other_name
                                );
                            },
                            visible: false,
                        },
                        {
                            data: 'parens.email',
                            className: 'center',
                            visible: false,
                        },
                        {
                            data: 'parens.phone',
                            className: 'center',
                            visible: false,
                        },
                        {
                            data: 'groups.name',
                            className: 'center',
                        },
                        {
                            data: 'districts.name',
                        },
                        {
                            data: 'registrations.goalkeeper',
                            render: function (data, type, row) {
                                let show_checkbox = '';
                                if (data == 1) {
                                    show_checkbox = 'checked';
                                }
                                return (
                                    '<input type="checkbox" class="shipping_chkbox" ' +
                                    ' name="' +
                                    row.registrations.id +
                                    '"' +
                                    show_checkbox +
                                    '>'
                                );
                            },
                        },
                        {
                            data: 'registrations.registered_date',
                        },
                        {
                            data: 'registrations.approval_status',
                            className: 'center',
                        },
                        {
                            data: 'registrations.emailed',
                            render: function (data, type, row, meta) {
                                // show badge
                                if (data == 0) {
                                    return '<span class="badge badge-info">Didn\'t send</span>';
                                } else if (data == 1 || data == 2) {
                                    return (
                                        '<span class="badge badge-danger">Can\'t send(' +
                                        data +
                                        ')</span>'
                                    );
                                } else if (data == 'Done') {
                                    return '<span class="badge badge-success"> Sent</span>';
                                }
                            },
                        },
                        {
                            data: 'registrations.sent_invoice_email',
                            render: function (data, type, row, meta) {
                                // show badge
                                if (data == 0) {
                                    return '<span class="badge badge-info">Didn\'t send</span>';
                                } else if (data == 1 || data == 2) {
                                    return (
                                        '<span class="badge badge-danger">Can\'t send(' +
                                        data +
                                        ')</span>'
                                    );
                                } else if (data == 'Done') {
                                    return '<span class="badge badge-success"> Sent</span>';
                                }
                            },
                        },
                        {
                            data: 'registrations.district_status',
                        },
                    ],
                    initComplete: function () {
                        // build the select list
                        var age_group_column = {
                            orderColumn: 9,
                            elementId: 'age-group-content',
                            selectId: 'selType',
                        };
                        var district_column = {
                            orderColumn: 10,
                            elementId: 'district-content',
                            selectId: 'selType',
                        };

                        var approval_column = {
                            orderColumn: 14,
                            elementId: 'approval-content',
                            selectId: 'selType',
                        };

                        filterColumns = [
                            age_group_column,
                            district_column,
                            approval_column,
                        ];

                        filterColumns.forEach((item) => {
                            this.api()
                                .columns(item.orderColumn)
                                .every(function () {
                                    var column = this;
                                    var select = $(
                                        `<select id="${item.orderColumn}" class="form-control"><option value="">All</option></select>`
                                    )
                                        .appendTo($(`#${item.elementId}`))
                                        .on('change', function () {
                                            var val =
                                                $.fn.dataTable.util.escapeRegex(
                                                    $(this).val()
                                                );

                                            column
                                                .search(
                                                    val ? val : '',
                                                    true,
                                                    false
                                                )
                                                .draw();
                                        })
                                        .select2();
                                    var column_data = column.data();
                                    var select_data = [];
                                    column_data.map((item) => {
                                        if (item != null) {
                                            item.indexOf(', ') > 0
                                                ? (item = item.split(', '))
                                                : (item = [item]);
                                            item.forEach((item) => {
                                                select_data.push(item);
                                            });
                                        }
                                    });
                                    select_data
                                        .filter(onlyUnique)
                                        .sort()
                                        .map(function (d, j) {
                                            select.append(
                                                `<option value="${d}">${d}</option>`
                                            );
                                        });
                                });
                        });
                    },
                    select: {
                        style: SELECT_MODE,
                        selector: 'td:not(:last-child)',
                    },
                    columnDefs: [
                        {
                            type: 'justNum',
                            targets: 8,
                        },
                    ],
                    order: [[9, 'asc']],
                    buttons: [
                        {
                            extend: 'excel',
                            name: 'excel',
                            text: '<i class="fa fa-file-excel-o"></i>&emsp;Export to Excel',
                            titleAttr: 'Export data to an Excel file',
                            filename: 'Beginner - Registration',
                            title:
                                'Beginner - Registration - ' +
                                $scope.event_name,
                            exportOptions: {
                                columns: ':visible',
                                modifier: {
                                    autoFilter: true,
                                    // selected: true
                                },
                            },
                        },
                        {
                            extend: 'selectedSingle',
                            text: 'Edit Registration',
                            action: function () {
                                editRegistration();
                            },
                        },
                        // {
                        //     extend: 'selectedSingle',
                        //     text: 'Delete Registration',
                        //     action: function () {
                        //         deleteRegistration();
                        //     },
                        // },
                        {
                            extend: 'colvis',
                            text: 'Columns',
                        },
                    ],
                });
                tableRegistration.on(
                    'click',
                    'input[type="checkbox"][class="shipping_chkbox"]',
                    function (e, dt, type, indexes) {
                        var input = $(this);
                        var name = input.attr('name');

                        var checked = input.prop('checked');

                        $scope.checkGoalkeeper(name, checked);
                    }
                );
                $('#goalkeeper').unbind();
                $('#goalkeeper').change(function () {
                    // check if checkbox is checked
                    if ($(this).is(':checked')) {
                        tableRegistration.column(11).search('checked').draw();
                    } else {
                        tableRegistration.column(11).search('').draw();
                    }
                });
            }, 400);
        }
        
        $scope.goBack = function () {
            window.history.back();
        };

        function deleteRegistration() {
            var table_selected = tableRegistration
                .rows({ selected: true })
                .data();

            var player = table_selected[0];

            var DT_RowId = player['DT_RowId'];

            var registration_id = DT_RowId.split('_')[1];
            BootstrapDialog.confirm(
                'Delete Registration',
                'Are you sure to delete this registration?',
                function (result) {
                    if (result) {
                        jQuery.ajax({
                            type: 'POST',
                            url: SERVER_PATH + 'district/deleteRegistraion',
                            async: true,
                            headers: {	
                                'x-user-id': $rootScope.user_id,
                                'x-user-email': $rootScope.user_name
                            },
                            data: {
                                registration_id: registration_id,
                            },
                            dataType: 'json',
                            beforeSend: function () {
                                Swal.fire({
                                    title: 'Please Wait!',
                                    allowOutsideClick: false,
                                    didOpen: () => {
                                        Swal.showLoading();
                                    },
                                });
                            },
                            complete: function (response) {
                                Swal.close();
                                var jsonData = JSON.parse(
                                    response.responseText
                                );

                                tableRegistration.ajax.reload();

                                if (jsonData.status == 'OK') {
                                    Swal.fire({
                                        type: 'success',
                                        icon: 'success',
                                        title: jsonData.message,
                                        confirmButtonClass: 'btn btn-primary',
                                        buttonsStyling: false,
                                    });
                                } else {
                                    Swal.fire({
                                        title: 'ERROR!',
                                        text: jsonData.message,
                                        icon: 'error',
                                        type: 'error',
                                    });
                                }
                            },
                        });
                    }
                }
            );
        }

        $scope.checkGoalkeeper = function (registration_id, status) {
            $.ajax({
                type: 'POST',
                url: SERVER_PATH + 'district/checkGoalkeeper',
                headers: {	
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name
                },
                data: {
                    registration_id: registration_id,
                    status: status,
                },
                async: false,
                dataType: 'json',
                complete: function (response) {
                    var jsonData = JSON.parse(response.responseText);
                    if (jsonData.status == 'OK') {
                        Swal.fire({
                            toast: true,
                            icon: 'success',
                            title: jsonData.message,
                            animation: false,
                            position: 'top-right',
                            showConfirmButton: false,
                            timer: 2000,
                            timerProgressBar: true,
                            didOpen: (toast) => {
                                toast.addEventListener(
                                    'mouseenter',
                                    Swal.stopTimer
                                );
                                toast.addEventListener(
                                    'mouseleave',
                                    Swal.resumeTimer
                                );
                            },
                        });
                    } else {
                        Swal.fire({
                            toast: true,
                            icon: 'error',
                            title: jsonData.message,
                            animation: false,
                            position: 'top-right',
                            showConfirmButton: false,
                            timer: 2000,
                            timerProgressBar: true,
                            didOpen: (toast) => {
                                toast.addEventListener(
                                    'mouseenter',
                                    Swal.stopTimer
                                );
                                toast.addEventListener(
                                    'mouseleave',
                                    Swal.resumeTimer
                                );
                            },
                        });

                        tableRegistration.ajax.reload();
                    }
                },
                error: function (xhr, status, error) {
                    console.log(error);
                },
            });
        };

        function editRegistration() {
            var table_selected = tableRegistration
                .rows({ selected: true })
                .data();

            var player = table_selected[0];

            var team_district_player_id = player['team_district_players']['id'];

            var player_selected_district =
                player['team_districts']['district_id'];

            var player_age_group = player['team_districts']['group_id'];

            jQuery.ajax({
                type: 'POST',
                url: SERVER_PATH + 'district/getAllAgeGroupsAtSelectedPlayer',
                async: false,
                headers: {	
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name
                },
                data: {
                    team_district_player_id: team_district_player_id,
                },
                dataType: 'json',
                beforeSend: function () {
                    Swal.fire({
                        title: 'Please Wait!',
                        allowOutsideClick: false,
                        didOpen: () => {
                            Swal.showLoading();
                        },
                    });
                },
                complete: function (response) {
                    Swal.close();
                    var jsonData = JSON.parse(response.responseText);
                    if (jsonData.status == 'OK') {
                        groups = jsonData.data.groups;

                        selectedPlayerGroup = jsonData.data.selected_group;
                    } else {
                        Swal.fire({
                            title: 'ERROR!',
                            text: jsonData.message,
                            icon: 'error',
                            type: 'error',
                        });
                    }
                },
            });

            jQuery.ajax({
                type: 'POST',
                url: SERVER_PATH + 'district/getAllDistrictOfAgeGroup',
                async: false,
                headers: {	
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name
                },
                data: {
                    age_group_id: selectedPlayerGroup.id,
                },
                dataType: 'json',
                beforeSend: function () {
                    Swal.fire({
                        title: 'Please Wait!',
                        allowOutsideClick: false,
                        didOpen: () => {
                            Swal.showLoading();
                        },
                    });
                },
                complete: function (response) {
                    Swal.close();
                    var jsonData = JSON.parse(response.responseText);

                    if (jsonData.status == 'OK') {
                        districts = jsonData.data;
                    } else {
                        Swal.fire({
                            title: 'ERROR!',
                            text: jsonData.message,
                            icon: 'error',
                            type: 'error',
                        });
                    }
                },
            });

            var editor_edit_registration = new $.fn.dataTable.Editor({
                ajax: {
                    type: 'POST',
                    url: SERVER_PATH + 'district/editRegistration',
                    headers: {	
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name
                    },
                    data: {
                        event_id: event_id,
                        user_id: $rootScope.user_id,
                        team_district_player_id: team_district_player_id,
                    },
                    dataType: 'json',
                    complete: function (response) {
                        var jsonData = JSON.parse(response.responseText);
                        if (jsonData.status == 'OK') {
                            Swal.fire({
                                toast: true,
                                icon: 'success',
                                title: jsonData.message,
                                animation: false,
                                position: 'top-right',
                                showConfirmButton: false,
                                timer: 2000,
                                timerProgressBar: true,
                                didOpen: (toast) => {
                                    toast.addEventListener(
                                        'mouseenter',
                                        Swal.stopTimer
                                    );
                                    toast.addEventListener(
                                        'mouseleave',
                                        Swal.resumeTimer
                                    );
                                },
                            });

                            tableRegistration.ajax.reload();
                        } else {
                            Swal.fire({
                                title: 'ERROR!',
                                text: jsonData.message,
                                icon: 'error',
                                type: 'error',
                            });
                        }
                    },
                    error: function (xhr, status, error) {
                        console.log(error);
                    },
                },
                formOptions: {
                    main: {
                        onBlur: 'none',
                    },
                },
                fields: [
                    {
                        label: 'Age Group:',
                        name: 'age_group_id',
                        type: 'select2',
                        opts: {
                            placeholder: 'Select Age Group',
                        },
                        options: groups.map(function (group) {
                            return {
                                label: group.type + '-' + group.name,
                                value: group.id,
                            };
                        }),
                        default: player_age_group,
                    },
                    {
                        label: 'Team District:',
                        name: 'districs_target',
                        type: 'select2',
                        opts: {
                            placeholder: 'Select a District',
                        },
                        options: districts.map(function (district) {
                            return {
                                label: district.name,
                                value: district.id,
                            };
                        }),
                        default: player_selected_district,
                    },
                ],
            });

            editor_edit_registration
                .title('Edit Registration')
                .buttons({
                    label: 'Save',
                    fn: function () {
                        this.submit();
                    },
                })
                .edit()
                .open();

            editor_edit_registration.dependent(
                'age_group_id',
                function (val, data, callback) {
                    // get selected row
                    var table_selected = tableRegistration
                        .rows({ selected: true })
                        .data()[0];
                    var player_selected_district =
                        table_selected['team_districts']['district_id'];
                    jQuery.ajax({
                        type: 'POST',
                        url: SERVER_PATH + 'district/getAllDistrictOfAgeGroup',
                        async: false,
                        headers: {	
                            'x-user-id': $rootScope.user_id,
                            'x-user-email': $rootScope.user_name
                        },
                        data: {
                            age_group_id: val,
                        },
                        dataType: 'json',
                        beforeSend: function () {
                            Swal.fire({
                                title: 'Please Wait!',
                                allowOutsideClick: false,
                                didOpen: () => {
                                    Swal.showLoading();
                                },
                            });
                        },
                        complete: function (response) {
                            Swal.close();
                            var jsonData = JSON.parse(response.responseText);

                            if (jsonData.status == 'OK') {
                                districts = jsonData.data;
                                // update the options list for second select box
                                editor_edit_registration
                                    .field('districs_target')
                                    .update(
                                        districts.map(function (district) {
                                            return {
                                                label: district.name,
                                                value: district.id,
                                            };
                                        })
                                    );

                                // check player_registration['district_region'] is in the new options list
                                var isDistrictExist = false;
                                for (var i = 0; i < districts.length; i++) {
                                    if (
                                        districts[i].id ==
                                        player_selected_district
                                    ) {
                                        isDistrictExist = true;
                                        break;
                                    }
                                }

                                if (isDistrictExist) {
                                    editor_edit_registration
                                        .field('districs_target')
                                        .set(player_selected_district);
                                } else {
                                    // check districts[0] is not null
                                    if (districts[0] != null) {
                                        editor_edit_registration
                                            .field('districs_target')
                                            .set(districts[0].id);
                                    }
                                }
                            } else {
                                Swal.fire({
                                    title: 'ERROR!',
                                    text: jsonData.message,
                                    icon: 'error',
                                    type: 'error',
                                });
                            }
                        },
                    });

                    return true;
                }
            );
        }

        function onlyUnique(value, index, self) {
            return self.indexOf(value) === index;
        }

        initRegistrationsTable();
    }
);
