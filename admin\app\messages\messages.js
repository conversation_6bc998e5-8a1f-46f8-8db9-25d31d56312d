app.controller(
    'messagesCtrl',
    function ($scope, $rootScope, $routeParams, $http) {
        $('#page-wrapper').removeClass('nav-small');

        var event_id = $routeParams.id;

        var players = [];

        // get event info
        jQuery.ajax({
            type: 'POST',
            url: SERVER_PATH + 'event/getEventInfo',
            async: false,
            headers: {	
                'x-user-id': $rootScope.user_id,
                'x-user-email': $rootScope.user_name
            },
            data: {
                event_id: event_id,
            },
            dataType: 'json',
            complete: function (response) {
                var jsonData = JSON.parse(response.responseText);
                var event = jsonData.info;
                event_name = event.name;
                event_type = event.type;
                normalizedType = normalizeEventType(event_type);
            },
        });

        $scope.normalizedType = normalizedType;

        function normalizeEventType(eventType) {
            switch (eventType) {
                case 'Summer Scheme':
                    return 'summer-scheme';
                case 'Regional':
                    return 'regional';
                case 'PL Junior':
                    return 'pl-junior';
                case 'Golden Age':
                    return 'golden-age';
                case 'Beginner':
                    return 'beginner';
                case 'District':
                    return 'district';
                default:
                    return '';
            }
        }

        // get all player
        // jQuery.ajax({
        //   type: 'POST',
        //   url: SERVER_PATH + '/message/getALLPlayerToSelection',
        //   async: false,
        //   data: {
        //     event_id: event_id,
        //   },
        //   dataType: 'json',
        //   complete: function (response) {
        //     var jsonData = JSON.parse(response.responseText);
        //     players = jsonData.info;
        //   },
        // });

        // get all course
        jQuery.ajax({
            type: 'POST',
            url: SERVER_PATH + '/course/getAllCourses',
            async: false,
            headers: {	
                'x-user-id': $rootScope.user_id,
                'x-user-email': $rootScope.user_name
            },
            data: {
                event_id: event_id,
            },
            dataType: 'json',
            complete: function (response) {
                var jsonData = JSON.parse(response.responseText);
                $scope.courses = jsonData.info;
                console.log($scope.courses);
            },
        });

        //get all team of event
        jQuery.ajax({
            type: 'POST',
            url: SERVER_PATH + '/district/getAllTeamOfDistrict',
            async: false,
            headers: {	
                'x-user-id': $rootScope.user_id,
                'x-user-email': $rootScope.user_name
            },
            data: {
                event_id: event_id,
            },
            dataType: 'json',
            complete: function (response) {
                var jsonData = JSON.parse(response.responseText);
                $scope.teams = jsonData.data;
            },
        });

        // get venue all in all courses
        jQuery.ajax({
            type: 'POST',
            url: SERVER_PATH + '/course/getAllLocationCourses',
            async: false,
            headers: {	
                'x-user-id': $rootScope.user_id,
                'x-user-email': $rootScope.user_name
            },
            data: {
                event_id: event_id,
            },
            dataType: 'json',
            complete: function (response) {
                var jsonData = JSON.parse(response.responseText);
                $scope.venues = jsonData.info;
            },
        });

        // define shipping type\

        $scope.shipping_types = [
            {
                label: 'Self pick up',
                value: 'Self pick up',
            },
            {
                label: 'Home',
                value: 'home',
            },
            {
                label: 'Locker',
                value: 'locker',
            },
        ];

        arrEventType = [];

        switch (event_type) {
            case EVENT_GOLDENAGE: {
                arrEventType = [
                    { label: 'All', value: SEND_TO_ALL },
                    { label: 'Group', value: SEND_TO_GROUPS },
                    { label: 'Club', value: SEND_TO_CLUBS },
                    { label: 'Group and Club', value: SEND_TO_GROUPS_IN_CLUBS },
                    { label: 'Players', value: SEND_TO_PARENT_OF_PLAYER },
                ];
                break;
            }
            case EVENT_SUMMER_SCHEME: {
                arrEventType = [
                    // { label: 'Self pick up', value: SEND_TO_SELF_PICK_UP },
                    { label: 'Course', value: SEND_TO_COURSE },
                    { label: 'Venue', value: SEND_TO_VENUE },
                    { label: 'Shipping type', value: SEND_TO_SHIPPING_TYPE },
                    { label: 'All', value: SEND_TO_ALL },
                ];
                break;
            }
            case EVENT_BEGINNER: {
                arrEventType = [
                    {
                        label: 'Send to team',
                        value: SEND_TO_TEAM,
                    },
                    {
                        label: 'All',
                        value: SEND_TO_ALL,
                    },
                ];
                break;
            }
            case EVENT_DISTRICT: {
                arrEventType = [
                    {
                        label: 'Send to team',
                        value: SEND_TO_TEAM,
                    },
                    {
                        label: 'All',
                        value: SEND_TO_ALL,
                    },
                ];
                break;
            }
            case EVENT_REGIONAL: {
                arrEventType = [
                    {
                        label: 'Send to team',
                        value: SEND_TO_TEAM,
                    },
                    {
                        label: 'All',
                        value: SEND_TO_ALL,
                    },
                ];
                break;
            }
            default: {
                arrEventType = [{ label: 'All', value: SEND_TO_ALL }];
            }
        }

        $scope.event_id = event_id;
        $scope.event_name = event_name;
        $scope.event_type = event_type;

        $scope.goBack = function () {
            window.history.back();
        };

        $scope.getClubs = function () {
            jQuery.ajax({
                type: 'POST',
                url: SERVER_PATH + 'club/getClubs',
                async: false,
                headers: {	
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name
                },
                data: {},
                dataType: 'json',
                complete: function (response) {
                    var jsonData = JSON.parse(response.responseText);
                    $scope.clubs = [];
                    var option = {};
                    $.each(jsonData.data, function (i, e) {
                        option.label = e.name;
                        option.value = e.id;
                        $scope.clubs.push(option);
                        option = {};
                    });
                },
            });
        };

        $scope.getGroupsByEvent = function () {
            jQuery.ajax({
                type: 'POST',
                url: SERVER_PATH + 'event/getGroupsByEvent',
                async: false,
                headers: {	
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name
                },
                data: {
                    event_id: $scope.event_id,
                },
                dataType: 'json',
                complete: function (response) {
                    var jsonData = JSON.parse(response.responseText);
                    $scope.groups = [];
                    var option = {};
                    $.each(jsonData.info, function (i, e) {
                        option.label = e.name;
                        option.value = e.id;
                        $scope.groups.push(option);
                        option = {};
                    });
                },
            });
        };

        $scope.getClubs();
        $scope.getGroupsByEvent();

        // datatable & editor master
        var active_tab = 'Email';
        var sender_id = 0;
        var table_name = '#table_send_message';
        var table;

        $scope.loadingElement = document.getElementById('loading');
        $scope.loadingElement.style.display = 'none';

        $scope.init = function (flag) {
            editor = new $.fn.dataTable.Editor({
                ajax: {
                    type: 'POST',
                    url: SERVER_PATH + 'message/editorSendMessage',
                    data: function (d) {
                        d.event_id = $scope.event_id;
                    },
                    headers: {	
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name
                    },
                    dataType: 'application/json',
                    complete: function (response) {
                        var jsonData = JSON.parse(response.responseText);
                        // --- may need to reload
                        if (DEVELOPMENT_ENVIRONMENT)
                            console.log('status = ' + jsonData.status);
                        if (jsonData.status == 'OK') {
                            table.ajax.reload();
                        }
                    },
                    error: function (xhr, status, error) {},
                },
                table: table_name,
                formOptions: {
                    main: {
                        onBlur: 'none',
                    },
                },
                i18n: {
                    create: {
                        button: 'New',
                        title: 'New message',
                        submit: 'Send',
                    },
                    remove: {
                        button: 'Delete',
                        title: 'Delete',
                        submit: 'Delete',
                        confirm: {
                            _: 'Are you sure you want to delete?',
                            1: 'Are you sure you want to delete?',
                        },
                    },
                    error: {
                        system: 'System error, please contact administrator.',
                    },
                },
                fields: [
                    {
                        label: 'Event ID',
                        name: 'send_message.event_id',
                        def: $scope.event_id,
                        type: 'hidden',
                    },
                    // {
                    //   label: 'Cc',
                    //   name: 'send_message.cc',
                    //   attr: {
                    //     placeholder: 'Ex: <EMAIL>; <EMAIL>; ...',
                    //   },
                    // },
                    // {
                    //   label: 'Bcc',
                    //   name: 'send_message.bcc',
                    //   attr: {
                    //     placeholder: 'Ex: <EMAIL>; <EMAIL>; ...',
                    //   },
                    // },
                    {
                        label: 'Options',
                        name: 'send_message.type',
                        type: 'radio',
                        options: [
                            { label: 'Email', value: SEND_VIA_EMAIL },
                            { label: 'Push', value: SEND_VIA_NOTIFICAION },
                            {
                                label: 'Email & Push',
                                value: SEND_VIA_EMAIL_AND_NOTIFICAION,
                            },
                        ],
                        def: SEND_VIA_EMAIL,
                    },
                    {
                        label: 'Send to',
                        name: 'send_message.send_to',
                        type: 'radio',
                        options: arrEventType,
                        def: 0,
                    },
                    {
                        label: 'Select course',
                        name: 'send_message.course_ids',
                        type: 'select2',
                        options: $scope.courses,
                        opts: {
                            multiple: 'multiple',
                            placeholder: 'Select a course',
                        },
                    },
                    {
                        label: 'Select team',
                        name: 'send_message.team_ids',
                        type: 'select2',
                        options: $scope.teams.map((team) => {
                            return {
                                label: team.name,
                                value: team.id,
                            };
                        }),
                        opts: {
                            multiple: 'multiple',
                            placeholder: 'Select a team',
                        },
                    },
                    {
                        label: 'Select type of player',
                        name: 'send_message.player_type',
                        type: 'select',
                        options: [
                            { label: 'All', value: 'All' },
                            { label: 'Approved', value: 'Approved' },
                            { label: 'Registered', value: 'Registered' },
                        ].filter((item) =>
                            event_type == EVENT_BEGINNER
                                ? item.value == 'All'
                                : item
                        ),
                        opts: {
                            placeholder: 'Select a type of player',
                        },
                    },
                    {
                        label: 'Select location',
                        name: 'send_message.venue_ids',
                        type: 'select2',
                        options: $scope.venues,
                        opts: {
                            multiple: 'multiple',
                            placeholder: 'Select a location',
                        },
                    },
                    {
                        label: 'Select shipping type',
                        name: 'send_message.shipping_types',
                        type: 'select2',
                        options: $scope.shipping_types,
                        opts: {
                            multiple: true,
                            placeholder: 'Search and select shipping type...',
                        },
                    },
                    {
                        label: 'Select group',
                        name: 'send_message.group_ids',
                        type: 'select2',
                        options: $scope.groups,
                        opts: {
                            multiple: true,
                            placeholder: 'Search and select group...',
                        },
                    },
                    {
                        label:'Select gender',
                        name: 'send_message.gender',
                        type: 'radio',
                        options: [
                            {
                                label: 'All',
                                value: 'All'
                            },
                            { label: 'Male', value: 'Male' },
                            {
                                label: 'Female',
                                value: 'Female',
                            }
                        ],
                        default: 'All'
                    },
                    {
                        label: 'Select club',
                        name: 'send_message.club_ids',
                        type: 'select2',
                        options: $scope.clubs,
                        opts: {
                            multiple: true,
                            placeholder: 'Search and select club...',
                        },
                    },
                    {
                        label: 'Select user',
                        name: 'players[].id',
                        type: 'select2',
                        // options: players,
                        allowClear: true,
                        minimumResultsForSearch: 10,
                        opts: {
                            multiple: true,
                            placeholder: 'Search and select players...',
                            ajax: {
                                url:
                                    SERVER_PATH +
                                    'message/processSearchPlayerFunction',
                                type: 'POST',
                                dataType: 'json',
                                headers: {	
                                    'x-user-id': $rootScope.user_id,
                                    'x-user-email': $rootScope.user_name
                                },
                                data: function (params) {
                                    var searchParamsSent = {
                                        search: params.term,
                                    };
                                    return searchParamsSent;
                                },
                                processResults: function (data) {
                                    return {
                                        results: $.map(
                                            data.info,
                                            function (item) {
                                                return {
                                                    id: item.value,
                                                    text: item.label,
                                                };
                                            }
                                        ),
                                    };
                                },
                            },
                        },
                    },
                    {
                        label: 'Title',
                        name: 'send_message.title',
                    },
                    {
                        label: 'Content',
                        name: 'send_message.content',
                        type: 'ckeditor',
                        className: 'block',
                    },
                    {
                        label: 'Sender',
                        name: 'send_message.user_id',
                        def: $rootScope.user_id,
                        type: 'hidden',
                    },
                    {
                        label: 'Date',
                        name: 'send_message.created_at',
                        type: 'hidden',
                        def: function () {
                            return moment().format('YYYY-MM-DD HH:mm:ss');
                        },
                    },
                    {
                        label: 'Attachments',
                        name: 'files[].id',
                        type: 'uploadMany',
                        display: function (fileId, counter) {
                            return editor.file('files', fileId).filename;
                        },
                        noFileText: 'No files',
                    },
                ],
            });

            editor.dependent('send_message.send_to', function (val) {
                arrayField = [
                    'send_message.club_ids',
                    'players[].id',
                    'send_message.course_ids',
                    'send_message.venue_ids',
                    'send_message.shipping_types',
                    'send_message.group_ids',
                    'send_message.team_ids',
                    'send_message.player_type',
                    'send_message.gender'
                ];

                // console.log(val);
                if (val === SEND_TO_GROUPS)
                    return {
                        show: ['send_message.group_ids'],
                        hide: arrayField.filter(
                            (field) => field !== 'send_message.group_ids'
                        ),
                    };
                else if (val === SEND_TO_CLUBS)
                    return {
                        show: ['send_message.club_ids'],
                        hide: arrayField.filter(
                            (field) => field !== 'send_message.club_ids'
                        ),
                    };
                else if (val === SEND_TO_GROUPS_IN_CLUBS)
                    return {
                        show: [
                            'send_message.group_ids',
                            'send_message.club_ids',
                        ],
                        hide: arrayField.filter(
                            (field) =>
                                field !== 'send_message.group_ids' &&
                                field !== 'send_message.club_ids'
                        ),
                    };
                else if (val === SEND_TO_PARENT_OF_PLAYER)
                    return {
                        show: ['players[].id'],
                        hide: arrayField.filter(
                            (field) => field !== 'players[].id'
                        ),
                    };
                else if (val === SEND_TO_COURSE) {
                    return {
                        show: ['send_message.course_ids'],
                        hide: arrayField.filter(
                            (field) => field !== 'send_message.course_ids'
                        ),
                    };
                } else if (val === SEND_TO_VENUE) {
                    return {
                        show: ['send_message.venue_ids'],
                        hide: arrayField.filter(
                            (field) => field !== 'send_message.venue_ids'
                        ),
                    };
                } else if (val === SEND_TO_SHIPPING_TYPE) {
                    return {
                        show: ['send_message.shipping_types'],
                        hide: arrayField.filter(
                            (field) => field !== 'send_message.shipping_types'
                        ),
                    };
                } else if (val === SEND_TO_TEAM) {
                    return {
                        show: [
                            'send_message.team_ids',
                            'send_message.player_type',
                            'send_message.gender'
                        ],
                        hide: arrayField.filter(
                            (field) =>
                                field !== 'send_message.team_ids' &&
                                field !== 'send_message.player_type' &&
                                field !== 'send_message.gender'
                        ),
                    };
                }
                return {
                    hide: arrayField,
                };
            });

            // call send function after create new message
            editor.on('create', function (e, json, data) {
                sender_id = data.send_message.id;
                $scope.send(sender_id);
            });

            table = $(table_name).DataTable({
                dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
                stateSave: false,
                deferRender: true,
                ajax: {
                    url: SERVER_PATH + 'message/editorSendMessage',
                    type: 'POST',
                    headers: {	
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name
                    },
                    data: {
                        event_id: $scope.event_id,
                    },
                    dataType: 'json',
                    complete: function (response) {
                        if (flag) $scope.init(false);
                    },
                    error: function (xhr, status, error) {},
                },
                language: {
                    paginate: {
                        previous: '<i class="fa fa-chevron-left"></i>',
                        next: '<i class="fa fa-chevron-right"></i>',
                    },
                },
                columnDefs: [
                    {
                        targets: [0],
                        visible: false,
                    },
                ],
                columns: [
                    {
                        data: 'send_message.id',
                    },
                    {
                        data: 'send_message.title',
                        render: function (data, type, row) {
                            return (
                                '<a data-match-route="/message_details/' +
                                row.send_message.id +
                                '?title=' +
                                row.send_message.title +
                                '&eventId=' +
                                event_id +
                                '" href="#/message_details/' +
                                row.send_message.id +
                                '?title=' +
                                row.send_message.title +
                                '&eventId=' +
                                event_id +
                                '">' +
                                row.send_message.title +
                                '</a>'
                            );
                        },
                    },
                    {
                        data: 'send_message.content',
                    },
                    {
                        data: null,
                        render: function (data, type, row) {
                            return (
                                data.parens.surname +
                                ' ' +
                                data.parens.other_name
                            );
                        },
                    },
                    {
                        data: null,
                        render: function (data, type, row) {
                            switch (+data.send_message.send_to) {
                                case SEND_TO_ALL:
                                    return 'All';

                                case SEND_TO_GROUPS:
                                    var group_names =
                                        data.send_message.group_ids.map(
                                            (id) => {
                                                return $scope.groups.find(
                                                    (group) => group.value == id
                                                ).label;
                                            }
                                        );
                                    return group_names.join('<br>');

                                case SEND_TO_CLUBS:
                                    var club_names =
                                        data.send_message.club_ids.map((id) => {
                                            return $scope.clubs.find(
                                                (club) => club.value == id
                                            ).label;
                                        });
                                    return club_names.join('<br>');

                                case SEND_TO_TEAM:
                                    var team_names =
                                        data.send_message.team_ids.map((id) => {
                                            return $scope.teams.find(
                                                (team) => team.id == id
                                            )?.name;
                                        });
                                    return team_names.join('<br>');
                                case SEND_TO_GROUPS_IN_CLUBS:
                                    var group_names =
                                        data.send_message.group_ids.map(
                                            (id) => {
                                                return $scope.groups.find(
                                                    (group) => group.value == id
                                                ).label;
                                            }
                                        );
                                    var club_names =
                                        data.send_message.club_ids.map((id) => {
                                            return $scope.clubs.find(
                                                (club) => club.value == id
                                            ).label;
                                        });
                                    return (
                                        group_names.join('<br>') +
                                        '<br><i>In:</i><br> ' +
                                        club_names.join('<br>')
                                    );
                                case SEND_TO_PARENT_OF_PLAYER: {
                                    var player_names = data.players.map(
                                        (user) => {
                                            return (
                                                user.surname +
                                                ' ' +
                                                user.other_name
                                            );
                                        }
                                    );
                                    return player_names.join('<br>');
                                }
                                // case SEND_TO_SELF_PICK_UP: {
                                //   return 'Self Pick Up Parent';
                                // }
                                case SEND_TO_COURSE: {
                                    var course_names =
                                        data.send_message.course_ids.map(
                                            (id) => {
                                                if (
                                                    $scope.courses.find(
                                                        (course) =>
                                                            course.value == id
                                                    ) == undefined
                                                )
                                                    return 'N/A';
                                                return $scope.courses.find(
                                                    (course) =>
                                                        course.value == id
                                                ).label;
                                            }
                                        );

                                    return course_names.join('<br>');
                                }
                                case SEND_TO_VENUE: {
                                    var venue_names =
                                        data.send_message.venue_ids.map(
                                            (id) => {
                                                if (
                                                    $scope.venues.find(
                                                        (venue) =>
                                                            venue.value == id
                                                    ) == undefined
                                                )
                                                    return 'N/A';
                                                return $scope.venues.find(
                                                    (venue) => venue.value == id
                                                ).label;
                                            }
                                        );

                                    return venue_names.join('<br>');
                                }
                                case SEND_TO_SHIPPING_TYPE: {
                                    var shipping_type_names =
                                        data.send_message.shipping_types.map(
                                            (id) => {
                                                if (
                                                    $scope.shipping_types.find(
                                                        (shipping_type) =>
                                                            shipping_type.value ==
                                                            id
                                                    ) == undefined
                                                )
                                                    return 'N/A';
                                                return $scope.shipping_types.find(
                                                    (shipping_type) =>
                                                        shipping_type.value ==
                                                        id
                                                ).label;
                                            }
                                        );

                                    return shipping_type_names.join('<br>');
                                }
                                case SEND_FROM_COACH_TO_PLAYER: {
                                    var player_names = data.players.map(
                                        (user) => {
                                            return (
                                                user.surname +
                                                ' ' +
                                                user.other_name
                                            );
                                        }
                                    );

                                    if (player_names.length == 0) return 'N/A';

                                    return player_names.join(',<br>');
                                }
                                default:
                                    return 'N/A';
                            }
                        },
                    },
                    {
                        data: null,
                        render: function (data, type, row) {
                            if (data.send_message.type == SEND_VIA_EMAIL)
                                return 'Email';
                            if (data.send_message.type == SEND_VIA_NOTIFICAION)
                                return 'Push';
                            if (
                                data.send_message.type ==
                                SEND_VIA_EMAIL_AND_NOTIFICAION
                            )
                                return 'Email and Push';
                            return '';
                        },
                    },
                    {
                        data: 'send_message.created_at',
                    },
                ],
                select: {
                    style: 'single',
                },
                order: [[0, 'desc']],
                lengthMenu: [
                    [10, 25, 50, 100, -1],
                    [10, 25, 50, 100, 'All'],
                ],
                buttons: [
                    {
                        extend: 'create',
                        editor: editor,
                    },
                    {
                        extend: 'collection',
                        text: 'Delete',
                        autoClose: true,
                        enabled: false,
                        buttons: [
                            {
                                text: 'Delete (Admin & Push)',
                                action: function (e, dt, node, config) {
                                    var rowData = table
                                        .rows({ selected: true })
                                        .data()[0];
                                    $scope.delete(
                                        rowData.send_message.id,
                                        true
                                    );
                                },
                            },
                            {
                                text: 'Delete (Admin)',
                                action: function (e, dt, node, config) {
                                    var rowData = table
                                        .rows({ selected: true })
                                        .data()[0];
                                    $scope.delete(rowData.send_message.id);
                                },
                            },
                        ],
                    },
                    // {
                    //   text: 'Send',
                    //   action: function (e, dt, node, config) {
                    //     $scope.send(7);
                    //   },
                    // },
                    {
                        extend: 'colvis',
                        text: 'Columns',
                    },
                ],
            });

            table
                .on('select', function (e, dt, type, indexes) {
                    console.log('select');
                    table.button(1).enable(); // enable Delete button
                    var rowData = table.rows(indexes).data().toArray();
                    // console.log('select ',rowData);
                    sender_id = rowData['0'].send_message.id;
                })
                .on('deselect', function (e, dt, type, indexes) {
                    console.log('deselect');
                    table.button(1).disable(); // disable Delete button
                    // var rowData = table.rows( indexes ).data().toArray();
                    // console.log('deselect ',rowData);
                    sender_id = 0;
                });
        };

        $scope.delete = function (id, deleted = false) {
            BootstrapDialog.show({
                title: 'Delete',
                message: 'Are you sure you want to delete?',
                type: BootstrapDialog.TYPE_DANGER,
                closable: false,
                buttons: [
                    {
                        label: 'No',
                        action: function (dialogRef) {
                            dialogRef.close();
                        },
                    },
                    {
                        label: 'Yes',
                        cssClass: 'btn-primary',
                        action: function (dialogRef) {
                            // call to server
                            $http({
                                method: 'POST',
                                url: SERVER_PATH + 'message/deleteMessage',
                                headers: {	
                                    'x-user-id': $rootScope.user_id,
                                    'x-user-email': $rootScope.user_name
                                },
                                data: 'id=' + id + '&deleted=' + deleted,
                                headers: {
                                    'Content-Type':
                                        'application/x-www-form-urlencoded',
                                },
                            }).success(function (response) {
                                console.log(response);
                                BootstrapDialog.show({
                                    title: response.status,
                                    type:
                                        response.status == 'OK'
                                            ? BootstrapDialog.TYPE_SUCCESS
                                            : BootstrapDialog.TYPE_WARNING,
                                    message: response.message,
                                    onhide: function (dialogRef) {
                                        table.ajax.reload();
                                        if (response.status == 'OK') {
                                            sender_id = 0;
                                        }
                                    },
                                });
                            });
                            dialogRef.close();
                        },
                    },
                ],
            });
        };

        $scope.send = function (id) {
            $scope.loadingElement.style.display = 'block';
            $http({
                method: 'POST',
                url: SERVER_PATH + 'message/sendMessage',
                headers: {	
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name
                },
                data: 'id=' + id,
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
            }).success(function (response) {
                console.log(response);
                $scope.loadingElement.style.display = 'none';

                BootstrapDialog.show({
                    title: response.status,
                    type:
                        response.status == 'Successful!'
                            ? BootstrapDialog.TYPE_SUCCESS
                            : BootstrapDialog.TYPE_WARNING,
                    message: response.message,
                });
            });
        };

        if ($.fn.dataTable.isDataTable(table_name)) {
            console.log('table_send_message_' + event_id + ' is DataTable 1 ');
            if ($(table_name).DataTable().destroy()) {
                console.log('Destroy');
                $scope.init(true);
            }
        } else {
            console.log('table_send_message_' + event_id + ' is NOT DataTable');
            $scope.init(false);
        }

        $('.nav-tabs a').on('shown.bs.tab', function (event) {
            var tab = $(event.target).text(); // active tab
            active_tab = tab;
        });
    }
);
