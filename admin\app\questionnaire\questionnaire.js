app.filter('filterBySeason', function () {
    return function (items, season) {
        if (season.id == 0) {
            return items;
        } else {
            var filtered = [];
            for (var i = 0; i < items.length; i++) {
                var item = items[i];
                if (item.season_id == season.id || item.season_id == 0) {
                    filtered.push(item);
                }
            }
            return filtered;
        }
    };
});
app.controller(
    'questionnaireCtrl',
    function ($scope, $rootScope, $routeParams, $http, $q) {
        let eventLists = [];
        let sessionLists = [];
        $scope.selectedSession = null;
        $scope.selectedEvent = null;

        $scope.init = function () {
            jQuery.ajax({
                type: 'POST',
                url: SERVER_PATH + 'questionnaire/getAllEvents',
                async: false,
                headers: {
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name,
                },
                data: {},
                dataType: 'json',
                complete: function (response) {
                    var jsonData = JSON.parse(response.responseText);
                    sessionLists = jsonData.sessions;
                    eventLists = jsonData.events;
                    let temp_sessions = [];

                    for (let i = 0; i < sessionLists.length; i++) {
                        temp_sessions.push({
                            id: sessionLists[i].id,
                            name: sessionLists[i].name,
                        });
                    }

                    let temp_events = [];

                    temp_events.push({
                        id: 0,
                        name: 'All',
                        season_id: 0,
                    });

                    for (let i = 0; i < eventLists.length; i++) {
                        temp_events.push({
                            id: eventLists[i].id,
                            name: eventLists[i].name,
                            season_id: eventLists[i].season_id,
                        });
                    }

                    $scope.eventLists = temp_events;
                    $scope.sessionLists = temp_sessions;
                    $scope.selectedSession = temp_sessions[0];
                    $scope.selectedEvent = temp_events[0];
                },
            });
        };

        $scope.$watch('selectedSession', function (newValue, oldValue) {
            if (newValue !== oldValue) {
                setTimeout(() => {
                    $scope.selectedEvent = $scope.eventLists[0];

                    $scope.$apply();
                }, 200);
            }
        });

        initTable();

        $scope.changeEvent = function (event) {
            initTable();
        };

        $scope.changeSession = function (session) {
            initTable();
        };

        function initTable() {
            if ($.fn.DataTable.isDataTable(`#questionnaireTable`)) {
                $(`#questionnaireTable`).DataTable().destroy();
                console.log('destroy table');
            }

            table_html =
                "<div class='main-box clearfix'><div class='main-box-body clearfix'><table id='questionnaireTable" +
                "' class='table table-striped table-bordered table-hover' cellspacing='0'width='100%'><thead><tr><th>Id</th><th>Title</th><td>Description</td><td>Created at</td><td>Created by</td><td>Season</td><td>Event</td><td>Status</td><td>Action</td></tr></thead></table></div></div>";

            $('#table-content').html(table_html);

            var questionnaireTable = null;

            setTimeout(function () {
                editor = new $.fn.dataTable.Editor({
                    ajax: {
                        type: 'POST',
                        url:
                            SERVER_PATH + 'questionnaire/getquestionnaireTable',
                        headers: {
                            'x-user-id': $rootScope.user_id,
                            'x-user-email': $rootScope.user_name,
                        },
                        data: function (d) {
                            d.event_id = $scope.selectedEvent.id;
                            d.session_id = $scope.selectedSession.id;
                        },
                        dataType: 'application/json',
                        complete: function (response) {
                            var jsonData = JSON.parse(response.responseText);
                            if (jsonData.status == 'OK') {
                                table.ajax.reload();
                            }
                        },
                        error: function (xhr, status, error) {},
                    },
                    table: '#questionnaireTable',
                    formOptions: {
                        main: {
                            onBlur: 'none',
                        },
                    },
                    i18n: {
                        create: {
                            button: 'New',
                            title: 'Create new questionnaire',
                            submit: 'Send',
                        },
                        edit: {
                            button: 'Edit',
                            title: 'Edit questionnaire',
                            submit: 'Update',
                        },
                    },
                    fields: [
                        {
                            label: 'Select season',
                            name: 'events.season_id',
                            type: 'select',
                            options: sessionLists.map((session) => {
                                return {
                                    label: session.name,
                                    value: session.id,
                                };
                            }),
                        },
                        {
                            label: 'Select event',
                            name: 'questionnaires.event_id',
                            type: 'select',
                            options: eventLists.map((event) => {
                                return {
                                    label: event.name,
                                    value: event.id,
                                };
                            }),
                        },
                        {
                            label: 'Status',
                            name: 'questionnaires.status',
                            options: [
                                { label: 'Public', value: 1 },
                                { label: 'Hidden', value: 0 },
                            ],
                            type: 'radio',
                            def: 0,
                        },
                        {
                            label: 'Title',
                            name: 'questionnaires.title',
                        },
                        {
                            label: 'Description',
                            name: 'questionnaires.description',
                            type: 'textarea',
                            attr: {
                                class: 'form-control',
                                rows: '7',
                                cols: '40',
                            },
                        },
                        {
                            label: 'user_id',
                            name: 'questionnaires.user_id',
                            type: 'hidden',
                            def: $rootScope.user_id,
                        },
                        {
                            label: 'created_at',
                            name: 'questionnaires.created_at',
                            type: 'hidden',
                            def: function () {
                                return moment().format('YYYY-MM-DD HH:mm:ss');
                            },
                        },
                        {
                            label: 'updated_at',
                            name: 'questionnaires.updated_at',
                            type: 'hidden',
                            def: function () {
                                return moment().format('YYYY-MM-DD HH:mm:ss');
                            },
                        },
                    ],
                });

                // depending on the selected session, we need to filter the events
                // that are related to the selected session
                editor.dependent('events.season_id', function (val) {
                    var fiteredEvents = eventLists.filter((event) => {
                        return event.season_id == val;
                    });

                    // update the options of the event field
                    editor.field('questionnaires.event_id').update(
                        fiteredEvents.map((event) => {
                            return {
                                label: event.name,
                                value: event.id,
                            };
                        })
                    );

                    // return the value of the event field
                    return [];
                });

                editor.on('submitSuccess', function (e, json, data, action) {
                    if (action === 'create') {
                        swal.fire({
                            title: 'Success',
                            text: 'questionnaire created successfully',
                            icon: 'success',
                            button: 'Ok',
                        });
                    } else if (action === 'edit') {
                        swal.fire({
                            title: 'Success',
                            text: 'questionnaire updated successfully',
                            icon: 'success',
                            button: 'Ok',
                        });
                    } else if (action === 'remove') {
                        swal.fire({
                            title: 'Success',
                            text: 'questionnaire removed successfully',
                            icon: 'success',
                            button: 'Ok',
                        });
                    }
                });

                //  block post field session_id when post
                editor.on('preSubmit', function (e, d, action) {
                    // delete d.questionnaires.season_id;
                    var key = Object.keys(d.data)[0];

                    delete d.data[key]['events']['season_id'];
                });

                // hide the event_id when editing
                editor.on('initEdit', function (e, node, data) {
                    editor.field('questionnaires.event_id').hide();
                    editor.field('events.season_id').hide();
                });

                // show the event_id when creating
                editor.on('initCreate', function (e, node, data) {
                    editor.field('questionnaires.event_id').show();
                    editor.field('events.season_id').show();
                });

                questionnaireTable = $('#questionnaireTable').DataTable({
                    dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
                    stateSave: false,
                    deferRender: true,
                    ajax: {
                        url:
                            SERVER_PATH + 'questionnaire/getquestionnaireTable',
                        headers: {
                            'x-user-id': $rootScope.user_id,
                            'x-user-email': $rootScope.user_name,
                        },
                        type: 'POST',
                        data: {
                            event_id: $scope.selectedEvent.id,
                            session_id: $scope.selectedSession.id,
                        },
                        dataType: 'json',
                        complete: function (response) {},
                        error: function (xhr, status, error) {},
                    },
                    language: {
                        paginate: {
                            previous: '<i class="fa fa-chevron-left"></i>',
                            next: '<i class="fa fa-chevron-right"></i>',
                        },
                    },
                    columnDefs: [
                        {
                            targets: [0],
                            visible: false,
                        },
                    ],
                    columns: [
                        {
                            data: 'questionnaires.id',
                        },
                        {
                            data: 'questionnaires.title',
                        },
                        {
                            data: 'questionnaires.description',
                        },
                        {
                            data: 'questionnaires.created_at',
                        },
                        {
                            data: 'parens.other_name',
                            render: function (data, type, row) {
                                return (
                                    row.parens.surname +
                                    ' ' +
                                    row.parens.other_name
                                );
                            },
                        },
                        {
                            data: 'events.season_name',
                        },
                        {
                            data: 'events.name',
                        },
                        {
                            data: 'questionnaires.status',
                            render: function (data, type, row) {
                                return data == 1 ? 'Public' : 'Hidden';
                            },
                        },
                        {
                            data: null,
                            render: function (data, type, row) {
                                return "<button class='btn btn-primary btn-sm Qbutton'>Questions List</button> <button style='margin-top:10px' class='btn btn-danger btn-sm Abutton'>Answers List</button>";
                            },
                        },
                    ],
                    select: {
                        style: 'single',
                    },
                    order: [[0, 'desc']],
                    lengthMenu: [
                        [10, 25, 50, 100, -1],
                        [10, 25, 50, 100, 'All'],
                    ],
                    buttons: [
                        {
                            extend: 'create',
                            editor: editor,
                        },
                        {
                            extend: 'edit',
                            editor: editor,
                        },
                        {
                            extend: 'remove',
                            editor: editor,
                        },
                    ],
                });
            }, 200);

            var questionListHtml =
                '<table id="questionTable" class="table table-striped table-bordered table-hover" cellspacing="0" width="100%"> <thead> <tr> <th></th> <th>Questions</th> <th>Is Required</th> <th>Type</th><th>Reply Method</th> </tr> </thead> </table>';

            function getanswerListHtml(id) {
                return (
                    '<table id="optionsTable_' +
                    id +
                    '" class="table table-striped table-bordered table-hover" cellspacing="0" width="100%"> <thead> <tr> <th>Value</th> </tr> </thead> </table>'
                );
            }

            setTimeout(() => {
                $('#questionnaireTable ' + ' tbody').off(
                    'click',
                    'button.Qbutton'
                );

                $('#questionnaireTable tbody').on(
                    'click',
                    'button.Qbutton',
                    function () {
                        let tr = $(this).closest('tr');
                        let row = questionnaireTable.row(tr);
                        let data = row.data();

                        BootstrapDialog.show({
                            size: BootstrapDialog.SIZE_WIDE,
                            type: BootstrapDialog.TYPE_DANGER,
                            closable: true,
                            closeByBackdrop: false,
                            closeByKeyboard: true,
                            title: 'Questions List',
                            id: 'question-list',
                            message: questionListHtml,
                            onshown: function (dialog) {
                                $scope.initQuestionListTable(data);
                            },
                            onhide: function (dialog) {},
                            onhidden: function (dialogRef) {},
                        });
                    }
                );

                $scope.initQuestionListTable = function (data) {
                    if ($.fn.DataTable.isDataTable(`#questionTable`)) {
                        $(`#questionTable`).DataTable().destroy();
                        console.log('destroy table');
                    }

                    let questionEditor = new $.fn.dataTable.Editor({
                        ajax: {
                            type: 'POST',
                            url:
                                SERVER_PATH + 'questionnaire/getQuestionsTable',
                            headers: {
                                'x-user-id': $rootScope.user_id,
                                'x-user-email': $rootScope.user_name,
                            },
                            data: {
                                questionnaire_id: data.questionnaires.id,
                            },
                            dataType: 'json',
                            complete: function (response) {},
                            error: function (xhr, status, error) {},
                        },
                        table: '#questionTable',
                        formOptions: {
                            main: {
                                onBlur: 'none',
                            },
                        },
                        i18n: {
                            create: {
                                button: 'New',
                                title: 'Create new question',
                                submit: 'Create',
                            },
                            edit: {
                                button: 'Edit',
                                title: 'Edit question',
                                submit: 'Update',
                            },
                            remove: {
                                button: 'Delete',
                                title: 'Delete question',
                                submit: 'Delete',
                            },
                            error: {
                                system: 'System error, please contact administrator.',
                            },
                        },
                        fields: [
                            {
                                label: 'Question',
                                name: 'questions.question',
                            },
                            {
                                label: 'questionnaire_id',
                                name: 'questions.questionnaire_id',
                                def: data.questionnaires.id,
                                type: 'hidden',
                            },
                            {
                                label: 'Type',
                                name: 'questions.type',
                                type: 'select',
                                options: [
                                    { label: 'General', value: 'general' },
                                    { label: 'Private', value: 'private' },
                                ],
                            },
                            {
                                label: 'Required to answer',
                                name: 'questions.required',
                                type: 'select',
                                options: [
                                    { label: 'Yes', value: 1 },
                                    { label: 'No', value: 0 },
                                ],
                                def: 1,
                            },
                            {
                                label: 'Reply method',
                                name: 'questions.field_type',
                                type: 'select',
                                options: [
                                    { label: 'Text', value: 'text' },
                                    { label: 'Select', value: 'select' },
                                    { label: 'Radio', value: 'radio' },
                                    { label: 'Checkbox', value: 'checkbox' },
                                ],
                            },
                        ],
                    });

                    questionEditor.on('close', function () {
                        // alert('close');
                        $('.modal-backdrop').show();
                        $('.modal').show();
                        setTimeout(() => {
                            // show loading sweetalert2
                            if (!$('body').hasClass('modal-open')) {
                                // add class modal-open
                                $('body').addClass('modal-open');
                            }
                        }, 500);
                    });

                    let questionTable = $('#questionTable').DataTable({
                        dom: '<"row"B>rt<"row"i>',
                        stateSave: true,
                        deferRender: true,
                        ajax: {
                            url:
                                SERVER_PATH + 'questionnaire/getQuestionsTable',
                            type: 'POST',
                            headers: {
                                'x-user-id': $rootScope.user_id,
                                'x-user-email': $rootScope.user_name,
                            },
                            data: {
                                questionnaire_id: data.questionnaires.id,
                            },
                            dataType: 'json',
                            complete: function (response) {},
                            error: function (xhr, status, error) {},
                        },
                        language: {
                            info: 'Showing _TOTAL_ total questions',
                            infoEmpty: 'Showing 0 questions',
                            lengthMenu: 'Show _MENU_ questions',
                            select: {
                                rows: {
                                    _: 'You have selected %d questions',
                                    0: 'Click an question to select',
                                    1: '1 question selected',
                                },
                            },
                            paginate: {
                                previous: '<i class="fa fa-chevron-left"></i>',
                                next: '<i class="fa fa-chevron-right"></i>',
                            },
                        },
                        columns: [
                            {
                                className: '',
                                orderable: false,
                                data: null,
                                defaultContent: '',
                                render: function (data, type, row, meta) {
                                    if (data.questions.field_type == 'text') {
                                        return '';
                                    } else {
                                        return '<i class="fa fa-plus-circle dt-control"></i>';
                                    }
                                },
                            },
                            {
                                data: 'questions.question',
                            },
                            {
                                data: 'questions.required',
                                render: function (data, type, row) {
                                    return data == 1 ? 'Yes' : 'No';
                                },
                            },
                            {
                                data: 'questions.type',
                            },
                            {
                                data: 'questions.field_type',
                            },
                        ],
                        select: {
                            style: 'single',
                        },
                        order: [[1, 'asc']],
                        displayLength: -1,
                        buttons: [
                            {
                                extend: 'create',
                                editor: questionEditor,
                            },
                            {
                                extend: 'edit',
                                editor: questionEditor,
                            },
                            {
                                extend: 'remove',
                                editor: questionEditor,
                            },
                        ],
                    });

                    $('#questionTable tbody').off('click', 'td i.dt-control');

                    $('#questionTable tbody').on(
                        'click',
                        'td i.dt-control',
                        function (e) {
                            let tr = $(this).closest('tr');
                            let row = questionTable.row(tr);
                            let id = row.data().questions.id;
                            let html = getanswerListHtml(id);

                            if (row.child.isShown()) {
                                row.child.hide();
                            } else {
                                row.child(html).show();

                                $(this)
                                    .closest('tr')
                                    .next()
                                    .addClass('child-row-detail');

                                initTeamQuestionTable(id);
                            }
                        }
                    );
                };

                $('#questionnaireTable tbody').off('click', 'button.Abutton');

                $('#questionnaireTable tbody').on(
                    'click',
                    'button.Abutton',
                    function () {
                        let tr = $(this).closest('tr');
                        let row = questionnaireTable.row(tr);
                        let data = row.data();
                        // redirect to the answers page
                        window.location.href =
                            '#/questionnaire/' +
                            data.questionnaires.id +
                            '/responses';
                    }
                );
            }, 1000);
        }

        function initTeamQuestionTable(question_id) {
            if ($.fn.DataTable.isDataTable(`#optionsTable_` + question_id)) {
                $(`#optionsTable_` + question_id)
                    .DataTable()
                    .destroy();
            }

            editorTeamQuestion = new $.fn.dataTable.Editor({
                ajax: {
                    type: 'POST',
                    url: SERVER_PATH + 'questionnaire/getOptionsTable',
                    headers: {
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name,
                    },
                    data: {
                        question_id: question_id,
                    },
                    dataType: 'json',
                    complete: function (response) {
                        tableTeamQuestionTable.ajax.reload();
                    },
                    error: function (xhr, status, error) {},
                },
                table: `#optionsTable_` + question_id,
                formOptions: {
                    main: {
                        onBlur: 'none',
                    },
                },
                i18n: {
                    create: {
                        button: 'New',
                        title: 'Create new option',
                        submit: 'Create',
                    },
                    edit: {
                        button: 'Edit',
                        title: 'Edit option',
                        submit: 'Update',
                    },
                    remove: {
                        button: 'Delete',
                        title: 'Delete option',
                        submit: 'Delete',
                    },
                    error: {
                        system: 'System error, please contact administrator.',
                    },
                },
                fields: [
                    {
                        label: 'Value',
                        name: 'options.value',
                    },
                    {
                        label: 'question_id',
                        name: 'options.question_id',
                        type: 'hidden',
                        def: question_id,
                    },
                ],
            });

            editorTeamQuestion.on('close', function () {
                // alert('close');
                $('.modal-backdrop').show();
                $('.modal').show();
                setTimeout(() => {
                    // show loading sweetalert2
                    if (!$('body').hasClass('modal-open')) {
                        // add class modal-open
                        $('body').addClass('modal-open');
                    }
                }, 500);
            });

            editorTeamQuestion.on(
                'submitSuccess',
                function (e, json, data, action) {
                    if (action === 'create') {
                        swal.fire({
                            title: 'Success',
                            text: 'Option created successfully',
                            icon: 'success',
                            button: 'Ok',
                        });
                    } else if (action === 'edit') {
                        swal.fire({
                            title: 'Success',
                            text: 'Option updated successfully',
                            icon: 'success',
                            button: 'Ok',
                        });
                    } else if (action === 'remove') {
                        swal.fire({
                            title: 'Success',
                            text: 'Option removed successfully',
                            icon: 'success',
                            button: 'Ok',
                        });
                    }
                }
            );

            tableTeamQuestionTable = $(
                '#optionsTable_' + question_id
            ).DataTable({
                dom: '<"row"<"col-sm-6"B><"col-sm-6"f>>rt<"row"i>',
                stateSave: true,
                deferRender: true,
                ajax: {
                    url: SERVER_PATH + 'questionnaire/getOptionsTable',
                    type: 'POST',
                    headers: {
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name,
                    },
                    data: {
                        question_id: question_id,
                    },
                    dataType: 'json',
                    complete: function (response) {},
                    error: function (xhr, status, error) {},
                },
                language: {
                    paginate: {
                        previous: '<i class="fa fa-chevron-left"></i>',
                        next: '<i class="fa fa-chevron-right"></i>',
                    },
                },
                columns: [
                    {
                        data: 'options.value',
                    },
                ],
                select: {
                    style: SELECT_MODE,
                    // selector: 'td:first-child'
                },
                buttons: [
                    {
                        extend: 'create',
                        editor: editorTeamQuestion,
                    },
                    {
                        extend: 'edit',
                        editor: editorTeamQuestion,
                    },
                    {
                        extend: 'remove',
                        editor: editorTeamQuestion,
                    },
                ],
                order: [[0, 'asc']],
                displayLength: -1,
            });
        }
    }
);
