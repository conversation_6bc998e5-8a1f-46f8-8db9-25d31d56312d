{"APP_VER": "App ver", "CANCEL": "Cancel", "NO": "No", "OK": "OK", "PAGES": {"ABOUT": "Course Information", "CODE_OF_CONDUCT": "Competition Information", "HOME": "Home", "NOTIFICATIONS": "Notifications", "LEAGUES_MANAGER": "Leagues Manager", "LEAGUES": "Leagues", "REGISTRATION": "Registration", "USER_PROFILE": "User profile", "WEATHER_POLICY": "Weather Policy", "TEAM_SHEET": "Team sheet", "RESPECT": "Respect", "SPONSORS": "Sponsors", "HEALTH_GUIDELINE": "Health Guideline", "GOLDEN_AGE": "Golden Age", "TRAINING_SCHEME": "Training Scheme", "SUMMER_SCHEME": "Summer Scheme", "DISTRICT": "District", "REGISTER_COURSE": "Register Course", "TRAINING_VIDEO": "Training Video", "SELECT_COURSE": "Course", "CERTIFICATE": "Certificate", "GRASSROOTS_FOOTBALL": "Grassroots Football", "MESSAGE": "Message", "DOCUMENTS": "Documents", "COACH_ATTENDANCE": "Coach Attendance", "COURSES": "My Courses", "COURSE_TIMETABLE": "Course timetable and course code", "LIST_PLAYER": "List player", "SELECT_T_SHIRT_SIZE": "Select T-Shirt size", "SIZE": "Size", "CHEST_WIDTH": "Chest Width", "SHIRT_LENGTH": "Shirt Length", "SUITABLE": "Size Suggestion", "SELECT_LOCKER": "Select locker", "SELECT_TEAM": "Select team", "TEAM": "Team", "TEAMS": "Teams", "APPLICATION": "Application"}, "PROFILE_PAGE": {"ACCOUNT": "Account", "FOLLOW": "Follow", "CHANGE_PASSWORD": "Change password", "EDIT_PROFILE": "Edit profile", "LANGUAGE": "Language", "LOGOUT": "Logout", "WANT_LOGOUT": "Do you want to log out?", "SETTINGS": "Settings", "MOBILE_NO": "Parent mobile number", "SURNAME": "Surname", "OTHER_NAMES": "Other names", "EMAIL": "Email"}, "REGISTRATION_PAGE": {"NEW_PLAYER_REGISTRATION": "New player registration", "UPDATE_REGISTRATION": "Update registration", "REGISTRATION_DETAILS": "Registration details", "ENGLISH_SURNAME_(PLAYER)": "English Surname (Player)", "ENGLISH_OTHER_NAMES_(PLAYER)": "English Other names (Player)", "PLAYER_PHOTO": "Player photo", "UPLOAD_IMAGE": "Upload image", "DOB": "DOB", "GENDER": "Gender", "MALE": "Male", "FEMALE": "Female", "HKID/Passport_type": "HKID/Passport type", "HKID": "HKID", "PASSPORT": "Passport", "BALL_SIZE": "Ball size", "T_SHIRT_SIZE": "T-Shirt size", "MAINLAND_TRAVEL_PERMIT": "Mainland Travel Permit", "ID_DOCUMENT_SHOWING_PLAYER_PHOTO_AND_DOB": "ID document showing player photo and DOB", "PASSPORT_EXPIRY_DATE": "Passport expiry date", "HKID_NO": "HKID No", "PARENT_HKID_NO": "Parent HKID No", "TAKE_GOODS_TYPE": "Take Goods Type", "PICK_UP": "Pick Up", "SHIPPING": "Shipping", "SHIPPING_TO": "Shipping To", "HOME": "Home", "LOCKER": "Locker", "PHOTO_ID": "Photo ID", "MOBILE_NO": "Parent mobile number", "RES_ADDRESS": "Res Address", "HOME_ADDRESS": "Home Address", "LOCKER_BRAND": "Self Pick up type", "PROVINCE": "Province", "CITY": "City", "PLAYER_INFO": "Player info", "PLAYER_DESCRIPTION": "Same as Hong Kong Identity Card/Passport", "PLAYER_UPDATE": "Please check player information again", "CHINESE_NAME_PLAYER": "Chinese  Name (Player)", "AREA_CHI": "Region", "SUB_DISTRICTS_CHI": "District", "COURSE": "Course", "NORMAL_COURSE": "Training class", "PARENT_COURSE": "Parent class", "GOALKEEPER_COURSE": "Goalkeeper class", "THIS_IS_NORMAL_COURSE": "This is training class", "THIS_IS_PARENT_COURSE": "This is parent class", "THIS_IS_GOALKEEPER_COURSE": "This is GK class", "TRAINING_DATE": "Training Date", "GAME_DAYS_DATE": "Game Days Date", "TRAINING": "Training", "GAME_DAYS": "Game Days", "COURSE_INFO": "Course info", "DELIVERY_ADDRESS_AT_OUTLYING_ISLANDS": "Delivery Address at Outlying Islands?", "ELEVATOR_ACCESS_TO_DELIVERY_ADDRESS": "Elevator Access to Delivery Address?", "SHIPPING_TYPE": "Shipping type", "SHIPPING_PROVINCE": "Shipping Province", "SHIPPING_CITY": "Shipping City", "POSTCODE": "Postcode", "CLUB": "Club", "UPDATE": "Update", "UPDATE_PLAYER_INFO": "Update player info", "SUBMIT": "Submit", "WAITING_PAYMENT": "Please pay to complete the registration process", "SELF_PICK_UP": "Self Pick Up", "CONFIRM": "Confirm", "PLAYER_T_SHIRT_SIZE": "Player bib size", "PARENT_T_SHIRT_SIZE": "Parent bib size", "TEAM": "Team", "REGION": "Region", "SELECT_A_VENUES": "Select a Venues", "DOOR": "Door", "SELF_PICK_UP_LOCATION": "Self Pick-up Location", "PICK_UP_FROM_HKFA": "Pick up from HKFA", "DELIVERY_ADDRESS": "Delivery address", "PARENT_NAME": "Parent name", "PARENT_DOB": "Parent DOB", "CREATE_NEW_PLAYER": "Create New Player", "UPDATE_PLAYER": "Update Player", "SELECT_CLASS": "Select Class", "SELECT_DELIVERY": "Select Delivery", "CREATE": "Create", "NEXT": "Next", "REGISTER": "Register", "NOT_ACCEPTED_TERM_OF_SERVICE_MESSAGE": "Please accept the terms before continuing to register", "SUBHEADER_CONFIRM_BEFORE_PAYMENT": "Player must meet Vaccine Pass requirements to participate", "SUBHEADER_CONFIRM_BEFORE_PAYMENT_2": "Course information and receipt will be sent to registered email", "RED_NOTE_REGISTRATION": "Player must satisfy Vaccine Pass requirement in order to participate. For Vaccine Pass detail, please refer https://www.coronavirus.gov.hk/eng/vaccine-pass.html", "CLASS_CODE": "Class type", "APPLICATION_STATUS": "Application status", "PAYMENT_STATUS": "Payment status", "PLEASE_VERIFY_YOUR_PHONE_NUMBER": "Please verify your phone number", "HKID_NOTIFICATION": "Enter HKID number without bracket. E.g. A1234567", "PLEASE_SELECT_DISTRICT": "Please select a district/regions", "ADD_NEW_REGISTRATION": "Add new registration"}, "LOGIN_PAGE": {"Welcome": "Welcome", "LEAGUE_NAME": "HKFA Grassroots Football", "Email": "Email", "Password": "Password", "Login": "<PERSON><PERSON>", "ForgotPassword": "Forgot Password?", "SignUp": "Sign Up!"}, "REGISTER_PAGE": {"BackButton": "Back", "CreateParentAccount": "Create your parent account!", "Surname": "Surname (Parent/Guest)", "Othername": "Other names (Parent/Guest)", "Email": "<PERSON><PERSON> (Parent/Guest)", "REGISTER_HEADER": "Sign Up", "AlreadyAccount": "Already have an account ?", "TermOfServices": "Terms of Service <br> and Privacy Policy", "TermOfServices_register": "Terms of Service and Privacy Policy", "SignUp": "Sign Up", "AccountAgree": "By creating an account you agree to the HKFA ", "I_AGREE_TO_THE": "I agree to the", "YOU_HAVE_AN_ACCOUNT": "If you have an account, please", "LOGIN": "<PERSON><PERSON>"}, "REGISTRATION_MODAL": {"AWAITING_APPROVAL": "Your submission is awaiting approval from HKFA", "ADD_NEW_PLAYER": "Add new player", "PLEASE_SELECT_A_CLUB": "Please select a club", "REGISTER_NOW": "Register now", "CAN_T_REGISTER_NOW": "Can't register now", "THIS_PLAYER_IS_NOT_VALIDATED": "This player is not validated", "NO_CLUB": "No club", "CHANGE_SHIPPING_TYPE": "Change shipping type"}, "UPDATE_DETAIL_MATCH": {"THIS_PLAYER_WAS_SENT_OFF": "This player was sent off", "ARE_YOU_SURE": "Are you sure?", "MATCH_IS_UPDATED": "match is updated", "SCORER_SMALLER_THAN_SCORE": "number of scorer must be less than the number of goals"}, "APP_NOTIFICATION": {"SOMETHING_WENT_WRONG": "Something went wrong!", "ERROR": "Error", "SUCCESSFUL": "Successful", "NOTIFICATION": "Notification", "ALERT": "<PERSON><PERSON>"}, "PROFILE": {"CURRENT_PASSWORD": "Current password", "NEW_PASSWORD": "New password", "CONFIRM_NEW_PASSWORD": "Confirm new password"}, "FORGOT_PASSWORD": {"FORGOT_PASSWORD": "Forgot Password?", "RECOVER_YOUR_PASSWORD": "Recover your password", "RECOVER_MESSAGE": "Please enter your email address and we'll send you an email to reset your password.", "EMAIL": "Email", "RESET_PASSWORD": "Reset Password"}, "NOTIFICATION": {"ARE_YOU_SURE_YOU_WANT_TO_DELETE": "Are you sure you want to delete?"}, "VENUE": {"INSTALL_GOOGLE_MAPS": "You need install Google Maps to use this feature"}, "REGISTRATION": {"PLAYER_REGISTRATION_STATUS": "Player registration status"}, "LEAGUE": {"LEAGUE": "League", "TOURNAMENT": "Tournament", "SEASON": "Season", "GROUPS": "Group", "FIXTURES": "Fixtures", "RESULTS": "Results", "TABLES": "Tables", "LOCATION": "Location", "DATE": "Date", "START_TIME": "Start time", "END_TIME": "End time", "STAT": "Stat", "TIMELINE": "Timeline", "TEAM_STATS": "TEAM STATS", "RED_CARDS": "Red cards", "YELLOW_CARDS": "Yellow cards", "GREEN_CARD": "Green cards", "GOAL": "Goals", "DIRECTIONS": "Directions", "UPDATED_POINTS": "Updated Points", "CLUB": "Club", "TEAM": "Team", "SEARCH": "Search"}, "LEAGUES_MANAGER": {"UPDATE_SCORE": "Update Score", "SELECT_A_VENUES": "Select a Venues", "TIME": "Time", "SELECT_PLAYER": "Select Player", "NOTE": "Note", "UPDATE_MATCHES": "Update Matches", "LEAGUES": "Leagues", "TEAM": "Team", "ADD": "Add", "RESPECT_SCORE": "Respect score"}, "TEAM_SHEET": {"NOT_YET_SUBMITTED": "Not yet submitted", "SEASON": "Season", "GROUPS": "Groups", "CLUB": "Club"}, "FOLLOW": {"FAVOURITE_CLUB": "Favourite Club", "FAVOURITE_TEAM": "Favourite Team", "ALERT_IN_ADVANCE": "<PERSON><PERSON> in advance", "DAY": "days"}, "RESPECT": {"DOCUMENT": "Document", "VIDEO": "Video", "ARTICLE": "Article"}, "EXERCISE_PAGE": {"DRILL": "Drill", "TIME": "Time", "SET": "Set", "DEMONSTRATION": "Demonstration", "OBJECTIVE": "Objective", "PRECAUTION": "Precaution"}, "HOME_PAGE": {"BACK_MAIN_SCREEN": "Back to the main screen"}, "SELECT_COURSE": {"COURSE_TYPE": "Course type", "QUOTA": "<PERSON><PERSON><PERSON>", "CLASS_CODE": "Class code", "TRAINING_DATE": "Training date", "TRAINING_TIME": "Training time", "SPACE_AVAILABLE": "space available", "LOCATION": "Location", "SEND_MESSAGE_TO_PLAYER": "Send message to player", "PLEASE_ENTER_SUBJECT": "Please enter a subject", "PLEASE_ENTER_A_CONTENT": "Please enter a content", "NO_COURSE": "No available classes, please try another region or venue", "FROM": "From", "SUBJECT": "Subject", "SEND": "Send", "NEW_MESSAGE": "New Message", "ALL_PLAYERS": "All Players", "DOTW": "Day of the Week", "VENUE": "Venue", "BIRTH_YEAR": "Birth year", "AM": "AM", "PM": "PM", "DATE": "Date", "TIME": "Time"}, "PAYMENT": {"SUCCESSFUL": "Successful", "WAITING_TO_BE_PAID": "Waiting to be paid", "TEXT_NOTIFY_PAYMENT": "Please pay within {timer} system will automatically cancel application if exceed time", "PAY_NOW": "Pay now", "REMAINING_TIME": "Remaining time", "CHECK_OUT": "Check out"}, "SUMMER_SCHEME": {"CHECK_IN_SUCCESS": "Check in time updated", "CHECK_OUT_SUCCESS": "Check out time updated"}, "START_DATE": "Registration Start", "END_DATE": "Registration End", "SAVE": "Save", "SELECT": "Select", "SEARCH": "Search", "ALL": "All", "SELECT_LANGUAGE": "Select your language", "YES": "Yes", "SEND_MAIL": "Send Mail", "DESCRIPTION": "Description", "CREATE_AT": "Create at", "CLOSE": "Close", "TODAY": "Today", "UPCOMING": "Upcoming", "PAST": "Past", "CHECK_IN": "Check-in", "CHECK_OUT": "Check-out", "PAY_NOW": "Pay now", "REMAINING_TIME": "remaining time", "BACK": "Back"}