app.controller(
    'coachCertificateCtrl',
    function ($scope, $rootScope, $routeParams, $http) {
        $('#page-wrapper').removeClass('nav-small');

        var editorCoachCertificates = new $.fn.dataTable.Editor({
            ajax: {
                url: SERVER_PATH + 'setting/getSetCoachCertificate',
                type: 'POST',
                headers: {	
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name
                },
                data: {},
                dataType: 'json',
                complete: function (response) {},
                error: function (xhr, status, error) {},
            },
            table: '#tableCoachCertificate',
            formOptions: {
                main: {
                    onBlur: 'none',
                },
            },
            i18n: {
                create: {
                    button: 'New',
                    title: 'Create new coach certificate',
                    submit: 'Create',
                },
                edit: {
                    button: 'Edit',
                    title: 'Edit coach certificate',
                    submit: 'Save',
                },
                remove: {
                    button: 'Delete',
                    title: 'Delete coach certificate',
                    submit: 'Delete',
                },
                error: {
                    system: 'System error, please contact administrator.',
                },
            },
            fields: [
                {
                    label: 'order',
                    name: 'coach_certificates.order',
                    type: 'hidden',
                },
                {
                    label: 'Name:',
                    name: 'coach_certificates.name',
                },
            ],
        });

        var tblCoachCertificates = $('#tableCoachCertificate').DataTable({
            dom: '<"row"<"col-sm-6"B><"col-sm-6"f>>rt<"row"i>',
            stateSave: true,
            deferRender: true,
            ajax: {
                url: SERVER_PATH + 'setting/getSetCoachCertificate',
                type: 'POST',
                headers: {	
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name
                },
                data: {},
                dataType: 'json',
                complete: function (response) {},
                error: function (xhr, status, error) {},
            },
            language: {
                paginate: {
                    previous: '<i class="fa fa-chevron-left"></i>',
                    next: '<i class="fa fa-chevron-right"></i>',
                },
            },
            columns: [
                {
                    data: null,
                    sortable: false,
                    
                },
                {
                    data: 'coach_certificates.name',
                    sortable: false
                },
                {
                    data: 'coach_certificates.order',
                    visible: false,
                    sortable: false,
                    searchable: false
                },
            ],
            columnDefs: [
                {
                    defaultContent: '-',
                    targets: '_all',
                },
            ],
            select: {
                style: 'single',
            },
            buttons: [
                { extend: 'create', editor: editorCoachCertificates },
                { extend: 'edit', editor: editorCoachCertificates },
                { extend: 'remove', editor: editorCoachCertificates },
            ],
            rowReorder: {
                dataSrc: 'coach_certificates.order',
                editor: editorCoachCertificates,
            },
            order: [[2, 'asc']],
            displayLength: -1
        });

        editorCoachCertificates.on('postCreate postRemove', function () {
            tblCoachCertificates.ajax.reload(null, false);
        });

        tblCoachCertificates.on('order.dt search.dt', function () {
            tblCoachCertificates.column(0, {search: 'applied', order: 'applied'}).nodes().each(function (cell, i) {
              cell.innerHTML = i + 1;
            });
          }).draw()
    }
);
