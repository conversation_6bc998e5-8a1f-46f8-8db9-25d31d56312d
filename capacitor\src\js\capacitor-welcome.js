import { SplashScreen } from '@capacitor/splash-screen';

window.customElements.define(
  'capacitor-welcome',
  class extends HTMLElement {
    constructor() {
      super();

      SplashScreen.hide();

      const root = this.attachShadow({ mode: 'open' });

      root.innerHTML = `
      <style>
        :host {
          font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
          display: block;
          width: 100%;
          height: 100%;
        }

        /* General Body Styles */
        body {
            font-family: 'Open Sans', sans-serif;
            line-height: 1.4; /* Adjusted for tighter spacing */
            margin: 0;
            padding: 15px; /* Reduced padding */
            background-color: #f4f7f6;
            color: #333;
            display: flex;
            justify-content: center;
            align-items: center; /* Center vertically for no scroll */
            min-height: 100vh; /* Full viewport height */
            overflow: hidden; /* Prevent scrolling */
            box-sizing: border-box; /* Include padding in height */
        }

        /* Container for content */
        .container {
            background-color: #ffffff;
            border-radius: 12px;
            max-width: 100%; /* Wider on small screens */
            padding: 20px 25px; /* Reduced padding */
            text-align: center;
            box-sizing: border-box; /* Include padding in width */
        }

        /* Header Styles */
        header {
            margin-top: 48px; /* Space for status bar */
            margin-bottom: 20px; /* Reduced margin */
            padding-bottom: 15px; /* Reduced padding */
            border-bottom: 1px solid #eee;
        }

        .logo {
            max-width: 100px; /* Smaller logo */
            height: auto;
            margin-bottom: 10px;
        }

        h1 {
            color: #2c3e50;
            font-size: 2em; /* Adjusted font size */
            font-weight: 700;
            margin-bottom: 0; /* Remove bottom margin */
        }

        /* Main Content Styles */
        main p {
            font-size: 1em; /* Base font size */
            margin-bottom: 10px; /* Reduced margin */
            color: #555;
        }

        .greeting {
            font-style: italic;
            font-weight: 600;
            color: #444;
        }

        .highlight {
            background-color: #e0f2f7;
            border-left: 5px solid #dd191d;
            padding: 15px; /* Reduced padding */
            margin: 20px 0; /* Reduced margin */
            border-radius: 8px;
        }

        .highlight p {
            font-size: 1.05em; /* Slightly larger for emphasis */
            font-weight: 600;
            color: #dd191d;
            margin-bottom: 15px; /* Reduced margin */
        }

        /* Button Styles */
        .btn {
            display: inline-block;
            background-color: #dd191d;
            color: #ffffff;
            padding: 12px 25px; /* Reduced padding */
            text-decoration: none;
            border-radius: 50px;
            font-weight: 700;
            font-size: 1em; /* Adjusted font size */
            transition: background-color 0.3s ease, transform 0.2s ease;
            box-shadow: 0 3px 8px rgba(0, 123, 255, 0.3); /* Lighter shadow */
        }

        .btn:hover {
            background-color: #0056b3;
            transform: translateY(-1px); /* Slight lift */
        }

        /* Footer Styles */
        footer {
            margin-top: 20px; /* Reduced margin */
            padding-top: 15px; /* Reduced padding */
            border-top: 1px solid #eee;
            font-size: 0.85em; /* Smaller font for footer */
            color: #777;
        }

        footer p {
            margin-bottom: 0; /* Remove bottom margin */
        }

        /* Responsive adjustments - Ensure it fits */
        @media (max-height: 600px), (max-width: 480px) {
            /* For smaller screens and shorter screens */
            body {
                padding: 10px; /* Even less padding */
            }
            .container {
                padding: 15px 20px 15px 20px; /* Even less padding */
                width: 100%; /* Make it almost full width */
            }
            .logo {
                max-width: 80px; /* Even smaller logo */
            }
            h1 {
                font-size: 1.8em; /* Adjusted heading size */
            }
            main p, .highlight p, .btn {
                font-size: 0.95em; /* Slightly smaller text for fit */
            }
            .btn {
                padding: 10px 20px; /* Smaller button padding */
            }
            footer {
                padding-top: 10px;
                font-size: 0.8em;
            }
        }
      </style>
      <div class="container">
        <header>
              <img src="https://grassroots.hkfa.com/images/logo.png" alt="HKFA Logo" class="logo">
              <h1>Important Notice</h1>
          </header>

          <main>
              <p class="greeting">Dear Valued User,</p>
              <p>The current Grassroots Football app is no longer active. Thank you for your support!</p>

              <div class="highlight">
                  <p>Please visit our new website to continue:</p>
                  <a target="_blank" href="https://grassroots.hkfa.com/" class="btn">
                      Visit grassroots.hkfa.com
                  </a>
              </div>

              <p>For any questions, please contact us via the new website.</p>
          </main>

          <footer>
              <p>The Grassroots Football Team</p>
          </footer>
      </div>
    `;
    }
  }
);
