<div class="row">
  <div class="col-lg-12">
    <div class="clearfix">
      <h1 class="pull-left">Players</h1>
    </div>
  </div>
</div>

<div class="row">
  <div class="col-lg-12 col-md-12 col-sm-12">
    <div class="main-box clearfix">
      <div class="tabs-wrapper player-tabs">
        <ul class="nav nav-tabs">
          <li class="active">
            <a showtab="" data-target="#tab-players" data-toggle="tab">Players</a>
          </li>
          <li>
            <a showtab="" data-target="#tab-request-update" data-toggle="tab">Request Update</a>
          </li>
          <li>
            <a showtab="" data-target="#tab-merge-players" data-toggle="tab">Merge Players</a>
          </li>
        </ul>
      </div>
      <div class="tab-content">
        <div class="tab-pane fade in active" id="tab-players">
          <div class="main-box-body clearfix">
            <div class="table-responsive">
              <table id="player_table" class="table table-striped table-bordered table-hover" cellspacing="0"
                width="100%">
                <thead>
                  <tr>
                    <th>Photo</th>
                    <th>Player name</th>
                    <th>Chinese name</th>
                    <th>Player ID</th>
                    <th>ID creation date</th>
                    <th>Parent</th>
                    <th>DOB</th>
                    <!-- <th>Year</th> -->
                    <th>Gender</th>
                    <!-- <th>Club</th> -->
                    <th>Player HKID</th>
                    <th>HKID/Passport type</th>
                    <th>HKID/Passport photo</th>
                    <th>Passport expiry date</th>
                    <th>Validate status</th>
                    <th>Created by Manager</th>
                    <th>Deleted</th>
                  </tr>
                </thead>
              </table>
            </div>
          </div>
        </div>
        <div class="tab-pane fade" id="tab-request-update">
          <div class="main-box-body clearfix">
            <form role="form">
              <div class="row">
                <!-- Location -->
                <div class="form-group col-lg-4" id="status-column-content"
                  style="display: flex; gap: 12px; align-content: center; align-items: baseline;"></div>
              </div>
            </form>
            <div class="table-responsive">
              <table id="requestTable" class="table table-striped table-bordered table-hover" cellspacing="0"
                width="100%">
                <thead>
                  <th>ID</th>
                  <th>Player name</th>
                  <th>Requested by</th>
                  <th>Parent email</th>
                  <th>Description</th>
                  <th>Status</th>
                  <th>Created at</th>
                  <th>Updated at</th>
                </thead>
              </table>
            </div>
          </div>
        </div>
        <div class="tab-pane fade in active" id="tab-merge-players">


          <div class="main-box-body clearfix">
            <div class="table-responsive d-flex flex-column align-items-center" style="gap:1em        ">
              <div class="m-3">
                <label for="groupByField">Select Fields:</label>
                <select id="groupByField" class="form-control" name="groupByField[]" multiple>
                    <option value="surname" selected>Surname</option>
                    <option value="other_name" selected>Other Name</option>
                    <option value="gender">Gender</option>
                    <option value="dob">DOB</option>
                    <option value="player_photo">Player Photo</option>
                    <option value="chinese_name">Chinese Name</option>
                    <option value="validate_status">Validate Status</option>

                </select>
                
            </div>


              <table id="merge_players_table" cellspacing="0" width="100%">
              </table>

            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  $(document).ready(function() {
      $('#groupByField').select2({
          placeholder: "Select Fields",
          allowClear: true,
          closeOnSelect: false
      });
  });
</script>
