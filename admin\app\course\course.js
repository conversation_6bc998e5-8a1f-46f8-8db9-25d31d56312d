app.controller(
    'courseCtrl',
    function ($scope, $rootScope, $routeParams, $http) {
        $('#page-wrapper').removeClass('nav-small');

        // get info event
        var event_id = $routeParams.id;
        jQuery.ajax({
            type: 'POST',
            url: SERVER_PATH + 'event/getEventInfo',
            headers: {	
                'x-user-id': $rootScope.user_id,
                'x-user-email': $rootScope.user_name
            },
            async: false,
            data: {
                event_id: event_id,
            },
            dataType: 'json',
            complete: function (response) {
                var jsonData = JSON.parse(response.responseText);
                var event = jsonData.info;
                $scope.selectedEvent = jsonData.info;
                $scope.selectedEvent.id = event_id;
                $scope.selectedEventType =
                    $scope.selectedEvent.type == 'District'
                        ? 'District'
                        : 'Summer Scheme';
                event_name = event.name;
                event_type = event.type;
            },
        });
        console.log(
            'paymentsCtrl - event_id, name, type  = ' +
                event_id +
                ', ' +
                event_name +
                ', ' +
                event_type
        );
        $scope.event_name = event_name;
        $scope.event_type = event_type;

        var group_arr = [];

        var tableCourse;
        var editorCourse;

        function getDateOfWeek() {
            return [
                { label: 'Monday', value: 'Mon' },
                { label: 'Tuesday', value: 'Tue' },
                { label: 'Wednesday', value: 'Wed' },
                { label: 'Thursday', value: 'Thu' },
                { label: 'Friday', value: 'Fri' },
                { label: 'Saturday', value: 'Sat' },
                { label: 'Sunday', value: 'Sun' },
            ];
        }
        $scope.goBack = function () {
            window.history.back();
        };
        
        $scope.initCourse = function (flag) {
            editorCourse = new $.fn.dataTable.Editor({
                ajax: {
                    type: 'POST',
                    url: SERVER_PATH + 'course/setCourse',
                    headers: {	
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name
                    },
                    data: {
                        event_id: $scope.selectedEvent.id,
                        user_id: $rootScope.user_id,
                    },
                    async: false,
                    dataType: 'json',
                    complete: function (response) {
                        var jsonData = JSON.parse(response.responseText);
                        if (typeof jsonData.fieldErrors == 'undefined') {
                            tableCourse.ajax.reload();
                        }
                    },
                    error: function (xhr, status, error) {},
                },
                table: '#courseTable',
                formOptions: {
                    main: {
                        onBlur: 'none',
                    },
                },
                i18n: {
                    create: {
                        button: 'New',
                        title:
                            $scope.selectedEventType == 'District'
                                ? 'Create new district'
                                : 'Create new course',
                        submit: 'Create',
                    },
                    edit: {
                        button: 'Edit',
                        title:
                            $scope.selectedEventType == 'District'
                                ? 'Edit district'
                                : 'Edit course',
                        submit: 'Update',
                    },
                    remove: {
                        button: 'Delete',
                        title:
                            $scope.selectedEventType == 'District'
                                ? 'Delete district'
                                : 'Delete course',
                        submit: 'Delete',
                        confirm: {
                            _: 'Are you sure you want to delete these?',
                            1: 'Are you sure you want to delete this?',
                        },
                    },

                    error: {
                        system: 'System error, please contact administrator.',
                    },
                },
                fields: [
                    // id hidden
                    {
                        label: 'ID',
                        name: 'courses.id',
                        type: 'hidden',
                    },
                    {
                        label:
                            $scope.selectedEventType != 'District'
                                ? 'Class code'
                                : 'Team code',
                        name: 'courses.class_code',
                    },
                    {
                        label: 'Venue',
                        name: 'courses.venue_id',
                        type: 'select2',
                    },
                    {
                        label: 'Select a Group:',
                        name: 'courses.group_id',
                        type: 'select2',
                        options: group_arr,
                        opts: {
                            placeholder: 'Select a Group',
                            allowClear: true,
                        },
                    },
                    {
                        label: 'Type:',
                        name: 'courses.train_type',
                        type: 'radio',
                        options: [
                            { label: 'Normal', value: 'normal' },
                            { label: 'Parent', value: 'parent' },
                            { label: 'Mother', value: 'mother' },
                            { label: 'Goalkeeper', value: 'goalkeeper' },
                            { label: 'Sibling', value: 'sibling' },
                            { label: 'SF', value: 'sf' },
                        ],
                        default: 'normal',
                    },
                    {
                        label: 'DOTW',
                        name: 'courses.dotw',
                        type: 'select2',
                        options: getDateOfWeek(),
                        opts: {
                            multiple: 'multiple',
                            placeholder: 'Select a date',
                            allowClear: true,
                        },
                    },
                    {
                        label: 'Training Max Player',
                        name: 'courses.train_max_player',
                        attr: {
                            type: 'number',
                        },
                        default: 0,
                    },
                    {
                        label: 'Is Active?',
                        name: 'courses.status',
                        type: 'radio',
                        options: [
                            { label: 'Active', value: 1 },
                            { label: 'Inactive', value: 0 },
                            { label: 'Closed', value: 2 }
                        ],
                        default: 1,
                    },
                    {
                        label: 'is_Course',
                        name: 'courses.isCourse',
                        type: 'hidden',
                        attr: {
                            type: 'number',
                        },
                        default: 1,
                    },
                    {
                        label: 'Training Date',
                        name: 'title_training_dates',
                        type: 'title',
                    },
                    {
                        label: 'Training dates',
                        name: 'courses.training_dates',
                    },
                    {
                        label: 'Training start time',
                        name: 'courses.training_start_time',
                        type: 'datetime',
                        format: 'HH:mm',
                        def: '00:00',
                    },
                    {
                        label: 'Training end time',
                        name: 'courses.training_end_time',
                        type: 'datetime',
                        format: 'HH:mm',
                        def: '00:00',
                    },
                    {
                        label: 'Game Days Date',
                        name: 'title_game_days_dates',
                        type: 'title',
                    },
                    {
                        label: 'Game Days dates',
                        name: 'courses.game_days_dates',
                    },
                    {
                        label: 'Game Days start time',
                        name: 'courses.game_days_start_time',
                        type: 'datetime',
                        format: 'HH:mm',
                        def: '00:00',
                    },
                    {
                        label: 'Game Days end time',
                        name: 'courses.game_days_end_time',
                        type: 'datetime',
                        format: 'HH:mm',
                        def: '00:00',
                    },
                ],
            });

            editorCourse.on('initCreate', function (e, json, data, action) {
                let isCourse = $scope.selectedEventType == 'District' ? 0 : 1;
                editorCourse.set('courses.isCourse', isCourse);
                editorCourse.clear('courses.dotw');
                editorCourse;
                editorCourse.add(
                    {
                        label: 'DOTW',
                        name: 'courses.dotw',
                        type: 'select2',
                        options: getDateOfWeek(),
                        // default is Monday
                        opts: {
                            multiple: 'multiple',
                            placeholder: 'Select a date',
                            allowClear: true,
                        },
                    },
                    'courses.train_type'
                );

                // disable field courses.train_type
                editorCourse.enable('courses.train_type');
            });
            editorCourse.on('initEdit', function (e, json, data, action) {
                let isCourse = $scope.selectedEventType == 'District' ? 0 : 1;
                editorCourse.set('courses.isCourse', isCourse);
                let dotwArr = null;
                let dotw = data.courses.dotw;
                if (dotw != null && dotw != '') {
                    dotwArr = dotw.split(', ');
                }
                editorCourse.set('courses.dotw', dotwArr);

                // disable field courses.train_type
                editorCourse.disable('courses.train_type');

                editorCourse.hide('title_game_days_dates');
                editorCourse.hide('courses.game_days_dates');
                editorCourse.hide('courses.game_days_start_time');
                editorCourse.hide('courses.game_days_end_time');
            });

            editorCourse.on('open', function () {
                if ($scope.selectedEvent.type == 'Training Scheme') {
                    editorCourse.field('courses.train_type').update([
                        { label: 'normal', value: 'normal' },
                        { label: 'parent', value: 'parent' },
                    ]);
                }
                if ($scope.selectedEvent.type == EVENT_PL_JUNIOR) {
                    editorCourse.field('courses.train_type').update([
                        { label: 'FTC Class', value: 'ftc' },
                        { label: 'Regular Class', value: 'regular' },
                    ]);
                }

                if ($scope.selectedEvent.type == EVENT_ELDERLY) {
                    editorCourse
                        .field('courses.train_type')
                        .update([{ label: 'Regular Class', value: 'regular' }]);
                }

                // get current action
                let action = editorCourse.s.action;

                if (
                    ($scope.selectedEvent.type == EVENT_PL_JUNIOR ||
                        $scope.selectedEvent.type == EVENT_ELDERLY) &&
                    (action === 'create' || action === 'edit')
                ) {
                    // hide field game days
                    editorCourse.hide('title_game_days_dates');
                    editorCourse.hide('courses.game_days_dates');
                    editorCourse.hide('courses.game_days_start_time');
                    editorCourse.hide('courses.game_days_end_time');
                } else {
                    if (action != 'edit') {
                        // show field game days
                        editorCourse.show('title_game_days_dates');
                        editorCourse.show('courses.game_days_dates');
                        editorCourse.show('courses.game_days_start_time');
                        editorCourse.show('courses.game_days_end_time');
                    }
                }
            });

            function cbDropdown(column) {
                return $('<ul>', {
                    class: 'cb-dropdown',
                }).appendTo(
                    $('<div>', {
                        class: 'cb-dropdown-wrap',
                    }).appendTo(column)
                );
            }

            tableCourse = $('#courseTable').DataTable({
                dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
                stateSave: false,
                deferRender: true,
                bDestroy: true,
                ajax: {
                    url: SERVER_PATH + 'course/getCourse',
                    headers: {	
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name
                    },
                    type: 'POST',
                    data: {
                        event_id: $scope.selectedEvent.id,
                        user_id: $rootScope.user_id,
                    },
                    dataType: 'json',
                    beforeSend: function (xhr) {
                        Swal.fire({
                            title: 'Please Wait!',
                            allowOutsideClick: false,
                            didOpen: () => {
                                Swal.showLoading();
                            },
                        });
                    },
                    complete: function (response) {
                        Swal.close();
                        if (flag) $scope.initCourse(false);
                    },
                    error: function (xhr, status, error) {},
                },
                language: {
                    paginate: {
                        previous: '<i class="fa fa-chevron-left"></i>',
                        next: '<i class="fa fa-chevron-right"></i>',
                    },
                },
                columns: [
                    {
                        data: 'courses.class_code',
                        render: function (data, type, row) {
                            return (
                                '<a data-match-route="/event/' +
                                $scope.selectedEvent.id +
                                '/course/' +
                                row.courses.id +
                                '/date?course_type=' +
                                row.courses.train_type +
                                '&class_code=' +
                                row.courses.class_code +
                                '" href="#/event/' +
                                $scope.selectedEvent.id +
                                '/course/' +
                                row.courses.id +
                                '/date?course_type=' +
                                row.courses.train_type +
                                '&class_code=' +
                                row.courses.class_code +
                                '">' +
                                data +
                                '</a>'
                            );
                        },
                    },
                    {
                        data: 'venues.region',
                    },
                    {
                        data: 'venues.name',
                    },
                    { data: 'courses.dotw' },
                    {
                        data: 'courses.train_type',
                        render: function (data) {
                            // text to uppercase
                            return data.toUpperCase();
                        }
                    },
                    {
                        data: null,
                        visible: false,
                        render: function (data) {
                            return data.courses.training_time;
                        },
                    },
                    {
                        data: null,
                        visible: false,
                        render: function (data) {
                            return data.courses.game_time;
                        },
                    },
                    {
                        data: null,
                        class: 'text-center',
                        render: function (data) {
                            return data.courses.number_player_registered;
                        },
                    },
                    {
                        data: null,
                        class: 'text-center',
                        render: function (data) {
                            return data.courses.train_max_player;
                        },
                    },
                    {
                        data: null,
                        class: 'text-center',
                        render: function (data) {
                            if (data.courses.train_max_player == 0) {
                                return '0%';
                            } else {
                                return (
                                    parseInt(
                                        (data.courses.number_player_registered /
                                            data.courses.train_max_player) *
                                            100
                                    ) + '%'
                                );
                            }
                        },
                    },
                    {
                        data: 'courses.count_course_in_cart',
                    },
                    { data: 'groups.name' },
                    {
                        data: 'courses.status',
                        class: 'text-center',
                        render: function (data) {
                            if (data == 1) {
                                return '<span class="label label-success">Active</span>';
                            } else if (data == 2) {
                                return '<span class="label label-warning">Closed</span>';
                            } else {
                                return '<span class="label label-danger">Inactive</span>';
                            }
                        },
                    },
                    {
                        data: 'role',
                        class: 'text-center',
                        render: function (data) {
                            if (
                                parseInt(data) == ROLE_HEAD_COACH ||
                                parseInt(data) == USER_SUPER_ADMIN ||
                                parseInt(data) == USER_LEAGUE_ADMIN
                            ) {
                                actions =
                                    '<button type="button" data-toggle="modal" data-target="#modal-coaches" class="btn btn-primary modal-coaches" >Manage Coaches</button>';
                                return actions;
                            }
                            return '';
                        },
                    },
                ],
                initComplete: function () {
                    // hide column 8, 9, 10 if user is not league admin
                    if (
                        $rootScope.user_role != USER_SUPER_ADMIN &&
                        $rootScope.user_role != USER_LEAGUE_ADMIN
                    ) {
                        tableCourse.column(8).visible(false);
                        tableCourse.column(9).visible(false);
                        tableCourse.column(10).visible(false);
                    }

                    if ($scope.selectedEvent.type == EVENT_PL_JUNIOR) {
                        tableCourse.column(6).visible(false);
                        tableCourse.column(10).visible(false);
                    }

                    var api = this.api();
                    // Add filter by course type
                    $('.form-group').html('');
                    this.api()
                        .columns('4')
                        .every(function () {
                            var column = this;
                            //added class "mymsel"
                            var ddmenu = cbDropdown($(column.header())).on(
                                'change',
                                ':checkbox',
                                function () {
                                    var vals = $(':checked', ddmenu)
                                        .map(function (index, element) {
                                            return $.fn.dataTable.util.escapeRegex(
                                                $(element).val()
                                            );
                                        })
                                        .toArray()
                                        .join('|');

                                    column
                                        .search(
                                            vals.length > 0
                                                ? '^(' + vals + ')$'
                                                : '',
                                            true,
                                            false
                                        )
                                        .draw();
                                }
                            );

                            column
                                .data()
                                .unique()
                                .sort()
                                .each(function (d, j) {
                                    var // wrapped
                                        $label = $('<label>'),
                                        $text = $('<span>', {
                                            style: 'margin: 0px 15px 10px 0px',
                                            text: d,
                                        }),
                                        $cb = $('<input>', {
                                            type: 'checkbox',
                                            style: 'height: 17px; width: 17px; vertical-align: bottom; position: relative; top: -1px; *overflow: hidden; margin-right: 2px;',
                                            value: d,
                                        });

                                    $cb.appendTo($label);
                                    $text.appendTo($label);

                                    ddmenu.append($label);
                                    ddmenu
                                        .add()
                                        .css('margin', '0px 0px -20px -35px')
                                        .appendTo('.form-group');
                                });
                        });

                    $('.cb-dropdown-wrap').each(function () {
                        console.log($(this).parent().width());
                        $(this).width($(this).parent().width());
                    });

                    // Add total participants to footer of column
                    this.api()
                        .columns(7)
                        .every(function () {
                            var column = this;
                            // caculate total participants
                            var total_participant = this.data().reduce(
                                function (a, b) {
                                    return (
                                        parseInt(a) +
                                        parseInt(
                                            b.courses.number_player_registered
                                        )
                                    );
                                },
                                0
                            );
                            // caculate total max players
                            var total_max = this.data().reduce(function (a, b) {
                                return (
                                    parseInt(a) +
                                    parseInt(b.courses.train_max_player)
                                );
                            }, 0);
                            // Update footer
                            $(column.footer()).html(
                                total_participant + '/' + total_max
                            );
                            if (total_max == 0) {
                                $(api.column(9).footer()).html('0%');
                            } else {
                                $(api.column(9).footer()).html(
                                    parseInt(
                                        (total_participant / total_max) * 100
                                    ) + '%'
                                );
                            }
                        });
                },
                select: {
                    // style: 'single',
                    selector: 'td:not(:last-child)',
                },
                order: [[0, 'asc']],
                buttons: [
                    {
                        extend: 'create',
                        className: 'btn-create-course',
                        editor: editorCourse,
                    },
                    {
                        extend: 'edit',
                        className: 'btn-edit-course',
                        editor: editorCourse,
                    },
                    {
                        extend: 'remove',
                        className: 'btn-remove-course',
                        editor: editorCourse,
                    },
                    {
                        extend: 'excel',
                        name: 'excel',
                        text: '<i class="fa fa-file-excel-o"></i>&emsp;Export to Excel',
                        titleAttr: 'Export data to an Excel file',
                        filename: 'HKFA - Summer Scheme - Courses',
                        title: 'HKFA - Summer Scheme - Courses',
                        exportOptions: {
                            columns: ':visible',
                        },
                    },
                    {
                        extend: 'selected',
                        name: 'generate_attendance_sheet',
                        text: 'Generate attendance sheet',
                        className: 'btn-generate-attendance-sheet',
                        titleAttr:
                            'Generate and download player attendance sheet',
                        action: function () {
                            let coursesSelected = tableCourse
                                .rows({ selected: true })
                                .data()
                                .toArray();

                            let course_ids = [];
                            coursesSelected.forEach((course) => {
                                course_ids.push(course.courses.id);
                            });

                            if (
                                course_ids !== undefined &&
                                course_ids.length > 0
                            ) {
                                if (course_ids.length > 10) {
                                    Swal.fire({
                                        title: 'Warning',
                                        text: 'You can only generate attendance sheet for 10 courses at once. Please select less than 10 courses.',
                                        icon: 'warning',
                                        type: 'warning',
                                        confirmButtonText: 'OK',
                                        confirmButtonColor: '#ed1c24',
                                    });
                                } else {
                                    let server_action =
                                        'course/autoCreateAttendanceSheets';
                                    if (
                                        $scope.selectedEvent.type == 'PL Junior'
                                    ) {
                                        server_action =
                                            'course/autoCreatePLJAttendanceSheets';
                                    }
                                    let editor = new $.fn.dataTable.Editor({
                                        ajax: {
                                            type: 'POST',
                                            url: SERVER_PATH + server_action,
                                            headers: {	
                                                'x-user-id': $rootScope.user_id,
                                                'x-user-email': $rootScope.user_name
                                            },
                                            data: {
                                                type: 'course_ids',
                                                course_ids:
                                                    course_ids.join(','),
                                            },
                                            dataType: 'json',
                                            complete: function (response) {
                                                console.log(response);
                                                var jsonData = JSON.parse(
                                                    response.responseText
                                                );

                                                if (
                                                    jsonData.status == 'ERROR'
                                                ) {
                                                    Swal.fire({
                                                        title: 'Error',
                                                        html: jsonData.message,
                                                        icon: 'error',
                                                        type: 'error',
                                                        confirmButtonText: 'OK',
                                                        confirmButtonColor:
                                                            '#ed1c24',
                                                    });
                                                } else {
                                                    if (
                                                        jsonData.status == 'OK'
                                                    ) {
                                                        fileNames =
                                                            jsonData.info;
                                                        fileNames.forEach(
                                                            (fileName) => {
                                                                downloadFile(
                                                                    PRODUCT_IMAGE_PATH +
                                                                        fileName
                                                                            .split(
                                                                                ' '
                                                                            )
                                                                            .join(
                                                                                '%20'
                                                                            ),
                                                                    fileName
                                                                );
                                                            }
                                                        );
                                                        Swal.fire({
                                                            title: 'Success',
                                                            html: jsonData.message,
                                                            icon: 'success',
                                                            type: 'success',
                                                            confirmButtonText:
                                                                'OK',
                                                            confirmButtonColor:
                                                                '#ed1c24',
                                                        });
                                                    }
                                                }
                                            },
                                        },
                                    });
                                    let swal = null;
                                    editor.on(
                                        'preSubmit',
                                        function (e, data, action) {
                                            swal = Swal.fire({
                                                title: 'Processing!',
                                                html: 'Please wait...',
                                                allowEscapeKey: false,
                                                allowOutsideClick: false,
                                                confirmButtonColor: '#ed1c24',
                                                onBeforeOpen: () => {
                                                    Swal.showLoading();
                                                },
                                            });
                                        }
                                    );
                                    editor.on(
                                        'submitComplete',
                                        function (e, data, action) {
                                            swal.close();
                                        }
                                    );
                                    editor.create(false).submit();
                                }
                            }
                        },
                    },
                    {
                        extend: 'colvis',
                        text: 'Columns',
                        columns: [0, 1, 2, 3, 4, 5, 7, 8, 9, 11, 12, 13],
                    },
                ],
                lengthMenu: [
                    [10, 25, 50, 100, -1],
                    [10, 25, 50, 100, 'All'],
                ],
                columnDefs: [
                    {
                        type: 'justNum',
                        targets: 2,
                    },
                ],
            });

            tableCourse.on('init', function () {
                // hide buttons
                getRoleByUserID($scope.user_id).then(function (data) {
                    if (
                        data.role != USER_SUPER_ADMIN &&
                        data.role != USER_LEAGUE_ADMIN
                    ) {
                        tableCourse.buttons('.btn-create-course').remove();
                        tableCourse.buttons('.btn-edit-course').remove();
                        tableCourse.buttons('.btn-remove-course').remove();
                    }
                });
                // if ($scope.selectedEvent.type == 'PL Junior') {
                //     tableCourse.buttons('.btn-generate-attendance-sheet').remove();
                // }
            });

            function getRoleByUserID() {
                return new Promise((resolve, reject) => {
                    $.ajax({
                        type: 'POST',
                        url: SERVER_PATH + 'user/getInfoUser',
                        headers: {	
                            'x-user-id': $rootScope.user_id,
                            'x-user-email': $rootScope.user_name
                        },
                        data: {
                            user_id: $rootScope.user_id,
                        },
                        async: false,
                        dataType: 'json',
                        complete: function (response) {
                            let data = response.responseJSON;
                            if (data.status == 'OK') {
                                resolve(data.info);
                            }
                        },
                        error: function (xhr, status, error) {
                            console.log(error);
                        },
                    });
                });
            }

            function showUploadModal() {
                var uploadEditor = new $.fn.dataTable.Editor({
                    fields: [
                        {
                            label: 'CSV file:',
                            name: 'csv',
                            type: 'upload',
                            fieldInfo:
                                'You can download the sample file <a href="' +
                                SYSTEM_IMAGE_PATH +
                                'Template_Import_Sessions.xlsx" target="_blank">here</a>',
                            ajax: function (files, done) {},
                        },
                    ],
                });

                uploadEditor.create({
                    title: 'Import file courses',
                });
            }

            $('#courseTable').on('click', 'tbody .modal-coaches', function () {
                let row = $(this).closest('tr');
                let data = tableCourse.row(row).data();

                var msg = getModalCoachesTableHtml(data.courses.id);

                BootstrapDialog.show({
                    title: 'Manage Coaches - ' + data.courses.class_code,
                    message: msg,
                    size: BootstrapDialog.SIZE_WIDE,
                    onshown: function (dialogRef) {
                        initCoachTbl(data);
                    },
                });
            });

            function getModalCoachesTableHtml(course_id) {
                var str =
                    '' +
                    '<div class="main-box-body clearfix">' +
                    '<div class="table-responsive">' +
                    // '<a data-match-route="/tables/coach" href="#/tables/coach" class="active">Add new Coach</a>'+
                    '<table id="tableCoaches_' +
                    course_id +
                    '" class="table table-striped table-bordered table-hover" cellspacing="0" width="100%">' +
                    '<thead>' +
                    '<tr>' +
                    '<th>Coach Name</th>' +
                    '<th>Chinese Name</th>' +
                    '<th>Email</th>' +
                    '<th>Phone</th>' +
                    '<th>Role</th>' +
                    '<th>Level</th>' +
                    '</tr>' +
                    '</thead>' +
                    '</table>' +
                    '</div>' +
                    '</div>' +
                    '';
                return str;
            }

            function initCoachTbl(d) {
                editorCoaches = new $.fn.dataTable.Editor({
                    ajax: {
                        type: 'POST',
                        url: SERVER_PATH + 'course/setAllPlayerInCourse',
                        headers: {	
                            'x-user-id': $rootScope.user_id,
                            'x-user-email': $rootScope.user_name
                        },
                        data: {
                            course_id: d.courses.id,
                        },
                        async: false,
                        dataType: 'json',
                        complete: function (response) {
                            var jsonData = JSON.parse(response.responseText);
                            if (typeof jsonData.fieldErrors == 'undefined') {
                                tableCoaches.ajax.reload();
                            }
                        },
                        error: function (xhr, status, error) {},
                    },
                    table: '#tableCoaches_' + d.courses.id,
                    formOptions: {
                        main: {
                            onBlur: 'none',
                        },
                    },
                    i18n: {
                        create: {
                            button: 'New',
                            title: 'Create new coach',
                            submit: 'Create',
                        },
                        edit: {
                            button: 'Edit Role',
                            title: 'Edit Role of Coach',
                            submit: 'Update',
                        },
                        remove: {
                            button: 'Delete',
                            title: 'Delete event',
                            submit: 'Delete',
                            confirm: {
                                _: 'Are you sure you want to delete these coach?',
                                1: 'Are you sure you want to delete this coach?',
                            },
                        },

                        error: {
                            system: 'System error, please contact administrator.',
                        },
                    },
                    fields: [
                        {
                            type: 'hidden',
                            name: 'course_coaches.course_id',
                            default: d.courses.id,
                        },
                        {
                            label: 'Select Coach',
                            name: 'course_coaches.coach_id',
                            type: 'select2',
                            opts: {
                                multiple: false,
                                placeholder: 'Search and select coach...',
                            },
                        },
                        {
                            label: 'Role',
                            name: 'course_coaches.team_role',
                            type: 'radio',
                            options: [
                                {
                                    label: ROLE_HEAD_COACH,
                                    value: ROLE_HEAD_COACH,
                                },
                                {
                                    label: ROLE_ASSITANT_COACH,
                                    value: ROLE_ASSITANT_COACH,
                                },
                                {
                                    label: ROLE_GOALKEEPER_COACH,
                                    value: ROLE_GOALKEEPER_COACH,
                                },
                            ],
                            default: ROLE_HEAD_COACH,
                        },
                    ],
                });

                editorCoaches.on('initEdit', function (e, json, data) {
                    editorCoaches.hide();
                    editorCoaches.field('course_coaches.team_role').show();
                });
                editorCoaches.on('initCreate', function (e, json, data) {
                    editorCoaches.show();
                });

                tableCoaches = $('#tableCoaches_' + d.courses.id).DataTable({
                    dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
                    stateSave: false,
                    deferRender: true,
                    ajax: {
                        url: SERVER_PATH + 'course/getAllCoachInCourse',
                        headers: {	
                            'x-user-id': $rootScope.user_id,
                            'x-user-email': $rootScope.user_name
                        },
                        type: 'POST',
                        data: {
                            course_id: d.courses.id,
                        },
                        dataType: 'json',
                        complete: function (response) {},
                        error: function (xhr, status, error) {},
                    },
                    language: {
                        paginate: {
                            previous: '<i class="fa fa-chevron-left"></i>',
                            next: '<i class="fa fa-chevron-right"></i>',
                        },
                    },

                    columns: [
                        {
                            data: null,
                            render: function (data) {
                                return (
                                    data.parens.surname +
                                    ' ' +
                                    data.parens.other_name
                                );
                            },
                        },
                        { data: 'parens.chinese_name' },
                        { data: 'parens.email' },
                        { data: 'parens.phone' },
                        { data: 'course_coaches.team_role' },
                        {
                            data: 'coach_levels.level',
                            className: 'text-center',
                        },
                    ],
                    select: {
                        style: 'single',
                        selector: 'td:not(:last-child)',
                    },
                    order: [[0, 'asc']],
                    buttons: [
                        {
                            extend: 'create',
                            editor: editorCoaches,
                        },
                        {
                            extend: 'edit',
                            editor: editorCoaches,
                        },
                        {
                            extend: 'remove',
                            editor: editorCoaches,
                        },
                        {
                            text: '<i class="fa fa-refresh" aria-hidden="true"></i>&emsp;Sync coach',
                            action: function (e, dt, node, config) {
                                syncCoach(d.courses.id);
                            },
                        },
                        {
                            extend: 'colvis',
                            text: 'Columns',
                        },
                    ],
                });
            }

            function syncCoach(course_id) {
                // sweet alert confirm
                Swal.fire({
                    title: 'Are you sure?',
                    text: 'This action will sync all coaches from course to sessions(all coaches in sessions will be deleted)',
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonText: 'Yes, sync it!',
                    cancelButtonText: 'No, cancel!',
                    reverseButtons: true,
                }).then((result) => {
                    if (result.isConfirmed) {
                        $.ajax({
                            url: SERVER_PATH + 'course/syncCoach',
                            headers: {	
                                'x-user-id': $rootScope.user_id,
                                'x-user-email': $rootScope.user_name
                            },
                            type: 'POST',
                            data: {
                                course_id: course_id,
                            },
                            dataType: 'json',
                            complete: function (response) {
                                var jsonData = JSON.parse(
                                    response.responseText
                                );
                                if (jsonData.status == 'OK') {
                                    Swal.fire({
                                        icon: 'success',
                                        title: 'Success',
                                        text: jsonData.message,
                                    });
                                    tableCoaches.ajax.reload();
                                } else {
                                    Swal.fire({
                                        icon: 'error',
                                        title: 'Oops...',
                                        text: jsonData.message,
                                    });
                                }
                            },
                            error: function (xhr, status, error) {},
                        });
                    }
                });
            }

            if ($scope.selectedEventType == 'District') {
                $(tableCourse.column(1).header()).text('District');
                $(tableCourse.column(2).header()).text('Team');
            }

            // Add event listener for opening and closing details
            $('#courseTable').on(
                'click',
                'tbody td.details-control.courses',
                function () {
                    let tr = $(this).closest('tr');
                    let row = tableCourse.row(tr);
                    console.log(row);

                    if (row.child.isShown()) {
                        // This row is already open - close it
                        row.child.hide();
                    } else {
                        // Open this row
                        row.child(formatCdates(row.data())).show();
                        $scope.initCdates(row.data());
                    }
                }
            );

            /**ON CREATE  */
            editorCourse.on('initCreate', function () {
                editorCourse.field('title_training_dates').show();
                editorCourse.field('courses.training_dates').show();
                editorCourse.field('courses.training_start_time').show();
                editorCourse.field('courses.training_end_time').show();
                editorCourse.field('title_game_days_dates').show();
                editorCourse.field('courses.game_days_dates').show();
                editorCourse.field('courses.game_days_start_time').show();
                editorCourse.field('courses.game_days_end_time').show();

                $.post(SERVER_PATH + 'event/getGroupsByEvent', {
                    event_id: event_id,
                }).then((response) => {
                    let optionsarr = [];
                    response = JSON.parse(response);
                    for (var i = 0; i < response.info.length; i++) {
                        let label = response.info[i].name;
                        let value = response.info[i].id;
                        optionsarr.push({ label: label, value: value });
                    }
                    var group_field = editorCourse.field('courses.group_id');
                    group_field._addOptions(optionsarr);
                });
            });

            /**ON EDIT */

            editorCourse.on('initEdit', function (e, json, data) {
                editorCourse.field('title_training_dates').hide();
                editorCourse.field('courses.training_dates').hide();
                editorCourse.field('courses.training_start_time').hide();
                editorCourse.field('courses.training_end_time').hide();
                editorCourse.field('title_game_days_dates').hide();
                editorCourse.field('courses.game_days_dates').hide();
                editorCourse.field('courses.game_days_start_time').hide();
                editorCourse.field('courses.game_days_end_time').hide();

                var rows_selected = tableCourse.rows({ selected: true }).data();
                var selectedCourse = rows_selected[0].courses.group_id;

                $.post(SERVER_PATH + 'event/getGroupsByEvent', {
                    event_id: event_id,
                }).then((response) => {
                    let optionsarr = [];
                    response = JSON.parse(response);
                    for (var i = 0; i < response.info.length; i++) {
                        let label = response.info[i].name;
                        let value = response.info[i].id;
                        optionsarr.push({ label: label, value: value });
                    }
                    var group_field = editorCourse.field('courses.group_id');
                    group_field._addOptions(optionsarr);
                    editorCourse
                        .field('courses.group_id')
                        ._addOptions(optionsarr);
                    editorCourse.set('courses.group_id', selectedCourse);
                });
            });

            editorCourse.on('open', function (e, mode, action) {
                // set datepicker for training dates
                $('#DTE_Field_courses-training_dates').datepicker({
                    multidate: true,
                    todayHighlight: true,
                    format: 'dd-M-yyyy',
                    multidateSeparator: ', ',
                    updateViewDate: false,
                });
                // set value for training dates
                if (action === 'edit') {
                    let selected_rows = tableCourse
                        .rows({ selected: true })
                        .data();
                    let training_dates =
                        selected_rows[0].courses.training_dates;
                    if (training_dates != null) {
                        let selected_dates = training_dates.split(', ');
                        $('#DTE_Field_courses-training_dates').datepicker(
                            'setDates',
                            selected_dates
                        );
                    }
                } else {
                    $('#DTE_Field_courses-training_dates').datepicker(
                        'setDates',
                        ''
                    );
                }

                // set datepicker for game days dates
                $('#DTE_Field_courses-game_days_dates').datepicker({
                    multidate: true,
                    todayHighlight: true,
                    format: 'dd-M-yyyy',
                    multidateSeparator: ', ',
                    updateViewDate: false,
                });
                // set value for game days dates
                if (action === 'edit') {
                    let selected_rows = tableCourse
                        .rows({ selected: true })
                        .data();
                    let game_days_dates =
                        selected_rows[0].courses.game_days_dates;
                    if (game_days_dates != null) {
                        let selected_dates = game_days_dates.split(', ');
                        $('#DTE_Field_courses-game_days_dates').datepicker(
                            'setDates',
                            selected_dates
                        );
                    }
                } else {
                    $('#DTE_Field_courses-game_days_dates').datepicker(
                        'setDates',
                        ''
                    );
                }
            });

            editorCourse.on('create', function (e, json, data) {
                $scope.generateCdateByCourse(data.courses.id);
                tableCourse.ajax.reload();
            });
        };

        $scope.generateCdateByCourse = function (course_id) {
            $.ajax({
                url: SERVER_PATH + 'course/generateCdateByCourse',
                type: 'POST',
                data: {
                    course_id: course_id,
                },
                headers: {	
						'x-user-id': $rootScope.user_id,
						'x-user-email': $rootScope.user_name
				},
                success: function(response) {
                    response = JSON.parse(response);
                },
                error: function(xhr, status, error) {
                    console.error('Error:', error);
                }
            });
        };

        $scope.checkEventType = function () {
            return $scope.selectedEventType == 'District' ? false : true;
        };

        if ($.fn.dataTable.isDataTable('#courseTable')) {
            if ($('#courseTable').DataTable().destroy()) {
                $scope.initCourse(true);
            }
        } else {
            $scope.initCourse(false);
        }

        function downloadFile(urlToSend, fileName) {
            var req = new XMLHttpRequest();
            req.open('GET', urlToSend, true);
            req.responseType = 'blob';
            req.onload = function (event) {
                var blob = req.response;
                //if you have the fileName header available
                var link = document.createElement('a');
                link.href = window.URL.createObjectURL(blob);
                link.download = fileName;
                link.click();
            };

            req.send();
        }
    }
);
