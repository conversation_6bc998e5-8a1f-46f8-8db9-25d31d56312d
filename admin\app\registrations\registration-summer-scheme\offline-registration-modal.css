.theme-red #msform a.list-group-item.active,
.theme-red #msform a.list-group-item.active:hover,
.theme-red #msform a.list-group-item.active:focus {
  background-color: #03a9f4;
  border-color: #03a9f4;
  color: white;
}
.theme-red #msform a,
.theme-red #msform .fc-state-default,
.theme-red #msform .jvectormap-zoomin,
.theme-red #msform .jvectormap-zoomout,
.theme-red #msform #user-profile .profile-details ul > li > span {
  color: #000000;
}
.theme-red #msform a:hover {
  color: #000000;
}

h2 {
  clear: both;
  font-size: 1.8em;
  margin-bottom: 10px;
  padding: 10px 0 10px 0px;
}
ul {
  padding-left: 0px;
}
.btn-close {
  /* padding: 10px; */
  background: white;
  border-radius: 50%;
  border: 1px solid #ccc;
  cursor: pointer;
}
/*form styles*/
#msform {
  text-align: center;
  position: relative;
  margin-top: 20px;
}

#msform fieldset .form-card {
  background: white;
  border: 0 none;
  border-radius: 0px;
  box-shadow: 0 2px 2px 2px rgba(0, 0, 0, 0.2);
  padding: 20px 40px 30px 40px;
  box-sizing: border-box;
  width: 94%;
  margin: 0 3% 20px 3%;

  /*stacking fieldsets above each other*/
  position: relative;
}

#msform fieldset .form-card-sm {
  background: white;
  border: 0 none;
  border-radius: 5px;
  box-shadow: 0 2px 2px 2px rgba(0, 0, 0, 0.2);

  box-sizing: border-box;
  width: 100%;
  color: #000;

  /*stacking fieldsets above each other*/
  position: relative;
}
#msform fieldset .form-card-sm .panel-heading {
  background-color: #f5f5f5;
  border-top: 1px solid #ddd;
}
#msform fieldset .form-card-sm .panel-heading .title {
  margin-left: 5px;
}
#msform fieldset {
  background: white;
  border: 0 none;
  border-radius: 15rem;
  box-sizing: border-box;
  width: 100%;
  margin: 0;
  padding-bottom: 20px;

  /*stacking fieldsets above each other*/
  position: relative;
}

/*Hide all except first fieldset*/
#msform fieldset:not(:first-of-type) {
  display: none;
}

#msform fieldset .form-card {
  text-align: left;
  color: #121212;
}

#msform .container-class {
  overflow: scroll;
  overflow-x: hidden;
  max-height: 200px;
  margin-bottom: 20px;
  margin-top: 20px;
}

#msform input[type='text'],
#msform input[type='email'],
#msform input[type='password'],
#msform input[type='date'] #msform textarea {
  padding: 0px 8px 4px 8px;
  /* border: none;
    border-bottom: 1px solid #ccc;
    border-radius: 0px;*/
  /* margin-bottom: 2px;
    margin-top: 2px; */
  width: 100%;
  /* box-sizing: border-box;
    font-family: montserrat;
    color: #2c3e50;
    font-size: 16px;
    letter-spacing: 1px;  */
}
#msform input[type='radio'] {
  padding: 0px 8px 4px 8px;
  margin-bottom: 25px;
  margin-top: 2px;
  margin-left: 5px;
}
#msform input:focus,
#msform textarea:focus {
  -moz-box-shadow: none !important;
  -webkit-box-shadow: none !important;
  box-shadow: none !important;
  border: none;
  font-weight: bold;
  border-bottom: 2px solid #03a9f4;
  outline-width: 0;
}

/*Blue Buttons*/
#msform .action-button {
  width: 100px;
  background: #03a9f4;
  font-weight: bold;
  color: white;
  border: 0 none;
  border-radius: 0px;
  cursor: pointer;
  padding: 10px 5px;
  margin: 10px 5px;
}

#msform .action-button:hover,
#msform .action-button:focus {
  box-shadow: 0 0 0 2px white, 0 0 0 3px #03a9f4;
}

/*Previous Buttons*/
#msform .action-button-previous {
  width: 100px;
  background: #616161;
  font-weight: bold;
  color: white;
  border: 0 none;
  border-radius: 0px;
  cursor: pointer;
  padding: 10px 5px;
  margin: 10px 5px;
}

#msform .action-button-previous:hover,
#msform .action-button-previous:focus {
  box-shadow: 0 0 0 2px white, 0 0 0 3px #616161;
}
#msform .b-addon.input-group-addon {
  padding: 0;
}

/*Dropdown List Exp Date*/
select.list-dt {
  border: none;
  outline: 0;
  border-bottom: 1px solid #ccc;
  padding: 2px 5px 3px 5px;
  margin: 2px;
}

select.list-dt:focus {
  border-bottom: 2px solid #03a9f4;
}

/*The background card*/
.card {
  z-index: 0;
  border: none;
  border-radius: 15rem;
  position: relative;
}

/*FieldSet headings*/
.fs-title {
  font-size: 25px;
  color: #2c3e50;
  margin-bottom: 10px;
  font-weight: bold;
  text-align: left;
}

/*progressbar*/
#progressbar {
  margin-bottom: 30px;
  overflow: hidden;
  color: lightgrey;
}

#progressbar .active {
  color: #000000;
}

#progressbar li {
  list-style-type: none;
  font-size: 12px;
  width: 20%;
  float: left;
  position: relative;
}

/*Icons in the ProgressBar*/
#progressbar #account:before {
  font-family: FontAwesome;
  content: '\f21b';
}

#progressbar #personal:before {
  font-family: FontAwesome;
  content: '\f007';
}

#progressbar #course:before {
  font-family: FontAwesome;
  content: '\f1e3';
}
#progressbar #shipping:before {
  font-family: FontAwesome;
  content: '\f279';
}

#progressbar #confirm:before {
  font-family: FontAwesome;
  content: '\f00c';
}
.errorContainer {
  margin-bottom: 20px;
  color: red;
}

/*ProgressBar before any progress*/
#progressbar li:before {
  width: 50px;
  height: 50px;
  line-height: 45px;
  display: block;
  font-size: 18px;
  color: #ffffff;
  background: lightgray;
  border-radius: 50%;
  margin: 0 auto 10px auto;
  padding: 2px;
}

/*ProgressBar connectors*/
#progressbar li:after {
  content: '';
  width: 100%;
  height: 2px;
  background: lightgray;
  position: absolute;
  left: 0;
  top: 25px;
  z-index: -1;
}

/*Color number of the step and the connector before it*/
#progressbar li.active:before,
#progressbar li.active:after {
  background: #03a9f4;
}

/*Imaged Radio Buttons*/
.radio-group {
  position: relative;
  margin-bottom: 25px;
}

.radio {
  display: inline-block;
  width: 204;
  height: 104;
  border-radius: 0;
  background: lightblue;
  box-shadow: 0 2px 2px 2px rgba(0, 0, 0, 0.2);
  box-sizing: border-box;
  cursor: pointer;
  margin: 8px 2px;
}

.radio:hover {
  box-shadow: 2px 2px 2px 2px rgba(0, 0, 0, 0.3);
}

.radio.selected {
  box-shadow: 1px 1px 2px 2px rgba(0, 0, 0, 0.1);
}

/*Fit image in bootstrap div*/
.fit-image {
  width: 100%;
  object-fit: cover;
}
.btn-outline-primary {
  border: 2px solid black;
  background-color: white;
  margin-top: 15px;
  padding: 2px 12px;
  font-size: 16px;
  cursor: pointer;
  border-color: #367cf4;
  color: #367cf4;
}
.btn-outline-primary:hover {
  background: #367cf4;
  color: white;
}
select {
  margin-bottom: 5px;
}
.btn-addon {
  border: none;
  padding: 6px 12px;
  transition: border-color 0.1s ease-in-out 0s,
    background-color 0.1s ease-in-out 0s;
  outline: none;
  border-radius: 3px;
  background-clip: padding-box;
  display: inline-block;
  padding: 6px 12px;
  margin-bottom: 0;
  font-size: 13px;
  font-weight: 400;
  line-height: 1.42857143;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  -ms-touch-action: manipulation;
  touch-action: manipulation;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  background-image: none;
  border-radius: 4px;
}
.btn-addon:hover {
  background-color: #d1d3d4;
  color: #545252;
}
.btn-addon:disabled {
  border: 1px solid #b9b8b8;
  background-color: #cfcfcf;
  color: #848383;
}
