/**
 * Cube - Bootstrap Admin Theme
 * Copyright 2014 Phoonio
 */

var userId = 1;
var userName = '';
var logUserId = 2;

var app = angular.module('hkjflApp', [
    'ngRoute',
    'angular-loading-bar',
    'ngAnimate',
    'ckeditor',
    'LocalStorageModule',
    'ngIntlTelInput',
    'summernote',
]);

// const websocket_url = "ws://ws.localhost/ws/";

// app.factory('socket', [function() {
//     var stack = [];
//     var onmessageDefer;
//     var socket = {
//         ws: new WebSocket(websocket_url),
//         send: function(data) {
//             data = JSON.stringify(data);
//             if (socket.ws.readyState == 1) {
//                 socket.ws.send(data);
//             } else {
//                 stack.push(data);
//             }
//         },
//         onmessage: function(callback) {
//             if (socket.ws.readyState == 1) {
//                 socket.ws.onmessage = callback;
//             } else {
//                 onmessageDefer = callback;
//             }
//         }
//     };
//     socket.ws.onopen = function(event) {
//         for (i in stack) {
//             socket.ws.send(stack[i]);
//         }
//         stack = [];
//         if (onmessageDefer) {
//             socket.ws.onmessage = onmessageDefer;
//             onmessageDefer = null;
//         }
//     };
//     return socket;
// }]);

app.config([
    'cfpLoadingBarProvider',
    function (cfpLoadingBarProvider) {
        console.log('app.config - cfpLoadingBarProvider');

        cfpLoadingBarProvider.includeBar = true;
        cfpLoadingBarProvider.includeSpinner = true;
        cfpLoadingBarProvider.latencyThreshold = 100;
    },
]);

app.config(function (localStorageServiceProvider) {
    localStorageServiceProvider.setPrefix('hkjflApp').setNotify(true, true);
});
/**
 * Configure the Routes
 */

app.factory('socket', function ($rootScope) {
    const socket = io.connect(SOCKET_URL); // Replace with your server's URL
    const listeners = {}; // Track listeners

    return {
        joinChannel: function (channelName) {
            socket.emit('join-channel', channelName);
        },
        on: function (eventName, callback) {
            // Add the listener only if it doesn't already exist
            if (!listeners[eventName]) {
                socket.on(eventName, function () {
                    const args = arguments;
                    $rootScope.$apply(function () {
                        callback.apply(socket, args);
                    });
                });
                listeners[eventName] = true; // Mark as listening
            }
        },
        off: function (eventName, callback) {
            // Only attempt to remove the listener if it exists
            if (listeners[eventName]) {
                socket.off(eventName, callback);
                delete listeners[eventName]; // Remove tracking when unsubscribing
            }
        },
        isOn: function (eventName) {
            return !!listeners[eventName]; // Check if listening
        },
        connect: function () {
            if (!socket.connected) {
                socket.connect();
            }
        },
        disconnect: function () {
            if (socket.connected) {
                socket.disconnect();
            }
        },
    };
});


app.factory('getUser', function ($q, $http, $rootScope) {
    var defer = $q.defer();
    $http({
        method: 'POST',
        url: SERVER_PATH + 'user/getInfoUser',
        data: 'user_id=' + $rootScope.user_id,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
    }).success(function (response) {
        defer.resolve(response.info);
        userId = $rootScope.user_id;
        $rootScope.user_name = response.info.username;
        userName = response.info.username;
    });

    return defer.promise;
});

app.factory('getCoachCertificate', function ($q, $http, $rootScope) {
    var defer = $q.defer();
    $http({
        method: 'POST',
        url: SERVER_PATH + 'setting/getSetCoachCertificate',
        data: 'user_id=' + $rootScope.user_id,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'x-user-id': $rootScope.user_id,
            'x-user-email': $rootScope.user_name,
        },
    }).success(function (response) {
        let coachCertificates = [];
        response.data.forEach((element) => {
            coachCertificates[element.coach_certificates.id] = {
                name: element.coach_certificates.name,
                order: Number(element.coach_certificates.order),
                value: element.coach_certificates.id,
            };
        });
        COACH_CERTIFICATE = coachCertificates;
        defer.resolve(coachCertificates);
    });
    return defer.promise;
});

app.config([
    '$routeProvider',
    function ($routeProvider) {
        console.log('app.config - routeProvider');

        $routeProvider
            .when('/', {
                redirectTo: '/dashboard',
            })

            .when('/profile', {
                templateUrl: 'app/profile/profile.html',
                controller: 'profileCtrl',
                title: 'Profile',
                resolve: {
                    user: function (getUser) {
                        return getUser;
                    },
                },
            })

            /* Dashboard */
            .when('/dashboard', {
                templateUrl: 'app/dashboard/dashboard.html',
                controller: 'dashboardCtrl',
                title: 'Dashboard',
                resolve: {
                    user: function (getUser) {
                        return getUser;
                    },
                },
            })

            /* Golden Age */
            .when('/events/golden-age', {
                templateUrl: 'app/events/golden-age/golden-age.html',
                controller: 'goldenAgeCtrl',
                title: 'Golden Age',
                resolve: {
                    user: function (getUser) {
                        return getUser;
                    },
                },
            })

            /* Summer Scheme */
            .when('/events/summer-scheme', {
                templateUrl: 'app/events/summer-scheme/summer-scheme.html',
                controller: 'summerSchemeCtrl',
                title: 'Summer Scheme',
                resolve: {
                    user: function (getUser) {
                        return getUser;
                    },
                },
            })

            /* PL Junior */
            .when('/events/pl-junior', {
                templateUrl: 'app/events/pl-junior/pl-junior.html',
                controller: 'plJuniorCtrl',
                title: 'PL Junior',
                resolve: {
                    user: function (getUser) {
                        return getUser;
                    },
                },
            })

            /* District */
            .when('/events/district', {
                templateUrl: 'app/events/district/district.html',
                controller: 'districtCtrl',
                title: 'District',
                resolve: {
                    user: function (getUser) {
                        return getUser;
                    },
                },
            })

            /* Beginner */
            .when('/events/beginner', {
                templateUrl: 'app/events/beginner/beginner.html',
                controller: 'beginnerCtrl',
                title: 'Beginner',
                resolve: {
                    user: function (getUser) {
                        return getUser;
                    },
                },
            })

            /* Regional */
            .when('/events/regional', {
                templateUrl: 'app/events/regional/regional.html',
                controller: 'regionalCtrl',
                title: 'Regional',
                resolve: {
                    user: function (getUser) {
                        return getUser;
                    },
                },
            })

            /* Email Manager */
            .when('/template', {
                templateUrl: 'app/email_manager/email_manager.html',
                controller: 'emailManagerCtrl',
                title: 'Email Manager',
            })

            /* Registrations */
            .when('/registrations/:id', {
                templateUrl: 'app/registrations/registrations.html',
                controller: 'registrationsCtrl',
                title: 'Registrations',
            })

            /* Offline Registrations */

            .when(
                '/registrations/registration-summer-scheme/offline-registration/:id',
                {
                    templateUrl:
                        'app/registrations/registration-summer-scheme/offline-registration/offline-registration.html',
                    controller: 'offlineRegistrationSummerSchemeCtrl',
                    title: 'Offline Registration',
                }
            )

            .when('/registration-training-scheme/:id', {
                templateUrl:
                    'app/registrations/registration-training-scheme/registration-training-scheme.html',
                controller: 'registrationsTrainingSchemeCtrl',
                title: 'Registrations Training Scheme',
            })

            .when('/registration-summer-scheme/:id', {
                templateUrl:
                    'app/registrations/registration-summer-scheme/registration-summer-scheme.html',
                controller: 'registrationsSummerSchemeCtrl',
                title: 'Registrations Summer Scheme',
            })

            .when('/registration-district/:id', {
                templateUrl:
                    'app/registrations/registration-district/registration-district.html',
                controller: 'registrationsDistrictCtrl',
                title: 'Registrations District',
            })

            .when('/registration-pl-junior/:id', {
                templateUrl:
                    'app/registrations/registration-pl-junior/registration-pl-junior.html',
                controller: 'registrationsPLJuniorCtrl',
                title: 'Registrations PL Junior',
            })

            .when('/registration-beginner/:id', {
                templateUrl:
                    'app/registrations/registration-beginner/registration-beginner.html',
                controller: 'registrationsBeginnerCtrl',
                title: 'Registrations Beginner',
            })

            /* Club Players */
            .when('/clubs/:id', {
                templateUrl: 'app/clubs/clubs.html',
                controller: 'clubsCtrl',
                title: 'Manage Club',
            })

            /* Clubcoach */
            .when('/clubcoach/:id', {
                templateUrl: 'app/clubcoach/clubcoach.html',
                controller: 'clubcoachCtrl',
                title: 'Coach Club',
            })

            /* Club Coaches */
            .when('/club_coaches/:id', {
                templateUrl: 'app/club_coaches/club_coaches.html',
                controller: 'club_coachesCtrl',
                title: 'Club coaches',
            })

            /* Payment */
            .when('/payments/:id', {
                templateUrl: 'app/payments/payments.html',
                controller: 'paymentsCtrl',
                title: 'Payments',
            })

            /* Payment Summer Scheme */
            .when('/payments/summer-scheme/:id', {
                templateUrl: 'app/payments/summer-scheme/summer-scheme.html',
                controller: 'paymentsSummerSchemeCtrl',
                title: 'Payments',
            })

            /* Payment District */
            .when('/payments/district/:id', {
                templateUrl: 'app/payments/district/district.html',
                controller: 'paymentsDistrictCtrl',
                title: 'Payments',
            })

            /* Payment PL Junior */
            .when('/payments/pl-junior/:id', {
                templateUrl: 'app/payments/pl-junior/pl-junior.html',
                controller: 'paymentsPLJuniorCtrl',
                title: 'Payments',
            })

            /* Payment Club */
            .when('/payment_clubs/:id', {
                templateUrl: 'app/payment_clubs/payment_clubs.html',
                controller: 'paymentClubsCtrl',
                title: 'Payments',
            })

            /* Payment Club */
            .when('/payment_teams/:id', {
                templateUrl: 'app/payment_teams/payment_teams.html',
                controller: 'paymentTeamsCtrl',
                title: 'Payments',
            })

            /* Shipping */
            .when('/shipping/:id', {
                templateUrl: 'app/shipping/shipping.html',
                controller: 'shippingCtrl',
                title: 'Shipping',
            })

            /* Shipping PL Junior */
            .when('/shipping-plj/:id', {
                templateUrl: 'app/shipping/shipping-pl-junior/shipping.html',
                controller: 'shippingPLJCtrl',
                title: 'Shipping',
            })

            /* Self Pick Up */
            .when('/self-pick-up/:id', {
                templateUrl: 'app/self-pick-up/self-pick-up.html',
                controller: 'selfPickUpCtrl',
                title: 'Self Pick Up',
            })

            /* Seasons */
            .when('/seasons/:id', {
                templateUrl: 'app/seasons/seasons.html',
                controller: 'seasonsCtrl',
                title: 'Seasons',
            })

            /* admin clubs manager */
            .when('/admin_club_manager/:id', {
                templateUrl: 'app/admin_clubs/admin_clubs.html',
                controller: 'admin_clubsCtrl',
                title: 'CLub Manager',
            })

            /* Leagues */
            .when('/leagues/:id', {
                templateUrl: 'app/leagues_group/leagues_group.html',
                controller: 'leaguesGroupsCtrl',
                title: 'Leagues',
            })

            /* Leagues Detail */
            .when('/leagues/:id/:leaguesid', {
                templateUrl: 'app/league_detail/league_detail.html',
                controller: 'leagueDetailCtrl',
                title: 'League Detail',
            })

            /* Leagues List */
            .when('/leaguesList/:groupId/:eventId/:eventName', {
                templateUrl: 'app/league_list/league_list.html',
                controller: 'leagueListCtrl',
                title: 'League List',
            })

            .when('/leagues/leagueTournament/:id/:leaguesid', {
                templateUrl: 'app/tournament/tournament.html',
                controller: 'leagueTournamentCtrl',
                title: 'Tournament',
            })

            /* League Tournament */
            .when('/leagues/leagueTournament/groupStage/:id/:leaguesid', {
                templateUrl:
                    'app/tournament_group_stage/tournament_group_stage.html',
                controller: 'TournamentGroupStageCtrl',
                title: 'Tournament',
            })

            .when('/leagues/leagueTournament/knockout/:id/:leaguesid', {
                templateUrl:
                    'app/tournament_knockout_stage/tournament_knockout_stage.html',
                controller: 'TournamentKnockoutStageCtrl',
                title: 'Tournament',
            })

            /* Year Group */
            .when('/cgroups/:id', {
                templateUrl: 'app/cgroups/cgroups.html',
                controller: 'cgroupsCtrl',
                title: 'Year Groups',
            })

            .when('/cgroups_admin/:id', {
                templateUrl: 'app/cgroups_admin/cgroups_admin.html',
                controller: 'cgroupsAdminCtrl',
                title: 'Year Groups',
            })

            /* questionnaire */

            .when('/questionnaire', {
                templateUrl: 'app/questionnaire/questionnaire.html',
                controller: 'questionnaireCtrl',
                title: 'questionnaire'
            })
            
            .when('/questionnaire/:id/responses', {
                templateUrl: 'app/questionnaire/responses/responses.html',
                controller: 'responsesCtrl',
                title: 'Responses'
            })

            /* Team Sheet */
            .when('/team_sheet/:id', {
                templateUrl: 'app/team_sheet/team_sheet.html',
                controller: 'teamSheetCtrl',
                title: 'Team Sheet',
            })

            /* SHOP PRODUCTS */
            .when('/shop_products', {
                templateUrl: 'app/shop_products/shop_products.html',
                controller: 'shopProductsCtrl',
                title: 'Shop Products',
            })

            /* SHOP PRODUCT DETAILS */
            .when('/product_details/:id', {
                templateUrl:
                    'app/shop_product_details/shop_product_details.html',
                controller: 'shopProducDetailsCtrl',
                title: 'Product Details',
            })

            /* Messages */
            .when('/messages/:id', {
                templateUrl: 'app/messages/messages.html',
                controller: 'messagesCtrl',
                title: 'Messages',
            })

            /* Messages Course */
            .when('/message-coach/course_coach_message/:id', {
                templateUrl:
                    'app/messages/messages_course_coach/messages_course_coach.html',
                controller: 'messagesCourseCoachCtrl',
                title: 'Messages',
            })

            /* Messages Detail */
            .when('/message_details/:id', {
                templateUrl:
                    'app/messages/messages_details/messages_details.html',
                controller: 'messagesDetailCtrl',
                title: 'Message Details',
            })

            /* Messages Course Detail*/
            .when('/message-coach/course_coach_message_detail/:id', {
                templateUrl:
                    'app/messages/messages_course_coach/messages_course_coach_detail/messages_course_coach_detail.html',
                controller: 'messagesCourseCoachDetailCtrl',
                title: 'Message Details',
            })

            /* Messages Teams */
            .when('/message-coach/message-team-coach/:id', {
                templateUrl:
                    'app/messages/message_team_coach/message_team_coach.html',
                controller: 'messageTeamCoachCtrl',
                title: 'Messages',
            })

            /* Reports */
            .when('/reports/registration', {
                templateUrl:
                    'app/reports_registration/reports_registration.html',
                resolve: {
                    report_id: function () {
                        return REPORT_ID_REGISTRATION;
                    },
                },
                controller: 'reportsCtrl',
                title: 'Reports',
            })

            /* Training Schedule */
            .when('/training_schedule', {
                templateUrl: 'app/training_schedule/training_schedule.html',
                controller: 'trainingScheduleCtrl',
                title: 'Training Schedule',
            })

            /** Course */
            .when('/event/:id/course', {
                templateUrl: 'app/course/course.html',
                controller: 'courseCtrl',
                title: 'Courses',
            })

            /* Course > Date */
            .when('/event/:id/course/:courseId/date', {
                templateUrl: 'app/course/date/date.html',
                controller: 'cdateCtrl',
                title: 'Event Dates',
            })

            /* Team District */
            .when('/event/:id/team_district', {
                templateUrl: 'app/course/team_district/team_district.html',
                controller: 'teamDistrictCtrl',
                title: 'Team',
            })

            .when('/event/:id/team_district/:teamId/details', {
                templateUrl:
                    'app/course/team_district/team_details/team_details.html',
                controller: 'teamDistrictDetailsCtrl',
                title: 'Team',
            })

            /* Team Golden Age */
            .when('/event/:id/team_golden_age', {
                templateUrl: 'app/course/team_golden_age/team_golden_age.html',
                controller: 'teamGoldenAgeCtrl',
                title: 'Team',
                resolve: {
                    user: function (getUser) {
                        return getUser;
                    },
                },
            })

            .when('/event/:id/team_golden_age_player/:teamId', {
                templateUrl:
                    'app/course/team_golden_age/team_golden_age_player/team_golden_age_player.html',
                controller: 'teamGoldenAgePlayerCtrl',
                title: 'Team',
                resolve: {
                    user: function (getUser) {
                        return getUser;
                    },
                },
            })

            /* Coach */
            .when('/tables/coach', {
                templateUrl: 'app/tables/coach/coach_editor.html',
                controller: 'tablesCtrl',
                title: 'Coach',
                resolve: {
                    coachCertificate: function (getCoachCertificate) {
                        return getCoachCertificate;
                    },
                    task_id: function () {
                        return TASK_ID_NOT_ADMINISTRATOR_EDITOR;
                    },
                },
            })

            /* certificate */
            .when('/certificates/:eventId', {
                templateUrl: 'app/certificate/certificate.html',
                controller: 'certificateCtrl',
                title: 'certificate',
            })

            .when('/certificates/create/:pageType/:eventId', {
                templateUrl:
                    'app/certificate/certificate-create-detail/certificate-create-detail.html',
                controller: 'certificateCreateCtrl',
                title: 'certificateCreate',
            })

            .when('/certificates/print/:id/:eventId', {
                templateUrl:
                    'app/certificate/certificate_print/certificate_print.html',
                controller: 'certificatePrintCtrl',
                title: 'Certificate',
            })

            .when('/certificates/print/player/:id', {
                templateUrl:
                    'app/certificate/certificate_player/certificate_player.html',
                controller: 'certificatePlayerCtrl',
                title: 'Print certificate',
            })

            /* Training Scheme */
            .when('/training_videos', {
                templateUrl: 'app/training_scheme/training_scheme.html',
                controller: 'trainingSchemeCtrl',
                title: 'Training Scheme',
            })

            .when('/training_scheme_exercise/:id', {
                templateUrl:
                    'app/training_scheme/training_scheme_exercise/training_scheme_exercise.html',
                controller: 'trainingSchemeExerciseCtrl',
                title: 'Training Scheme Exercise',
            })

            /* Respects */
            .when('/respects', {
                templateUrl: 'app/respects/respects.html',
                controller: 'respectCtrl',
                title: 'Respect',
            })

            /* Documents */
            .when('/documents', {
                templateUrl: 'app/documents/documents.html',
                controller: 'documentsCtrl',
                title: 'Documents',
            })

            /* Document Sections */
            .when('/documents/:id/:name', {
                templateUrl:
                    'app/documents/document_sections/document_sections.html',
                controller: 'documentSectionsCtrl',
                title: 'Document Sections',
            })

            /* Tables */
            .when('/tables/event_editor', {
                templateUrl: 'app/tables/event/event_editor.html',
                controller: 'eventEditorCtrl',
                title: 'Events',
                resolve: {
                    coachCertificate: function (getCoachCertificate) {
                        return getCoachCertificate;
                    },
                    task_id: function () {
                        return TASK_ID_NOT_ADMINISTRATOR_EDITOR;
                    },
                },
            })

            .when('/tables/club_editor', {
                templateUrl: 'app/tables/club/club_editor.html',
                controller: 'tablesCtrl',
                resolve: {
                    task_id: function () {
                        return TASK_ID_NOT_ADMINISTRATOR_EDITOR;
                    },
                },
                title: 'Clubs',
            })

            .when('/tables/player_editor', {
                templateUrl: 'app/tables/player/player_editor.html',
                controller: 'playerCtrl',
                resolve: {
                    task_id: function () {
                        return TASK_ID_NOT_ADMINISTRATOR_EDITOR;
                    },
                },
                title: 'Players',
            })

            .when('/tables/player_editor/:playerId', {
                templateUrl:
                    'app/tables/tables/player/player_report/player_report.html',
                controller: 'activityLogEachPlayerCtrl',
                title: 'Activity each player Log',
            })

            .when('/tables/parent_editor', {
                templateUrl: 'app/tables/parent/parent_editor.html',
                controller: 'tablesCtrl',
                resolve: {
                    task_id: function () {
                        return TASK_ID_NOT_ADMINISTRATOR_EDITOR;
                    },
                },
                title: 'Parents',
            })

            .when('/tables/manager_editor', {
                templateUrl: 'app/tables/manager/manager_editor.html',
                controller: 'tablesCtrl',
                resolve: {
                    task_id: function () {
                        return TASK_ID_NOT_ADMINISTRATOR_EDITOR;
                    },
                },
                title: 'Club Managers',
            })

            .when('/tables/league_admin', {
                templateUrl: 'app/tables/league_admin/league_admin.html',
                controller: 'leagueAdminCtrl',
                title: 'League Admins',
            })

            .when('/tables/super_admin', {
                templateUrl: 'app/tables/super_admin/super_admin.html',
                controller: 'superAdminCtrl',
                title: 'Super Admins',
            })

            .when('/tables/managerial_coach', {
                templateUrl:
                    'app/tables/managerial_coach/managerial_coach.html',
                controller: 'ManagerialCoachCtrl',
                title: 'Managerial Coach',
            })

            .when('/tables/user_editor', {
                templateUrl: 'app/tables/users/users.html',
                controller: 'usersCtrl',
                title: 'Users',
            })

            .when('/tables/venues_editor', {
                templateUrl: 'app/tables/venues/venues.html',
                controller: 'venuesCtrl',
                title: 'Venues',
            })

            .when('/tables/coach-certificate', {
                templateUrl:
                    'app/tables/coach-certificate/coach-certificate.html',
                controller: 'coachCertificateCtrl',
                title: 'Coach Certificate',
            })

            /* Table sponsor */
            .when('/tables/sponsor', {
                templateUrl: 'app/tables/sponsor/sponsor.html',
                controller: 'sponsorCtrl',
                title: 'Sponsor',
            })

            /* Table Finance Team */
            .when('/tables/finance_team', {
                templateUrl: 'app/tables/finance_team/finance_team.html',
                controller: 'financeTeamCtrl',
                title: 'Finance Team',
            })

            /* Table Grassroots Finance */
            .when('/tables/grassroots_finance', {
                templateUrl:
                    'app/tables/grassroots_finance/grassroots_finance.html',
                controller: 'grassrootsFinanceCtrl',
                title: 'Grassroots Finance',
            })

            /* Table Shipping Users */
            .when('/tables/shipping-user', {
                templateUrl: 'app/tables/shipping-user/shipping-user.html',
                controller: 'shippingUserCtrl',
                title: 'Shipping Users',
            })

            /* Table Supervisor */
            .when('/tables/supervisor', {
                templateUrl: 'app/tables/supervisor/supervisor.html',
                controller: 'supervisorCtrl',
                title: 'Supervisor',
            })

            /* Table Supervisor Coach */
            .when('/tables/supervisor_coach', {
                templateUrl: 'app/tables/supervisor_coach/supervisor_coach.html',
                controller: 'supervisorCoachCtrl',
                title: 'Supervisor Coach',
            })

            /* Table Non training days */
            .when('/tables/non_training_days', {
                templateUrl:
                    'app/tables/non_training_days/non_training_days.html',
                controller: 'tablesCtrl',
                resolve: {
                    task_id: function () {
                        return TASK_ID_NOT_ADMINISTRATOR_EDITOR;
                    },
                },
                title: 'Non Training Days',
            })

            /* Logout */
            .when('/logout', {
                templateUrl: 'app/frontend/frontend.html',
                controller: 'logoutCtrl',
                title: 'Logout',
            })

            /* System setting */
            .when('/settings/system', {
                templateUrl: 'app/settings/system/system.html',
                controller: 'settingsCtrl',
                title: 'System',
            })

            /*System Notification (App start)*/
            .when('/settings/notification_app_start', {
                templateUrl:
                    'app/settings/notification_app_start/notification_app_start_editor.html',
                controller: 'settingsCtrl',
                title: 'Notification (App start)',
            })

            /*Comment Suggestion*/
            .when('/settings/comment_suggestion', {
                templateUrl:
                    'app/settings/comment_suggestion/comment_suggestion.html',
                controller: 'settingsCtrl',
                title: 'Comment Suggestion',
            })

            .when('/language/:language', {
                templateUrl: 'app/language/language.html',
                controller: 'languageCtrl',
                title: 'language',
            })

            /* General Settings*/
            .when('/settings/general_settings', {
                templateUrl:
                    'app/settings/general_settings/general_settings.html',
                controller: 'generalSettingsCtrl',
                title: 'General Setting',
            })

            /* Product */
            .when('/tables/products', {
                templateUrl: 'app/tables/products/products.html',
                controller: 'productsCtrl',
                title: 'Product Setting',
            })

            .when('/tables/logs', {
                templateUrl: 'app/tables/log/log.html',
                controller: 'logsCtrl',
                title: 'logs',
            })

            .when('/reports/district/:eventId', {
                templateUrl: 'app/reports/district_report/district_report.html',
                controller: 'districtReportsCtrl',
                title: 'District Report',
                resolve: {
                    user: function (getUser) {
                        return getUser;
                    },
                    coachCertificate: function (getCoachCertificate) {
                        return getCoachCertificate;
                    },
                },
            })

            .when('/reports/regional/:eventId', {
                templateUrl: 'app/reports/regional_report/regional_report.html',
                controller: 'regionalReportsCtrl',
                title: 'Regional Report',
                resolve: {
                    user: function (getUser) {
                        return getUser;
                    },
                    coachCertificate: function (getCoachCertificate) {
                        return getCoachCertificate;
                    },
                },
            })

            .when('/reports/beginner/:eventId', {
                templateUrl: 'app/reports/beginner_report/beginner_report.html',
                controller: 'beginnerReportsCtrl',
                title: 'Beginner Report',
                resolve: {
                    user: function (getUser) {
                        return getUser;
                    },
                    coachCertificate: function (getCoachCertificate) {
                        return getCoachCertificate;
                    },
                },
            })

            .when('/reports/summer_scheme/:eventId', {
                templateUrl:
                    'app/reports/summer_scheme_report/summer_scheme_report.html',
                controller: 'summerSchemeReportsCtrl',
                title: 'Summer Scheme Report',
                resolve: {
                    user: function (getUser) {
                        return getUser;
                    },
                    coachCertificate: function (getCoachCertificate) {
                        return getCoachCertificate;
                    },
                },
            })

            .when('/reports/pl_junior/:eventId', {
                templateUrl: 'app/reports/plj_report/plj_report.html',
                controller: 'PLJReportsCtrl',
                title: 'PLJ Report',
                resolve: {
                    user: function (getUser) {
                        return getUser;
                    },
                    coachCertificate: function (getCoachCertificate) {
                        return getCoachCertificate;
                    },
                },
            })
            .when('/reports/elderly_report', {
                templateUrl: 'app/reports/elderly_report/elderly_report.html',
                controller: 'ElderlyReportsCtrl',
                title: 'Elderly Report',
                resolve: {
                    user: function (getUser) {
                        return getUser;
                    },
                    coachCertificate: function (getCoachCertificate) {
                        return getCoachCertificate;
                    },
                },
            })

            /* Error 404 */
            .when('/error-404', {
                templateUrl: 'app/error-404/error-404.html',
                controller: 'mainCtrl',
                title: 'Error 404',
            })

            .otherwise({
                redirectTo: '/error-404',
            });
    },
]);

app.run([
    '$location',
    '$rootScope',
    '$http',
    'localStorageService',
    function ($location, $rootScope, $http, localStorageService) {
        $rootScope.$on('$routeChangeStart', function (event, next, current) {
            console.log(next);
            if ($rootScope.user_role != undefined) {
                var next_route = next.$$route.controller;
                if (
                    $rootScope.user_role == USER_PARENT &&
                    USER_PARENT_PERMISSION.includes(next_route) == false
                )
                    $location.path('/error-404');
                if (
                    $rootScope.user_role == USER_TEAM_COACH &&
                    USER_TEAM_COACH_PERMISSION.includes(next_route) == false
                )
                    $location.path('/error-404');
                if (
                    $rootScope.user_role == USER_CLUB_MANAGER &&
                    USER_CLUB_MANAGER_PERMISSION.includes(next_route) == false
                )
                    $location.path('/error-404');
                if (
                    $rootScope.user_role == USER_SUPER_ADMIN &&
                    USER_SUPER_ADMIN_PERMISSION.includes(next_route) == false
                ) {
                    console.log(next_route);
                    $location.path('/error-404');
                }
                if (
                    $rootScope.user_role == USER_LEAGUE_ADMIN &&
                    USER_LEAGUE_ADMIN_PERMISSION.includes(next_route) == false
                ) {
                    console.log(next_route);
                    $location.path('/error-404');
                }
                if (
                    $rootScope.user_role == USER_FINANCE &&
                    USER_FINANCE_PERMISSION.includes(next_route) == false
                ) {
                    console.log(next_route);
                    $location.path('/error-404');
                }
                if (
                    $rootScope.user_role == USER_GRASSROOTS_FINANCE &&
                    USER_GRASSROOTS_FINANCE_PERMISSION.includes(next_route) ==
                        false
                ) {
                    console.log(next_route);
                    $location.path('/error-404');
                }
                if (
                    $rootScope.user_role == USER_SHIPPING &&
                    USER_SHIPPING_PERMISSION.includes(next_route) == false
                ) {
                    console.log(next_route);
                    $location.path('/error-404');
                }
                if (
                    $rootScope.user_role == USER_SUPERVISOR &&
                    USER_SUPERVISOR_PERMISSION.includes(next_route) == false
                ) {
                    console.log(next_route);
                    $location.path('/error-404');
                }
                if (
                    $rootScope.user_role == USER_SUPERVISOR_COACH &&
                    USER_SUPERVISOR_COACH_PERMISSION.includes(next_route) == false
                ) {
                    console.log(next_route);
                    $location.path('/error-404');
                }
            }
        });
        $rootScope.$on(
            '$routeChangeSuccess',
            function (event, current, previous) {
                $rootScope.title = current.$$route.title;
            }
        );

        // Phan Viet Hoang - 02/07/16 - get user data for header
        var absUrl = $location.absUrl();

        var idx = absUrl.indexOf('=');
        if (idx == -1) {
            location.href = 'login.html';
            return;
        }

        var idx_pound = absUrl.indexOf('#', idx + 1);

        if (idx_pound > 0) base64 = absUrl.substring(idx + 1, idx_pound);
        else base64 = absUrl.substring(idx + 1);

        console.log('base64 = ' + base64);

        try {
            str = atob(base64);
        } catch (err) {
            console.log('message = ' + err.message);
            BootstrapDialog.show({
                title: 'ERROR',
                type: BootstrapDialog.TYPE_WARNING,
                message: 'Please log in again!',
            });

            // must go to login
            location.href = 'login.html';
            return;
        }

        // get id and time
        var vals = str.split('|');
        user_id = vals[0];
        $rootScope.user_id = user_id;

        localStorageService.bind($rootScope, 'language');
        localStorageService.set('user_id', user_id);
        $rootScope.getUser = function () {
            $http({
                method: 'POST',
                url: SERVER_PATH + 'user/getUserName',
                data: 'user_id=' + $rootScope.user_id + '&ss_id=' + vals[2],
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name,
                },
            }).success(function (response) {
                console.log(response);
                if (response.status == 'OK') {
                    $rootScope.user = response.info;
                    $rootScope.user_name = response.info.username;
                    $rootScope.user_role = response.info.role;
                    localStorageService.set('user_name', $rootScope.user_name);
                    if ($rootScope.language == null) {
                        $rootScope.language = 'English';
                    }
                    if ($rootScope.translate == null) {
                        switch ($rootScope.language) {
                            case 'English': {
                                $rootScope.translate = EN;
                                break;
                            }
                            case '简体中文': {
                                $rootScope.translate = HANS;
                                break;
                            }
                            case '繁體中文': {
                                $rootScope.translate = HANT;
                                break;
                            }
                        }
                    }
                    $rootScope.initSideMenu();
                } else {
                    alert(response.message);
                    location.href = 'login.html';
                    return;
                }
            });
        };
        $rootScope.initSideMenu = function () {
            // get list events
            $http({
                method: 'POST',
                url: SERVER_PATH + 'event/getEventList',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name,
                },
                data: 'user_id=' + $rootScope.user_id,
            }).success(function (response) {
                // build index.html dynamically
                var events = response.info;

                var season_event = '';
                var golden_age_events = '';
                var summer_scheme_events = '';
                // Registrations
                var sub_html_registrations = '';

                for (var ip = 0; ip < events.length; ip++) {
                    season_event = events[ip].season_event;
                    golden_age_events = events[ip].golden_age_events;
                    training_scheme_events = events[ip].training_scheme_events;
                    summer_scheme_events = events[ip].summer_scheme_events;
                    district_events = events[ip].district_events;
                    pl_junior_events = events[ip].pl_junior_events;
                    regional_events = events[ip].regional_events;
                    beginner_events = events[ip].beginner_events;
                    elderly_events = events[ip].elderly_events;

                    if (
                        golden_age_events.length == 0 &&
                        training_scheme_events.length == 0 &&
                        summer_scheme_events.length == 0 &&
                        district_events.length == 0 &&
                        pl_junior_events.length == 0 &&
                        regional_events.length == 0 &&
                        beginner_events.length == 0 &&
                        elderly_events.length == 0
                    ) {
                        continue;
                    }

                    sub_html_registrations +=
                        '<li>' +
                        '<a href="" class="dropdown-toggle">' +
                        '<span>' +
                        season_event.name +
                        '</span>' +
                        '<i class="fa fa-angle-right drop-icon"></i>' +
                        '</a>' +
                        '<ul class="submenu">';

                    if (
                        golden_age_events.length > 0 &&
                        ($rootScope.user_role == USER_SUPER_ADMIN ||
                            $rootScope.user_role == USER_LEAGUE_ADMIN)
                    ) {
                        for (let i = 0; i < golden_age_events.length; i++) {
                            sub_html_registrations +=
                                '<li>' +
                                '<a data-match-route="/registrations/' +
                                golden_age_events[i].id +
                                '" href="#/registrations/' +
                                golden_age_events[i].id +
                                '">' +
                                golden_age_events[i].name +
                                '</a>' +
                                '</li>';
                        }
                    }

                    if (
                        training_scheme_events.length > 0 &&
                        ($rootScope.user_role == USER_SUPER_ADMIN ||
                            $rootScope.user_role == USER_LEAGUE_ADMIN)
                    ) {
                        for (
                            let i = 0;
                            i < training_scheme_events.length;
                            i++
                        ) {
                            sub_html_registrations +=
                                '<li>' +
                                '<a data-match-route="/registration-training-scheme/' +
                                training_scheme_events[i].id +
                                '" href="#/registration-training-scheme/' +
                                training_scheme_events[i].id +
                                '">' +
                                training_scheme_events[i].type +
                                '</a>' +
                                '</li>';
                        }
                    }

                    if (
                        summer_scheme_events.length > 0 &&
                        ($rootScope.user_role == USER_SUPER_ADMIN ||
                            $rootScope.user_role == USER_LEAGUE_ADMIN)
                    ) {
                        for (let i = 0; i < summer_scheme_events.length; i++) {
                            sub_html_registrations +=
                                '<li>' +
                                '<a data-match-route="/registration-summer-scheme/' +
                                summer_scheme_events[i].id +
                                '" href="#/registration-summer-scheme/' +
                                summer_scheme_events[i].id +
                                '">' +
                                summer_scheme_events[i].type +
                                '</a>' +
                                '</li>';
                        }
                    }

                    if (
                        district_events.length > 0 &&
                        ($rootScope.user_role == USER_TEAM_COACH ||
                            $rootScope.user_role == USER_SUPERVISOR_COACH ||
                            $rootScope.user_role == USER_SUPER_ADMIN ||
                            $rootScope.user_role == USER_LEAGUE_ADMIN)
                    ) {
                        var is_show = events[ip].is_show;

                        if (is_show == true) {
                            for (let i = 0; i < district_events.length; i++) {
                                sub_html_registrations +=
                                    '<li>' +
                                    '<a data-match-route="/registration-district/' +
                                    district_events[i].id +
                                    '" href="#/registration-district/' +
                                    district_events[i].id +
                                    '">' +
                                    district_events[i].type +
                                    '</a>' +
                                    '</li>';
                            }
                        }
                    }

                    if (
                        pl_junior_events.length > 0 &&
                        ($rootScope.user_role == USER_SUPER_ADMIN ||
                            $rootScope.user_role == USER_LEAGUE_ADMIN)
                    ) {
                        for (let i = 0; i < pl_junior_events.length; i++) {
                            sub_html_registrations +=
                                '<li>' +
                                '<a data-match-route="/registration-pl-junior/' +
                                pl_junior_events[i].id +
                                '" href="#/registration-pl-junior/' +
                                pl_junior_events[i].id +
                                '">' +
                                pl_junior_events[i].name +
                                '</a>' +
                                '</li>';
                        }
                    }
                    if (
                        elderly_events.length > 0 &&
                        ($rootScope.user_role == USER_SUPER_ADMIN ||
                            $rootScope.user_role == USER_LEAGUE_ADMIN)
                    ) {
                        alert;
                        for (let i = 0; i < elderly_events.length; i++) {
                            sub_html_registrations +=
                                '<li>' +
                                '<a data-match-route="/registration-pl-junior/' +
                                elderly_events[i].id +
                                '" href="#/registration-pl-junior/' +
                                elderly_events[i].id +
                                '">' +
                                elderly_events[i].name +
                                '</a>' +
                                '</li>';
                        }
                    }

                    if (
                        regional_events.length > 0 &&
                        ($rootScope.user_role == USER_TEAM_COACH ||
                            $rootScope.user_role == USER_SUPERVISOR_COACH ||
                            $rootScope.user_role == USER_SUPER_ADMIN ||
                            $rootScope.user_role == USER_LEAGUE_ADMIN)
                    ) {
                        var is_show = events[ip].is_show;

                        if (is_show == true) {
                            for (let i = 0; i < regional_events.length; i++) {
                                sub_html_registrations +=
                                    '<li>' +
                                    '<a data-match-route="/registration-district/' +
                                    regional_events[i].id +
                                    '" href="#/registration-district/' +
                                    regional_events[i].id +
                                    '">' +
                                    regional_events[i].type +
                                    '</a>' +
                                    '</li>';
                            }
                        }
                    }

                    if (
                        beginner_events.length > 0 &&
                        ($rootScope.user_role == USER_TEAM_COACH ||
                            $rootScope.user_role == USER_SUPERVISOR_COACH ||
                            $rootScope.user_role == USER_SUPER_ADMIN ||
                            $rootScope.user_role == USER_LEAGUE_ADMIN)
                    ) {
                        var is_show = events[ip].is_show;

                        if (is_show == true) {
                            for (let i = 0; i < beginner_events.length; i++) {
                                sub_html_registrations +=
                                    '<li>' +
                                    '<a data-match-route="/registration-beginner/' +
                                    beginner_events[i].id +
                                    '" href="#/registration-beginner/' +
                                    beginner_events[i].id +
                                    '">' +
                                    beginner_events[i].name +
                                    '</a>' +
                                    '</li>';
                            }
                        }
                    }

                    sub_html_registrations += '</ul>' + '</li>';
                }

                // Clubs
                var sub_html_clubs = '';

                for (var ip = 0; ip < events.length; ip++) {
                    season_event = events[ip].season_event;
                    golden_age_events = events[ip].golden_age_events;

                    if (golden_age_events.length == 0) {
                        continue;
                    }

                    sub_html_clubs +=
                        '<li>' +
                        '<a href="" class="dropdown-toggle">' +
                        '<span>' +
                        season_event.name +
                        '</span>' +
                        '<i class="fa fa-angle-right drop-icon"></i>' +
                        '</a>' +
                        '<ul class="submenu">';

                    if (golden_age_events.length > 0) {
                        for (let i = 0; i < golden_age_events.length; i++) {
                            sub_html_clubs +=
                                '<li>' +
                                '<a data-match-route="/clubs/' +
                                golden_age_events[i].id +
                                '" href="#/clubs/' +
                                golden_age_events[i].id +
                                '">' +
                                golden_age_events[i].type +
                                '</a>' +
                                '</li>';
                        }
                    }

                    sub_html_clubs += '</ul>' + '</li>';
                }

                // payments
                var html_payment = '';
                for (var ip = 0; ip < events.length; ip++) {
                    season_event = events[ip].season_event;
                    // golden_age_events = events[ip].golden_age_events;
                    // training_scheme_events = events[ip].training_scheme_events;
                    summer_scheme_events = events[ip].summer_scheme_events;
                    district_events = events[ip].district_events;
                    pl_junior_events = events[ip].pl_junior_events;
                    regional_events = events[ip].regional_events;
                    beginner_events = events[ip].beginner_events;
                    elderly_events = events[ip].elderly_events;

                    if (
                        summer_scheme_events.length == 0 &&
                        district_events.length == 0 &&
                        pl_junior_events.length == 0 &&
                        regional_events.length == 0 &&
                        beginner_events.length == 0 &&
                        elderly_events.length == 0
                    ) {
                        console.log('Payment empty');
                        continue;
                    }

                    html_payment +=
                        '<li>' +
                        '<a href="" class="dropdown-toggle">' +
                        '<span>' +
                        season_event.name +
                        '</span>' +
                        '<i class="fa fa-angle-right drop-icon"></i>' +
                        '</a>' +
                        '<ul class="submenu">';

                    console.warn($rootScope.user_role);

                    if (summer_scheme_events.length > 0) {
                        for (let i = 0; i < summer_scheme_events.length; i++) {
                            html_payment +=
                                '<li>' +
                                '<a data-match-route="/payments/summer-scheme/' +
                                summer_scheme_events[i].id +
                                '" href="#/payments/summer-scheme/' +
                                summer_scheme_events[i].id +
                                '">' +
                                summer_scheme_events[i].type +
                                '</a>' +
                                '</li>';
                        }
                    }

                    if (district_events.length > 0) {
                        for (let i = 0; i < district_events.length; i++) {
                            html_payment +=
                                '<li>' +
                                '<a data-match-route="/payments/district/' +
                                district_events[i].id +
                                '" href="#/payments/district/' +
                                district_events[i].id +
                                '">' +
                                district_events[i].type +
                                '</a>' +
                                '</li>';
                        }
                    }

                    if (pl_junior_events.length > 0) {
                        for (let i = 0; i < pl_junior_events.length; i++) {
                            html_payment +=
                                '<li>' +
                                '<a data-match-route="/payments/pl-junior/' +
                                pl_junior_events[i].id +
                                '" href="#/payments/pl-junior/' +
                                pl_junior_events[i].id +
                                '">' +
                                pl_junior_events[i].name +
                                '</a>' +
                                '</li>';
                        }
                    }
                    if (elderly_events.length > 0) {
                        for (let i = 0; i < elderly_events.length; i++) {
                            html_payment +=
                                '<li>' +
                                '<a data-match-route="/payments/pl-junior/' +
                                elderly_events[i].id +
                                '" href="#/payments/pl-junior/' +
                                elderly_events[i].id +
                                '">' +
                                elderly_events[i].name +
                                '</a>' +
                                '</li>';
                        }
                    }

                    if (regional_events.length > 0) {
                        for (let i = 0; i < regional_events.length; i++) {
                            html_payment +=
                                '<li>' +
                                '<a data-match-route="/payments/district/' +
                                regional_events[i].id +
                                '" href="#/payments/district/' +
                                regional_events[i].id +
                                '">' +
                                regional_events[i].type +
                                '</a>' +
                                '</li>';
                        }
                    }

                    if (beginner_events.length > 0) {
                        for (let i = 0; i < beginner_events.length; i++) {
                            html_payment +=
                                '<li>' +
                                '<a data-match-route="/payments/district/' +
                                beginner_events[i].id +
                                '" href="#/payments/district/' +
                                beginner_events[i].id +
                                '">' +
                                beginner_events[i].name +
                                '</a>' +
                                '</li>';
                        }
                    }

                    html_payment += '</ul>' + '</li>';
                }

                // seasons
                var html_season = '';
                for (var ip = 0; ip < events.length; ip++) {
                    season_event = events[ip].season_event;
                    golden_age_events = events[ip].golden_age_events;

                    if (golden_age_events.length == 0) {
                        continue;
                    }

                    html_season +=
                        '<li>' +
                        '<a href="" class="dropdown-toggle">' +
                        '<span>' +
                        season_event.name +
                        '</span>' +
                        '<i class="fa fa-angle-right drop-icon"></i>' +
                        '</a>' +
                        '<ul class="submenu">';

                    if (golden_age_events.length > 0) {
                        for (let i = 0; i < golden_age_events.length; i++) {
                            html_season +=
                                '<li>' +
                                '<a data-match-route="/seasons/' +
                                golden_age_events[i].id +
                                '" href="#/seasons/' +
                                golden_age_events[i].id +
                                '">' +
                                golden_age_events[i].type +
                                '</a>' +
                                '</li>';
                        }
                    }

                    html_season += '</ul>' + '</li>';
                }

                // admin club manager
                var html_admin_club_manager = '';
                for (var ip = 0; ip < events.length; ip++) {
                    season_event = events[ip].season_event;
                    golden_age_events = events[ip].golden_age_events;

                    if (golden_age_events.length == 0) {
                        continue;
                    }

                    html_admin_club_manager +=
                        '<li>' +
                        '<a href="" class="dropdown-toggle">' +
                        '<span>' +
                        season_event.name +
                        '</span>' +
                        '<i class="fa fa-angle-right drop-icon"></i>' +
                        '</a>' +
                        '<ul class="submenu">';

                    if (golden_age_events.length > 0) {
                        for (let i = 0; i < golden_age_events.length; i++) {
                            html_admin_club_manager +=
                                '<li>' +
                                '<a data-match-route="/admin_club_manager/' +
                                golden_age_events[i].id +
                                '" href="#/admin_club_manager/' +
                                golden_age_events[i].id +
                                '">' +
                                golden_age_events[i].type +
                                '</a>' +
                                '</li>';
                        }
                    }

                    html_admin_club_manager += '</ul>' + '</li>';
                }

                // leagues
                var html_league = '';
                for (var ip = 0; ip < events.length; ip++) {
                    season_event = events[ip].season_event;
                    golden_age_events = events[ip].golden_age_events;

                    if (golden_age_events.length == 0) {
                        continue;
                    }

                    html_league +=
                        '<li>' +
                        '<a href="" class="dropdown-toggle">' +
                        '<span>' +
                        season_event.name +
                        '</span>' +
                        '<i class="fa fa-angle-right drop-icon"></i>' +
                        '</a>' +
                        '<ul class="submenu">';

                    if (golden_age_events.length > 0) {
                        for (let i = 0; i < golden_age_events.length; i++) {
                            html_league +=
                                '<li>' +
                                '<a data-match-route="/leagues/' +
                                golden_age_events[i].id +
                                '" href="#/leagues/' +
                                golden_age_events[i].id +
                                '">' +
                                golden_age_events[i].type +
                                '</a>' +
                                '</li>';
                        }
                    }

                    html_league += '</ul>' + '</li>';
                }

                // cgroups
                var html_cgroup = '';
                for (var ip = 0; ip < events.length; ip++) {
                    season_event = events[ip].season_event;
                    golden_age_events = events[ip].golden_age_events;

                    if (golden_age_events.length == 0) {
                        continue;
                    }

                    html_cgroup +=
                        '<li>' +
                        '<a href="" class="dropdown-toggle">' +
                        '<span>' +
                        season_event.name +
                        '</span>' +
                        '<i class="fa fa-angle-right drop-icon"></i>' +
                        '</a>' +
                        '<ul class="submenu">';

                    if (golden_age_events.length > 0) {
                        for (let i = 0; i < golden_age_events.length; i++) {
                            html_cgroup +=
                                '<li>' +
                                '<a data-match-route="/cgroups/' +
                                golden_age_events[i].id +
                                '" href="#/cgroups/' +
                                golden_age_events[i].id +
                                '">' +
                                golden_age_events[i].type +
                                '</a>' +
                                '</li>';
                        }
                    }

                    html_cgroup += '</ul>' + '</li>';
                }

                var html_cgroup_admin = '';
                for (var ip = 0; ip < events.length; ip++) {
                    season_event = events[ip].season_event;
                    golden_age_events = events[ip].golden_age_events;

                    if (golden_age_events.length == 0) {
                        continue;
                    }

                    html_cgroup_admin +=
                        '<li>' +
                        '<a href="" class="dropdown-toggle">' +
                        '<span>' +
                        season_event.name +
                        '</span>' +
                        '<i class="fa fa-angle-right drop-icon"></i>' +
                        '</a>' +
                        '<ul class="submenu">';

                    if (golden_age_events.length > 0) {
                        for (let i = 0; i < golden_age_events.length; i++) {
                            html_cgroup_admin +=
                                '<li>' +
                                '<a data-match-route="/cgroups_admin/' +
                                golden_age_events[i].id +
                                '" href="#/cgroups_admin/' +
                                golden_age_events[i].id +
                                '">' +
                                golden_age_events[i].type +
                                '</a>' +
                                '</li>';
                        }
                    }

                    html_cgroup_admin += '</ul>' + '</li>';
                }

                // team sheet
                var html_team_sheet = '';
                for (var ip = 0; ip < events.length; ip++) {
                    season_event = events[ip].season_event;
                    golden_age_events = events[ip].golden_age_events;

                    if (golden_age_events.length == 0) {
                        continue;
                    }

                    html_team_sheet +=
                        '<li>' +
                        '<a href="" class="dropdown-toggle">' +
                        '<span>' +
                        season_event.name +
                        '</span>' +
                        '<i class="fa fa-angle-right drop-icon"></i>' +
                        '</a>' +
                        '<ul class="submenu">';

                    if (golden_age_events.length > 0) {
                        for (let i = 0; i < golden_age_events.length; i++) {
                            html_team_sheet +=
                                '<li>' +
                                '<a data-match-route="/team_sheet/' +
                                golden_age_events[i].id +
                                '" href="#/team_sheet/' +
                                golden_age_events[i].id +
                                '">' +
                                golden_age_events[i].type +
                                '</a>' +
                                '</li>';
                        }
                    }

                    html_team_sheet += '</ul>' + '</li>';
                }

                // Registrations
                var html_registrations_component =
                    '<a href="" class="dropdown-toggle">' +
                    '<i class="fa fa-cube"></i>' +
                    '<span>' +
                    $rootScope.translate.REGISTRATION +
                    '</span>' +
                    '<i class="fa fa-angle-right drop-icon"></i>' +
                    '</a>' +
                    '<ul class="submenu">' +
                    sub_html_registrations +
                    '</ul>';

                // Clubs
                var html_clubs_component =
                    '<a href="" class="dropdown-toggle">' +
                    '<i class="fa fa-cube"></i>' +
                    '<span>' +
                    $rootScope.translate.CLUBS +
                    '</span>' +
                    '<i class="fa fa-angle-right drop-icon"></i>' +
                    '</a>' +
                    '<ul class="submenu">' +
                    sub_html_clubs +
                    '</ul>';

                // //  Club Coachs
                // var html_club_coach_component =
                //     '<a href="" class="dropdown-toggle">' +
                //     '<i class="fa fa-cube"></i>' +
                //     '<span>Clubs</span>' +
                //     '<i class="fa fa-angle-right drop-icon"></i>' +
                //     '</a>' +
                //     '<ul class="submenu">' +
                //     sub_html_club_coach +
                //     '</ul>';

                // Setting
                var html_setting_component =
                    '<a href="" class="dropdown-toggle" cy-data="menu-settings">' +
                    // '<i class="fa fa-cogs" aria-hidden="true"></i>' +
                    '<img src="images/sidemenu-icon/settings.png" class="sidemenu-icon">' +
                    '<span  class="sidemenu-label">' +
                    $rootScope.translate.SETTINGS +
                    '</span>' +
                    '<i class="fa fa-angle-right drop-icon"></i>' +
                    '</a>' +
                    '<ul class="submenu">' +
                    '<li>' +
                    '<a data-match-route="/settings/general_settings" href="#/settings/general_settings" cy-data="settings-general-settings">' +
                    'General Settings' +
                    '</a>' +
                    '</li>' +
                    // <a href="#/template" cy-data="menu-email-editor">
					// 						<!-- <i class="fa fa-child"></i> -->
					// 						<img class="sidemenu-icon" src="images/sidemenu-icon/email_editor.png"></img>
					// 						<span class="sidemenu-label">{{translate.SETTINGS_LIST.EMAIL_TEMPLATE}}</span>
					// 					</a>
                    '<li>' +
                    '<a href="#/template" cy-data="menu-email-editor">' +
                    $rootScope.translate.SETTINGS_LIST.EMAIL_TEMPLATE +
                    '</a>' +
                    '</li>' +
                    '<li>' +
                    '<a data-match-route="/settings/system" href="#/settings/system" cy-data="settings-systems">' +
                    $rootScope.translate.SETTINGS_LIST.SYSTEM +
                    '</a>' +
                    '</li>' +
                    '<li>' +
                    '<a data-match-route="/settings/notification_app_start" href="#/settings/notification_app_start" cy-data="settings-notification">' +
                    $rootScope.translate.SETTINGS_LIST.NOTIFICATION_APP_START +
                    '</a>' +
                    '</li>' +
                    '<li>' +
                    '<a data-match-route="/settings/comment_suggestion" href="#/settings/comment_suggestion" cy-data="settings-comment-suggestion">' +
                    $rootScope.translate.SETTINGS_LIST.COMMENT_SUGGESTION +
                    '</a>' +
                    '</li>' +
                    '</ul>';

                //  // Club Coaches
                //  var html_club_coaches_component =
                //  '<a href="" class="dropdown-toggle">' +
                //      '<i class="fa fa-user"></i>' +
                //          '<span>Coaches</span>' +
                //      '<i class="fa fa-angle-right drop-icon"></i>' +
                //  '</a>' +
                //  '<ul class="submenu">' +
                //      sub_html_club_coaches +
                //  '</ul>';

                // Tables

                var html_super_tables_component =
                    '<li>' +
                    '<a data-match-route="/tables/managerial_coach" href="#/tables/managerial_coach">' +
                    $rootScope.translate.TABLES_LIST.MANAGERIAL_COACH +
                    '</a>' +
                    '</li>' +
                    '<li>' +
                    '<a data-match-route="/tables/finance_team" href="#/tables/finance_team">' +
                    $rootScope.translate.TABLES_LIST.FINANCE_TEAM +
                    '</a>' +
                    '</li>' +
                    '<li>' +
                    '<a data-match-route="/tables/grassroots_finance" href="#/tables/grassroots_finance">' +
                    $rootScope.translate.TABLES_LIST.GRASSROOTS_FINANCE +
                    '</a>' +
                    '</li>' +
                    '<li>' +
                    '<a data-match-route="/tables/league_admin" href="#/tables/league_admin">' +
                    $rootScope.translate.TABLES_LIST.LEAGUE_ADMINS +
                    '</a>' +
                    '</li>' +
                    '<li>' +
                    '<a data-match-route="/tables/super_admin" href="#/tables/super_admin">' +
                    $rootScope.translate.TABLES_LIST.SUPER_ADMINS +
                    '</a>' +
                    '</li>' +
                    '<li>' +
                    '<a data-match-route="/tables/user_editor" href="#/tables/user_editor">' +
                    $rootScope.translate.TABLES_LIST.USERS +
                    '</a>' +
                    '</li>';
                if ($rootScope.user_id == logUserId) {
                    html_super_tables_component +=
                        '<li>' +
                        '<a data-match-route="/tables/logs" href="#/tables/logs">' +
                        $rootScope.translate.TABLES_LIST.LOG +
                        '</a>' +
                        '</li>';
                }

                var html_educations_component = 
                    '<a href="" class="dropdown-toggle" cy-data="menu-educations">' +
                    '<img src="images/sidemenu-icon/education.png" class="sidemenu-icon">' +
                    '<span class="sidemenu-label">' +
                    $rootScope.translate.EDUCATION +
                    '</span>' +
                    '<i class="fa fa-angle-right drop-icon"></i>' +
                    '</a>' +
                    '<ul class="submenu">' +
                    '<li >' +
                    '<a data-match-route="/respects" href="#/respects" cy-data="menu-respect">' +
                    $rootScope.translate.RESPECT +
                    '</a>' +
                    '</li>' +
                    '<li>' +
                    '<a  data-match-route="/documents" href="#/documents" cy-data="menu-document">' +
                    $rootScope.translate.DOCUMENT +
                    '</a>' +
                    '</li>'
                    '</ul>';


                var html_tables_component =
                    '<a href="" class="dropdown-toggle" cy-data="menu-tables">' +
                    // '<i class="fa fa-table"></i>' +
                    '<img src="images/sidemenu-icon/table.png" class="sidemenu-icon">' +
                    '<span class="sidemenu-label">' +
                    $rootScope.translate.TABLES +
                    '</span>' +
                    '<i class="fa fa-angle-right drop-icon"></i>' +
                    '</a>' +
                    '<ul class="submenu">' +
                    '<li >' +
                    '<a data-match-route="/tables/event_editor" href="#/tables/event_editor" cy-data="tables-events">' +
                    $rootScope.translate.TABLES_LIST.EVENTS +
                    '</a>' +
                    '</li>' +
                    '<li>' +
                    '<a data-match-route="/tables/user_editor" href="#/tables/user_editor" cy-data="tables-user">' +
                    $rootScope.translate.TABLES_LIST.USERS +
                    '</a>' +
                    '</li>' +
                    '<li>' +
                    '<a data-match-route="/tables/club_editor" href="#/tables/club_editor" cy-data="tables-clubs">' +
                    $rootScope.translate.TABLES_LIST.CLUBS +
                    '</a>' +
                    '</li>' +
                    '<li>' +
                    '<a data-match-route="/tables/venues_editor" href="#/tables/venues_editor" cy-data="tables-venues">' +
                    $rootScope.translate.TABLES_LIST.VENUES +
                    '</a>' +
                    '</li>' +
                    '<li>' +
                    '<a data-match-route="/tables/sponsor" href="#/tables/sponsor" cy-data="tables-sponsors">' +
                    $rootScope.translate.TABLES_LIST.SPONSORS +
                    '</a>' +
                    '</li>' +
                                        '<li>' +
                    '<a data-match-route="/tables/products" href="#/tables/products" cy-data="tables-products">' +
                    $rootScope.translate.TABLES_LIST.PRODUCTS +
                    '</a>' +
                    '</li>' +
                    '<li>' +
                    '<a data-match-route="/tables/non_training_days" href="#/tables/non_training_days" cy-data="tables-non-training-days">' +
                    $rootScope.translate.TABLES_LIST.NON_TRAINING_DAYS +
                    '</a>' +
                    '</li>' +
                    '<li>' +
                    '<a data-match-route="/tables/coach-certificate" href="#/tables/coach-certificate" cy-data="tables-coach-certificate">' +
                    $rootScope.translate.TABLES_LIST.COACH_CERTIFICATE +
                    '</a>' +
                    '</li>' +
                    '</ul>';
                /*     '<li>' +
                    '<a data-match-route="/tables/player_editor" href="#/tables/player_editor">' +
                    $rootScope.translate.TABLES_LIST.PLAYERS +
                    '</a>' +
                    '</li>' +
                    '<li>' +
                    '<a data-match-route="/tables/parent_editor" href="#/tables/parent_editor">' +
                    $rootScope.translate.TABLES_LIST.PARENTS +
                    '</a>' +
                    '</li>' +
                    '<li>' +
                    '<a data-match-route="/tables/coach" href="#/tables/coach">' +
                    $rootScope.translate.TABLES_LIST.COACHES +
                    '</a>' +
                    '</li>' +
                    '<li>' +
                    '<a data-match-route="/tables/manager_editor" href="#/tables/manager_editor">' +
                    $rootScope.translate.TABLES_LIST.CLUB_MANAGERS +
                    '</a>' +
                    '</li>' +
                    '<li>' +
                    '<a data-match-route="/tables/shipping-user" href="#/tables/shipping-user">' +
                    $rootScope.translate.TABLES_LIST.SHIPPING +
                    '</a>' +
                    '</li>' +
                    '<li>' +
                    '<a data-match-route="/tables/supervisor" href="#/tables/supervisor">' +
                    $rootScope.translate.TABLES_LIST.SUPERVISOR +
                    '</a>' +
                    '</li>' +
                    '<li>' +
                    '<a data-match-route="/tables/supervisor_coach" href="#/tables/supervisor_coach">' +
                    $rootScope.translate.TABLES_LIST.SUPERVISOR_COACH +
                    '</a>' +
                    '</li>';
                if ($rootScope.user_role == USER_SUPER_ADMIN) {
                    html_tables_component += html_super_tables_component;
                }
                html_tables_component += */



                if (IS_PAYMENT == true) {
                    var html_payments_component =
                        '<a href="" class="dropdown-toggle">' +
                        '<i class="fa fa-money"></i>' +
                        '<span>' +
                        $rootScope.translate.PAYMENTS +
                        '</span>' +
                        '<i class="fa fa-angle-right drop-icon"></i>' +
                        '</a>' +
                        '<ul class="submenu">' +
                        html_payment +
                        '</ul>';

                    // // Payment_Clubs
                    // var html_payment_clubs_component =
                    //     '<a href="" class="dropdown-toggle">' +
                    //     '<i class="fa fa-money"></i>' +
                    //     '<span>' +
                    //     $rootScope.translate.PAYMENTS +
                    //     '</span>' +
                    //     '<i class="fa fa-angle-right drop-icon"></i>' +
                    //     '</a>' +
                    //     '<ul class="submenu">' +
                    //     sub_html_payment_clubs +
                    //     '</ul>';

                    // Payment_Teams
                    // var html_payment_teams_component =
                    //     '<a href="" class="dropdown-toggle">' +
                    //     '<i class="fa fa-money"></i>' +
                    //     '<span>' +
                    //     $rootScope.translate.PAYMENTS +
                    //     '</span>' +
                    //     '<i class="fa fa-angle-right drop-icon"></i>' +
                    //     '</a>' +
                    //     '<ul class="submenu">' +
                    //     sub_html_payment_teams +
                    //     '</ul>';
                }

                var html_seasons_component =
                    '<a href="" class="dropdown-toggle">' +
                    '<i class="fa fa-group"></i>' +
                    '<span>' +
                    $rootScope.translate.SEASON +
                    '</span>' +
                    '<i class="fa fa-angle-right drop-icon"></i>' +
                    '</a>' +
                    '<ul class="submenu">' +
                    html_season +
                    '</ul>';

                var html_admin_club_manager_component =
                    '<a href="" class="dropdown-toggle">' +
                    '<i class="fa fa-group"></i>' +
                    '<span>' +
                    $rootScope.translate.CLUBS +
                    '</span>' +
                    '<i class="fa fa-angle-right drop-icon"></i>' +
                    '</a>' +
                    '<ul class="submenu">' +
                    html_admin_club_manager +
                    '</ul>';

                var html_leagues_component =
                    '<a href="" class="dropdown-toggle">' +
                    '<i class="fa fa-trophy"></i>' +
                    '<span>' +
                    $rootScope.translate.LEAGUE +
                    '</span>' +
                    '<i class="fa fa-angle-right drop-icon"></i>' +
                    '</a>' +
                    '<ul class="submenu">' +
                    html_league +
                    '</ul>';

                var html_cgroups_component =
                    '<a href="" class="dropdown-toggle">' +
                    '<i class="fa fa-group"></i>' +
                    '<span>' +
                    $rootScope.translate.YEAR_GROUPS +
                    '</span>' +
                    '<i class="fa fa-angle-right drop-icon"></i>' +
                    '</a>' +
                    '<ul class="submenu">' +
                    html_cgroup +
                    '</ul>';

                var html_cgroups_admin_component =
                    '<a href="" class="dropdown-toggle">' +
                    '<i class="fa fa-group"></i>' +
                    '<span>' +
                    $rootScope.translate.YEAR_GROUPS +
                    '</span>' +
                    '<i class="fa fa-angle-right drop-icon"></i>' +
                    '</a>' +
                    '<ul class="submenu">' +
                    html_cgroup_admin +
                    '</ul>';

                var html_team_sheet_component =
                    '<a href="" class="dropdown-toggle">' +
                    '<i class="fa fa-list-ul"></i>' +
                    '<span>' +
                    $rootScope.translate.TEAM_SHEET +
                    '</span>' +
                    '<i class="fa fa-angle-right drop-icon"></i>' +
                    '</a>' +
                    '<ul class="submenu">' +
                    html_team_sheet +
                    '</ul>';

                var html_messages = '';
                for (var ip = 0; ip < events.length; ip++) {
                    season_event = events[ip].season_event;
                    golden_age_events = events[ip].golden_age_events;
                    training_scheme_events = events[ip].training_scheme_events;
                    summer_scheme_events = events[ip].summer_scheme_events;
                    district_events = events[ip].district_events;
                    pl_junior_events = events[ip].pl_junior_events;
                    elderly_events = events[ip].elderly_events;
                    beginner_events = events[ip].beginner_events;
                    regional_events = events[ip].regional_events;

                    if (
                        golden_age_events.length == 0 &&
                        training_scheme_events.length == 0 &&
                        summer_scheme_events.length == 0 &&
                        district_events.length == 0 &&
                        pl_junior_events.length == 0 &&
                        regional_events.length == 0
                    ) {
                        continue;
                    }

                    html_messages +=
                        '<li>' +
                        '<a href="" class="dropdown-toggle">' +
                        '<span>' +
                        season_event.name +
                        '</span>' +
                        '<i class="fa fa-angle-right drop-icon"></i>' +
                        '</a>' +
                        '<ul class="submenu">';

                    if (golden_age_events.length > 0) {
                        for (let i = 0; i < golden_age_events.length; i++) {
                            html_messages +=
                                '<li>' +
                                '<a data-match-route="/messages/' +
                                golden_age_events[i].id +
                                '" href="#/messages/' +
                                golden_age_events[i].id +
                                '">' +
                                golden_age_events[i].type +
                                '</a>' +
                                '</li>';
                        }
                    }

                    if (training_scheme_events.length > 0) {
                        for (
                            let i = 0;
                            i < training_scheme_events.length;
                            i++
                        ) {
                            html_messages +=
                                '<li>' +
                                '<a data-match-route="/messages/' +
                                training_scheme_events[i].id +
                                '" href="#/messages/' +
                                training_scheme_events[i].id +
                                '">' +
                                training_scheme_events[i].type +
                                '</a>' +
                                '</li>';
                        }
                    }

                    if (summer_scheme_events.length > 0) {
                        for (let i = 0; i < summer_scheme_events.length; i++) {
                            html_messages +=
                                '<li>' +
                                '<a data-match-route="/messages/' +
                                summer_scheme_events[i].id +
                                '" href="#/messages/' +
                                summer_scheme_events[i].id +
                                '">' +
                                summer_scheme_events[i].type +
                                '</a>' +
                                '</li>';
                        }
                    }

                    if (beginner_events.length > 0) {
                        for (let i = 0; i < beginner_events.length; i++) {
                            html_messages +=
                                '<li>' +
                                '<a data-match-route="/messages/' +
                                beginner_events[i].id +
                                '" href="#/messages/' +
                                beginner_events[i].id +
                                '">' +
                                beginner_events[i].name +
                                '</a>' +
                                '</li>';
                        }
                    }

                    if (district_events.length > 0) {
                        for (let i = 0; i < district_events.length; i++) {
                            html_messages +=
                                '<li>' +
                                '<a data-match-route="/messages/' +
                                district_events[i].id +
                                '" href="#/messages/' +
                                district_events[i].id +
                                '">' +
                                district_events[i].type +
                                '</a>' +
                                '</li>';
                        }
                    }
                    if (regional_events.length > 0) {
                        for (let i = 0; i < regional_events.length; i++) {
                            html_messages +=
                                '<li>' +
                                '<a data-match-route="/messages/' +
                                regional_events[i].id +
                                '" href="#/messages/' +
                                regional_events[i].id +
                                '">' +
                                regional_events[i].type +
                                '</a>' +
                                '</li>';
                        }
                    }

                    if (pl_junior_events.length > 0) {
                        for (let i = 0; i < pl_junior_events.length; i++) {
                            html_messages +=
                                '<li>' +
                                '<a data-match-route="/messages/' +
                                pl_junior_events[i].id +
                                '" href="#/messages/' +
                                pl_junior_events[i].id +
                                '">' +
                                pl_junior_events[i].name +
                                '</a>' +
                                '</li>';
                        }
                    }
                    if (elderly_events.length > 0) {
                        for (let i = 0; i < elderly_events.length; i++) {
                            html_messages +=
                                '<li>' +
                                '<a data-match-route="/messages/' +
                                elderly_events[i].id +
                                '" href="#/messages/' +
                                elderly_events[i].id +
                                '">' +
                                elderly_events[i].name +
                                '</a>' +
                                '</li>';
                        }
                    }

                    html_messages += '</ul>' + '</li>';
                }

                // Courses
                var html_course = '';
                for (var ip = 0; ip < events.length; ip++) {
                    season_event = events[ip].season_event;
                    summer_scheme_events = events[ip].summer_scheme_events;
                    district_events = events[ip].district_events;
                    pl_junior_events = events[ip].pl_junior_events;
                    regional_events = events[ip].regional_events;
                    beginner_events = events[ip].beginner_events;
                    elderly_events = events[ip].elderly_events;
                    golden_age_events = events[ip].golden_age_events;

                    if (
                        summer_scheme_events.length == 0 &&
                        district_events.length == 0 &&
                        pl_junior_events.length == 0 &&
                        regional_events.length == 0 &&
                        beginner_events.length == 0 &&
                        elderly_events.length == 0 &&
                        golden_age_events.length == 0
                    ) {
                        continue;
                    }

                    html_course +=
                        '<li>' +
                        '<a href="" class="dropdown-toggle">' +
                        '<span>' +
                        season_event.name +
                        '</span>' +
                        '<i class="fa fa-angle-right drop-icon"></i>' +
                        '</a>' +
                        '<ul class="submenu">';

                    if (
                        summer_scheme_events.length > 0 &&
                        $rootScope.user_role != USER_SUPERVISOR
                    ) {
                        for (let i = 0; i < summer_scheme_events.length; i++) {
                            html_course +=
                                '<li>' +
                                '<a data-match-route="/event/' +
                                summer_scheme_events[i].id +
                                '/course" href="#/event/' +
                                summer_scheme_events[i].id +
                                '/course">' +
                                summer_scheme_events[i].type +
                                '</a>' +
                                '</li>';
                        }
                    }

                    if (district_events.length > 0) {
                        for (let i = 0; i < district_events.length; i++) {
                            html_course +=
                                '<li>' +
                                '<a data-match-route="/event/' +
                                district_events[i].id +
                                '/course" href="#/event/' +
                                district_events[i].id +
                                '/team_district">' +
                                district_events[i].type +
                                '</a>' +
                                '</li>';
                        }
                    }

                    if (
                        pl_junior_events.length > 0 &&
                        $rootScope.user_role != USER_SUPERVISOR
                    ) {
                        for (let i = 0; i < pl_junior_events.length; i++) {
                            html_course +=
                                '<li>' +
                                '<a data-match-route="/event/' +
                                pl_junior_events[i].id +
                                '/course" href="#/event/' +
                                pl_junior_events[i].id +
                                '/course">' +
                                pl_junior_events[i].name +
                                '</a>' +
                                '</li>';
                        }
                    }
                    if (
                        elderly_events.length > 0 &&
                        $rootScope.user_role != USER_SUPERVISOR
                    ) {
                        for (let i = 0; i < elderly_events.length; i++) {
                            html_course +=
                                '<li>' +
                                '<a data-match-route="/event/' +
                                elderly_events[i].id +
                                '/course" href="#/event/' +
                                elderly_events[i].id +
                                '/course">' +
                                elderly_events[i].name +
                                '</a>' +
                                '</li>';
                        }
                    }

                    if (regional_events.length > 0) {
                        for (let i = 0; i < regional_events.length; i++) {
                            html_course +=
                                '<li>' +
                                '<a data-match-route="/event/' +
                                regional_events[i].id +
                                '/course" href="#/event/' +
                                regional_events[i].id +
                                '/team_district">' +
                                regional_events[i].type +
                                '</a>' +
                                '</li>';
                        }
                    }

                    if (beginner_events.length > 0) {
                        for (let i = 0; i < beginner_events.length; i++) {
                            html_course +=
                                '<li>' +
                                '<a data-match-route="/event/' +
                                beginner_events[i].id +
                                '/course" href="#/event/' +
                                beginner_events[i].id +
                                '/team_district">' +
                                beginner_events[i].name +
                                '</a>' +
                                '</li>';
                        }
                    }

                    if (golden_age_events.length > 0) {
                        for (let i = 0; i < golden_age_events.length; i++) {
                            html_course +=
                                '<li>' +
                                '<a data-match-route="/event/' +
                                golden_age_events[i].id +
                                '/team_golden_age" href="#/event/' +
                                golden_age_events[i].id +
                                '/team_golden_age">' +
                                golden_age_events[i].name +
                                '</a>' +
                                '</li>';
                        }
                    }

                    html_course += '</ul>' + '</li>';
                }

                var html_coach_message_html = '';

                for (var ip = 0; ip < events.length; ip++) {
                    season_event = events[ip].season_event;
                    training_scheme_events = events[ip].training_scheme_events;
                    summer_scheme_events = events[ip].summer_scheme_events;
                    district_events = events[ip].district_events;
                    // pl_junior_events = events[ip].pl_junior_events;

                    if (
                        training_scheme_events.length == 0 &&
                        summer_scheme_events.length == 0 &&
                        district_events.length == 0
                    ) {
                        continue;
                    }

                    html_coach_message_html +=
                        '<li>' +
                        '<a href="" class="dropdown-toggle">' +
                        '<span>' +
                        season_event.name +
                        '</span>' +
                        '<i class="fa fa-angle-right drop-icon"></i>' +
                        '</a>' +
                        '<ul class="submenu">';

                    if (training_scheme_events.length > 0) {
                        for (
                            let i = 0;
                            i < training_scheme_events.length;
                            i++
                        ) {
                            html_coach_message_html +=
                                '<li>' +
                                '<a data-match-route="/message-coach/course_coach_message/' +
                                training_scheme_events[i].id +
                                '" href="#/message-coach/course_coach_message/' +
                                training_scheme_events[i].id +
                                '">' +
                                training_scheme_events[i].type +
                                '</a>' +
                                '</li>';
                        }
                    }

                    if (summer_scheme_events.length > 0) {
                        for (let i = 0; i < summer_scheme_events.length; i++) {
                            html_coach_message_html +=
                                '<li>' +
                                '<a data-match-route="/message-coach/course_coach_message/' +
                                summer_scheme_events[i].id +
                                '" href="#/message-coach/course_coach_message/' +
                                summer_scheme_events[i].id +
                                '">' +
                                summer_scheme_events[i].type +
                                '</a>' +
                                '</li>';
                        }
                    }

                    if (district_events.length > 0) {
                        for (let i = 0; i < district_events.length; i++) {
                            html_coach_message_html +=
                                '<li>' +
                                '<a data-match-route="/message-coach/message-team-coach/' +
                                district_events[i].id +
                                '" href="#/message-coach/message-team-coach/' +
                                district_events[i].id +
                                '">' +
                                district_events[i].type +
                                '</a>' +
                                '</li>';
                        }
                    }

                    html_coach_message_html += '</ul>' + '</li>';
                }

                var html_team_sheet_component =
                    '<a href="" class="dropdown-toggle">' +
                    '<i class="fa fa-list-ul"></i>' +
                    '<span>' +
                    $rootScope.translate.TEAM_SHEET +
                    '</span>' +
                    '<i class="fa fa-angle-right drop-icon"></i>' +
                    '</a>' +
                    '<ul class="submenu">' +
                    html_team_sheet +
                    '</ul>';
                var html_messages_component =
                    '<a href="" class="dropdown-toggle">' +
                    '<i class="fa fa-paper-plane"></i>' +
                    '<span>' +
                    $rootScope.translate.MESSAGES +
                    '</span>' +
                    '<i class="fa fa-angle-right drop-icon"></i>' +
                    '</a>' +
                    '<ul class="submenu">' +
                    html_messages +
                    '</ul>';

                var html_respect_component =
                    '<a  data-match-route="/respects" href="#/respects" cy-data="menu-respect">' +
                    // '<i class="fa fa-newspaper-o" aria-hidden="true"></i>' +
                    '<img src="images/sidemenu-icon/respect.png" class="sidemenu-icon">' +
                    '<span class="sidemenu-label">' +
                    $rootScope.translate.RESPECT +
                    '</span>' +
                    '</a>';

                var html_shop_product_component =
                    '<a  data-match-route="/shop_products" href="#/shop_products">' +
                    // '<i class="fa fa-shopping-cart" aria-hidden="true"></i>' +
                    '<img src="images/sidemenu-icon/shop.png" class="sidemenu-icon">' +
                    '<span class="sidemenu-label">' +
                    $rootScope.translate.SHOP +
                    '</span>' +
                    '</a>';

                var html_document_component =
                    '<a  data-match-route="/documents" href="#/documents" cy-data="menu-document">' +
                    // '<i class="fa fa-book" aria-hidden="true"></i>' +
                    '<img src="images/sidemenu-icon/document.png" class="sidemenu-icon">' +
                    '<span class="sidemenu-label">' +
                    $rootScope.translate.DOCUMENT +
                    '</span>' +
                    '</a>';

                var html_course_component =
                    '<a href="" class="dropdown-toggle">' +
                    // '<i class="fa fa-book"></i>' +
                    '<img src="images/sidemenu-icon/course_team.png" class="sidemenu-icon">' +
                    '<span class="sidemenu-label">' +
                    $rootScope.translate.COURSE +
                    '</span>' +
                    '<i class="fa fa-angle-right drop-icon"></i>' +
                    '</a>' +
                    '<ul class="submenu">' +
                    html_course +
                    '</ul>';

                var html_training_video_component =
                    '<a data-match-route="/training_videos" href="#/training_videos" cy-data="event-training-scheme">' +
                    // '<i class="fa-brands fa-youtube"></i>' +
                    '<img src="images/sidemenu-icon/training_scheme.png" class="sidemenu-icon">' +
                    '<span class="sidemenu-label">' +
                    $rootScope.translate.TRAINING_SCHEME +
                    '</span>' +
                    '</a>';

                var html_certificates_component =
                    '<a  data-match-route="/certificates" href="#/certificates">' +
                    '<i class="fa fa-certificate" aria-hidden="true"></i>' +
                    '<span>' +
                    $rootScope.translate.CERTIFICATE +
                    '</span>' +
                    '</a>';

                var html_report_registration =
                    '<li>' +
                    '<a data-match-route="/reports/registration" href="#/reports/registration">' +
                    $rootScope.translate.REGISTRATION +
                    '</a>' +
                    '</li>';

                var html_report_district_component =
                    '<li>' +
                    '<a data-match-route="/reports/district" href="#/reports/district">' +
                    'District' +
                    '</a>' +
                    '</li>';

                var html_report_regional_component =
                    '<li>' +
                    '<a data-match-route="/reports/regional" href="#/reports/regional">' +
                    'Regional' +
                    '</a>' +
                    '</li>';

                var html_report_beginner_component =
                    '<li>' +
                    '<a data-match-route="/reports/beginner" href="#/reports/beginner">' +
                    'Beginner' +
                    '</a>' +
                    '</li>';

                var html_report_summer_scheme_component =
                    '<li>' +
                    '<a data-match-route="/reports/summer_scheme" href="#/reports/summer_scheme">' +
                    'Summer Scheme' +
                    '</a>' +
                    '</li>';
                var html_report_pl_junior_component =
                    '<li>' +
                    '<a data-match-route="/reports/pl_junior" href="#/reports/pl_junior">' +
                    'PL Junior' +
                    '</a>' +
                    '</li>';

                var html_report_elderly_report_component =
                    '<li>' +
                    '<a data-match-route="/reports/elderly_report" href="#/reports/elderly_report">' +
                    'Elderly' +
                    '</a>' +
                    '</li>';

                var html_reports_component =
                    '<a href="" class="dropdown-toggle">' +
                    '<i class="fa fa-bar-chart-o"></i>' +
                    '<span>' +
                    $rootScope.translate.REPORT +
                    '</span>' +
                    '<i class="fa fa-angle-right drop-icon"></i>' +
                    '</a>' +
                    '<ul class="submenu">' +
                    html_report_registration +
                    html_report_summer_scheme_component +
                    html_report_district_component +
                    html_report_pl_junior_component +
                    html_report_regional_component +
                    html_report_beginner_component +
                    html_report_elderly_report_component +
                    '</ul>';
                var html_email_template_component =
                    '<a href="#/template">' +
                    // '<i class="fa fa-envelope"></i>' +
                    '<img src="images/sidemenu-icon/email_template.png" class="sidemenu-icon">' +
                    '<span>Email Editor</span>' +
                    '</a>';
                var html_coach_message_component =
                    '<a href= "" class="dropdown-toggle">' +
                    '<i class="fa fa-paper-plane"></i>' +
                    '<span>' +
                    $rootScope.translate.MESSAGES +
                    '</span>' +
                    '<i class="fa fa-angle-right drop-icon"></i>' +
                    '</a>' +
                    '<ul class="submenu">' +
                    html_coach_message_html +
                    '</ul>';

                var html_shipping_component =
                    '<a href="" class="dropdown-toggle">' +
                    '<i class="fa fa-truck" aria-hidden="true"></i>' +
                    '<span>' +
                    $rootScope.translate.SHIPPING +
                    '</span>' +
                    '<i class="fa fa-angle-right drop-icon"></i>' +
                    '</a>' +
                    '<ul class="submenu">';

                for (var ip = 0; ip < events.length; ip++) {
                    season_event = events[ip].season_event;
                    summer_scheme_events = events[ip].summer_scheme_events;
                    pl_junior_events = events[ip].pl_junior_events;
                    elderly_events = events[ip].elderly_events;

                    if (summer_scheme_events.length == 0) {
                        continue;
                    }

                    html_shipping_component +=
                        '<li>' +
                        '<a href="" class="dropdown-toggle">' +
                        '<span>' +
                        season_event.name +
                        '</span>' +
                        '<i class="fa fa-angle-right drop-icon"></i>' +
                        '</a>' +
                        '<ul class="submenu">';

                    if (summer_scheme_events.length > 0) {
                        for (let i = 0; i < summer_scheme_events.length; i++) {
                            html_shipping_component +=
                                '<li>' +
                                '<a data-match-route="/shipping/' +
                                summer_scheme_events[i].id +
                                '" href="#/shipping/' +
                                summer_scheme_events[i].id +
                                '">' +
                                summer_scheme_events[i].type +
                                '</a>' +
                                '</li>';
                        }
                    }

                    if (pl_junior_events.length > 0) {
                        for (let i = 0; i < pl_junior_events.length; i++) {
                            html_shipping_component +=
                                '<li>' +
                                '<a data-match-route="/shipping-plj/' +
                                pl_junior_events[i].id +
                                '" href="#/shipping-plj/' +
                                pl_junior_events[i].id +
                                '">' +
                                pl_junior_events[i].name +
                                '</a>' +
                                '</li>';
                        }
                    }
                    if (elderly_events.length > 0) {
                        for (let i = 0; i < elderly_events.length; i++) {
                            html_shipping_component +=
                                '<li>' +
                                '<a data-match-route="/shipping-plj/' +
                                elderly_events[i].id +
                                '" href="#/shipping-plj/' +
                                elderly_events[i].id +
                                '">' +
                                elderly_events[i].name +
                                '</a>' +
                                '</li>';
                        }
                    }

                    html_shipping_component += '</ul>' + '</li>';
                }

                var html_shipping_component = html_shipping_component + '</ul>';

                var html_self_pick_up_component =
                    '<a href="" class="dropdown-toggle">' +
                    '<i class="fa-duotone fa-box-open-full"></i>' +
                    '<span>' +
                    $rootScope.translate.SELF_PICK_UP +
                    '</span>' +
                    '<i class="fa fa-angle-right drop-icon"></i>' +
                    '</a>' +
                    '<ul class="submenu">';

                for (var ip = 0; ip < events.length; ip++) {
                    season_event = events[ip].season_event;
                    summer_scheme_events = events[ip].summer_scheme_events;

                    if (summer_scheme_events.length == 0) {
                        continue;
                    }

                    html_self_pick_up_component +=
                        '<li>' +
                        '<a href="" class="dropdown-toggle">' +
                        '<span>' +
                        season_event.name +
                        '</span>' +
                        '<i class="fa fa-angle-right drop-icon"></i>' +
                        '</a>' +
                        '<ul class="submenu">';

                    if (summer_scheme_events.length > 0) {
                        for (let i = 0; i < summer_scheme_events.length; i++) {
                            html_self_pick_up_component +=
                                '<li>' +
                                '<a data-match-route="/self-pick-up/' +
                                summer_scheme_events[i].id +
                                '" href="#/self-pick-up/' +
                                summer_scheme_events[i].id +
                                '">' +
                                summer_scheme_events[i].type +
                                '</a>' +
                                '</li>';
                        }
                    }

                    html_self_pick_up_component += '</ul>' + '</li>';
                }

                var html_self_pick_up_component =
                    html_self_pick_up_component + '</ul>';

                // Questionaire
                var html_questionaire_component =                
                    '<a  data-match-route="/questionnaire" href="#/questionnaire" cy-data="menu-questionaire">' +
                    // '<i class="fa fa-tasks" aria-hidden="true"></i>' +
                    '<img src="images/sidemenu-icon/questionaire.png" class="sidemenu-icon">' +
                    '<span class="sidemenu-label">' +
                    $rootScope.translate.QUESTIONNAIRE +
                    '</span>' +
                    '</a>';
                // Super Admin
                if (
                    $rootScope.user_role == USER_SUPER_ADMIN ||
                    $rootScope.user_role == USER_LEAGUE_ADMIN
                ) {
                    $('#registrationsContent').html(
                        html_registrations_component
                    );
                    $('#tablesContent').html(html_tables_component);
                    $('#educationsContent').html(html_educations_component);
                    $('#paymentsContent').html(html_payments_component);
                    $('#seasonsContent').html(html_seasons_component);
                    $('#clubManagerContent').html(
                        html_admin_club_manager_component
                    );
                    $('#leaguesContent').html(html_leagues_component);
                    $('#cgroupsAdminContent').html(
                        html_cgroups_admin_component
                    );
                    $('#teamSheetContent').html(html_team_sheet_component);
                    $('#respectsContent').html(html_respect_component);
                    $('#documentsContent').html(html_document_component);
                    $('#courseContent').html(html_course_component);
                    $('#trainingVideosContent').html(
                        html_training_video_component
                    );
                    $('#messagesContent').html(html_messages_component);
                    $('#reportsContent').html(html_reports_component);
                    $('#certificatesContent').html(html_certificates_component);
                    $('#settingsContent').html(html_setting_component);
                    $('#emailTemplateContent').html(html_email_template_component);
                    $('#shippingContent').html(html_shipping_component);
                    $('#shopProductsContent').html(html_shop_product_component);
                    $('#selfPickUpContent').html(html_self_pick_up_component);
                    $('#questionaireContent').html(html_questionaire_component);
                }

                // Club Manager
                if ($rootScope.user_role == USER_CLUB_MANAGER) {
                    $('#clubsContent').html(html_clubs_component);
                    $('#cgroupsContent').html(html_cgroups_component);
                    // $('#paymentClubsContent').html(
                    //     html_payment_clubs_component
                    // );
                    // $('#ClubCoachesContent').html(html_club_coaches_component);
                    $('#teamSheetContent').html(html_team_sheet_component);
                }

                // Team Coach
                if ($rootScope.user_role == USER_TEAM_COACH) {
                    $('#registrationsContent').html(
                        html_registrations_component
                    );
                    // $('#cgroupsContent').html(html_cgroups_component);
                    // $('#paymentteamsContent').html(
                    //     html_payment_teams_component
                    // );
                    $('#teamSheetContent').html(html_team_sheet_component);
                    $('#courseCoachContent').html(html_coach_message_component);
                    $('#courseContent').html(html_course_component);
                }

                // Finance
                if ($rootScope.user_role == USER_FINANCE) {
                    $('#paymentsContent').html(html_payments_component);
                    $('#reportsContent').html(html_reports_component);
                }

                // Grassroots Finance
                if ($rootScope.user_role == USER_GRASSROOTS_FINANCE) {
                    $('#paymentsContent').html(html_payments_component);
                    $('#reportsContent').html(html_reports_component);
                }

                // Shipping
                if ($rootScope.user_role == USER_SHIPPING) {
                    $('#selfPickUpContent').html(html_self_pick_up_component);
                }

                // Supervisor
                if ($rootScope.user_role == USER_SUPERVISOR) {
                    $('#courseContent').html(html_course_component);
                }

                // Managerial Coach
                if ($rootScope.user_role == USER_MANAGERIAL_COACH) {
                    $('#clubsContent').html(html_clubs_component);
                    $('#cgroupsContent').html(html_cgroups_component);
                    $('#courseCoachContent').html(html_coach_message_component);
                    $('#courseContent').html(html_course_component);
                }

                // Supervisor Coach
                if ($rootScope.user_role == USER_SUPERVISOR_COACH) {
                    $('#registrationsContent').html(
                        html_registrations_component
                    );
                    $('#teamSheetContent').html(html_team_sheet_component);
                    $('#courseCoachContent').html(html_coach_message_component);
                    $('#courseContent').html(html_course_component);
                }
            });
        };

        $rootScope.getUser();

        console.log('app.run - before return');

        // override confirm function to implement more options, make sure to place after BootstrapDialog.js file is loaded
        BootstrapDialog.confirm = function () {
            var options = {};
            var defaultOptions = {
                type: BootstrapDialog.TYPE_PRIMARY,
                title: null,
                message: null,
                closable: false,
                draggable: false,
                btnCancelLabel: BootstrapDialog.DEFAULT_TEXTS.CANCEL,
                btnOKLabel: BootstrapDialog.DEFAULT_TEXTS.OK,
                btnOKClass: null,
                onshown: null,
                callback: null,
            };
            if (
                typeof arguments[0] === 'object' &&
                arguments[0].constructor === {}.constructor
            ) {
                options = $.extend(true, defaultOptions, arguments[0]);
            } else {
                options = $.extend(true, defaultOptions, {
                    title: arguments[0],
                    message: arguments[1],
                    closable: false,
                    buttonLabel: BootstrapDialog.DEFAULT_TEXTS.OK,
                    callback:
                        typeof arguments[2] !== 'undefined'
                            ? arguments[2]
                            : null,
                });
            }
            if (options.btnOKClass === null) {
                options.btnOKClass = ['btn', options.type.split('-')[1]].join(
                    '-'
                );
            }

            return new BootstrapDialog({
                type: options.type,
                // title: options.title,
                title: options.title,
                message: options.message,
                closable: options.closable,
                draggable: options.draggable,
                size: options.size, //<-- added size
                onshown: options.onshown, //<-- added onshown callback
                tabindex: false,
                data: {
                    callback: options.callback,
                },
                buttons: [
                    {
                        label: options.btnCancelLabel,
                        action: function (dialog) {
                            if (
                                typeof dialog.getData('callback') ===
                                    'function' &&
                                dialog.getData('callback').call(this, false) ===
                                    false
                            ) {
                                return false;
                            }
                            return dialog.close();
                        },
                    },
                    {
                        label: options.btnOKLabel,
                        cssClass: options.btnOKClass,
                        action: function (dialog) {
                            if (
                                typeof dialog.getData('callback') ===
                                    'function' &&
                                dialog.getData('callback').call(this, true) ===
                                    false
                            ) {
                                return false;
                            }
                            return dialog.close();
                        },
                    },
                ],
            }).open();
        };
    },
]);

app.controller('logoutCtrl', function ($scope, $rootScope, $http, $location) {
    $('#page-wrapper').removeClass('nav-small');

    $http({
        method: 'POST',
        url: SERVER_PATH + 'user/logout',
        data: 'user_id=' + $rootScope.user_id,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'x-user-id': $rootScope.user_id,
            'x-user-email': $rootScope.user_name,
        },
    }).success(function (response) {
        if (response.status == 'OK') {
        } else {
            // alert(response.message);
            BootstrapDialog.show({
                title: 'ERROR',
                type: BootstrapDialog.TYPE_WARNING,
                message: response.message,
            });
        }
    });
});



