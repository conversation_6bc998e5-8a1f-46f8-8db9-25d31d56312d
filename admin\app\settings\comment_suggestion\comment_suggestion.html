<div class="row" id="tab-page">
    <div class="row">
        <div class="col-lg-12">
            <ol class="breadcrumb">
                <li>Setup</li>
                <li class="active"><span>Comment Suggestion</span></li>
            </ol>
        </div>
    </div>
</div>
<div class="row">
    <div class="col-lg-12">
        <div class="clearfix">
            <h1 class="pull-left">Comment Suggestion</h1>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-12">
        <div class="main-box clearfix">
            <div class="main-box-body clearfix">
                <div class="table-responsive">
                    <table id="comment_suggestion_table" class="table table-striped table-bordered table-hover"
                        cellspacing="0" width="100%">
                        <thead>
                            <tr>
                                <th>id</th>
                                <th>Text</th>
                                <th>Type</th>
                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript" language="javascript" class="init">
    $(document).ready(function () {
        console.log(COMMENT_SUGGESTION_OPTIONS[0].text);

        user_id = localStorage.getItem('hkjflApp.user_id')
        user_name = localStorage.getItem('hkjflApp.user_name')

        var commentSuggestionEditor = new $.fn.dataTable.Editor({
            ajax: {
                type: 'POST',
                url: SERVER_PATH + "registration/getCommentSuggestion",
                headers: {	
                    'x-user-id': user_id,
                    'x-user-email': user_name
                },
                data: {},
                dataType: 'json',
                complete: function (response) {
                },
                error: function (xhr, status, error) { },
            },
            table: '#comment_suggestion_table',
            formOptions: {
                main: {
                    onBlur: 'none'
                }
            },
            i18n: {
                create: {
                    button: "Add",
                    title: "Add Suggestion",
                    submit: "Add"
                },
                edit: {
                    button: "Edit",
                    title: "Edit Suggestion",
                    submit: "Save"
                },
                remove: {
                    button: "Delete",
                    title: "Delete Suggestion",
                    submit: "Delete"
                },
                error: {
                    system: "System error, please contact administrator."
                },
            },
            fields: [{
                label: "Comment",
                name: "comment_suggestion.text"
            },
            {
                label: "Type",
                name: "comment_suggestion.type",
                type: "radio",
                options: [{
                    label: COMMENT_SUGGESTION_OPTIONS[0].text,
                    value: COMMENT_SUGGESTION_OPTIONS[0].value
                },
                {
                    label: COMMENT_SUGGESTION_OPTIONS[1].text,
                    value: COMMENT_SUGGESTION_OPTIONS[1].value
                },
                {
                    label: COMMENT_SUGGESTION_OPTIONS[2].text,
                    value: COMMENT_SUGGESTION_OPTIONS[2].value
                }
                ]
            }
            ]
        });
        var commentSuggestionTable = $('#comment_suggestion_table').DataTable({
            dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
            stateSave: false,
            deferRender: true,
            ajax: {
                url: SERVER_PATH + "registration/getCommentSuggestion",
                type: 'POST',
                headers: {	
                    'x-user-id': user_id,
                    'x-user-email': user_name
                },
                data: {},
                dataType: 'json',
                complete: function (response) { },
                error: function (xhr, status, error) { },
            },
            language: {
                paginate: {
                    previous: '<i class="fa fa-chevron-left"></i>',
                    next: '<i class="fa fa-chevron-right"></i>'
                }
            },
            "aoColumns": [
                { "mData": null },
                { "mData": "comment_suggestion.text" },
                {
                "mData": "comment_suggestion.type",
                "mRender": function (data, type, row) {
                    data=COMMENT_SUGGESTION_OPTIONS[data-1].text;
                    return data;
                }
                },
            ],
            select: {
                style: 'single',
            },
            order: [
                [0, 'asc']
            ],
            lengthMenu: [
                [10, 25, 50, 100, -1],
                [10, 25, 50, 100, "All"]
            ],
            buttons: [
                {
                    extend: 'create',
                    editor: commentSuggestionEditor,
                    text: 'Add'
                },
                {
                    extend: "edit",
                    editor: commentSuggestionEditor,
                    text: 'Edit'
                },
                {
                    extend: "remove",
                    editor: commentSuggestionEditor,
                    text: 'Remove'
                },
                {
                    extend: 'colvis',
                    text: 'Columns'
                }
            ]
        });
        commentSuggestionTable.on('order.dt search.dt', function () {
            commentSuggestionTable.column(0, { search: 'applied', order: 'applied' }).nodes().each(function (cell, i) {
                cell.innerHTML = i + 1;
            });
        }).draw();
    })
</script>