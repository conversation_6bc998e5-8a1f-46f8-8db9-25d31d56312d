app.controller('clubsCtrl', function ($scope, $rootScope, $routeParams, $http, PlayerAssignmentService ) {
    $('#page-wrapper').removeClass('nav-small');

    // add css file to controller
    document.head.appendChild(Object.assign(document.createElement("link"), {
        rel: "stylesheet",
        href: "./app/clubs/clubs.css"
    }));
    
    $scope.club_name = '';
    $scope.event_name = '';

    var CLUBS_ACTION_ASSIGN_PLAYERS_TO_CGROUP = 1;
    var CLUBS_ACTION_AUTO_ASSIGN = 2;

    var event_id = $routeParams.id;

    // get event info
    jQuery.ajax({
        type: 'POST',
        url: SERVER_PATH + 'event/getEventInfo',
        async: false,
        headers: {	
            'x-user-id': $rootScope.user_id,
            'x-user-email': $rootScope.user_name
		},
        data: {
            event_id: event_id,
        },
        dataType: 'json',
        complete: function (response) {
            var jsonData = JSON.parse(response.responseText);
            var event = jsonData.info;
            event_name = event.name;
            event_type = event.type;
        },
    });
    console.log(
        'registrationsCtrl - event_id, name, type  = ' +
            event_id +
            ', ' +
            event_name +
            ', ' +
            event_type
    );
    $scope.event_id = event_id;
    $scope.event_name = event_name;

    jQuery.ajax({
        type: 'POST',
        url: SERVER_PATH + 'club/getClubByUser',
        async: false,
        headers: {	
            'x-user-id': $rootScope.user_id,
            'x-user-email': $rootScope.user_name
		},
        data: {
            user_id: $rootScope.user_id,
        },
        dataType: 'json',
        complete: function (response) {
            var jsonData = JSON.parse(response.responseText);
            if (jsonData.status == 'OK') {
                var club = jsonData.info;
                club_id = club.id;
                club_name = club.name;
                $scope.club_name = club_name;

                setTimeout(() => {
                    // initialize html
                    var ClubPlayersTableHtml = getClubPlayersTableHtml(club_id);
                    var CgroupCoachesTableHtml =
                        getCgroupCoachesTableHtml(club_id);

                    var html =
                        '<div class="tabs-wrapper cgroups-tabs">' +
                        '<ul class="nav nav-tabs">' +
                        '<li class="active"><a data-target="#tab-ClubPlayers" data-toggle="tab"><i class="fa fa-fw fa-users"></i> Players</a></li>' +
                        '<li><a data-target="#tab-CgroupCoaches" data-toggle="tab"><i class="fa fa-user"></i> Team Coaches</a></li>' +
                        '</ul>' +
                        '<div class="tab-content">' +
                        '<div class="tab-pane fade in active" id="tab-ClubPlayers">' +
                        ClubPlayersTableHtml +
                        '</div>' +
                        '<div class="tab-pane fade" id="tab-CgroupCoaches">' +
                        CgroupCoachesTableHtml +
                        '</div>' +
                        '</div>' +
                        '</div>';

                    $('#clubsPageContent').html(html);

                    initClubPlayersTable(club_id, club_name);
                    initCgroupCoachesTable(club_id, club_name);
                }, 100);
            } else {
                BootstrapDialog.show({
                    title: 'ERROR',
                    type: BootstrapDialog.TYPE_WARNING,
                    message: jsonData.message,
                });
            }
        },
    });

    function getClubPlayersTableHtml(club_id) {
        var str =
            '' +
            '<div class="table-responsive">' +
            '<table id="tblClubPlayers_' +
            club_id +
            '" class="table table-striped table-bordered table-hover" cellspacing="0" width="100%">' +
            '<thead>' +
            '<tr>' +
            '<th></th>' +
            '<th>Photo</th>' +
            '<th>Player</th>' +
            '<th class="text-center">Groups</th>' +
            '<th class="text-center">Team</th>' +
            '<th class="text-center">Year</th>' +
            '<th class="text-center">Gender</th>' +
            '<th class="text-center">Status</th>' +
            '</tr>' +
            '</thead>' +
            '</table>' +
            '</div>';
        return str;
    }

    function getCgroupCoachesTableHtml(club_id) {
        var str =
            '' +
            '<div class="table-responsive">' +
            '<table id="tblCgroupCoaches_' +
            club_id +
            '" class="table table-striped table-bordered table-hover" cellspacing="0" width="100%">' +
            '<thead>' +
            '<tr>' +
            '<th></th>' +
            '<th class="text-center">Name</th>' +
            '<th class="text-center">Email</th>' +
            '<th class="text-center">Phone</th>' +
            '<th class="text-center">Group</th>' +
            '</tr>' +
            '</thead>' +
            '</table>' +
            '</div>';
        return str;
    }

    function initClubPlayersTable(club_id, club_name) {
        if ($.fn.dataTable.isDataTable('#tblClubPlayers_' + club_id)) {
            $('#tblClubPlayers_' + club_id)
                .DataTable()
                .destroy();
        }

        tableClubPlayers = $('#tblClubPlayers_' + club_id).DataTable({
            dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
            stateSave: true,
            deferRender: true,
            ajax: {
                url: SERVER_PATH + 'club/getClubPlayers',
                type: 'POST',
                headers: {	
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name
                },
                data: {
                    club_id: club_id,
                    event_id: event_id,
                },
                dataType: 'json',
                complete: function (response) {
                    // response = JSON.parse(response.responseText);
                },
                error: function (xhr, status, error) {},
            },
            language: {
                paginate: {
                    previous: '<i class="fa fa-chevron-left"></i>',
                    next: '<i class="fa fa-chevron-right"></i>',
                },
            },
            columns: [
                {
                    data: 'DT_RowId',
                    // defaultContent: '',
                    // className: 'select-checkbox',
                    // orderable: false
                    targets: 0,
                    render: function (data, type, row, meta) {
                        if (type === 'display') {
                            data =
                                '<div class="checkbox"><input type="checkbox" class="dt-checkboxes"><label></label></div>';
                        }
                        return data;
                    },
                    checkboxes: {
                        selectRow: true,
                        selectAllRender:
                            '<div class="checkbox"><input type="checkbox" class="dt-checkboxes"><label></label></div>',
                    },
                },
                {
                    data: 'players.player_photo',
                    className: 'avatar',
                    orderable: false,
                    render: function (data) {
                        if (data !== null && data !== '') {
                            return (
                                '<img src="' + PRODUCT_IMAGE_PATH + data + '">'
                            );
                        } else {
                            return (
                                '<img src="' +
                                SYSTEM_IMAGE_PATH +
                                'favicon.png">'
                            );
                        }
                    },
                },
                {
                    data: null,
                    render: function (data, type, row) {
                        return (
                            data.players.surname + ' ' + data.players.other_name
                        );
                    },
                },
                {
                    data: 'groups.name',
                    className: 'center',
                    // visible: false
                },
                {
                    data: 'teams.name',
                    className: 'center',
                },
                {
                    data: 'players.dob',
                    className: 'center',
                    // visible: false
                },
                {
                    data: 'players.gender',
                    className: 'center',
                    // visible: false
                },
                {
                    data: 'players.validate_status',
                    className: 'center',
                    render: function (data, type, full, meta) {
                        switch (data) {
                            case VALIDATE_STATUS_Pending:
                                return (
                                    '<span class="label label-info">' +
                                    data +
                                    '</span>'
                                );
                            case VALIDATE_STATUS_Invalid:
                                return (
                                    '<span class="label label-danger">' +
                                    data +
                                    '</span>'
                                );
                            case VALIDATE_STATUS_Updated:
                                return (
                                    '<span class="label label-warning">' +
                                    data +
                                    '</span>'
                                );
                            case VALIDATE_STATUS_Validated:
                                return (
                                    '<span class="label label-success">' +
                                    data +
                                    '</span>'
                                );
                            default:
                                return (
                                    '<span class="label label-default">' +
                                    data +
                                    '</span>'
                                );
                        }
                    },
                },
            ],
            select: {
                style: 'multi',
                // selector: 'td:first-child'
            },
            buttons: [
                {
                    extend: 'collection',
                    text: 'Actions',
                    autoClose: true,
                    buttons: [
                        {
                            extend: 'excel',
                            name: 'excel',
                            text: '<i class="fa fa-file-excel-o"></i>&emsp;Export to Excel',
                            titleAttr: 'Export data to an Excel file',
                            filename: club_name + ' - Players',
                            title: club_name,
                            exportOptions: {
                                columns: ':visible',
                                modifier: {
                                    autoFilter: true,
                                    // selected: true
                                },
                            },
                        },
                        {
                            text: '<i class="fa fa-share-square"></i>&emsp;Assign player to Year Group...',
                            titleAttr: 'Assign players to year group',
                            club_id: club_id,
                            action: function (e, dt, node, config) {
                                action = config.text;
                                club_id = config.club_id;
                                checkRequest(
                                    club_id,
                                    CLUBS_ACTION_ASSIGN_PLAYERS_TO_CGROUP,
                                    action
                                );
                            },
                        },
                    ],
                },
                {
                    text: '<i class="fa fa-check-circle"></i>&emsp;Auto assign',
                    titleAttr: 'Auto assign player to year group',
                    club_id: club_id,
                    action: function (e, dt, node, config) {
                        action = config.text;
                        club_id = config.club_id;
						$scope.club_id = club_id;
						console.log(club_id);
						PlayerAssignmentService.quickAssign($scope, tableClubPlayers, event_id);


                    },
                },
                { extend: 'colvis', text: 'Columns' },
            ],
            order: [[1, 'asc']],
            lengthMenu: [
                [10, 25, 50, 100, -1],
                [10, 25, 50, 100, 'All'],
            ],
        });
    }

    function initCgroupCoachesTable(club_id, club_name) {
        if ($.fn.dataTable.isDataTable('#tblCgroupCoaches_' + club_id)) {
            $('#tblCgroupCoaches_' + club_id)
                .DataTable()
                .destroy();
        }

        var editorClubCoaches = new $.fn.dataTable.Editor({
            ajax: {
                type: 'POST',
                url: SERVER_PATH + 'group/setCgroupCoaches',
                headers: {	
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name
                },
                data: {
                    club_id: club_id,
                    event_id: event_id,
                },
                dataType: 'json',
                complete: function (response) {
                    tblCgroupCoaches_.ajax.reload();
                },
                error: function (xhr, status, error) {},
            },
            table: '#tblCgroupCoaches_' + club_id,
            formOptions: {
                main: {
                    onBlur: 'none',
                },
            },
            i18n: {
                create: {
                    button: 'New',
                    title: 'Create new club coach',
                    submit: 'Create',
                },
                remove: {
                    button: 'Delete',
                    title: 'Delete coach',
                    submit: 'Delete',
                },
                error: {
                    system: 'System error, please contact administrator.',
                },
            },
            fields: [
                {
                    label: 'Surname Coach:',
                    name: 'surname',
                },
                {
                    label: 'Other name Coach:',
                    name: 'other_name',
                },
                {
                    label: 'Email:',
                    name: 'email',
                },
                {
                    name: 'country_code',
                    type: 'hidden',
                    def: '+852',
                },
                {
                    name: 'iso_code',
                    type: 'hidden',
                    def: 'hk',
                },
                {
                    label: 'Phone number',
                    name: 'phone',
                    type: 'telephone',
                    opts: {
                        preferredCountries: ['hk', 'cn'],
                        initialCountry: 'hk',
                    },
                },
                {
                    name: 'groups.id',
                    label: 'Group:',
                    type: 'select2',
                    opts: {
                        multiple: 'multiple',
                        // closeOnSelect: false,
                        placeholder: 'Select a group',
                    },
                },
            ],
        });

        tblCgroupCoaches_ = $('#tblCgroupCoaches_' + club_id).DataTable({
            dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
            stateSave: true,
            deferRender: true,
            ajax: {
                url: SERVER_PATH + 'group/getCgroupCoaches',
                type: 'POST',
                headers: {	
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name
                },
                data: {
                    club_id: club_id,
                    event_id: event_id,
                },
                dataType: 'json',
                complete: function (response) {
                    // response = JSON.parse(response.responseText);
                },
                error: function (xhr, status, error) {},
            },
            language: {
                paginate: {
                    previous: '<i class="fa fa-chevron-left"></i>',
                    next: '<i class="fa fa-chevron-right"></i>',
                },
            },
            columns: [
                {
                    data: 'DT_RowId',
                    // defaultContent: '',
                    // className: 'select-checkbox',
                    // orderable: false
                    targets: 0,
                    render: function (data, type, row, meta) {
                        if (type === 'display') {
                            data =
                                '<div class="checkbox"><input type="checkbox" class="dt-checkboxes"><label></label></div>';
                        }
                        return data;
                    },
                    checkboxes: {
                        selectRow: true,
                        selectAllRender:
                            '<div class="checkbox"><input type="checkbox" class="dt-checkboxes"><label></label></div>',
                    },
                },
                {
                    data: null,
                    render: function (data, type, row) {
                        return (
                            data.parens.surname + ' ' + data.parens.other_name
                        );
                    },
                },
                {
                    data: 'parens.email',
                    className: 'center',
                    // visible: false
                },
                {
                    data: 'parens.phone',
                    className: 'center',
                    // visible: false
                },
                {
                    data: 'groups.name',
                    className: 'center',
                    // visible: false
                },
            ],
            select: {
                style: 'single',
                // selector: 'td:first-child'
            },
            buttons: [
                {
                    extend: 'collection',
                    text: 'Actions',
                    autoClose: true,
                    buttons: [
                        {
                            extend: 'excel',
                            name: 'excel',
                            text: '<i class="fa fa-file-excel-o"></i>&emsp;Export to Excel',
                            titleAttr: 'Export data to an Excel file',
                            filename: club_name + ' - Year Group Coaches',
                            title: club_name,
                            exportOptions: {
                                columns: ':visible',
                                modifier: {
                                    autoFilter: true,
                                    // selected: true
                                },
                            },
                        },
                    ],
                },
                { extend: 'create', editor: editorClubCoaches },
                { extend: 'remove', editor: editorClubCoaches, text: 'Delete' },
                { extend: 'colvis', text: 'Columns' },
            ],
            order: [[1, 'asc']],
            lengthMenu: [
                [10, 25, 50, 100, -1],
                [10, 25, 50, 100, 'All'],
            ],
        });
    }

    function checkRequest(club_id, action_id, action) {
        if (action_id == CLUBS_ACTION_ASSIGN_PLAYERS_TO_CGROUP) {
            table = $('#tblClubPlayers_' + club_id).DataTable();
        }

        if (DEVELOPMENT_ENVIRONMENT)
            console.log('checkRequest - action: ' + action);

        var table_selected = table.rows({ selected: true }).data();
        var countRows = table_selected.length;

        var MAX_NAMES_TO_DISPLAY = 10;

        var names = '';
        var name_index = 0;
        var rows_selected = [];
        for (var i = 0; i < table_selected.length; i++) {
            if (action_id == CLUBS_ACTION_ASSIGN_PLAYERS_TO_CGROUP) {
                id = table_selected[i].club_players.id;
                name =
                    table_selected[i].players.surname +
                    ' ' +
                    table_selected[i].players.other_name;
            }
            rows_selected.push(id);
            name_index = i + 1;
            names += name_index + '. ' + name + '<br/>';
        }

        if (DEVELOPMENT_ENVIRONMENT)
            console.log('checkRequest - rows_selected: ' + countRows);

        rows_selected.sort();
        var selectedRows = rows_selected.join('_');
        var data = null;

        if (action_id == CLUBS_ACTION_ASSIGN_PLAYERS_TO_CGROUP) {
            message =
                countRows == 0
                    ? 'At least one player must be selected!'
                    : countRows == 1
                    ? '<strong>1 player?</strong></br>' + names
                    : '<strong>' +
                      countRows +
                      ' players?</strong></br>' +
                      names;
        }

        if (selectedRows == '') {
            // Not selected
            BootstrapDialog.show({
                title: 'Information - ' + action,
                type: BootstrapDialog.TYPE_WARNING,
                message: message,
            });
        } else {
            if (data == null) {
                // for select multiple rows
                title = 'Confirmation - ' + action;
                var dialog = BootstrapDialog.confirm(
                    title,
                    message,
                    function (result) {
                        if (result) {
                            dialog.close();
                            if (DEVELOPMENT_ENVIRONMENT)
                                console.log(
                                    'checkRequest - action_id: ' + action_id
                                );
                            var urlStr = '';
                            if (
                                action_id ==
                                CLUBS_ACTION_ASSIGN_PLAYERS_TO_CGROUP
                            ) {
                                urlStr =
                                    SERVER_PATH + 'group/assignPlayersToCgroup';
                                ajaxAssignPlayerToCgroupAction(
                                    club_id,
                                    rows_selected,
                                    urlStr,
                                    action,
                                    action_id
                                );
                            }
                        }
                    }
                );
                if (name_index > 15) {
                    dialog.getModalBody().css('height', '330px');
                    dialog.getModalBody().css('overflow-y', 'scroll');
                }
            } else {
                // for select one row
            }
        }
    }

    // Assign player to year group
    function ajaxAssignPlayerToCgroupAction(
        club_id,
        club_player_ids,
        URL,
        action,
        action_id
    ) {
        jQuery.ajax({
            type: 'POST',
            url: SERVER_PATH + 'group/getCgroupsByClub',
            async: false,
            headers: {	
                'x-user-id': $rootScope.user_id,
                'x-user-email': $rootScope.user_name
            },
            data: {
                event_id: event_id,
                club_id: club_id,
            },
            dataType: 'json',
            complete: function (response) {
                response = JSON.parse(response.responseText);
                cgroupSelection = response.cgroupSelection;

                var editorAssignPlayer = new $.fn.dataTable.Editor({
                    ajax: {
                        type: 'POST',
                        url: URL,
                        async: false,
                        data: {
                            user_id: $rootScope.user_id,
                            club_id: club_id,
                            club_player_ids: club_player_ids,
                        },
                        dataType: 'json',
                        complete: function (response) {
                            var jsonData = JSON.parse(response.responseText);
                            displayActionMessage(jsonData, action, action_id);
                        },
                        error: function (xhr, status, error) {
                            alert(
                                'ajaxAssignPlayerToCgroupAction.Error - status, error = ' +
                                    status +
                                    ',' +
                                    error +
                                    ',' +
                                    xhr
                            );
                        },
                    },
                    formOptions: {
                        main: {
                            onBlur: 'none',
                        },
                    },
                    fields: [
                        {
                            label: 'Year Group:',
                            name: 'cgroup_id',
                            type: 'select2',
                            opts: {
                                placeholder: 'Select a year group',
                            },
                            options: cgroupSelection,
                        },
                    ],
                });
                editorAssignPlayer
                    .title('Assign player to year group')
                    .buttons({
                        label: 'Submit',
                        fn: function () {
                            this.submit();
                        },
                    })
                    .edit()
                    .open();
            },
        });
    }

    function ajaxAutoAssignPlayerToGroupAction(
        club_id,
        URL,
        action,
        action_id
    ) {
        jQuery.ajax({
            type: 'POST',
            url: URL,
            async: false,
            data: {
                event_id: event_id,
                club_id: club_id,
            },
            dataType: 'json',
            complete: function (response) {
                console.log(response);
                var jsonData = JSON.parse(response.responseText);
                displayActionMessage(jsonData, action, action_id);
            },
        });
    }

    function displayActionMessage(jsonData, action, action_id) {
        if (jsonData.status == 'OK') {
            BootstrapDialog.show({
                title: 'Information - ' + action,
                type: function () {
                    if (jsonData.message.indexOf('Please try again') == -1)
                        return BootstrapDialog.TYPE_SUCCESS;
                    return BootstrapDialog.TYPE_WARNING;
                },
                message: jsonData.message,
                onhidden: function (dialogRef) {
                    tableClubPlayers.ajax.reload();
                },
            });
        } else if (jsonData.status == 'ERROR') {
            BootstrapDialog.show({
                title: 'Information - ' + action,
                type: BootstrapDialog.TYPE_DANGER,
                message: jsonData.message,
            });
        }
    }
});
