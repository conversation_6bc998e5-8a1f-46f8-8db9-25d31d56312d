app.controller('generalSettingsCtrl', function ($scope, $rootScope, $http) {
    $('#page-wrapper').removeClass('nav-small');

    $scope.items = [];
    $scope.selectedItem = null;

    $http({
        method: 'GET',
        url: SERVER_PATH + 'general-setting/getSettings',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'x-user-id': $rootScope.user_id,
            'x-user-email': $rootScope.user_name,
        },
    }).then(
        function success(response) {
            $scope.items = response['data']['info'];
            // set selected item to first item
            $scope.selectedItem = $scope.items[0];
            console.log($scope.selectedItem);
        },
        function error(response) {
            alert('ERROR ' + response);
        }
    );

    $scope.selectItem = function (item) {
        console.log(item);
        $scope.selectedItem = item;

        const { type } = item;

        if (
            type == 'Organisation - Summer Scheme Notify When Change Shipping'
        ) {
            $scope.options = {
                height: 300,
                toolbar: [
                    ['edit', ['undo', 'redo']],
                    ['headline', ['style']],
                    [
                        'style',
                        [
                            'bold',
                            'italic',
                            'underline',
                            'superscript',
                            'subscript',
                            'strikethrough',
                            'clear',
                        ],
                    ],
                    ['fontface', ['fontname']],
                    ['textsize', ['fontsize']],
                    ['fontclr', ['color']],
                    [['ul', 'ol', 'paragraph', 'lineheight']],
                    ['height', ['height']],
                ],
            };
        } else {
            $scope.options = {
                height: 300,
                toolbar: [
                    ['edit', ['undo', 'redo']],
                    ['headline', ['style']],
                    [
                        'style',
                        [
                            'bold',
                            'italic',
                            'underline',
                            'superscript',
                            'subscript',
                            'strikethrough',
                            'clear',
                        ],
                    ],
                    ['fontface', ['fontname']],
                    ['textsize', ['fontsize']],
                    ['fontclr', ['color']],
                    ['alignment', ['ul', 'ol', 'paragraph', 'lineheight']],
                    ['height', ['height']],
                    ['table', ['table']],
                    ['insert', ['link', 'picture', 'video', 'hr']],
                    ['view', ['fullscreen', 'codeview']],
                    ['help', ['help']],
                ],
            };
        }
    };

    $scope.saveChanges = function () {
        console.log($scope.selectedItem);

        const { type } = $scope.selectedItem;

        var url = SERVER_PATH + 'general-setting/setSettings';
        var data = $scope.selectedItem;

        let fields = $scope.selectedItem.data;

        if (
            type == 'Organisation - Summer Scheme Notify When Change Shipping'
        ) {
            const temp_data = data.data;

            const [{ value: chineseValue }, { value: englishValue }] =
                temp_data;

            if (chineseValue.length > 255 || englishValue.length > 255) {
                if (chineseValue.length > 255) {
                    showError('Content is too long');
                    return;
                } else if (englishValue.length > 255) {
                    showError('Content is too long');
                    return;
                }
            }

            if (chineseValue.trim() == '' || englishValue.trim() == '') {
                if (chineseValue == '') {
                    showError('Content is required');
                }

                if (englishValue == '') {
                    showError('Content is required');
                }

                return;
            }

            // check empty html
            if (chineseValue.trim() == '<p><br></p>') {
                showError('Chinese Content is required');
                return;
            }

            if (englishValue.trim() == '<p><br></p>') {
                showError('English Content is required');
                return;
            }
        }

        $http
            .post(
                url,
                data,
                // add headers
                {
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name,
                    },
                }
            )
            .then(
                function success(response) {
                    console.log(response);
                    data = response['data'];
                    if (data.status == 'OK') {
                        let firstRow = fields[0];

                        if (firstRow.list_fields != '') {
                            showConfirm(
                                'Reset user acceptance?',
                                'Do you want',
                                'Yes',
                                'No'
                            ).then(function (result) {
                                if (result.value) {
                                    resetUserAcceptance(firstRow.list_fields);
                                } else {
                                    showSuccess(data.message);
                                }
                            });
                        } else {
                            showSuccess(data.message);
                        }
                    } else {
                        showError(data.message);
                    }
                },
                function error(response) {
                    alert('ERROR ' + response);
                }
            );
    };

    $('.summernote').summernote();

    function resetUserAcceptance(listFields) {
        var url = SERVER_PATH + 'general-setting/resetUserAcceptance';

        jQuery.ajax({
            type: 'POST',
            url: url,
            async: false,
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'x-user-id': $rootScope.user_id,
                'x-user-email': $rootScope.user_name,
            },
            data: {
                listFields: listFields,
            },
            dataType: 'json',
            complete: function (response) {
                data = response.responseJSON;
                if (data.status == 'OK') {
                    showSuccess(data.message);
                } else {
                    showError(data.message);
                }
            },
        });
    }
});
