<div class="row" id="tab-page">
  <div class="row">
    <div class="col-lg-12">
      <ol class="breadcrumb">
        <li>Events</li>
        <li><a href="" ng-click="goBack()">Summer Scheme</a></li>
        <li>{{event_name}}</li>
        <li class="active"><span>Registrations</span></li>
      </ol>
    </div>
  </div>
</div>

<div class="row">
  <div class="col-lg-12">
    <div class="clearfix">
      <h1 class="pull-left">Registrations</h1>
    </div>
  </div>
</div>

<div class="row" id="loading" style="display: none">
  <div class="col-lg-12">
    <div class="main-box clearfix">
      <div class="main-box-body clearfix">
        <div class="table-responsive" style="text-align: center; color: gray">
          <p style="margin-top: 10px">Email is sending, please wait...</p>
          <loading></loading>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="row">
  <div class="col-lg-12">
    <div class="main-box clearfix">
      <!-- form -->
      <form role="form" class="main-box-body clearfix" style="margin-top: 16px;">
        <div class="row d-flex justify-content-between">
          <!-- Location -->
          <div class="form-group col-lg-2" style="margin-top: 8px; margin-bottom: 0;" id="type-content">
            <label>Filter by type</label>
          </div>
          <div class="form-group col-lg-2" style="margin-top: 8px; margin-bottom: 0;" id="group-content">
            <label>Filter by group</label>
          </div>
          <div class="form-group col-lg-2" style="margin-top: 8px; margin-bottom: 0;" id="course-content">
            <label>Filter by course</label>
          </div>
          <div class="form-group col-lg-2" style="margin-top: 8px; margin-bottom: 0;" id="shipping-content">
            <label>Filter by shipping type</label>
          </div>
          <div class="form-group col-lg-2" style="margin-top: 8px; margin-bottom: 0;" id="status-content">
            <label>Filter by status</label>
          </div>
          <div class="form-group col-lg-2" style="margin-top: 8px; margin-bottom: 0;" id="payment-content">
            <label>Filter by payment status</label>
          </div>
        </div>
      </form>
      <div class="main-box-body clearfix">
        <div class="table-responsive">
          <table id="summer_scheme_table_{{event_id}}" class="table table-striped table-bordered table-hover"
            cellspacing="0" width="100%">
            <thead>
              <tr>
                <th>Player name</th>
                <th>Chinese name</th>
                <!-- <th>HKID</th> -->
                <th>DOB</th>
                <th>Gender</th>
                <th>Parent phone</th>
                <!-- <th>Parent name</th> -->
                <th>Parent email</th>
                <th>Type</th>
                <th>Group</th>
                <th>Course code</th>
                <th>Status</th>
                <!-- <th>Product</th> -->
                <th>Registered date</th>
                <th>Invoice number</th>
                <th>Payment status</th>
                <th>From</th>
                <th>Shipping type</th>
                <th>Emailed</th>
              </tr>
            </thead>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>

<ng-include src="'app/registrations/registration-summer-scheme/offline-registration-modal.html'"></ng-include>

<div style="display: none">
  <div id="customForm">
    <editor-field name="registration_cancellations.reason"></editor-field>
    <div style="margin-left: 8px">
      <div>
        <input class="form-check-input" name="refund" type="radio" id="refund_offline" value="refund_offline" checked />
        <label class="form-check-label" for="refund_offline">
          <!-- Refund Offline -->
          Mark as refunded
        </label>
      </div>
      <div>
        <input class="form-check-input" name="refund" type="radio" id="refund_asia" value="refund_asia" />
        <label class="form-check-label" for="refund_asia">
          Refund via Asia Pay (Refund full)
        </label>
      </div>
      <div>
        <input class="form-check-input" name="refund" type="radio" id="no_refund" value="no_refund" />
        <label class="form-check-label" for="no_refund"> No refund </label>
      </div>
    </div>
  </div>
</div>
<style>
  .avatar>img {
    position: relative;
    max-width: 96px;
    float: left;
    margin: auto;
    border-radius: 18%;
    background-clip: padding-box;
  }

  .class a {
    width: -webkit-fill-available;
    height: 70px;
    margin-bottom: 10px;
    text-align: center;
    border: solid;
    background-color: white;
  }

  .class a:hover {
    background-color: red;
    color: white;
  }

  .class h4 {
    margin-top: 16px;
    text-decoration: none;
  }

  .select-row {
    display: flex;
  }

  .select-row>div {
    margin-right: 20px;
  }

  .d-flex {
    display: flex;
  }

  .justify-content-between {
    justify-content: space-between;
  }
</style>

<!-- <script type="text/javascript">
    $('selType').select2();
    $('selGroup').select2();
    $('selCourse').select2();
</script> -->