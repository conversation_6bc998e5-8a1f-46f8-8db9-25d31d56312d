<div class="row" id="tab-page">
    <div class="row">
        <div class="col-lg-12">
            <ol class="breadcrumb">
                <li>Setup</li>
                <li class="active"><span>General Settings</span></li>
            </ol>
        </div>
    </div>
</div>
<div class="row">
    <div class="col-lg-12">
        <div class="clearfix">
            <h1 class="pull-left">General Settings</h1>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-4">
            <div>
                <!-- add search box -->
                <div style="padding: 0 8px 8px 8px;">
                    <form action="">
                        <div class="form-group" style="margin-bottom: 0;">
                            <div class="input-group">
                                <span class="input-group-addon"
                                    style="background-color: white; border-radius: 50px 0 0 50px;"><i
                                        class="fa fa-search fa-lg"></i></span>
                                <input type="text" id="search-box" class="form-control" placeholder="Search"
                                    ng-model="searchText" style="height: 40px; border-radius: 0 50px 50px 0;">
                            </div>
                        </div>
                    </form>
                </div>
                <!-- list items -->
                <ul id="email-nav-items" class="clearfix"
                    style="padding-left: 0; list-style-type: none; margin-top: 0;">
                    <li ng-repeat="item in items | filter:searchText"
                        style="margin: 8px; background-color: white; padding: 4px; border-radius: 5px;"
                        ng-class="item.type == selectedItem.type ? 'active' : ''">
                        <a href="" ng-click="selectItem(item)"><span style="font-size: 14px;">{{item.type}}</span> </a>
                    </li>
                </ul>
            </div>
        </div>
        <div class="col-lg-8">
            <div class="main-box clearfix" style="padding: 16px;">

                <form ng-submit="saveChanges()">
                    <div ng-repeat="(key, item) in selectedItem.data" class="text-content" style="margin-top: 16px;">
                        <hr ng-if="key == 1">
                        <h4 style="font-weight: bold;">{{item.title}}</h4>
                        <summernote ng-model="item.value" config="options" height="300"></summernote>
                    </div>

                    <button type="submit" style="margin-top: 24px;" class="btn btn-primary">Save</button>
                </form>
            </div>
        </div>
    </div>
</div>

<style>
    .panel-default>.panel-heading {
        color: #333 !important;
        background-color: #f5f5f5 !important;
        border-color: #ddd !important;
    }

    .note-btn-group {
        margin-top: 0px;
    }
</style>