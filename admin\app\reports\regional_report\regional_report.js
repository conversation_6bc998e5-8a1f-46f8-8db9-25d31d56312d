app.controller(
    'regionalReportsCtrl',
    function (user, $scope, $rootScope, $routeParams, $http) {
        $('#page-wrapper').removeClass('nav-small');
        console.log('regionalReportsCtrl');
        var table = null;
        var event_id = $routeParams.eventId;   

        $.fn.dataTable.moment('DD/MM/YYYY');

        $scope.events = [];
        $scope.selectedEvent;

        $scope.teamDistricts = [];

        $scope.coaches = null;

        jQuery.ajax({
            type: 'POST',
            url: SERVER_PATH + 'event/getEventInfo',
            async: false,
            headers: {	
                'x-user-id': $rootScope.user_id,
                'x-user-email': $rootScope.user_name
            },
            data: {
                event_id: event_id,
            },
            dataType: 'json',
            complete: function (response) {
                var jsonData = JSON.parse(response.responseText);
                var event = jsonData.info;
                event_name = event.name;
                event_type = event.type;
            },
        });
        $scope.event_name = event_name;
        $scope.event_type = event_type;
        // getEventRegionals();
        $('#select_event_regional').select2();

        $('#select_event_regional').change(function () {
            // fix bug delay change value
            setTimeout(function () {
                $scope.initReport();
            }, 100);
        });

        // config date range picker
        $('input[name="dateFilter"]').daterangepicker({
            startDate: moment().startOf('month'),
            endDate: moment().endOf('month'),
            locale: {
                format: 'DD/MM/YYYY',
            },
            maxSpan: {
                days: 90,
            },
            ranges: {
                Yesterday: [
                    moment().subtract(1, 'days'),
                    moment().subtract(1, 'days'),
                ],
                Today: [moment(), moment()],
                Tomorrow: [moment().add(1, 'days'), moment().add(1, 'days')],
                'Last week': [
                    moment().subtract(1, 'week').startOf('week').add(1, 'days'),
                    moment().subtract(1, 'week').endOf('week').add(1, 'days'),
                ],
                'This week': [
                    // start week on monday
                    moment().startOf('week').add(1, 'days'),
                    moment().endOf('week').add(1, 'days'),
                ],
                'Next week': [
                    moment().add(1, 'week').startOf('week').add(1, 'days'),
                    moment().add(1, 'week').endOf('week').add(1, 'days'),
                ],
                'Last Month': [
                    moment().subtract(1, 'month').startOf('month'),
                    moment().subtract(1, 'month').endOf('month'),
                ],
                'This Month': [
                    moment().startOf('month'),
                    moment().endOf('month'),
                ],
                'Next Month': [
                    moment().subtract(-1, 'month').startOf('month'),
                    moment().subtract(-1, 'month').endOf('month'),
                ],
            },
        });
        // Date range picker
        $scope.start_date = $('input[name="dateFilter"]')
            .data('daterangepicker')
            .startDate.format('YYYY-MM-DD');
        $scope.end_date = $('input[name="dateFilter"]')
            .data('daterangepicker')
            .endDate.format('YYYY-MM-DD');

        $('input[name="dateFilter"]').on(
            'apply.daterangepicker',
            function (ev, picker) {
                console.log('date change');
                $scope.start_date = picker.startDate.format('YYYY-MM-DD');
                $scope.end_date = picker.endDate.format('YYYY-MM-DD');
                $scope.initReport();
            }
        );

        $scope.initReport = function () {
            initReportTable(event_id);
        };

        function getEventRegionals() {
            $http({
                method: 'POST',
                url: SERVER_PATH + 'event/getEventRegionals',
                headers: {	
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name
                },
            }).success(function (response) {
                console.log(response);
                if (response.status == 'OK') {
                    $scope.events = response.data;
                    console.log($scope.events[0]);
                    $scope.selectedEvent = $scope.events[0];
                    $scope.initReport();
                    initTeamCoachesReport($scope.selectedEvent.id);
                }
            });
        }

        $scope.initReport();
        initTeamCoachesReport(event_id);

        $scope.goBack = function () {
            window.history.back();
        };

        function innitReportHtml(event_id) {
            var html =
                '<div class="table-responsive">' +
                '<table id="reports_district_' +
                event_id +
                '" class="table table-striped table-bordered table-hover" cellspacing="0"width="100%">' +
                '<thead>' +
                '<tr>' +
                '<th colspan="9" class="center">Coach information</th>' +
                '<th colspan="5" class="center">Session information</th>' +
                '<th colspan="2" class="center">Team information</th>' +
                '<th colspan="5" class="center">Approval information</th>' +
                '<th rowspan="2" class="center">Action</th>' +
                '</tr>' +
                '<tr>' +
                '<th>Name - Phone</th>' +
                '<th>Coach name</th>' +
                '<th>Coach ID</th>' +
                '<th>Chinese name</th>' +
                '<th>Email</th>' +
                '<th>Phone</th>' +
                '<th>Role in Session</th>' +
                '<th>Level</th>' +
                '<th>Attendance</th>' +
                '<th>Session Name</th>' +
                '<th>Date</th>' +
                '<th>Time</th>' +
                '<th>Status</th>' +
                '<th>Reason</th>' +
                '<th>District</th>' +
                '<th>Group</th>' +
                '<th>Status</th>' +
                '<th>Reason</th>' +
                '<th>Date</th>' +
                '<th>Duration</th>' +
                '<th>Amount</th>' +
                '</tr>' +
                '</thead>' +
                '</table>' +
                '</div>';
            return html;
        }

        function onlyUnique(value, index, self) {
            return self.indexOf(value) === index;
        }
        function initReportTable(event_id) {
            $('#attendanceContent').html(innitReportHtml(event_id));
            console.log('initReportTable');
            table = $('#reports_district_' + event_id).DataTable({
                dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
                // stateSave: false,
                deferRender: true,
                bDestroy: true,
                processing: true,
                serverSide: true,
                ajax: {
                    url:
                        SERVER_PATH +
                        'district-session/getReportDistrictSession',
                    type: 'POST',
                    headers: {	
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name
                    },
                    data: {
                        event_id: event_id,
                        start_date: $scope.start_date,
                        end_date: $scope.end_date,
                        user_id: $rootScope.user_id,
                    },
                    dataType: 'json',
                    complete: function (response) {},
                    error: function (xhr, status, error) {},
                },
                language: {
                    paginate: {
                        previous: '<i class="fa fa-chevron-left"></i>',
                        next: '<i class="fa fa-chevron-right"></i>',
                    },
                },
                columns: [
                    {
                        data: 'parens.for_filter',
                        visible: false,
                    },
                    {
                        data: 'parens.surname',
                        render: function (data, type, row) {
                            const name =
                                row.parens.surname +
                                ' ' +
                                row.parens.other_name;

                            if (row.is_substitute) {
                                return (
                                    name +
                                    '(Substitute for ' +
                                    row.is_substitute +
                                    ')'
                                );
                            } else {
                                return name;
                            }
                        },
                    },
                    {
                        data: 'parens.coach_id_no',
                    },
                    {
                        data: 'parens.chinese_name',
                        visible: false,
                    },
                    { data: 'parens.email', visible: false },
                    { data: 'parens.phone', visible: false },
                    { data: 'district_session_coaches.session_role' },
                    {
                        data: 'district_session_coaches.level',
                        className: 'center',
                        render: function (data, type, row) {
                            if (data == null) {
                                return '';
                            }

                            // check COACH_CERTIFICATE[data]['name'] is undefined
                            if (
                                COACH_CERTIFICATE[data] == undefined ||
                                COACH_CERTIFICATE[data] == null
                            ) {
                                console.log('data: ' + data);
                                return '';
                            }

                            return COACH_CERTIFICATE[data]['name'];
                        },
                    },
                    {
                        data: 'district_session_coaches.check_attendance',
                        className: 'center',
                        render: function (data, type, row) {
                            data = +data;
                            switch (data) {
                                case COACH_ATTENDANCE_STATUS_PENDING:
                                    return (
                                        '<span class="label label-sm label-primary">' +
                                        ATTENDANCE_STATUS.get(
                                            COACH_ATTENDANCE_STATUS_PENDING
                                        ) +
                                        '</span>'
                                    );
                                case COACH_ATTENDANCE_STATUS_ATTENDED:
                                    return (
                                        '<span class="label label-sm label-success">' +
                                        ATTENDANCE_STATUS.get(
                                            COACH_ATTENDANCE_STATUS_ATTENDED
                                        ) +
                                        '</span>'
                                    );
                                case COACH_ATTENDANCE_STATUS_CANCELLED:
                                    return (
                                        '<span class="label label-sm label-warning">' +
                                        ATTENDANCE_STATUS.get(
                                            COACH_ATTENDANCE_STATUS_CANCELLED
                                        ) +
                                        '</span>'
                                    );
                                case COACH_ATTENDANCE_STATUS_DID_NOT_ATTEND:
                                    return (
                                        '<span class="label label-sm label-danger">' +
                                        ATTENDANCE_STATUS.get(
                                            COACH_ATTENDANCE_STATUS_DID_NOT_ATTEND
                                        ) +
                                        '</span>'
                                    );
                                case COACH_ATTENDANCE_STATUS_SUBSTITUTED:
                                    return (
                                        '<span class="label label-sm label-info">' +
                                        ATTENDANCE_STATUS.get(
                                            COACH_ATTENDANCE_STATUS_SUBSTITUTED
                                        ) +
                                        '</span>'
                                    );
                                default:
                                    return '<span class="label label-sm label-default"></span>';
                            }
                        },
                    },
                    { data: 'district_sessions.name', visible: false },
                    {
                        data: 'district_sessions.start_time',
                        render: function (data, type, row) {
                            if (data == null) {
                                return '';
                            }
                            return moment(data).format('DD/MM/YYYY');
                        },
                    },
                    {
                        data: 'district_sessions.start_time',
                        render: function (data, type, row) {
                            if (data == null) {
                                return '';
                            }
                            return (
                                moment(row.district_sessions.start_time).format(
                                    'HH:mm'
                                ) +
                                ' - ' +
                                moment(row.district_sessions.end_time).format(
                                    'HH:mm'
                                )
                            );
                        },
                    },
                    {
                        data: 'district_sessions.status',
                        render: function (data, type, row) {
                            if (data == 'Active') {
                                // badge success
                                return '<span class="badge badge-success">Active</span>';
                            } else if (data == 'Cancelled') {
                                return '<span class="badge badge-danger">Cancelled</span>';
                            } else if (data == 'Completed') {
                                // badge warning
                                return '';
                            }
                        },
                    },
                    { data: 'district_sessions.cancelled_reason' },
                    {
                        data: 'districts.id',
                        render: function (data, type, row) {
                            return row.districts.name;
                        },
                    },
                    {
                        data: 'groups.name',
                        name: 'groups.name',
                    },
                    {
                        data: 'district_session_coaches.approval_status',
                        name: 'district_session_coaches.approval_status',
                        className: 'center',
                        render: function (data, type, row) {
                            switch (data) {
                                case 'ACCEPT':
                                    return '<span class="label label-sm label-success">Accept</span>';
                                case 'DECLINE':
                                    return '<span class="label label-sm label-danger">Decline</span>';
                                case 'PENDING FOR PAYMENT':
                                    return '<span class="label label-sm label-warning">Pending for payment</span>';
                                case 'APPROVE':
                                    return '<span class="label label-sm label-success">Approve</span>';
                                case 'REJECT':
                                    return '<span class="label label-sm label-danger">Reject</span>';
                                case 'PAID':
                                    return '<span class="label label-sm label-primary">Paid</span>';
                                case 'REFUNDED':
                                    return '<span class="label label-sm label-danger">Refunded</span>';
                                default:
                                    return '<span class="label label-sm label-default"></span>';
                            }
                        },
                    },
                    {
                        data: 'district_session_coaches_log.reason',
                    },
                    {
                        data: 'district_session_coaches_log.update_time',
                    },
                    {
                        data: 'district_session_coaches.duration',
                        className: 'center',
                    },
                    {
                        data: 'district_session_coaches.amount',
                        render: $.fn.dataTable.render.number(',', '.', 2, '$'),
                    },
                    {
                        data: 'district_session_coaches.id',
                        className: 'center',
                        render: function (data, type, row) {
                            //   var action_menu = ' <ul class="dropdown-menu btn" role="menu" aria-labelledby="dLabel">';
                            //   if (user.role == USER_SUPER_ADMIN) {
                            //     action_menu += '<li><a href="javascript:void(0)" onclick="editDistrictSession(' + row.district_session_coaches.id + ')">Edit</a></li>';
                            //   }
                            //   action_menu += '<li><a href="javascript:void(0)" onclick="approveDistrictSession(' + row.district_session_coaches.id + ')">Approve</a></li>';
                            //   action_menu += '<li><a href="javascript:void(0)" onclick="historyDistrictSession(' + JSON.stringify(row) + ')">History</a></li>';
                            //   action_menu += '</ul>';
                            //   return action_menu;
                            var action_menu =
                                '<div class="btn-group" style="margin-top:0px">';
                            action_menu +=
                                '<button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">';
                            action_menu += 'Action <span class="caret"></span>';
                            action_menu += '</button>';
                            action_menu += '<ul class="dropdown-menu">';
                            if (
                                user.role == USER_SUPER_ADMIN ||
                                user.role == USER_LEAGUE_ADMIN ||
                                user.role == USER_GRASSROOTS_FINANCE ||
                                user.role == USER_FINANCE
                            ) {
                                action_menu +=
                                    '<li><a href="javascript:void(0)" onclick="editDistrictSession(' +
                                    row.district_session_coaches.id +
                                    ')"><i class="fa fa-pencil" aria-hidden="true"></i>Edit</a></li>';
                            }
                            action_menu +=
                                '<li><a href="javascript:void(0)" onclick="approveDistrictSession(' +
                                row.district_session_coaches.id +
                                ')"><i class="fa fa-check" aria-hidden="true"></i>Approve</a></li>';
                            action_menu +=
                                '<li><a href="javascript:void(0)" onclick="historyDistrictSession(' +
                                row.district_session_coaches.id +
                                ')"><i class="fa fa-history" aria-hidden="true"></i>History</a></li>';
                            action_menu += '</ul>';
                            action_menu += '</div>';
                            return action_menu;
                        },
                    },
                ],
                createdRow: function (row, data, dataIndex) {
                    if (data.district_sessions.is_non_training_day) {
                        $(row).css('background-color', '#FFB6C1');
                        return;
                    }
                    if (data.district_sessions.is_overlap) {
                        $(row).css('background-color', '#FFFACD');
                        return;
                    }
                },
                initComplete: function (settings, json) {
                    // hide first column
                    this.api().columns([0]).visible(false);

                    // Add filter by district, group, coach
                    var districtFilterElement = {
                        column: 'districts.id',
                        orderColumn: 14,
                        elementId: 'district_filter',
                        options: json.options['districts.id'],
                        name: 'district',
                        label: 'Filter by district:',
                        selectId: 'selDistrict',
                    };
                    var groupFilterElement = {
                        column: 'groups.name',
                        orderColumn: 15,
                        elementId: 'group_filter',
                        options: json.options['groups.name'],
                        name: 'group',
                        label: 'Filter by group:',
                        selectId: 'selGroup',
                    };
                    var coachFilterElement = {
                        column: 'parens.coach_id_no',
                        orderColumn: 2,
                        elementId: 'coach_filter',
                        options: json.options['parens.coach_id_no'],
                        selectId: 'selType',
                        name: 'coach',
                        label: 'Filter by coach:',
                    };
                    var approvalStatusFilterElement = {
                        orderColumn: 16,
                        column: 'district_session_coaches.approval_status',
                        elementId: 'approval_status_filter',
                        options:
                            json.options[
                                'district_session_coaches.approval_status'
                            ],
                        selectId: 'selApprovalStatus',
                        name: 'approval_status',
                        label: 'Filter by approval status:',
                    };

                    var filterColumns = [
                        districtFilterElement,
                        groupFilterElement,
                        coachFilterElement,
                        approvalStatusFilterElement,
                    ];

                    filterColumns.forEach((item) => {
                        this.api()
                            .columns(item.orderColumn)
                            .every(function () {
                                var column = this;

                                // set label
                                $(`#${item.elementId}`).html(
                                    `<label>${item.label}</label>`
                                );

                                // create select element
                                var select = $(
                                    `<select id="${item.orderColumn}" class="form-control"><option value="">All</option></select>`
                                )
                                    .appendTo($(`#${item.elementId}`))
                                    .on('change', function () {
                                        if ($(this).val()) {
                                            var val =
                                                $.fn.dataTable.util.escapeRegex(
                                                    $(this).val()
                                                );
                                            column
                                                .search(
                                                    val ? val : '',
                                                    true,
                                                    false
                                                )
                                                .draw();
                                        } else {
                                            column
                                                .search('', true, false)
                                                .draw();
                                        }
                                    })
                                    .select2();

                                // add options
                                item.options.forEach(function (d) {
                                    select.append(
                                        `<option value="${d.value}">${d.label}</option>`
                                    );
                                });
                            });
                    });
                },
                select: {
                    style: SELECT_MODE,
                    selector: 'td:not(:last-child)',
                },
                order: [[9, 'asc']],
                buttons: [
                    {
                        name: 'approve',
                        text: '<i class="fa fa-check" aria-hidden="true"></i>&emsp;Approve',
                        action: function (e, dt, node, config) {
                            // Get selected rows
                            var selectedRows = dt.rows({
                                selected: true,
                            });

                            // Get selected rows data
                            var selectedRowsData = selectedRows.data();

                            // Get selected rows id
                            var selectedRowsId = selectedRowsData
                                .toArray()
                                .map(
                                    (item) =>
                                        item['district_session_coaches']['id']
                                );

                            // Check if there is any selected row
                            if (selectedRowsId.length == 0) {
                                // alert('Please select at least one row');
                                Swal.fire({
                                    icon: 'warning',
                                    title: 'Oops...',
                                    text: 'Please select at least one row',
                                });
                                return;
                            } else {
                                let editorApproveMultiple =
                                    new $.fn.dataTable.Editor({
                                        ajax: {
                                            type: 'POST',
                                            url:
                                                SERVER_PATH +
                                                'district-session/approveDistrictSessionCoaches',
                                                headers: {	
                                                    'x-user-id': $rootScope.user_id,
                                                    'x-user-email': $rootScope.user_name
                                                },
                                            data: {
                                                user_id: $rootScope.user_id,
                                                district_session_coach_ids:
                                                    selectedRowsId,
                                            },
                                            dataType: 'json',
                                            complete: function (response) {
                                                let data =
                                                    response.responseJSON;
                                                if (data.status == 'OK') {
                                                    Swal.fire({
                                                        title: 'Completed!',
                                                        html: data.message,
                                                        className: 'align-left',
                                                        icon: 'success',
                                                        confirmButtonText: 'OK',
                                                    });
                                                    table.ajax.reload();
                                                } else if (
                                                    data.status == 'ERROR'
                                                ) {
                                                    Swal.fire({
                                                        title: 'Error!',
                                                        text: data.message,
                                                        icon: 'error',
                                                        confirmButtonText: 'OK',
                                                    });
                                                } else {
                                                    // skip
                                                }
                                            },
                                            error: function (
                                                xhr,
                                                status,
                                                error
                                            ) {
                                                console.log(error);
                                            },
                                        },
                                        formOptions: {
                                            main: {
                                                onBlur: 'none',
                                            },
                                        },
                                        fields: [
                                            {
                                                label: 'Status:',
                                                name: 'status',
                                                type: 'radio',
                                                options: [
                                                    ...SESSION_APPROVAL_STATUS.filter(
                                                        (item) =>
                                                            item.role ==
                                                            user.role
                                                    ).map((item) => {
                                                        return {
                                                            label: item.name,
                                                            value: item.name,
                                                        };
                                                    }),
                                                ],
                                            },
                                            // add field to enter reason
                                            {
                                                label: 'Reason:',
                                                name: 'reason',
                                                type: 'textarea',
                                            },
                                        ],
                                    });

                                editorApproveMultiple
                                    .title('Approve Session Attendance')
                                    .buttons({
                                        label: 'Save',
                                        fn: function () {
                                            this.submit();
                                        },
                                    })
                                    .edit()
                                    .open();
                            }

                            // Deselect all rows
                            // dt.rows().deselect();

                            // Reload datatable
                            // dt.ajax.reload();
                        },
                    },
                    {
                        extend: 'excel',
                        name: 'excel',
                        text: '<i class="fa fa-file-excel-o"></i>&emsp;Export to Excel',
                        titleAttr: 'Export data to an Excel file',
                        filename: `HKFA Grassroots - ${
                            event_type
                        } ${
                            event_name
                        } - Coach Attendance Report - ${moment().format(
                            'YYYY-MM-DD HH:mm:ss'
                        )}`,
                        exportOptions: {
                            columns: ':visible',
                            modifier: {
                                autoFilter: true,
                                // selected: true
                            },
                        },
                        action: exportAllToExcel,
                    },
                    {
                        extend: 'colvis',
                        text: 'Columns',
                        className:
                            'dt-button buttons-collection buttons-colvis',
                    },
                ],
                lengthMenu: [
                    [10, 25, 50, 100],
                    [10, 25, 50, 100],
                ],
                displayLength: 10,
                columnDefs: [
                    {
                        type: 'justNum',
                        targets: 2,
                    },
                    { orderable: false, searchable: false, targets: [17,18,21] },
                ],
            });

            approveDistrictSession = function (id) {
                if (
                    user.role != USER_SUPER_ADMIN &&
                    user.role != USER_LEAGUE_ADMIN &&
                    user.role != USER_GRASSROOTS_FINANCE &&
                    user.role != USER_FINANCE
                ) {
                    Swal.fire({
                        title: 'Error!',
                        text: 'You do not have permission to edit this session.',
                        icon: 'error',
                        confirmButtonText: 'OK',
                    });

                    return;
                }
                var data = table
                    .data()
                    .toArray()
                    .find(function (value) {
                        return value.district_session_coaches.id == id;
                    });

                // find in array
                var status_role = APPROVAL_STATUS_CAN_CHANGE.get(
                    parseInt(user.role)
                );

                let index = status_role.findIndex(
                    (x) => x == data.district_session_coaches.approval_status
                );

                if (index == -1) {
                    Swal.fire({
                        title: 'Error!',
                        text: 'You do not have permission to approve this session.',
                        icon: 'error',
                        confirmButtonText: 'OK',
                    });

                    return;
                }

                let approveEditor = new $.fn.dataTable.Editor({
                    ajax: {
                        type: 'POST',
                        url:
                            SERVER_PATH +
                            'district-session/adminApproveSession',
                        headers: {	
                            'x-user-id': $rootScope.user_id,
                            'x-user-email': $rootScope.user_name
                        },
                        data: {
                            user_id: $rootScope.user_id,
                            district_session_coaches_id:
                                data.district_session_coaches.id,
                        },
                        dataType: 'json',
                        complete: function (response) {
                            let data = response.responseJSON;
                            if (data.status == 'OK') {
                                table.ajax.reload();
                            } else if (data.status == 'ERROR') {
                                Swal.fire({
                                    title: 'Error!',
                                    text: data.message,
                                    icon: 'error',
                                    confirmButtonText: 'OK',
                                });
                            } else {
                                // skip
                            }
                        },
                        error: function (xhr, status, error) {
                            console.log(error);
                        },
                    },
                    formOptions: {
                        main: {
                            onBlur: 'none',
                        },
                    },
                    fields: [
                        {
                            label: 'Status:',
                            name: 'status',
                            type: 'radio',
                            options: [
                                ...SESSION_APPROVAL_STATUS.filter(
                                    (item) => item.role == user.role
                                ).map((item) => {
                                    return {
                                        label: item.name,
                                        value: item.name,
                                    };
                                }),
                            ],
                        },
                        // add field to enter reason
                        {
                            label: 'Reason:',
                            name: 'reason',
                            type: 'textarea',
                        },
                    ],
                });

                approveEditor
                    .title('Approve Session Attendance')
                    .buttons({
                        label: 'Save',
                        fn: function () {
                            this.submit();
                        },
                    })
                    .edit()
                    .open();
            };

            editDistrictSession = function (id) {
                if (
                    $rootScope.user.role != USER_SUPER_ADMIN &&
                    $rootScope.user.role != USER_LEAGUE_ADMIN &&
                    $rootScope.user.role != USER_GRASSROOTS_FINANCE &&
                    $rootScope.user.role != USER_FINANCE
                ) {
                    Swal.fire({
                        title: 'Error!',
                        text: 'You do not have permission to edit this session.',
                        icon: 'error',
                        confirmButtonText: 'OK',
                    });

                    return;
                }
                // get all the data from the row
                var data = table
                    .data()
                    .toArray()
                    .find(function (value) {
                        return value.district_session_coaches.id == id;
                    });

                // find in array
                var status_role = APPROVAL_STATUS_CAN_CHANGE.get(
                    parseInt(user.role)
                );

                let index = status_role.findIndex(
                    (x) => x == data.district_session_coaches.approval_status
                );

                if (index == -1) {
                    Swal.fire({
                        title: 'Error!',
                        text: 'You do not have permission to change this session.',
                        icon: 'error',
                        confirmButtonText: 'OK',
                    });

                    return;
                }

                let modifyEditor = new $.fn.dataTable.Editor({
                    ajax: {
                        type: 'POST',
                        url:
                            SERVER_PATH +
                            'district-session/modifyDurationAndRate',
                        headers: {	
                            'x-user-id': $rootScope.user_id,
                            'x-user-email': $rootScope.user_name
                        },
                        data: {
                            user_id: $rootScope.user_id,
                            district_session_coaches_id:
                                data.district_session_coaches.id,
                        },
                        dataType: 'json',
                        complete: function (response) {
                            let data = response.responseJSON;
                            if (data.status == 'OK') {
                                table.ajax.reload();
                            } else {
                                if (typeof data.fieldErrors == 'undefined') {
                                    Swal.fire({
                                        title: 'Error!',
                                        text: data.message,
                                        icon: 'error',
                                        confirmButtonText: 'OK',
                                    });
                                }
                            }
                        },
                        error: function (xhr, status, error) {
                            console.log(error);
                        },
                    },
                    formOptions: {
                        main: {
                            onBlur: 'none',
                        },
                    },
                    fields: [
                        {
                            label: 'Duration:',
                            name: 'duration',
                            attr: {
                                type: 'number',
                            },
                            default: data.district_session_coaches.duration,
                        },
                        {
                            label: 'Rate:',
                            name: 'rate',
                            type: 'select',
                            options: COACH_CERTIFICATE.filter(function (o) {
                                return typeof o.name != 'undefined';
                            }).map((item, index) => {
                                return {
                                    label: item.name,
                                    value: item.value,
                                };
                            }),
                            default: data.district_session_coaches.level,
                        },
                    ],
                });

                modifyEditor
                    .title('Modify Coach Attendance')
                    .buttons({
                        label: 'Save',
                        fn: function () {
                            this.submit();
                        },
                    })
                    .edit()
                    .open();
            };

            var getModalHistoryTableHtml = function (id) {
                var html =
                    '<table id="tbl_history_district_' +
                    id +
                    '" class="table table-striped table-bordered" style="width:100%"><thead><tr> <td>Action</td> <td>Reason</td> <td>Description</td> <td>Update time</td> <td>Update by</td> </tr></thead></table>';
                return html;
            };

            historyDistrictSession = function (id) {
                // get data from the row
                var data = table
                    .data()
                    .toArray()
                    .find(function (value) {
                        return value.district_session_coaches.id == id;
                    });

                var msg = getModalHistoryTableHtml(
                    data.district_session_coaches.id
                );

                BootstrapDialog.show({
                    title:
                        'History - ' +
                        data.parens.surname +
                        ' ' +
                        data.parens.other_name +
                        ' - ' +
                        data.district_sessions.name,
                    message: msg,
                    size: BootstrapDialog.SIZE_WIDE,
                    onshown: function (dialogRef) {
                        initHistoryTbl(data);
                    },
                });
            };

            initHistoryTbl = function (data) {
                var tableHistory = $(
                    '#tbl_history_district_' + data.district_session_coaches.id
                ).DataTable({
                    dom: '<"row"B>rt<"row"i>',
                    stateSave: true,
                    deferRender: true,
                    ajax: {
                        url:
                            SERVER_PATH +
                            'district-session/adminDistrictSessionHistory',
                        type: 'POST',
                        headers: {	
                            'x-user-id': $rootScope.user_id,
                            'x-user-email': $rootScope.user_name
                        },
                        data: {
                            district_session_coaches_id:
                                data.district_session_coaches.id,
                        },
                        dataType: 'json',
                        complete: function (response) {},
                        error: function (xhr, status, error) {},
                    },
                    language: {
                        paginate: {
                            previous: '<i class="fa fa-chevron-left"></i>',
                            next: '<i class="fa fa-chevron-right"></i>',
                        },
                    },
                    columns: [
                        {
                            data: 'district_session_coaches_log.status',
                        },
                        {
                            data: 'district_session_coaches_log.reason',
                        },
                        {
                            data: 'district_session_coaches_log.description',
                        },
                        {
                            data: 'district_session_coaches_log.update_time',
                            render: function (data, type, row) {
                                return moment(data).format('DD/MM/YYYY HH:mm');
                            },
                        },
                        {
                            data: 'name',
                        },
                    ],
                    select: {
                        style: 'single',
                        selector: 'td:not(:last-child)',
                    },
                    columnDefs: [{ type: 'date-euro', targets: 3 }],
                    order: [[3, 'desc']],
                });
            };
        }

        $scope.$on('$destroy', function () {
            $('#select_event_regional').select2('destroy');

            // $('#btnSelectDistrict').unbind();
        });

        /**
         * Team Coaches
         */
        function initTeamCoachesReportHtml(event_id) {
            var html =
                '<table id="tableTeamCoachesReport_' +
                event_id +
                '" class="table table-striped table-bordered table-hover" cellspacing="0"width="100%">' +
                '<thead>' +
                '<tr>' +
                '<th>Team</th>' +
                '<th>Coach ID</th>' +
                '<th>Coach name</th>' +
                '<th>Chinese name</th>' +
                '<th>Phone</th>' +
                '<th>Email</th>' +
                '<th>Role</th>' +
                '</tr>' +
                '</table>';
            return html;
        }

        function initTeamCoachesReport(event_id) {
            $('#content-team-coaches').html(
                initTeamCoachesReportHtml(event_id)
            );

            var teamColumn = 0;
            var table = $('#tableTeamCoachesReport_' + event_id).DataTable({
                dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
                stateSave: true,
                deferRender: true,
                ajax: {
                    url: SERVER_PATH + 'district/getDistrictTeamCoachesReport',
                    type: 'POST',
                    headers: {	
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name
                    },
                    data: {
                        event_id: event_id,
                    },
                    dataType: 'json',
                    complete: function (response) {
                        console.log(response);
                    },
                    error: function (xhr, status, error) {},
                },
                language: {
                    info: 'Showing _START_ to _END_ of _TOTAL_ coach',
                    infoEmpty: 'Showing 0 to 0 of 0 coaches',
                    lengthMenu: 'Show _MENU_ coaches',
                    select: {
                        rows: {
                            _: 'You have selected %d coaches',
                            0: 'Click a coach to select',
                            1: '1 coach selected',
                        },
                    },
                    paginate: {
                        previous: '<i class="fa fa-chevron-left"></i>',
                        next: '<i class="fa fa-chevron-right"></i>',
                    },
                },
                columns: [
                    {
                        data: 'team_districts.name',
                    },
                    {
                        data: 'parens.coach_id_no',
                    },
                    {
                        data: null,
                        render: function (data, type, row) {
                            return (
                                data.parens.surname +
                                ' ' +
                                data.parens.other_name
                            );
                        },
                    },
                    {
                        data: 'parens.chinese_name',
                    },
                    {
                        data: null,
                        render: function (data, type, row) {
                            return (
                                data.parens.country_code +
                                ' ' +
                                data.parens.phone
                            );
                        },
                    },
                    {
                        data: 'parens.email',
                    },
                    {
                        data: 'district_team_coaches.team_role',
                    },
                ],
                select: {
                    style: SELECT_MODE,
                },
                lengthMenu: [
                    [10, 25, 50, 100],
                    [10, 25, 50, 100],
                ],
                columnDefs: [{ targets: teamColumn }],
                order: [[teamColumn, 'asc']],
                displayLength: 100,
                drawCallback: function (settings) {
                    var api = this.api();
                    var rows = api.rows({ page: 'current' }).nodes();
                    var last = null;

                    api.column(teamColumn, { page: 'current' })
                        .data()
                        .each(function (group, i) {
                            // get number of visible columns
                            var colCount = 14;
                            let colCountLive = api
                                .columns(':visible')
                                .nodes().length;
                            if (colCountLive > 0) {
                                colCount = colCountLive;
                            }

                            if (last !== group) {
                                $(rows)
                                    .eq(i)
                                    .before(
                                        '<tr class="group"><td colspan="' +
                                            colCount +
                                            '">' +
                                            group +
                                            '</td></tr>'
                                    );

                                last = group;
                            }
                        });
                },
                buttons: [
                    {
                        extend: 'excel',
                        name: 'excel',
                        text: '<i class="fa fa-file-excel-o"></i>&emsp;Export to Excel',
                        titleAttr: 'Export data to an Excel file',
                        filename: `HKFA Grassroots - ${event_type} ${event_name} - Team Coaches Report - ${moment().format(
                            'YYYY-MM-DD HH:mm:ss'
                        )}`,
                        exportOptions: {
                            columns: ':visible',
                            modifier: {
                                autoFilter: true,
                                // selected: true
                            },
                        },
                    },
                    {
                        extend: 'colvis',
                        text: 'Columns',
                    },
                ],
            });

            // Order by the grouping
            $('#tableTeamCoachesReport_' + event_id + 'tbody').on(
                'click',
                'tr.group',
                function () {
                    var currentOrder = table.order()[0];
                    if (
                        currentOrder[0] === teamColumn &&
                        currentOrder[1] === 'asc'
                    ) {
                        table.order([teamColumn, 'desc']).draw();
                    } else {
                        table.order([teamColumn, 'asc']).draw();
                    }
                }
            );
        }
    }
);
