app.controller('paymentsCtrl', function($scope, $rootScope, $routeParams, $http){
    $('#page-wrapper').removeClass('nav-small');

    var PAYMENTS_ACTION_SEND_REMINDER = 1;
    var PAYMENTS_ACTION_RECORD_PAYMENT = 2;
    var PAYMENTS_ACTION_REFUND_PAYMENT = 3;

    var REPORTS_ACTION_UPDATE_PAYMENT = 4;

    var INVOICE_ACTION_EDIT_INVOICE = 5;

    $.fn.dataTable.moment( 'D-MMM-YYYY HH:mm:ss' );
    $.fn.dataTable.moment( 'D-MMM-YYYY' );

    // get info event
	var event_id = $routeParams.id;
    jQuery.ajax({
    	type: 'POST',
  		url: SERVER_PATH + "event/getEventInfo",
  		async: false,
        headers: {	
            'x-user-id': $rootScope.user_id,
            'x-user-email': $rootScope.user_name
        },
  		data: {
    		"event_id": event_id
    	},
  		dataType: 'json',
  		complete: function (response) {
  			var jsonData = JSON.parse(response.responseText);
  			var event = jsonData.info;
  			event_name = event.name;
  			event_type = event.type;
  		}
    });
    console.log('paymentsCtrl - event_id, name, type  = ' + event_id + ', ' + event_name + ', ' + event_type);
    $scope.event_id = event_id;
    $rootScope.event_name = event_name;

    // $('#paymentTableContent').html(getTableHtml(event_id));
    // initPaymentTable(event_id, event_name);

    $scope.events = [{id: event_id, name: event_name}];
    $scope.selectedEvent=$scope.events[0];

    // get all event
    jQuery.ajax({
        type: 'POST',
        url: SERVER_PATH + "event/getSpecialEvents",
        async: false,
        headers: {	
            'x-user-id': $rootScope.user_id,
            'x-user-email': $rootScope.user_name
        },
        data: {
            "event_id": event_id
        },
        dataType: 'json',
        complete: function (response) {
            var jsonData = JSON.parse(response.responseText);
            console.log(jsonData);
            if (jsonData.status == "OK") {
                console.log($scope.events);
                // angular.extend($scope.events, jsonData.info);
                for (var i=0; i<jsonData.info.length; i++){
                    $scope.events.push(jsonData.info[i]);
                }
                console.log($scope.events);
                $scope.selectedEvent = $scope.events[0];
                // getGroup();
            }
        }
    });

    $('button').on('click', function (e) {
        console.log("click");
        var special_event_id = $scope.selectedEvent.id;
        var special_event_name = $scope.selectedEvent.name;
        var html = getTableHtml(special_event_id);
        $('#paymentsTableContent').html(html);
        initPaymentTable(special_event_id, special_event_name);
    });

    // create html
    function getTableHtml(event_id) {
        var html = '' +
            '<div class="row">' +
                '<div class="col-lg-12">' +
                    '<div class="main-box clearfix">' +
                        '<div class="main-box-body clearfix">' +
                            '<div class="form-group">' +
                                '<label>Filter by Status</label>' +
                                '<br/>' +
                            '</div>' +
                            '<div class="table-responsive">' +
                                '<table id="table_invoice_' + event_id + '" class="table table-striped table-bordered table-hover" cellspacing="0" width="100%">' +
                                    '<thead>' +
                                        '<tr>' +
                                            '<th></th>' +
                                            '<th>Player</th>' +
                                            '<th>Year</th>' +
                                            '<th>Club</th>' +
                                            '<th>Parent</th>' +
                                            '<th>Email</th>' +
                                            '<th>App.date</th>' +
                                            '<th>App.status</th>' +
                                            '<th>Inv.number</th>' +
                                            '<th>Amount</th>' +
                                            '<th>Status</th>' +
                                        '</tr>' +
                                    '</thead>' +
                                    '<tfoot>' +
                                        '<tr>' +
                                            '<th></th>' +
                                            '<th>Player</th>' +
                                            '<th>Year</th>' +
                                            '<th>Club</th>' +
                                            '<th>Parent</th>' +
                                            '<th>Email</th>' +
                                            '<th>App.date</th>' +
                                            '<th>App.status</th>' +
                                            '<th>Inv.number</th>' +
                                            '<th>Amount</th>' +
                                            '<th>Status</th>' +
                                        '</tr>' +
                                    '</tfoot>' +
                                '</table>' +
                            '</div>' +
                        '</div>' +
                    '</div>' +
                '</div>' +
            '</div>' +
            '';
        return html;
    }

    // Payment Speical Event
    function initPaymentTable(event_id, event_name) {
        function cbDropdown(column) {
            return $('<ul>', {
              'class': 'cb-dropdown'
            }).appendTo($('<div>', {
              'class': 'cb-dropdown-wrap'
            }).appendTo(column));
          }
        table = $('#table_invoice_' + event_id).DataTable({
            dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
            stateSave: true,
            deferRender: true,
            ajax: {
                url: SERVER_PATH + "invoice/getPaymentReport",
                type: 'POST',
                dataType: 'json',
                headers: {	
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name
                },
                data: {
                    'event_id': event_id
                },
                complete: function (response) {
                },
                error: function(xhr, status, error) {
                }
            },
            columns: [
                {
                    data: 'DT_RowId',
                    // defaultContent: '',
                    // className: 'select-checkbox',
                    // orderable: false
                    targets: 0,
                    render: function (data, type, row, meta) {
                        if (type === 'display') {
                            data = '<div class="checkbox"><input type="checkbox" class="dt-checkboxes"><label></label></div>';
                        }

                        return data;
                    },
                    checkboxes: {
                        selectRow: true,
                        selectAllRender: '<div class="checkbox"><input type="checkbox" class="dt-checkboxes"><label></label></div>'
                    }
                },
                {
                    data: null,
                    render: function (data, type, row) {
                        return data.players.surname + ' ' + data.players.other_name;
                    }
                },
                { data: "players.dob", className: "center" },
                // { data: "pgroups.name" },
                { data: "clubs.name", className: "center" },
                {
                    data: null,
                    render: function (data, type, row) {
                        return data.parens.surname + ' ' + data.parens.other_name;
                    }
                },
                { data: "parens.email" },
                // { data: "parens.hkfc_account", className: "center" },
                { data: "registrations.approved_date", className: "center" },
                {
                    data: "registrations.approval_status",
                    className: "center",
                    render: function (data, type, full, meta) {
                        switch (data) {
                            case APPROVAL_STATUS_Approve: return '<span class="label label-primary">' + data + '</span>';
                            // case APPROVAL_STATUS_App_Int_Mail: return '<span class="label label-primary">' + data + '</span>';
                            // case APPROVAL_STATUS_App_Int_NotMail: return '<span class="label label-primary">' + data + '</span>';
                            // case APPROVAL_STATUS_Approve: return '<span class="label label-success">' + data + '</span>';
                            // case APPROVAL_STATUS_Register: return '<span class="label label-info">' + data + '</span>';
                            // case APPROVAL_STATUS_Roll_Register: return '<span class="label label-info">' + data + '</span>';
                            // case APPROVAL_STATUS_Waitlist: return '<span class="label label-warning">' + data + '</span>';
                            // case APPROVAL_STATUS_Withdraw: return '<span class="label label-danger">' + data + '</span>';
                            // case APPROVAL_STATUS_Roll_Withdraw: return '<span class="label label-danger">' + data + '</span>';
                            default: return '<span class="label label-default">' + data + '</span>';
                        }
                    }
                },
                { data: "invoices.invoice_number", className: "center" },
                { data: "registrations.amount", className: "center" },
                { 
                    data: "invoices.status",
                    name: "name",
                    name_index: "name_index",
                    className: "center"
                }
            ],
            select: {
                style: SELECT_MODE,
                selector: 'td:first-child'
            },
            order: [[1, 'asc']],
            lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "All"]],
            // displayLength: -1,
            buttons: [
                {
                    extend: 'collection',
                    text: 'Actions',
                    autoClose: true,
                    buttons: [
                        {
                            extend: 'excel',
                            name: 'excel',
                            text: '<i class="fa fa-file-excel-o"></i>&emsp;Export to Excel',
                            titleAttr: 'Export data to an Excel file',
                            filename: 'Payment Report - ' + event_name,
                            title: 'player',
                            exportOptions: {
                                columns: ':visible',
                                modifier: {
                                    selected: true
                                }
                            }
                        },
                        {
                            text: '<i class="fa fa-history"></i>&emsp;Reminder',
                            titleAttr: 'Reminder to parent(s)',
                            event_id: event_id,
                            action: function ( e, dt, node, config ) {
                                action = config.text;
                                event_id = config.event_id;
                                checkRequest(event_id, PAYMENTS_ACTION_SEND_REMINDER, action);
                            }
                        },
                        {
                            text: '<i class="fa fa-check"></i>&emsp;Mark as paid',
                            titleAttr: 'Reminder to parent(s)',
                            event_id: event_id,
                            action: function ( e, dt, node, config ) {
                                action = config.text;
                                event_id = config.event_id;
                                checkRequest(event_id, PAYMENTS_ACTION_RECORD_PAYMENT, action);
                            }
                        },
                        {
                            text: '<i class="fa fa-reply"></i>&emsp;Refund',
                            titleAttr: 'Refund to parent(s)',
                            event_id: event_id,
                            action: function ( e, dt, node, config ) {
                                action = config.text;   
                                event_id = config.event_id;
                                checkRequest(event_id, PAYMENTS_ACTION_REFUND_PAYMENT, action);
                            }
                        },
                        {
                            text: "<i class='fa fa-refresh'></i>&emsp;Update status",
                            event_id: event_id,
                            action: function ( e, dt, node, config ) {
                                action = config.text;
                                event_id = config.event_id;
                                checkRequest(event_id, REPORTS_ACTION_UPDATE_PAYMENT, action);
                            }
                        },
                    ]
                },
                // {
                //     text: "Edit Invoice",
                //     action: function ( e, dt, node, config ) {
                //         action = config.text;
                //         editInvoice(event_id, INVOICE_ACTION_EDIT_INVOICE, action);
                //     }
                // },
                { extend: 'colvis', text: 'Columns' }
            ],
            initComplete: function () {
                this.api().columns('10').every(function () {
                    var column = this;
                    //added class "mymsel"
                    var ddmenu = cbDropdown($(column.header()))
                        .on('change',':checkbox', function () {
                            var vals = $(':checked', ddmenu).map(function (index, element) {
                                return $.fn.dataTable.util.escapeRegex($(element).val());
                            }).toArray().join('|');

                            column
                                .search(vals.length > 0 ? '^(' + vals + ')$' : '', true, false)
                                .draw();
                        });

                    column.data().unique().sort().each(function (d, j) {
                        var // wrapped
                            $label = $('<label>'),
                            $text = $('<span>', {
                                style: 'margin: 0px 15px 10px 0px',
                                text: d
                            }),
                            $cb = $('<input>', {
                                type: 'checkbox',
                                style: 'height: 17px; width: 17px; vertical-align: bottom; position: relative; top: -1px; *overflow: hidden; margin-right: 2px;',
                                value: d
                            });
                        
                        $cb.appendTo($label);
                        $text.appendTo($label);

                        ddmenu.append($label);
                        ddmenu.add().css('margin','0px 0px -20px -35px').appendTo(".form-group")
                    });
                });

                $(".cb-dropdown-wrap").each(function () {
                    console.log($(this).parent().width());
                    $(this).width($(this).parent().width());
                });
            }
        });
    }

    function checkRequest(event_id, action_id, action) {
        if (action_id == PAYMENTS_ACTION_SEND_REMINDER || action_id == PAYMENTS_ACTION_RECORD_PAYMENT || action_id == PAYMENTS_ACTION_REFUND_PAYMENT || action_id == REPORTS_ACTION_UPDATE_PAYMENT) {
 			table = $('#table_invoice_' + event_id).DataTable();
        }

        if (DEVELOPMENT_ENVIRONMENT) console.log('checkRequest - action: ' + action);

        var table_selected = table.rows( { selected: true } ).data();
        var countRows = table_selected.length;

        var MAX_NAMES_TO_DISPLAY = 10;

        var names = ''; 
        var name_index = 0;
        var rows_selected = [];
        for (var i = 0; i < table_selected.length; i++) {
        	
			if (action_id == PAYMENTS_ACTION_SEND_REMINDER || action_id == PAYMENTS_ACTION_RECORD_PAYMENT || action_id == PAYMENTS_ACTION_REFUND_PAYMENT || action_id == REPORTS_ACTION_UPDATE_PAYMENT) {
 				id = table_selected[i].invoices.id;
 				name = table_selected[i].parens.email + ' (' + table_selected[i].invoices.invoice_number + ')';
 			}				
            rows_selected.push(id);
			// if (i <= MAX_NAMES_TO_DISPLAY) {
    	    //    	if (i != 0)
        	//     	names += '</br>';
	    	// 	if (i < MAX_NAMES_TO_DISPLAY)
            //         	names += name;
            //     else
            //     	names += '...';
            // }
            name_index = i + 1;
			names += name_index +'. ' + name +'<br/>';
        }

        if (DEVELOPMENT_ENVIRONMENT) console.log('checkRequest - rows_selected: ' + countRows);
        
        rows_selected.sort();
        var selectedRows = (rows_selected.join("_"));
        var data = null;

        if (action_id == PAYMENTS_ACTION_SEND_REMINDER) {
 			message = (countRows == 0) ? 'At least one invoice must be selected!' :
			((countRows == 1) ? '<strong>1 invoice?</strong></br>' + names :
            	'<strong>' + countRows + ' invoices?</strong></br>' + names); 
        } else if (action_id == REPORTS_ACTION_UPDATE_PAYMENT) {
            message = (countRows == 0) ? 'At least one invoice must be selected!' :
			((countRows == 1) ? '<strong>1 invoice?</strong></br>' + names :
            	'<strong>' + countRows + ' invoices?</strong></br>' + names); 
        } else if (action_id == PAYMENTS_ACTION_RECORD_PAYMENT) {
            if (countRows != 1) {
                selectedRows = "";
                if (countRows > 1) {
                    message = 'Only one invoice can be selected!';
                } else {
                    message = 'Please select one invoice!';
                }
            } else {
                data = table_selected[0];
            }
        } else if (action_id == PAYMENTS_ACTION_REFUND_PAYMENT) {
            if (countRows != 1) {
                selectedRows = "";
                if (countRows > 1) {
                    message = 'Only one invoice can be selected!';
                } else {
                    message = 'Please select one invoice!';
                }
            } else {
                data = table_selected[0];
                console.log(data);
                if (data.invoices.status ===  'REFUNDED') {
                    selectedRows = "";
                    message = 'Please select one invoice has been refunded';
                }
            }
        } 

        if (selectedRows == "") {
            // Not selected
            BootstrapDialog.show({
                title: 'Information - ' + action,
                type: BootstrapDialog.TYPE_WARNING,
                message: message
            });
        } else {
            if (data == null) {
                title = 'Confirmation - ' + action;
                var dialog = BootstrapDialog.confirm( title, message, function(result){
                    if(result) {
                        dialog.close();
                        if (DEVELOPMENT_ENVIRONMENT) console.log('checkRequest - action_id: ' + action_id);
                        var urlStr = "";
                        if (action_id == PAYMENTS_ACTION_SEND_REMINDER) {
                            urlStr = SERVER_PATH + "invoice/reminderPayment";
                            ajaxReminderAction(event_id, rows_selected, urlStr, action, action_id);
                        } else if ( action_id == REPORTS_ACTION_UPDATE_PAYMENT){
                            urlStr = SERVER_PATH + "invoice/UpdateStatus";
                            ajaxUpdateStatus(event_id, rows_selected, urlStr, action, action_id);
                        }
                    } 
                }); 
                if (name_index > 15 ) {
                    dialog.getModalBody().css('height', '330px');
                    dialog.getModalBody().css('overflow-y', 'scroll');
                }
            } else {
                if (action_id == PAYMENTS_ACTION_RECORD_PAYMENT) {
                    urlStr = SERVER_PATH + "invoice/recordPayment";
                    ajaxRecordPaymentAction(event_id, data, urlStr, action, action_id);
                } else if (action_id == PAYMENTS_ACTION_REFUND_PAYMENT) {
                    checkRefundInvoice(data.invoices.id, event_id, action, action_id, data);
                    // urlStr = SERVER_PATH + "invoice/refundPayment";
                    // ajaxRefundPaymentAction(event_id, data, urlStr, action, action_id);
                }
            }
        }
    }

    // Reminder
    function ajaxReminderAction(event_id, invoice_ids, URL, action, action_id) {

        var editor = new $.fn.dataTable.Editor( {
			ajax: {
                type: 'POST',
                url: URL,
                async: false,
                data: {
                    user_id: $rootScope.user_id,
                    event_id: event_id,
                    invoice_ids: invoice_ids

                },
                dataType: 'json',
                complete: function (response) {
                    var jsonData = JSON.parse(response.responseText);
                    displayActionMessage(jsonData, action, action_id);
                },
                error: function(xhr, status, error) {
                    alert('ajaxReminderAction.Error - status, error = ' + status + ',' + error + ',' + xhr);
                },
			},
			formOptions: {
				main: {
					onBlur: 'none'
				}
			},
			fields: [ 
                {
                    label: "",
                    name: "merchant",
                    type: "checkbox",
                    options: [
                        { label: "Send me a copy of the reminder", value: true}
                    ]
                }, {
                    label: "Subject:",
                    name: "subject",
                    def: "Reminder: Your payment for this invoices is due"
                }, {
                    label: "Note:",
                    name: "note",
                    type: "textarea",
                    fieldInfo: "Add personal note to recipient"
                }
            ]
		} );
		editor
			.title( 'Send payment reminder' )
			.buttons(
				{
					label: "Send reminder",
					fn: function () { this.submit(); }
				}
			)
			.edit()
			.open();
    }

    // record payment
    function ajaxRecordPaymentAction(event_id, data, URL, action, action_id) {
        var record_payment_editor = new $.fn.dataTable.Editor( {
			ajax: {
                type: 'POST',
                url: URL,
                async: false,
                data: {
                    user_id: $rootScope.user_id,
                    event_id: event_id
                },
                dataType: 'json',
                complete: function (response) {
                    var jsonData = JSON.parse(response.responseText);
                    displayActionMessage(jsonData, action, action_id);
                },
                error: function(xhr, status, error) {
                    alert('ajaxReminderAction.Error - status, error = ' + status + ',' + error + ',' + xhr);
                },
			},
			formOptions: {
				main: {
					onBlur: 'none'
				}
			},
			fields: [ 
                {
                    label: "Invoice number:",
                    name: "invoice_number",
                    type: "readonly",
                    def: data.invoices.invoice_number
                }, {
                    label: "Amount due:",
                    name: "amount_due",
                    type: "readonly",
                    def: data.registrations.amount
                    // def: "$" + data.registrations.amount + " HKD"
                }, {
                    label: "Payment amount",
                    name: "payment_amount",
                    def: data.registrations.amount
                }, {
                    label: "Payment date:",
                    name: "payment_date",
                    type: "datetime",
                    format: 'DD-MMM-YYYY',
                    default: moment().format("DD-MMM-YYYY"),
                }, {
                    label: "Payment method",
                    name: "payment_method",
                    type: "select2",
                    options: [
                        { label: 'Bank transfer', value: 'BANK_TRANSFER'},
                        { label: "Cash", value: "CASH"},
                        { label: "Check", value: "CHECK"},
                        { label: "Credit card", value: "CREDIT_CARD"},
                        { label: "Debit card", value: "DEBIT_CARD"},
                        { label: "PayPal", value: "PAYPAL"},
                        { label: "Wire transfer", value: "WIRE_TRANSFER"},
                        { label: "Other", value: "OTHER"}
                    ],
                    def: 'BANK_TRANSFER'
                }, {
                    label: "Note:",
                    name: "note",
                    type: "textarea",
                    fieldInfo: "Add a note for your records"
                }
            ]
		} );
		record_payment_editor
			.title( 'Record a payment' )
			.buttons(
				{
					label: "Record Payment",
					fn: function () { this.submit(); }
				}
			)
			.edit()
			.open();
    }

    function ajaxRefundPaymentAction(event_id, data, URL, action, action_id) {
        var refund_payment_editor = new $.fn.dataTable.Editor( {
			ajax: {
                type: 'POST',
                url: URL,
                async: false,
                data: {
                    invoice_identification : data.refund_infos.invoice_identification
                },
                dataType: 'json',
                complete: function (response) {
                    var jsonData = JSON.parse(response.responseText);
                    displayActionMessage(jsonData, action, action_id);
                },
                error: function(xhr, status, error) {
                    alert('ajaxReminderAction.Error - status, error = ' + status + ',' + error + ',' + xhr);
                },
			},
			formOptions: {
				main: {
					onBlur: 'none'
				}
			},
			fields: [ 
                {
                    label: "Invoice number:",
                    name: "invoice_number",
                    type: "readonly",
                    def: data.invoices.invoice_number 
                }, {
                    label: "Transaction Id:",
                    name: "transaction_id",
                    type: "readonly",
                    def: data.refund_infos.transaction_id
                }, {
                    label: "Original Payments:",
                    name: "original_payment",
                    type: "readonly",
                    def: data.refund_infos.original_payment
                }, {
                    label: "Amount Remaining:",
                    name: "amount_remaining",
                    type: "readonly",
                    def: data.refund_infos.amount_remaining
                }, {
                    label: "Refund amount:",
                    name: "refund_amount",
                    type: "text",
                    attr: {
                        type: "number"
                    },
                    def: data.refund_infos.amount_remaining
                }, {
                    label: "Description (Optional):",
                    name: "description",
                    type: "textarea",
                }
            ]
		} );
		refund_payment_editor
			.title( 'Refund' )
			.buttons(
				{
					label: "Refund",
					fn: function () { this.submit(); }
				}
			)
			.edit()
            .open();

        refund_payment_editor.on( 'preSubmit', function ( e, o, action ) {
            console.log(action);
            var refund_amount = this.field('refund_amount');
            var amount_remaining = this.field('amount_remaining').val();
            if ( ! refund_amount.isMultiValue() ) {
                if ( ! refund_amount.val()){
                    refund_amount.error( 'This field is required' );
                }
                if(refund_amount.val()){
                    if ( parseFloat(refund_amount.val()) > parseFloat(amount_remaining) ) {
                        refund_amount.error( 'Refund amount cannot be greater than $' + amount_remaining +' HKD' );
                    }
                }
            }

            // If any error was reported, cancel the submission so it can be corrected
            if ( this.inError() ) {
                return false;
            }
        })
    }

    function checkRefundInvoice(invoice_id, event_id, action, action_id, data) {
        var data;
        jQuery.ajax({
            type: 'POST',
            url: SERVER_PATH + "invoice/checkRefundInvoice",
            async: false,
            headers: {	
                'x-user-id': $rootScope.user_id,
                'x-user-email': $rootScope.user_name
            },
            data: {
                "invoice_id": invoice_id
            },
            dataType: 'json',
            complete: function (response) {
                var jsonData = JSON.parse(response.responseText);
                console.log(jsonData);
                if (jsonData.status == "OK") {
                   data['refund_infos'] = jsonData.data;
                   console.log(data);

                   urlStr = SERVER_PATH + "invoice/refundPayment";
                   ajaxRefundPaymentAction(event_id, data, urlStr, action, action_id);
                } else {
                    displayActionMessage(jsonData, action, action_id);
                }
            }
        });
        // console.log(data);
        return data;
    }

    function displayActionMessage(jsonData, action, action_id) {
        // console.log("displayActionMessage: " + jsonData);
        if (jsonData.status == "OK") {                    
            BootstrapDialog.show({
                title: 'Information - ' + action,
                type: function() {
                    if (jsonData.message.indexOf("Please try again") == -1)
                        return BootstrapDialog.TYPE_SUCCESS;
                    return BootstrapDialog.TYPE_WARNING;
                },
                message: jsonData.message,
                onhidden: function(dialogRef) {
                    table.ajax.reload();
                }
            });
        } else if (jsonData.status == "ERROR") {
            BootstrapDialog.show({
                title: 'Information - ' + action,
                type: BootstrapDialog.TYPE_DANGER,
                message: jsonData.message,                        
            });
        }  
    }

    function editInvoice(event_id, INVOICE_ACTION_EDIT_INVOICE, action) {
        table = $('#table_invoice_' + event_id).DataTable();
        var table_selected = table.rows( { selected: true } ).data();
        var data = table_selected[0];
        if (data.parens.hkfc_account != '') {
            BootstrapDialog.show({
                title: 'Information - ' + action,
                type: BootstrapDialog.TYPE_WARNING,
                message: 'This is HKFC account. Can\'t edit. '
            })
            return;
        }
        var update_invoice_editor = new $.fn.dataTable.Editor( {
            ajax: {
                type: 'POST',
                url: SERVER_PATH + "invoice/editInvoice",
                headers: {	
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name
                },
                data: {
                    "event_id": event_id,
                    "reg_id": data.registrations.id,
                    "old_amount": data.registrations.amount
                },
                dataType: 'json',
                complete: function (response) {
                    var jsonData = JSON.parse(response.responseText);
                    displayActionMessage(jsonData, action, INVOICE_ACTION_EDIT_INVOICE);
                },
                error: function(xhr, status, error) {
                },
            },
            formOptions: {
                main: {
                    onBlur: 'none'
                }
            },
            fields: [ 
                {
                    label: "Invoice number:",
                    name: "invoice_number",  
                    type:  "readonly",
                    def: data.invoices.invoice_number
                },
                {
                    label: "Player name:",
                    name: "playername",  
                    type:  "readonly",
                    def: data.players.surname + ' ' + data.players.other_name
                },
                {
                    label: "Amount:",
                    name: "amount",
                    type: "text",
                    def: data.registrations.amount
                }
            ]
        } ).title( 'Edit Invoice' )
        .buttons(
            {
                label: "Save",
                fn: function () { this.submit(); }
            }
        ).edit();
        update_invoice_editor.on( 'preSubmit' , function(){
            var amount = this.field('amount');
            if ( ! amount.isMultiValue() ) {
                
                if ( ! amount.val()){
                    amount.error( 'This field is required' );
                }
                if (! /^-?\d*[.,]?\d{0,5}$/.test(amount.val())) {
                    amount.error('Invalid input');
                }
                if (parseFloat(amount.val()) === parseFloat(data.registrations.amount)) {
                    amount.error('Please enter another amount ' + data.registrations.amount);
                }
            }

            if ( this.inError() ) {
                return false;
            }
        })
    }

    function ajaxUpdateStatus(event_id, invoice_ids, URL, action, action_id) {
        jQuery.ajax({
            type: 'POST',
            url: URL,
            async: false,
            data: {
                user_id: $rootScope.user_id,
                event_id: event_id,
                invoice_ids: invoice_ids
            },
            dataType: 'json',
            complete: function (response) {
                var jsonData = JSON.parse(response.responseText);
                displayActionMessage(jsonData, action, action_id);
            },
            error: function(xhr, status, error) {
                alert('ajaxReminderAction.Error - status, error = ' + status + ',' + error + ',' + xhr);
            },
        });
    }

});