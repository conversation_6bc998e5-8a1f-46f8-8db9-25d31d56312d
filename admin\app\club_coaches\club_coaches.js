app.controller('club_coachesCtrl', function($scope, $rootScope, $routeParams, $http) {
	$('#page-wrapper').removeClass('nav-small');

    var event_id = $routeParams.id;
    
    jQuery.ajax({
    	type: 'POST',
  		url: SERVER_PATH + "club/getClubByUser",
  		async: false,
		headers: {	
					'x-user-id': $rootScope.user_id,
					'x-user-email': $rootScope.user_name
		},
  		data: {
    		"user_id": $rootScope.user_id
    	},
  		dataType: 'json',
  		complete: function (response) {
			var jsonData = JSON.parse(response.responseText);
			if (jsonData.status == "OK") {
				var club = jsonData.info;
				club_id = club.id;
				club_name = club.name;

				// initialize html
				ClubCoachesTableHtml = getClubCoachesTableHtml(club_id);
				
				var html = '<div class="tabs-wrapper cgroups-tabs">' +
					'<ul class="nav nav-tabs">' +
						'<li class="active"><a data-target="#tab-ClubCoaches" data-toggle="tab"><i class="fa fa-fw fa-users"></i> Coahes</a></li>' +
					'</ul>' +
					'<div class="tab-content">' +
						'<div class="tab-pane fade in active" id="tab-ClubCoaches">' +
                            ClubCoachesTableHtml +
						'</div>' +
					'</div>' +
				'</div>';
				
				$('#clubCoachesPageContent').html(html);

				initClubCoachesTable(club_id, club_name);
				
			} else {
				BootstrapDialog.show({
					title: 'ERROR',
					type: BootstrapDialog.TYPE_WARNING,
					message: jsonData.message
				});
			}
  			
  		}
    });

    $scope.club_name = club_name;
	
	function getClubCoachesTableHtml(club_id) {
  		var str = ''+
			'<div class="table-responsive">' +
				'<table id="tblClubCoaches' + club_id + '" class="table table-striped table-bordered table-hover" cellspacing="0" width="100%">' +
					'<thead>' +
						'<tr>' +
							'<th></th>' +
							'<th class="text-center">Name</th>' +
							'<th class="text-center">Email</th>' +
							'<th class="text-center">Phone</th>' +
							'<th class="text-center">Groups</th>' +
							// '<th class="text-center">Role</th>' +
						'</tr>' +
					'</thead>' +
				'</table>' +
			'</div>';
    	return str;
    }
    
    function initClubCoachesTable(club_id, club_name) {
		
		var editorClubCoaches = new $.fn.dataTable.Editor( {
            ajax: {
                type: 'POST',
                url: SERVER_PATH + "club/setClubCoaches",
				headers: {	
					'x-user-id': $rootScope.user_id,
					'x-user-email': $rootScope.user_name
				},
                data: {
        			"club_id": club_id,
        			"event_id": event_id
    			},
                dataType: 'json',
                complete: function (response) {
                    tblClubCoaches.ajax.reload();
                },
                error: function(xhr, status, error) {
                },
            },
            table: '#tblClubCoaches' + club_id,
            formOptions: {
                main: {
                    onBlur: 'none'
                }
            },
            i18n: {
				create: {
					button: "New",
					title: "Create new club coach",
					submit: "Create"
				},
				edit: {
					button: "Edit",
					title: "Edit club coach",
					submit: "Save"
				},
                error: {
                    system: "System error, please contact administrator."
                },
            },
            fields: [ 
				{
					label: "Email",
					name: "parens.email"
				}, {
					name: "groups.id",
					label: "Group:",
					type: "select2",
						opts: {
							placeholder: "Select a group"
						}
				}, 
            ]
        } );
		
		tblClubCoaches = $('#tblClubCoaches' + club_id).DataTable( {
			dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
			stateSave: true,
			deferRender: true,
			ajax: {
				url: SERVER_PATH + "club/getClubCoaches",
				type: 'POST',
				headers: {	
					'x-user-id': $rootScope.user_id,
					'x-user-email': $rootScope.user_name
				},
  				data: {
        			"club_id": club_id,
        			"event_id": event_id
    			},
  				dataType: 'json',
  				complete: function (response) {
					// response = JSON.parse(response.responseText);
				},
		  		error: function(xhr, status, error) {
  				},
            },
            language: {
				paginate: {
					previous: '<i class="fa fa-chevron-left"></i>',
					next: '<i class="fa fa-chevron-right"></i>'
				}
			},
  			columns: [
				{
                    data: 'DT_RowId',
                    // defaultContent: '',
                    // className: 'select-checkbox',
                    // orderable: false
                    targets: 0,
                    render: function (data, type, row, meta) {
                        if (type === 'display') {
                            data = '<div class="checkbox"><input type="checkbox" class="dt-checkboxes"><label></label></div>';
                        }
                        return data;
                    },
                    checkboxes: {
                        selectRow: true,
                        selectAllRender: '<div class="checkbox"><input type="checkbox" class="dt-checkboxes"><label></label></div>'
                    }
                },
                {
                    data: null, 
                    render: function ( data, type, row ) {
                        return data.parens.surname + ' '+ data.parens.other_name;
                    } 
                },
                {
                    data: "parens.email",
                    className: "center",
                    // visible: false
                },
                {
                    data: "parens.phone",
                    className: "center",
                    // visible: false
				},
                {
                    data: "groups.name",
                    className: "center",
                    // visible: false
                },
                // {
                //     data: "club_coaches.role",
                //     className: "center",
                //     // visible: false
				// },
                
			],
			select: {
				style: 'multi',
				// selector: 'td:first-child'
			},
			buttons: [
				{ extend: "create", editor: editorClubCoaches },
				{ extend: "edit", editor: editorClubCoaches },
				{ extend: 'colvis', text: 'Columns' }
			],
			order: [[1, 'asc']],
			"lengthMenu": [[10, 25, 50, 100, -1], [10, 25, 50, 100, "All"]]
		} );
    }
    
});