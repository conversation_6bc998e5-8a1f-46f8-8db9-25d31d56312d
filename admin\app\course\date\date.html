<style>
    .tooltip:hover {
        display: inline-block;
        position: absolute;
        background-color: #000;
        color: #fff;
        padding: 5px;
        border-radius: 4px;
        z-index: 999;
        white-space: nowrap;
    }
</style>

<div class="row" id="tab-page">
    <div class="row">
        <div class="col-lg-12">
            <ol class="breadcrumb">
                <li>Event</li>
                <li><a data-match-route="/events/{{normalizedType}}" href="#/events/{{normalizedType}}">{{event_type}}</a></li>
                <li>{{event_name}}</li>
                <li><a data-match-route="/event/{{selectedEvent.id}}/course"
                        href="#/event/{{selectedEvent.id}}/course">Courses</a></li>
                <li class="active"><span>{{class_code}}</span></li>
            </ol>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-12">
        <div class="clearfix">
            <h1 class="pull-left">{{class_code}}</h1>
        </div>
    </div>
</div>
<div>
    <div class="row">
        <div class="col-lg-12">
            <div class="main-box clearfix">
                <div class="tabs-wrapper profile-tabs">
                    <ul class="nav nav-tabs">
                        <li class="active"><a showtab="" data-target="#tab-players" data-toggle="tab">Players</a></li>
                        <li><a showtab="" data-target="#tab-parents" data-toggle="tab" ng-if="course_type == 'parent' || course_type == 'mother'">Parents</a></li>
                        <li><a showtab="" data-target="#tab-events" data-toggle="tab">Event Dates</a></li>
                        <li><a showtab="" data-target="#tab-report" data-toggle="tab" ng-click="generateReport()">Reports</a></li>
                    </ul>
                    <div class="tab-content">
                        <div class="tab-pane fade in active" id="tab-players">
                            <div id="tablePlayers">
                            </div>
                        </div>
                        <div class="tab-pane fade in" id="tab-parents">
                            <div id="tableParents">
                            </div>
                        </div>
                        <div class="tab-pane fade" id="tab-events">
                            <div id="tableCdateContent">
                            </div>
                        </div>
                        <div class="tab-pane fade" id="tab-report">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">

    $(".selectedEvent").select2({
        width: 'resolve' // need to override the changed default
    });

    $(".selectedType").select2({
        width: 'resolve' // need to override the changed default
    });
</script>