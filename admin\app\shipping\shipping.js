app.controller(
    'shippingCtrl',
    function ($scope, $rootScope, $routeParams, $http) {
        var event_id = $routeParams.id;
        $scope.event_id = event_id;
        var locker_list = [];
        $scope.product_list = [];
        var table = $('#shippingTable_' + event_id).DataTable();
        $scope.data_shipping = [];

        jQuery.ajax({
            type: 'POST',
            url: SERVER_PATH + 'event/getEventInfo',
            async: false,
            headers: {
                'x-user-id': $rootScope.user_id,
                'x-user-email': $rootScope.user_name,
            },
            data: {
                event_id: event_id,
            },
            dataType: 'json',
            complete: function (response) {
                var jsonData = JSON.parse(response.responseText);
                var event = jsonData.info;
                $scope.event_name = event.name;
                $scope.event_type = event.type;
            },
        });

        jQuery.ajax({
            type: 'POST',
            url: SERVER_PATH + 'shipping/getAllLocker',
            async: false,
            data: {},
            headers: {
                'x-user-id': $rootScope.user_id,
                'x-user-email': $rootScope.user_name,
            },
            dataType: 'json',
            complete: function (response) {
                var jsonData = JSON.parse(response.responseText);
                locker_list = jsonData.info.map((item) => {
                    return {
                        label: item.locker_id,
                        value: item.locker_id,
                    };
                });
            },
        });

        jQuery.ajax({
            type: 'POST',
            url: SERVER_PATH + 'shipping/getListProductOptions',
            async: false,
            headers: {
                'x-user-id': $rootScope.user_id,
                'x-user-email': $rootScope.user_name,
            },
            data: {
                event_id: event_id,
            },
            dataType: 'json',
            complete: function (response) {
                var jsonData = JSON.parse(response.responseText);
                $scope.product_list = jsonData.info.map((item) => {
                    return {
                        label: item.label,
                        value: item.value,
                    };
                });
            },
        });

        var editor = new $.fn.dataTable.Editor({
            ajax: {
                type: 'POST',
                url: SERVER_PATH + 'shipping/setShipping',
                headers: {
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name,
                },
                data: {
                    // "pgroup_id": pgroup_id
                },
                dataType: 'json',
                complete: function (response) {
                    var jsonData = JSON.parse(response.responseText);
                    // --- may need to reload
                    if (DEVELOPMENT_ENVIRONMENT)
                        console.log('status = ' + jsonData.status);
                    if (jsonData.status == 'OK') {
                        if (DEVELOPMENT_ENVIRONMENT)
                            console.log('Before reload');
                        table.ajax.reload();
                    }
                },
                error: function (xhr, status, error) {},
            },
            table: '#shippingTable_' + event_id,
            formOptions: {
                main: {
                    onBlur: 'none',
                },
            },
            i18n: {
                create: {
                    button: 'New',
                    title: 'Create new shipping',
                    submit: 'Create',
                },
                edit: {
                    button: 'Edit',
                    title: 'Edit shipping',
                    submit: 'Save',
                },
                remove: {
                    button: 'Delete',
                    title: 'Delete shipping',
                    submit: 'Delete',
                    confirm: {
                        _: 'Are you sure you want to delete these shippings?',
                        1: 'Are you sure you want to delete this shipping?',
                    },
                },
                error: {
                    system: 'System error, please contact administrator.',
                },
                fields: [
                    {
                        label: 'Address:',
                        name: 'shipping.address',
                        type: 'textarea',
                    },
                ],
            },
        });

        initTable = function () {
            $('#shippingTable_' + event_id)
                .DataTable()
                .destroy();

            setTimeout(() => {
                table = $('#shippingTable_' + event_id).DataTable({
                    dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
                    stateSave: true,
                    destroy: true,
                    deferRender: true,
                    ajax: {
                        url: SERVER_PATH + 'shipping/getShipping',
                        type: 'POST',
                        headers: {
                            'x-user-id': $rootScope.user_id,
                            'x-user-email': $rootScope.user_name,
                        },
                        data: {
                            event_id: event_id,
                            start_date: $scope.start_date,
                            end_date: $scope.end_date,
                        },
                        dataType: 'json',
                        complete: function (response) {},
                        error: function (xhr, status, error) {},
                    },
                    language: {
                        info: 'Showing _START_ to _END_ of _TOTAL_ shipping',
                        infoEmpty: 'Showing 0 to 0 of 0 shippings',
                        lengthMenu: 'Show _MENU_ shippings',
                        select: {
                            rows: {
                                _: 'You have selected %d shippings',
                                0: 'Click a shipping to select',
                                1: '1 shipping selected',
                            },
                        },
                        paginate: {
                            previous: '<i class="fa fa-chevron-left"></i>',
                            next: '<i class="fa fa-chevron-right"></i>',
                        },
                    },
                    columns: [
                        {
                            data: null,
                            render: function (data, type, row) {
                                return (
                                    row.parens.surname +
                                    ' ' +
                                    row.parens.other_name
                                );
                            },
                        },
                        {
                            data: 'parens.email',
                        },
                        {
                            data: 'shipping.address',
                            visible: false,
                        },
                        {
                            data: 'shipping.city',
                            visible: false,
                        },
                        {
                            data: 'shipping.province',
                            visible: false,
                        },
                        {
                            data: 'shipping.postcode',
                            visible: false,
                        },
                        {
                            data: 'shipping.loker_code',
                            visible: false,
                        },
                        {
                            data: 'shipping.receiver_phone',
                        },
                        {
                            data: 'shipping.order_no',
                        },
                        {
                            data: 'shipping.order_id',
                        },
                        {
                            data: 'shipping.order_status',
                            render: function (data, type, row) {
                                switch (data) {
                                    case SHIPPING_STATUS_CONFIRMED:
                                        return `<span class="label label-info">${data}</span>`;
                                    case SHIPPING_STATUS_CANCELLED:
                                        return `<span class="label label-danger">${data}</span>`;
                                    case SHIPPING_STATUS_IN_PROGRESS:
                                        return `<span class="label label-warning">${data}</span>`;
                                    case SHIPPING_STATUS_COMPLETED:
                                        return `<span class="label label-success">${data}</span>`;
                                    default:
                                        return data;
                                }
                            },
                        },
                        {
                            data: 'shipping.courier_tracking_no',
                        },
                        {
                            data: 'none.invoice_numbers',
                        },
                        {
                            data: 'shipping.shipping_type',
                        },
                        {
                            data: null,
                            render: function (data) {
                                return '<button type="button" data-toggle="modal" data-target="#modal-group" class="btn btn-primary modal-shipping-details">Details</button>';
                            },
                        },
                    ],
                    responsive: true,
                    columnDefs: [
                        { responsivePriority: 1, targets: 7 },
                        { responsivePriority: 2, targets: -1 },
                        { responsivePriority: 3, targets: 0 },
                    ],
                    initComplete: function () {
                        var shipping_status_collunm = {
                            orderColumn: 10,
                            elementId: 'shipping-status_filter',
                            selectId: 'selType',
                            label: 'Filter by status: ',
                        };

                        filterColumns = [shipping_status_collunm];

                        filterColumns.forEach((item) => {
                            this.api()
                                .columns(item.orderColumn)
                                .every(function () {
                                    var column = this;
                                    //delete elements if they exist
                                    $(`#${item.elementId}`).empty();
                                    // add label
                                    $(`<label>${item.label}</label>`).appendTo(
                                        `#${item.elementId}`
                                    );

                                    var select = $(
                                        `<select id="${item.orderColumn}" class="form-control"><option value="">All</option></select>`
                                    );

                                    select
                                        .appendTo($(`#${item.elementId}`))
                                        .on('change', function () {
                                            var val =
                                                $.fn.dataTable.util.escapeRegex(
                                                    $(this).val()
                                                );

                                            column
                                                .search(
                                                    val ? val : '',
                                                    true,
                                                    false
                                                )
                                                .draw();
                                        })
                                        .select2();
                                    var column_data = column.data();
                                    var select_data = [];
                                    column_data.map((item) => {
                                        if (item != null) {
                                            item.indexOf(', ') > 0
                                                ? (item = item.split(', '))
                                                : (item = [item]);
                                            item.forEach((item) => {
                                                select_data.push(item);
                                            });
                                        }
                                    });

                                    select_data
                                        .filter(onlyUnique)
                                        .sort()
                                        .map(function (d, j) {
                                            select.append(
                                                `<option value="${d}">${d}</option>`
                                            );
                                        });
                                });
                        });
                    },
                    select: {
                        style: 'single',
                        // selector: 'td:first-child',
                    },
                    buttons: [
                        // { extend: "edit", editor: editor },
                        {
                            extend: 'collection',
                            text: 'Actions <i class="fa fa-caret-down"></i>',
                            className: 'btn btn-primary',
                            autoClose: true,
                            buttons: [
                                {
                                    text: '<i class="fa fa-edit"></i> Change sent item',
                                    extend: 'selectedSingle',
                                    action: function (e, dt, node, config) {
                                        var selected_row = table
                                            .rows({ selected: true })
                                            .data()[0];

                                        if (
                                            selected_row.shipping
                                                .order_status ==
                                            SHIPPING_STATUS_IN_PROGRESS
                                        ) {
                                            Swal.fire({
                                                title: 'Error!',
                                                text: 'The shipping status is in progress, you cannot change the sent item',
                                                icon: 'error',
                                                confirmButtonText: 'OK',
                                            });
                                            return;
                                        }

                                        $scope.data_shipping = [];

                                        jQuery.ajax({
                                            type: 'POST',
                                            url:
                                                SERVER_PATH +
                                                'shipping/getAllProductsInShipping',
                                            async: false,
                                            headers: {
                                                'x-user-id': $rootScope.user_id,
                                                'x-user-email':
                                                    $rootScope.user_name,
                                            },
                                            data: {
                                                shipping_id:
                                                    selected_row.shipping.id,
                                            },
                                            dataType: 'json',
                                            complete: function (response) {
                                                var jsonData = JSON.parse(
                                                    response.responseText
                                                );
                                                if (jsonData.status == 'OK') {
                                                    $scope.data_shipping =
                                                        jsonData.data;
                                                    $scope.$apply();
                                                } else {
                                                    Swal.fire({
                                                        title: 'Error!',
                                                        text: jsonData.message,
                                                        icon: 'error',
                                                        confirmButtonText: 'OK',
                                                    });
                                                    $scope.$apply();
                                                }
                                            },
                                        });

                                        $('#changeItemShipping').modal({
                                            backdrop: 'static',
                                            keyboard: false,
                                        });
                                    },
                                },
                                {
                                    text: '<i class="fa fa-map-marker"></i> Change shipping location',
                                    extend: 'selectedSingle',
                                    action: function () {
                                        var row_selected = table
                                            .rows({ selected: true })
                                            .data()[0];
                                        console.log(
                                            'rows selected ',
                                            row_selected
                                        );
                                        var shipping_id =
                                            row_selected.shipping.id;
                                        var shipping_type =
                                            row_selected.shipping.shipping_type;
                                        var loker_code =
                                            row_selected.shipping.loker_code;
                                        var shipping_address =
                                            row_selected.shipping.address;
                                        var registration_id =
                                            row_selected.registrations.id;
                                        check = false;
                                        if (
                                            row_selected.shipping
                                                .order_status != null
                                        ) {
                                            if (
                                                row_selected.shipping.order_status.toLowerCase() ==
                                                'confirmed'
                                            ) {
                                                check = true;
                                            }
                                        } else {
                                            check = true;
                                        }
                                        if (check) {
                                            var editor =
                                                new $.fn.dataTable.Editor({
                                                    ajax: {
                                                        type: 'POST',
                                                        url:
                                                            SERVER_PATH +
                                                            'shipping/editShipping',
                                                        headers: {
                                                            'x-user-id':
                                                                $rootScope.user_id,
                                                            'x-user-email':
                                                                $rootScope.user_name,
                                                        },
                                                        data: {
                                                            shipping_id:
                                                                shipping_id,
                                                            user_id:
                                                                $rootScope.user_id,
                                                            registration_id:
                                                                registration_id,
                                                        },
                                                        dataType: 'json',
                                                        complete: function (
                                                            response
                                                        ) {
                                                            var jsonData =
                                                                JSON.parse(
                                                                    response.responseText
                                                                );
                                                            if (
                                                                typeof jsonData.fieldErrors !=
                                                                'undefined'
                                                            ) {
                                                                return;
                                                            }
                                                            if (
                                                                jsonData.status ==
                                                                'OK'
                                                            ) {
                                                                table.ajax.reload();
                                                                Swal.fire({
                                                                    title: 'SUCCESS',
                                                                    type: 'success',
                                                                    icon: 'success',
                                                                    text: jsonData.message,
                                                                    showConfirmButton: false,
                                                                });
                                                            } else {
                                                                Swal.fire({
                                                                    title: 'ERROR',
                                                                    type: 'error',
                                                                    icon: 'error',
                                                                    text: jsonData.message,
                                                                    showConfirmButton: false,
                                                                });
                                                            }
                                                        },
                                                        error: function (
                                                            xhr,
                                                            status,
                                                            error
                                                        ) {},
                                                    },
                                                    table:
                                                        '#shippingTable_' +
                                                        event_id,
                                                    formOptions: {
                                                        main: {
                                                            onBlur: 'none',
                                                        },
                                                    },
                                                    i18n: {
                                                        create: {
                                                            button: 'Change',
                                                            title: 'Change shipping location',
                                                            submit: 'Save',
                                                        },
                                                    },
                                                    fields: [
                                                        {
                                                            label: 'Shipping type:',
                                                            name: 'shipping_type',
                                                            def: shipping_type,
                                                            type: 'readonly',
                                                        },
                                                        {
                                                            label: 'Address:',
                                                            name: 'address',
                                                            type: 'textarea',
                                                            def: shipping_address,
                                                        },
                                                        {
                                                            label: 'Loker code:',
                                                            name: 'loker_code',
                                                            type: 'select2',
                                                            options:
                                                                locker_list,
                                                            def: loker_code,
                                                        },
                                                    ],
                                                });

                                            editor
                                                .title(
                                                    'Change shipping location'
                                                )
                                                .buttons({
                                                    label: 'Save',
                                                    fn: function () {
                                                        this.submit();
                                                    },
                                                })
                                                .create()
                                                .open();

                                            editor.dependent(
                                                'shipping_type',
                                                function (val) {
                                                    var show = [
                                                        'shipping_type',
                                                    ];
                                                    var hide = [];
                                                    if (val == 'home') {
                                                        hide.push('loker_code');
                                                        show.push('address');
                                                    } else if (
                                                        val == 'locker'
                                                    ) {
                                                        show.push('loker_code');
                                                        hide.push('address');
                                                    } else {
                                                        hide.push('address');
                                                        hide.push('loker_code');
                                                    }

                                                    return {
                                                        hide: hide,
                                                        show: show,
                                                    };
                                                }
                                            );
                                        } else {
                                            Swal.fire({
                                                title: 'ERROR',
                                                type: 'error',
                                                icon: 'error',
                                                text: 'We can not change shipping location because order status is not confirmed',
                                                showConfirmButton: false,
                                            });
                                        }
                                    },
                                },
                                {
                                    text: '<i class="fa fa-truck"></i> Change shipping type for cancel order',
                                    extend: 'selectedSingle',
                                    action: function () {
                                        var selected_row = table
                                            .rows({ selected: true })
                                            .data()[0];
                                        if (
                                            selected_row.shipping
                                                .order_status !=
                                                SHIPPING_STATUS_CANCELLED ||
                                            selected_row.shipping
                                                .shipping_type != 'home'
                                        ) {
                                            Swal.fire({
                                                title: 'Error!',
                                                text: 'This shipping not support change shipping type',
                                                icon: 'error',
                                                confirmButtonText: 'OK',
                                            });
                                        } else {
                                            const { shipping } = selected_row;
                                            const shipping_id = shipping.id;

                                            const SHIPPING_TYPE_OPTIONS = [
                                                {
                                                    label: 'Self pick up',
                                                    value: 'Self pick up',
                                                },
                                                {
                                                    label: 'Do not need',
                                                    value: 'Do not need kit and ball',
                                                },
                                            ];

                                            var editor =
                                                new $.fn.dataTable.Editor({
                                                    ajax: {
                                                        type: 'POST',
                                                        url:
                                                            SERVER_PATH +
                                                            'shipping/editShippingCancel',
                                                        data: {
                                                            shipping_id:
                                                                shipping_id,
                                                            user_id:
                                                                $rootScope.user_id,
                                                        },
                                                        dataType: 'json',
                                                        complete: function (
                                                            response
                                                        ) {
                                                            var jsonData =
                                                                JSON.parse(
                                                                    response.responseText
                                                                );
                                                            if (
                                                                typeof jsonData.fieldErrors !=
                                                                'undefined'
                                                            ) {
                                                                return;
                                                            }

                                                            if (
                                                                jsonData.status ==
                                                                'OK'
                                                            ) {
                                                                table.ajax.reload();
                                                                Swal.fire({
                                                                    title: 'SUCCESS',
                                                                    type: 'success',
                                                                    icon: 'success',
                                                                    text: jsonData.message,
                                                                    showConfirmButton: false,
                                                                });
                                                            } else {
                                                                Swal.fire({
                                                                    title: 'ERROR',
                                                                    type: 'error',
                                                                    icon: 'error',
                                                                    text: jsonData.message,
                                                                    showConfirmButton: false,
                                                                });
                                                            }
                                                        },
                                                        error: function (
                                                            xhr,
                                                            status,
                                                            error
                                                        ) {},
                                                    },
                                                    table:
                                                        '#shippingTable_' +
                                                        event_id,
                                                    formOptions: {
                                                        main: {
                                                            onBlur: 'none',
                                                        },
                                                    },
                                                    i18n: {
                                                        create: {
                                                            button: 'Change',
                                                            title: 'Change shipping type',
                                                            submit: 'Save',
                                                        },
                                                    },
                                                    fields: [
                                                        {
                                                            label: 'Shipping type:',
                                                            name: 'shipping_type',
                                                            type: 'select2',
                                                            options:
                                                                SHIPPING_TYPE_OPTIONS,
                                                            def: 'Self pick up',
                                                        },
                                                    ],
                                                });
                                            editor
                                                .title('Change shipping type')
                                                .buttons({
                                                    label: 'Save',
                                                    fn: function () {
                                                        this.submit();
                                                    },
                                                })
                                                .create()
                                                .open();
                                        }
                                    },
                                },
                                {
                                    text: '<i class="fa fa-truck"></i> Update shipping status',
                                    extend: 'selectedSingle',
                                    action: function () {
                                        var row_selected = table
                                            .rows({ selected: true })
                                            .data()[0];

                                        var shipping_id =
                                            row_selected.shipping.id;

                                        jQuery.ajax({
                                            type: 'POST',
                                            url:
                                                SERVER_PATH +
                                                'shipping/updateShippingStatus',
                                            async: false,
                                            headers: {
                                                'x-user-id': $rootScope.user_id,
                                                'x-user-email':
                                                    $rootScope.user_name,
                                            },
                                            data: {
                                                shipping_id: shipping_id,
                                            },
                                            dataType: 'json',
                                            complete: function (response) {
                                                var jsonData = JSON.parse(
                                                    response.responseText
                                                );

                                                if (jsonData.status == 'OK') {
                                                    BootstrapDialog.show({
                                                        title: 'SUCCESS',
                                                        type: BootstrapDialog.TYPE_SUCCESS,
                                                        message:
                                                            jsonData.message,
                                                    });

                                                    table.ajax.reload();
                                                } else {
                                                    BootstrapDialog.show({
                                                        title: 'ERROR !',
                                                        type: BootstrapDialog.TYPE_DANGER,
                                                        message:
                                                            jsonData.message,
                                                    });
                                                }
                                            },
                                        });
                                    },
                                },
                                {
                                    text: '<i class="fa fa-arrow-right"></i> Shipping detail',
                                    extend: 'selectedSingle',
                                    action: function () {
                                        var row_reg = table
                                            .rows({ selected: true })
                                            .data()[0];

                                        var shipping_id = row_reg.shipping.id;

                                        showShippingDetails(
                                            shipping_id,
                                            row_reg
                                        );
                                    },
                                },
                                {
                                    text: '<i class="fa fa-times"></i> Cancel shippings',
                                    extend: 'selectedSingle',
                                    action: function () {
                                        // confirm before delete
                                        BootstrapDialog.confirm({
                                            title: 'WARNING',
                                            type: BootstrapDialog.TYPE_WARNING,
                                            message: 'Are you sure to cancel?',
                                            btnCancelLabel: 'No',
                                            btnOKLabel: 'Yes',
                                            callback: function (result) {
                                                if (result) {
                                                    console.log('confirm');
                                                    var row_selected = table
                                                        .rows({
                                                            selected: true,
                                                        })
                                                        .data()[0];

                                                    var shipping_id =
                                                        row_selected.shipping
                                                            .id;

                                                    jQuery.ajax({
                                                        type: 'POST',
                                                        url:
                                                            SERVER_PATH +
                                                            'shipping/cancelShipping',
                                                        async: false,
                                                        headers: {
                                                            'x-user-id':
                                                                $rootScope.user_id,
                                                            'x-user-email':
                                                                $rootScope.user_name,
                                                        },
                                                        data: {
                                                            shipping_id:
                                                                shipping_id,
                                                        },
                                                        dataType: 'json',
                                                        complete: function (
                                                            response
                                                        ) {
                                                            var jsonData =
                                                                JSON.parse(
                                                                    response.responseText
                                                                );

                                                            if (
                                                                jsonData.status ==
                                                                'OK'
                                                            ) {
                                                                BootstrapDialog.show(
                                                                    {
                                                                        title: 'SUCCESS',
                                                                        type: BootstrapDialog.TYPE_SUCCESS,
                                                                        message:
                                                                            jsonData.message,
                                                                    }
                                                                );

                                                                table.ajax.reload();
                                                            } else {
                                                                BootstrapDialog.show(
                                                                    {
                                                                        title: 'ERROR !',
                                                                        type: BootstrapDialog.TYPE_DANGER,
                                                                        message:
                                                                            jsonData.message,
                                                                    }
                                                                );
                                                            }
                                                        },
                                                    });
                                                }
                                            },
                                        });
                                    },
                                },
                            ],
                        },
                        {
                            extend: 'excel',
                            name: 'excel',
                            text: '<i class="fa fa-file-excel-o"></i> Export to Excel',
                            titleAttr: 'Export data to an Excel file',
                            filename:
                                'Shipping - ' +
                                $scope.event_type +
                                ' ' +
                                $scope.event_name,
                            title:
                                'Shipping - ' +
                                $scope.event_type +
                                ' ' +
                                $scope.event_name,
                            exportOptions: {
                                columns: ':visible',
                                modifier: {
                                    autoFilter: true,
                                    // selected: true
                                },
                            },
                        },
                        {
                            extend: 'colvis',
                            text: 'Columns <i class="fa fa-eye"></i>',
                            className: 'btn btn-secondary',
                        },
                    ],
                });

                // remove action button.modal-shipping-details
                $('#shippingTable_' + event_id).off(
                    'click',
                    'button.modal-shipping-details'
                );

                $('#shippingTable_' + event_id).on(
                    'click',
                    'button.modal-shipping-details',
                    function (e) {
                        console.log('click modal-shipping-details');
                        var $row = $(this).closest('tr');

                        // Get row data
                        var data = table.row($row).data();

                        console.log(data);

                        showShippingDetails(
                            data.shipping.id,
                            data,
                            data.shipping.shipping_type
                        );
                    }
                );
            }, 500);
        };

        function showShippingDetails(shipping_id, data, shipping_type) {
            console.log('showShippingDetails - shipping_id: ', shipping_id);
            let products = [];

            jQuery.ajax({
                type: 'POST',
                url: SERVER_PATH + 'product/getProductsInShipping',
                async: false,
                headers: {
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name,
                },
                data: {
                    shipping_id: shipping_id,
                },
                dataType: 'json',
                complete: function (response) {
                    var jsonData = JSON.parse(response.responseText);
                    products = jsonData.info;
                },
            });

            let table_products_html =
                '<h4 style="text-align: center; text-transform: uppercase;">Product List</h4>' +
                '<table id="table_products" class="table table-striped table-bordered table-hover" cellspacing="0" width="100%">' +
                '<thead>' +
                '<tr>' +
                '<th>#</th>' +
                '<th>Product name</th>' +
                '<th>Product code</th>' +
                '<th>Product type</th>' +
                '<th>Product quantity</th>' +
                (shipping_type == 'Self pick up' ? '<th>Picked up</th>' : '') +
                '</tr>' +
                '</thead>' +
                '<tbody>';

            products.forEach(function (product, index) {
                table_products_html +=
                    '<tr>' +
                    '<td>' +
                    (index + 1) +
                    '</td>' +
                    '<td>' +
                    product.product_name +
                    '</td>' +
                    '<td>' +
                    product.product_code +
                    '</td>' +
                    '<td>' +
                    product.product_type +
                    '</td>' +
                    '<td>' +
                    product.quantity +
                    '</td>' +
                    (shipping_type == 'Self pick up'
                        ? '<td>' +
                          (product.Picked_up == '1' ? 'Yes' : 'No') +
                          '</td>'
                        : '') +
                    '</tr>';
            });

            table_products_html += '</tbody>' + '</table>';

            BootstrapDialog.show({
                title:
                    'Shipping detail - ' +
                    data.parens.surname +
                    ' ' +
                    data.parens.other_name,
                message: table_products_html,
                onshown: function (dialogRef) {},
            });
        }

        setTimeout(() => {
            $('input[name="dateFilter"]').daterangepicker({
                startDate: moment().startOf('month'),
                endDate: moment().endOf('month'),
                locale: {
                    format: 'DD/MM/YYYY',
                },
                maxSpan: {
                    days: 90,
                },
                ranges: {
                    Yesterday: [
                        moment().subtract(1, 'days'),
                        moment().subtract(1, 'days'),
                    ],
                    Today: [moment(), moment()],
                    'Last week': [
                        moment()
                            .subtract(1, 'week')
                            .startOf('week')
                            .add(1, 'days'),
                        moment()
                            .subtract(1, 'week')
                            .endOf('week')
                            .add(1, 'days'),
                    ],
                    'This week': [
                        // start week on monday
                        moment().startOf('week').add(1, 'days'),
                        moment().endOf('week').add(1, 'days'),
                    ],
                    'Last Month': [
                        moment().subtract(1, 'month').startOf('month'),
                        moment().subtract(1, 'month').endOf('month'),
                    ],
                    'This Month': [
                        moment().startOf('month'),
                        moment().endOf('month'),
                    ],
                },
                defaultRange: 'This week',
            });

            $('input[name="dateFilter"]').on(
                'apply.daterangepicker',
                function (ev, picker) {
                    console.log('date change');
                    $scope.start_date = picker.startDate.format('YYYY-MM-DD');
                    $scope.end_date = picker.endDate.format('YYYY-MM-DD');

                    initTable();
                }
            );

            $scope.start_date = $('input[name="dateFilter"]')
                .data('daterangepicker')
                .startDate.format('YYYY-MM-DD');
            $scope.end_date = $('input[name="dateFilter"]')
                .data('daterangepicker')
                .endDate.format('YYYY-MM-DD');

            initTable();
        }, 200);

        $scope.goBack = function () {
            window.history.back();
        };

        $scope.submitGoods = function () {
            var selected_row = table.rows({ selected: true }).data()[0];

            let shipping_id = selected_row.shipping.id;

            jQuery.ajax({
                type: 'POST',
                url: SERVER_PATH + 'shipping/changeItemShipping',
                async: false,
                headers: {
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name,
                },
                data: {
                    data: JSON.stringify($scope.data_shipping),
                    shipping_id: shipping_id,
                    event_id: $scope.event_id,
                },
                dataType: 'json',
                complete: function (response) {
                    jsonData = JSON.parse(response.responseText);

                    if (jsonData.status == 'OK') {
                        Swal.fire({
                            title: 'SUCCESS',
                            type: 'success',
                            icon: 'success',
                            text: jsonData.message,
                            showConfirmButton: false,
                        });
                        table.ajax.reload();

                        // dissmiss modal
                        $('#changeItemShipping').modal('hide');
                    } else {
                        Swal.fire({
                            title: 'ERROR',
                            type: 'error',
                            icon: 'error',
                            text: jsonData.message,
                            showConfirmButton: false,
                        });
                    }
                },
            });
        };

        // on click close BootstrapDialog
        $(document).on('click', '.bootstrap-dialog-close-button', function () {
            console.log('close');
            table.ajax.reload();
        });

        // on shipping table change value
        $(document).on('change', '#shipping-table', function () {
            Swal.close();
        });

        function onlyUnique(value, index, self) {
            return self.indexOf(value) === index;
        }
    }
);
