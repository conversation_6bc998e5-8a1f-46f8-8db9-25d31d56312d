name: Backup Log

on:
  workflow_dispatch:
  schedule:
    # Runs daily at 6 PM UTC
    - cron: '35 8 * * 1-6'
permissions:
  contents: write
  actions: read

jobs:
  backup:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v2

      - name: Set up Node.js
        uses: actions/setup-node@v2
        with:
          node-version: "14"

      - name: Install dependencies
        run: npm install

      - name: Get the public IP of this runner
        id: get_gh_runner_ip
        shell: bash
        run: |
          echo "ip_address=$(curl https://checkip.amazonaws.com)" >> "$GITHUB_OUTPUT"

      - name: Setup MongoDB Atlas cli
        uses: mongodb/atlas-github-action@v0.2.0

      - name: Format IP address
        id: format-ip
        run: echo "formatted_ip=${{ steps.get_gh_runner_ip.outputs.ip_address }}/32" >> "$GITHUB_ENV"

      - name: Echo runner's public IP
        run: echo ${{ env.formatted_ip }}

      - name: Permit the runner to access MongoDB Atlas
        id: allow-ip
        shell: bash
        run: |
          curl \
            --data '[{"ipAddress": "${{ steps.get_gh_runner_ip.outputs.ip_address }}", "comment": "GitHub Actions Runner"}]' \
            --digest \
            --header 'Accept: application/vnd.atlas.2023-02-01+json' \
            --header 'Content-Type: application/json' \
            --user "$USERNAME:$PASSWORD" \
            "https://cloud.mongodb.com/api/atlas/v2/groups/$GROUP_ID/accessList"
        env:
          GROUP_ID: ${{ secrets.PROJECT_ID }}
          PASSWORD: ${{ secrets.ATLAS_PRIVATE_KEY }}
          USERNAME: ${{ secrets.ATLAS_PUBLIC_KEY }}

      - name: Install MongoDB tools
        run: |
          wget -qO - https://www.mongodb.org/static/pgp/server-4.4.asc | sudo gpg --dearmor -o /usr/share/keyrings/mongodb-archive-keyring.gpg
          echo "deb [ arch=amd64,arm64 signed-by=/usr/share/keyrings/mongodb-archive-keyring.gpg ] https://repo.mongodb.org/apt/ubuntu focal/mongodb-org/4.4 multiverse" | sudo tee /etc/apt/sources.list.d/mongodb-org-4.4.list
          sudo apt-get update
          sudo apt-get install -y mongodb-database-tools
      
      - name: Install Python dependencies
        run: |
          python -m pip install --upgrade pip
          pip install pymongo

# ...existing code...
      - name: Checkout or Create backup_log branch
        run: |
          git fetch --all
          git branch -a
          git checkout backup_log
          git pull origin backup_log
# ...existing code...

      - name: Access the database and export `production_logs` collection
        env:
          ATLAS_PUBLIC_KEY: ${{ secrets.ATLAS_PUBLIC_KEY }}
          ATLAS_PRIVATE_KEY: ${{ secrets.ATLAS_PRIVATE_KEY }}
          PROJECT_ID: ${{ secrets.PROJECT_ID }}
          MONGO_URI: ${{ secrets.MONGO_URI }}
        run: |
          mongoexport --uri "$MONGO_URI" --collection=production_logs --out=production_logs.json

      - name: Calculate file name
        run: |
          TIMESTAMP=$(date +"%Y%m%d%H%M%S")
          echo "timestamp=$TIMESTAMP" >> "$GITHUB_ENV"
      - name: Create backup directory
        run: mkdir -p back_up_log
        
      - name: Zip the backup file
        run: |
          tar -czvf "back_up_log/production_logs_${{ env.timestamp }}.json.tar.gz" production_logs.json

      - name: Commit and push backup_log
        run: |
          git config --global user.name 'github-actions[bot]'
          git config --global user.email 'github-actions[bot]@users.noreply.github.com'
          git add "back_up_log/production_logs_${{ env.timestamp }}.json.tar.gz"
          git commit -m "Backup production_logs.json"
          git push origin backup_log

# ...existing code...
      - name: Delete production_logs collection
        run: |
          python - <<EOF
          import pymongo
          
          def delete_production_logs():
              # Replace the following with your MongoDB connection details
              client = pymongo.MongoClient("${{ secrets.MONGO_URI }}")
              db = client["hkfa_logs"]
              collection = db["production_logs"]
              
              # Drop the collection
              collection.drop()
              print("production_logs collection deleted")

          if __name__ == "__main__":
              delete_production_logs()
          EOF
# ...existing code...
 
      - name: Revoke the runner's access to MongoDB Atlas
        if: always() && steps.allow-ip.outcome == 'success'
        shell: bash
        run: |
          curl \
            --digest \
            --header 'Accept: application/vnd.atlas.2023-02-01+json' \
            --request 'DELETE' \
            --user "$USERNAME:$PASSWORD" \
            "https://cloud.mongodb.com/api/atlas/v2/groups/$GROUP_ID/accessList/${{ steps.get_gh_runner_ip.outputs.ip_address }}"
        env:
          GROUP_ID: ${{ secrets.PROJECT_ID }}
          PASSWORD: ${{ secrets.ATLAS_PRIVATE_KEY }}
          USERNAME: ${{ secrets.ATLAS_PUBLIC_KEY }}