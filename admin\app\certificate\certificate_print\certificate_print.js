app.controller(
    'certificatePrintCtrl',
    function ($scope, $location, $rootScope, $routeParams, $http) {
        var ceneficateId = $routeParams.id;
        $scope.certificateName = $routeParams.certificateName;
        $scope.eventId = $routeParams.eventId;

        jQuery.ajax({
            type: 'POST',
            url: SERVER_PATH + 'event/getEventInfo',
            async: false,
            data: {
                event_id: $scope.eventId,
            },
            headers: {	
				'x-user-id': $rootScope.user_id,
				'x-user-email': $rootScope.user_name
		    },
            dataType: 'json',
            complete: function (response) {
                var jsonData = JSON.parse(response.responseText);
                var event = jsonData.info;
                $scope.event_name = event.name;
                $scope.event_type = event.type;
            },
        });

        var editor = new $.fn.dataTable.Editor({
            ajax: {
                type: 'POST',
                url: SERVER_PATH + 'certificate/setCertificatesPlayers',
                headers: {	
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name
                },
                data: {
                    cernificate_id: ceneficateId,
                },
                dataType: 'json',
                complete: function (response) {
                    table_certificate.ajax.reload();
                },
                error: function (xhr, status, error) {},
            },
            table: '#certificatePrintTable',
            formOptions: {
                main: {
                    onBlur: 'none',
                },
            },
            i18n: {
                remove: {
                    button: 'Delete',
                    title: 'Delete Players',
                    submit: 'Delete',
                    confirm: {
                        _: 'Are you sure you want to delete these players?',
                        1: 'Are you sure you want to delete this player?',
                    },
                },
                error: {
                    system: 'System error, please contact administrator.',
                },
            },
            fields: [
                {
                    label: 'Name:',
                    name: 'certificates_file.file_name',
                },
            ],
        });

        var table_certificate = $('#certificatePrintTable').DataTable({
            dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
            ajax: {
                url: SERVER_PATH + 'certificate/getCertificatesPlayers',
                type: 'POST',
                dataType: 'json',
                headers: {	
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name
                },
                data: {
                    cernificate_id: ceneficateId,
                },
                complete: function (response) {},
                error: function (xhr, status, error) {},
            },
            language: {
                paginate: {
                    previous: '<i class="fa fa-chevron-left"></i>',
                    next: '<i class="fa fa-chevron-right"></i>',
                },
            },
            columns: [
                {
                    data: 'DT_RowId',
                    render: function (data, type, row, meta) {
                        data =
                            '<div class="checkbox"><input type="checkbox" class="dt-checkboxes"><label></label></div>';
                        return data;
                    },
                    checkboxes: {
                        selectRow: true,
                        selectAllRender:
                            '<div class="checkbox"><input type="checkbox" class="dt-checkboxes"><label></label></div>',
                    },
                },
                {
                    data: 'players.player_photo',
                    className: 'avatar',
                    orderable: false,
                    render: function (data) {
                        if (data !== null && data !== '') {
                            return (
                                '<img src="' + PRODUCT_IMAGE_PATH + data + '">'
                            );
                        } else {
                            return (
                                '<img src="' +
                                SYSTEM_IMAGE_PATH +
                                'favicon.png">'
                            );
                        }
                    },
                },
                {
                    data: null,
                    render: function (data, type, row) {
                        return (
                            data.players.surname + ' ' + data.players.other_name
                        );
                    },
                },
                {
                    data: null,
                    render: function (data, type, row) {
                        return row.certificates_file.file_name != ''
                            ? '<a class="openPDF">Download</a>'
                            : '';
                    },
                },
                {
                    data: 'certificates_file.created_at',
                    className: 'center',
                },
            ],
            select: {
                style: 'os',
            },
            buttons: [
                {
                    text: 'Add Player',
                    action: function () {
                        let html = getHTMLAddPlayerToCertificate();

                        //show dialog
                        BootstrapDialog.show({
                            size: BootstrapDialog.SIZE_WIDE,
                            type: BootstrapDialog.TYPE_DANGER,
                            closable: true,
                            closeByBackdrop: false,
                            closeByKeyboard: true,
                            title: 'Add Player',
                            id: 'add-player',
                            message: html,
                            onshown: function (dialog) {
                                $scope.initTableSelectPlayer();
                            },
                            onhide: function (dialog) {
                                $('#certificatePrintTable')
                                    .DataTable()
                                    .ajax.reload();
                                $('body').removeClass('modal-open-custom');
                            },
                            onhidden: function (dialogRef) {
                                $('body').removeClass('modal-open-custom');
                            },
                        });
                    },
                },
                {
                    extend: 'selectedSingle',
                    text: 'Export',
                    action: function () {
                        let PlayerSelected = table_certificate
                            .rows({ selected: true })
                            .data()
                            .toArray();

                        let certificatesFileId =
                            PlayerSelected[0].certificates_file.id;

                        let editor = new $.fn.dataTable.Editor({
                            ajax: {
                                type: 'POST',
                                url:
                                    SERVER_PATH +
                                    'certificate/exportCertificate',
                                headers: {	
                                    'x-user-id': $rootScope.user_id,
                                    'x-user-email': $rootScope.user_name
                                },
                                data: {
                                    certificates_file_id: certificatesFileId,
                                },
                                dataType: 'json',
                                beforeSend: function () {
                                    var element =
                                        document.getElementById('saveNotice');
                                    element.style.display = 'block';
                                },
                                complete: function (response) {
                                    var element =
                                        document.getElementById('saveNotice');
                                    element.style.display = 'none';

                                    var jsonData = JSON.parse(
                                        response.responseText
                                    );

                                    if (jsonData.status == 'ERROR') {
                                        BootstrapDialog.show({
                                            size: BootstrapDialog.SIZE_NORMAL,
                                            type: BootstrapDialog.TYPE_WARNING,
                                            message: jsonData.message,
                                        });
                                    } else {
                                        if (jsonData.status == 'OK') {
                                            fileName = jsonData.fileName;

                                            downloadFile(
                                                PRODUCT_IMAGE_PATH +
                                                    fileName
                                                        .split(' ')
                                                        .join('%20'),
                                                PlayerSelected[0].players
                                                    .surname +
                                                    ' ' +
                                                    PlayerSelected[0].players
                                                        .other_name
                                            );

                                            BootstrapDialog.show({
                                                size: BootstrapDialog.SIZE_NORMAL,
                                                type: BootstrapDialog.TYPE_SUCCESS,
                                                message: jsonData.message,
                                                onhidden: function (dialogRef) {
                                                    table.ajax.reload();
                                                },
                                            });
                                        }
                                    }
                                },
                            },
                        });
                        editor.create(false).submit();
                    },
                },
                {
                    extend: 'remove',
                    editor: editor,
                },
            ],
        });

        $('#certificatePrintTable').on(
            'click',
            'tbody td a.openPDF',
            function (e, row) {
                var $row = $(this).closest('tr');
                var data = table_certificate.row($row).data();
                console.warn(data);
                if (data.certificates_file.file_name != '')
                    window.open(
                        PRODUCT_IMAGE_PATH + data.certificates_file.file_name
                    );
            }
        );

        function downloadFile(urlToSend, fileName) {
            var req = new XMLHttpRequest();
            req.open('GET', urlToSend, true);
            req.responseType = 'blob';
            req.onload = function (event) {
                var blob = req.response;
                //if you have the fileName header available
                var link = document.createElement('a');
                link.href = window.URL.createObjectURL(blob);
                link.download = fileName;
                link.click();
            };

            req.send();
        }

        function getHTMLAddPlayerToCertificate() {
            var html =
                '<style> #saveNotice { position: absolute; display: none; margin-top: 5em; margin-left: 10em; width: 50%; z-index: 2; cursor: pointer; } </style> <div id="saveNotice" class="alert alert-warning" style="display: none;"> <i class="fa fa-check-circle fa-fw fa-lg"></i> <span id="exporting"> Exporting... !</span> </div> <div style=" display: contents; " class="col-lg-12 col-md-12 col-sm-12"> <div class="main-box clearfix"> <div class="col-lg-12"> <div class="main-box clearfix"> <div class="main-box-body clearfix"> <div class="table-responsive"> <p>Select players and click "Add" button</p> <table id="playerTable" class="table table-striped table-bordered table-hover" cellspacing="0" width="100%"> <thead> <tr> <td>Photo</td> <td>Player</td> <td>Year</td> <td>Gender</td> <td>Status</td> </tr> </thead> </table> </div> </div> </div> </div> </div> </div>';

            return html;
        }

        $scope.initTableSelectPlayer = function () {
            var selectedEvent = $('#selEvent').find(':selected').val();

            if ($.fn.dataTable.isDataTable('#playerTable')) {
                $('#playerTable').DataTable().destroy();
            }

            table = $('#playerTable').DataTable({
                dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
                stateSave: true,
                deferRender: true,
                ajax: {
                    url: SERVER_PATH + 'certificate/getAllPlayersInEvent',
                    type: 'POST',
                    data: {
                        event_id: $scope.eventId,
                    },
                    headers: {	
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name
                    },
                    dataType: 'json',
                    complete: function (response) {},
                    error: function (xhr, status, error) {},
                },
                language: {
                    paginate: {
                        previous: '<i class="fa fa-chevron-left"></i>',
                        next: '<i class="fa fa-chevron-right"></i>',
                    },
                },
                columns: [
                    {
                        data: 'player_photo',
                        className: 'avatar',
                        render: function (data) {
                            if (data !== null && data !== '') {
                                return (
                                    '<img src="' +
                                    PRODUCT_IMAGE_PATH +
                                    data +
                                    '">'
                                );
                            } else {
                                return (
                                    '<img src="' +
                                    SYSTEM_IMAGE_PATH +
                                    'favicon.png">'
                                );
                            }
                        },
                    },
                    {
                        data: 'name',
                    },
                    {
                        data: 'birthYear',
                    },
                    {
                        data: 'gender',
                    },
                    {
                        data: 'status',
                        className: 'center',
                        render: function (data, type, full, meta) {
                            switch (data) {
                                case VALIDATE_STATUS_Pending:
                                    return (
                                        '<span class="label label-info">' +
                                        data +
                                        '</span>'
                                    );
                                case VALIDATE_STATUS_Invalid:
                                    return (
                                        '<span class="label label-danger">' +
                                        data +
                                        '</span>'
                                    );
                                case VALIDATE_STATUS_Updated:
                                    return (
                                        '<span class="label label-warning">' +
                                        data +
                                        '</span>'
                                    );
                                case VALIDATE_STATUS_Validated:
                                    return (
                                        '<span class="label label-success">' +
                                        data +
                                        '</span>'
                                    );
                                default:
                                    return (
                                        '<span class="label label-default">' +
                                        data +
                                        '</span>'
                                    );
                            }
                        },
                    },
                ],
                select: {
                    style: 'muti',
                },
                buttons: [
                    'selectAll',
                    'selectNone',
                    {
                        extend: 'selected',
                        text: 'Add',
                        action: function () {
                            var allPlayerSelected = [];
                            var table_selected = table
                                .rows({ selected: true })
                                .data();

                            player_id = -1;
                            player_name = '';

                            for (var i = 0; i < table_selected.length; i++) {
                                allPlayerSelected[i] =
                                    table_selected[i].player_id;
                            }

                            if (allPlayerSelected.length != 1) {
                                var editor = new $.fn.dataTable.Editor({
                                    ajax: {
                                        type: 'POST',
                                        url:
                                            SERVER_PATH +
                                            'certificate/printPDFCertificate',
                                        headers: {	
                                            'x-user-id': $rootScope.user_id,
                                            'x-user-email': $rootScope.user_name
                                        },
                                        data: {
                                            certificate_id: ceneficateId,
                                            player_id:
                                                allPlayerSelected.toString(),
                                        },
                                        dataType: 'json',
                                        complete: function (response) {
                                            var jsonData = JSON.parse(
                                                response.responseText
                                            );

                                            if (jsonData.status == 'ERROR') {
                                                BootstrapDialog.show({
                                                    size: BootstrapDialog.SIZE_NORMAL,
                                                    type: BootstrapDialog.TYPE_WARNING,
                                                    message: jsonData.message,
                                                });
                                            } else if (
                                                jsonData.status == 'OK'
                                            ) {
                                                BootstrapDialog.show({
                                                    size: BootstrapDialog.SIZE_NORMAL,
                                                    type: BootstrapDialog.TYPE_SUCCESS,
                                                    message: jsonData.message,
                                                });

                                                $rootScope.$evalAsync(
                                                    function () {
                                                        $location.path(
                                                            '/certificates/print/' +
                                                                ceneficateId
                                                        );
                                                    }
                                                );
                                            }
                                        },
                                    },
                                });

                                editor.create(false).submit();
                            } else if (allPlayerSelected.length == 1) {
                                var editor = new $.fn.dataTable.Editor({
                                    ajax: {
                                        type: 'POST',
                                        url:
                                            SERVER_PATH +
                                            'certificate/printPDFCertificate',
                                        headers: {	
                                            'x-user-id': $rootScope.user_id,
                                            'x-user-email': $rootScope.user_name
                                        },
                                        data: {
                                            certificate_id: ceneficateId,
                                            player_id:
                                                allPlayerSelected.toString(),
                                        },
                                        dataType: 'json',
                                        complete: function (response) {
                                            var element =
                                                document.getElementById(
                                                    'saveNotice'
                                                );
                                            element.style.display = 'none';

                                            var jsonData = JSON.parse(
                                                response.responseText
                                            );

                                            if (jsonData.status == 'ERROR') {
                                                BootstrapDialog.show({
                                                    size: BootstrapDialog.SIZE_NORMAL,
                                                    type: BootstrapDialog.TYPE_WARNING,
                                                    message: jsonData.message,
                                                });
                                            } else {
                                                if (jsonData.status == 'OK') {
                                                    if (
                                                        typeof jsonData.fileName !=
                                                        'undefined'
                                                    ) {
                                                        fileName =
                                                            jsonData.fileName;

                                                        downloadFile(
                                                            PRODUCT_IMAGE_PATH +
                                                                fileName
                                                                    .split(' ')
                                                                    .join(
                                                                        '%20'
                                                                    ),
                                                            table_selected[0]
                                                                .name
                                                        );
                                                    }

                                                    BootstrapDialog.show({
                                                        size: BootstrapDialog.SIZE_NORMAL,
                                                        type: BootstrapDialog.TYPE_SUCCESS,
                                                        message:
                                                            jsonData.message,
                                                    });
                                                }
                                            }
                                        },
                                    },
                                    beforeSend: function () {
                                        var element =
                                            document.getElementById(
                                                'saveNotice'
                                            );
                                        element.style.display = 'block';
                                    },
                                    fields: [
                                        {
                                            label: 'Generate pdf file:',
                                            name: 'export_pdf',
                                            type: 'radio',
                                            options: [
                                                { label: 'Yes', value: 1 },
                                                { label: 'No', value: 0 },
                                            ],
                                            def: 0,
                                        },
                                    ],
                                });

                                editor
                                    .title('Add player')
                                    .buttons({
                                        label: 'Save',
                                        fn: function () {
                                            this.submit();
                                        },
                                    })
                                    .create()
                                    .open();
                            }
                        },
                    },
                ],
            });
            function downloadFile(urlToSend, fileName) {
                var req = new XMLHttpRequest();
                req.open('GET', urlToSend, true);
                req.responseType = 'blob';
                req.onload = function (event) {
                    var blob = req.response;
                    //if you have the fileName header available
                    var link = document.createElement('a');
                    link.href = window.URL.createObjectURL(blob);
                    link.download = fileName;
                    link.click();
                };

                req.send();
            }
        };
    }
);
