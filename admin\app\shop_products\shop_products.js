
app.controller('shopProductsCtrl', function ($scope, $rootScope, $http) {
    let tableProduct;
    let editorProduct;
    let tableCategory;
    let editorCategory;
    let tableOrders;
    $scope.init = function () {
        initTableCategories();
        initTableProducts();
        initTableOrders();
    }

    function initTableProducts() {
        console.log('initTableProducts');
        editorProduct = new $.fn.dataTable.Editor({
            ajax: {
                type: 'POST',
                url: SERVER_PATH + "shopProducts/setProducts",
                headers: {	
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name
                },
                data: {},
                async: false,
                dataType: 'json',
                complete: function (response) {
                },
                error: function (xhr, status, error) {
                },
            },
            table: "#shopProductTable",
            formOptions: {
                main: {
                    onBlur: 'none'
                }
            },
            i18n: {
                create: {
                    button: "New",
                    title: "Create new product",
                    submit: "Create"
                },
                edit: {
                    button: "Edit",
                    title: "Edit product",
                    submit: "Save"
                },
                remove: {
                    button: "Delete",
                    title: "Delete product",
                    submit: "Delete",
                    confirm: {
                        _: "Are you sure you want to delete these products?",
                        1: "Are you sure you want to delete this product?"
                    }
                },
                error: {
                    system: "System error, please contact product."
                },
            },
            fields: [
                {
                    label: "Category ",
                    name: "shop_products.category_id",
                    type: "select",
                    options: []
                },
                {
                    label: "Name",
                    name: "shop_products.name",
                    attr: {
                        placeholder: "Enter product name"
                    },
                    type: "text",
                },
                {
                    label: "Show",
                    name: "shop_products.show",
                    type: "radio",
                    options: [
                        { label: "Yes", value: 1 },
                        { label: "No", value: 0 },
                    ],
                },
                {
                    label: "Show this product in events",
                    name: "show_in_events",
                    type: "select2",
                    opts: {
                        multiple: 'multiple',
                        // closeOnSelect: false,
                        placeholder: "Select event",
                    },
                    fieldInfo: "<span class='text-muted'>Default display for all users when left blank</span>",

                },
                {
                    label: "Show quantity?",
                    name: "show_quantity",
                    type: "radio",
                    options: [
                        { label: "Yes", value: 1 },
                        { label: "No", value: 0 },
                    ],
                },
                {
                    label: "Description",
                    name: "shop_products.description",
                    attr: {
                        placeholder: "Enter product description"
                    },
                    type: "textarea",
                },
                {
                    label: "Currency",
                    name: "shop_products.currency",
                    type: "select",
                    options: [{
                        label: "HKD",
                        value: "HKD"
                    }]
                },
                {
                    label: "Price",
                    name: "shop_products.price",
                    attr: {
                        type: "number",
                        placeholder: "Enter product price"
                    },
                    type: "text",
                },
                {
                    label: "SKU",
                    name: "shop_products.sku",
                    attr: {
                        placeholder: "Enter product SKU"
                    },
                    type: "text",
                },

                {
                    label: 'Image Upload',
                    name: 'files[].id',
                    type: 'uploadMany',
                    display: (fileId, counter) => {
                        let file = editorProduct.file('files', fileId);
                        let web_path = convertSystemPath2WebPath(file.web_path);

                        // console.log(file);
                        if (!file) return;
                        if (file.extension == 'pdf') {
                            return `<a href="${web_path}" target="_blank">${file.filename}</a>`;
                        }
                        else {
                            return `<img src="${web_path}" style="max-width: 80%!important; max-height: 350px;">`;
                        }
                    },
                    noFileText: 'No files',
                },


            ]
        });

        tableProduct = $('#shopProductTable').DataTable({
            dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
            stateSave: true,
            deferRender: true,
            ajax: {
                url: SERVER_PATH + 'shopProducts/getProducts',
                type: 'POST',
                headers: {	
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name
                },
                data: {},
                dataType: 'json',
                complete: function (response) {
                },
                error: function (xhr, status, error) {
                }
            },
            language: {
                info: "Showing _START_ to _END_ of _TOTAL_ products",
                infoEmpty: "Showing 0 to 0 of 0 products",
                lengthMenu: "Show _MENU_ products",
                select: {
                    rows: {
                        "_": "You have selected %d products",
                        "0": "Click an product to select",
                        "1": "1 product selected"
                    },
                },
                paginate: {
                    previous: '<i class="fa fa-chevron-left"></i>',
                    next: '<i class="fa fa-chevron-right"></i>'
                }
            },
            columns: [
                {
                    data: null,
                    title: "Image",
                    render: function (data, type, row) {
                        if (row.files.length > 0) {
                            let file = editorProduct.file('files', row.files[0].id);
                            return `<img src="${convertSystemPath2WebPath(file.web_path)}" style="max-width: 50px!important; aspect-ratio: 1;">`;
                        } else {
                            return 'No image';
                        }
                    }
                },
                {
                    data: "shop_products.name",
                    title: "Product Name",
                    render: (data, type, row) => {
                        return `<a href="#/product_details/${row.shop_products.id}">${data}</a>`;
                    }
                },
                {
                    data: "shop_products.price",
                    title: "Price",
                    render: function (data, type, row) {
                        // Format the price as currency when displaying
                        if (type === "display") {
                            return parseFloat(data).toFixed(2) + " " + row.shop_products.currency;
                        }
                        return data; // For other types, return the raw data
                    },
                },
                { data: "shop_products.sku", title: "SKU" },
                {
                    data: "shop_categories.name",
                    title: "Category"
                },
                { data: "shop_products.currency", title: "Currency" },
                { data: "shop_products.description", title: "Description" },
                {
                    data: "shop_products.created_at",
                    title: "Created At",
                    render: function (data, type, row) {
                        // Format date for display, assuming 'data' is a date string
                        if (type === "display") {
                            const date = new Date(data);
                            return date.toLocaleString(); // Customize date formatting as needed
                        }
                        return data; // For other types, return the raw data
                    },
                },
                // {
                //     data: "shop_products.updated_at",
                //     title: "Updated At",
                //     render: function (data, type, row) {
                //         // Format date for display, assuming 'data' is a date string
                //         if (type === "display") {
                //             const date = new Date(data);
                //             return date.toLocaleString(); // Customize date formatting as needed
                //         }
                //         return data; // For other types, return the raw data
                //     },
                // },
            ],
            select: {
                style: 'single',
                selector: 'td:first-child',
            },
            order: [[1, 'asc']],
            lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "All"]],
            buttons: [
                { extend: 'create', editor: editorProduct },
                { extend: 'edit', editor: editorProduct },
                { extend: 'remove', editor: editorProduct },
            ]
        });
    }

    function initTableCategories() {
        console.log('initTableCategories');
        editorCategory = new $.fn.dataTable.Editor({
            ajax: {
                type: 'POST',
                url: SERVER_PATH + "shopProducts/setCategories",
                headers: {	
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name
                },
                data: {},
                async: false,
                dataType: 'json',
                complete: function (response) {
                    tableProduct.ajax.reload();
                },
                error: function (xhr, status, error) {
                },
            },
            table: "#shopCategoriesTable",
            formOptions: {
                main: {
                    onBlur: 'none'
                }
            },
            i18n: {
                create: {
                    button: "New",
                    title: "Create new category",
                    submit: "Create"
                },
                edit: {
                    button: "Edit",
                    title: "Edit category",
                    submit: "Save"
                },
                remove: {
                    button: "Delete",
                    title: "Delete category",
                    submit: "Delete",
                    confirm: {
                        _: "Are you sure you want to delete these categories? All products under this category will be deleted.",
                        1: "Are you sure you want to delete this category? All products under this category will be deleted."
                    }
                },
                error: {
                    system: "System error, please contact developer."
                },
            },
            fields: [
                {
                    label: "Name",
                    name: "shop_categories.name",
                    attr: {
                        placeholder: "Enter product name"
                    },
                    type: "text",
                },
                {
                    label: "Description",
                    name: "shop_categories.description",
                    attr: {
                        placeholder: "Enter product description"
                    },
                    type: "textarea",
                },
                {
                    label: "Image Upload",
                    name: "shop_categories.file_id",
                    type: "upload",
                    display: (fileId, counter) => {
                        let file = editorCategory.file('files', fileId);
                        let web_path = convertSystemPath2WebPath(file.web_path);

                        // console.log(file);
                        if (!file) return;
                        if (file.extension == 'pdf') {
                            return `<a href="${web_path}" target="_blank">${file.filename}</a>`;
                        }
                        else {
                            return `<img src="${web_path}" style="max-width: 80%!important; max-height: 350px;">`;
                        }
                    },
                },
                {
                    label: "Icon",
                    name: "shop_categories.icon",
                    attr: {
                        placeholder: "Enter product icon"
                    },
                    fieldInfo: "<span class='text-muted'>*Please refer to <a href='https://fontawesome.com/v5/search' target='_blank'>Font Awesome</a> for icon name</span>",
                }

            ]
        });

        tableCategory = $('#shopCategoriesTable').DataTable({
            dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
            stateSave: true,
            deferRender: true,
            ajax: {
                url: SERVER_PATH + 'shopProducts/getCategories',
                type: 'POST',
                headers: {	
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name
                },
                data: {},
                dataType: 'json',
                complete: function (response) {

                },
                error: function (xhr, status, error) {
                }
            },
            language: {
                info: "Showing _START_ to _END_ of _TOTAL_ products",
                infoEmpty: "Showing 0 to 0 of 0 products",
                lengthMenu: "Show _MENU_ products",
                select: {
                    rows: {
                        "_": "You have selected %d products",
                        "0": "Click an product to select",
                        "1": "1 product selected"
                    },
                },
                paginate: {
                    previous: '<i class="fa fa-chevron-left"></i>',
                    next: '<i class="fa fa-chevron-right"></i>'
                }
            },
            columns: [
                {
                    data: "shop_categories.name",
                    title: "Name"
                },
                { data: "shop_categories.description", title: "Description" },
                {
                    data: 'shop_categories.file_id',
                    render: function (file_id) {
                        if (!file_id) return 'No image'
                        let file = editorCategory.file('files', file_id);
                        let web_path = convertSystemPath2WebPath(file.web_path);
                        return `<img src="${web_path}" style="max-width: 50px!important; aspect-ratio: 1;">`;
                    },
                    defaultContent: 'No image',
                    title: 'Image'
                },
                {
                    data: "shop_categories.icon",
                    title: "Icon",
                    defaultContent: 'No icon',
                    render: function (icon) { return `<i class="${icon}"></i>` },
                },
            ],
            select: {
                style: 'single',
                selector: 'td:first-child',
            },
            order: [[1, 'asc']],
            lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "All"]],
            buttons: [
                { extend: 'create', editor: editorCategory },
                { extend: 'edit', editor: editorCategory },
                { extend: 'remove', editor: editorCategory },
            ]
        });
    }

    function convertSystemPath2WebPath(systemPath) {
        let web_path = systemPath
        // replace "\" to "/"
        web_path = web_path.replace(/\\/g, "/");
        // get file name in web_path
        let fileName = web_path.split('/').pop();
        web_path = `${UPLOAD_FILE_PATH}${fileName}`;
        return web_path;
    }

    getPaymentDetailHTML = function (id) {
        var str =
            '' +
            '<div class="table-responsive">' +
            '<table class="table table-striped table-bordered table-hover" id="paymentDetailTable_' +
            id +
            '">'
        '</table>' +
            '</div>';

        return str;
    };

    initPaymentDetail = function (id) {
        tablePaymentDetail = $('#paymentDetailTable_' + id).DataTable({
            dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
            stateSave: false,
            deferRender: true,
            ajax: {
                url: SERVER_PATH + 'shopProducts/orderDetails',
                type: 'POST',
                async: true,
                headers: {	
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name
                },
                data: {
                    invoice_id: id,
                },
                dataType: 'json',
                complete: function (response) { },
                error: function (xhr, status, error) { },
            },
            language: {
                paginate: {
                    previous: '<i class="fa fa-chevron-left"></i>',
                    next: '<i class="fa fa-chevron-right"></i>',
                },
            },
            buttons: [],
            columns: [
                {
                    title: 'Image',
                    data: 'invoice_details.metadata.image',
                    render: function (data, type, row) {
                        let web_path = convertSystemPath2WebPath(data);
                        return `<img src="${web_path}" style="max-width: 50px!important; aspect-ratio: 1;">`;
                    },
                },
                {
                    title: 'Product Name',
                    data: 'invoice_details.metadata.product_name',
                },
                {
                    title: 'Quantity',
                    data: 'invoice_details.quantity',
                },
                {
                    title: 'Price',
                    data: 'invoice_details.price',
                },
                {
                    title: 'Attributes',
                    data: 'invoice_details.metadata.attributes',
                    render: function (data, type, row) {
                        data = data.value;
                        let str = '';
                        let hasDefault = false;
                        for (let key in data) {
                            let val = data[key];
                            if (isColorAttr(val.type, val.value)) {
                                str += `<b>${val.name}</b>: <span style="border-radius: 50%; background-color: ${val.value}; width: 20px; height: 20px; display: inline-block;"></span><br>`;
                            } else {
                                str += `<b>${val.name}</b>: ${val.value}<br>`;
                            }
                            if (val.type == 'default') {
                                hasDefault = true;
                            }
                        }
                        if (!hasDefault) {
                            return str;
                        } else {
                            return '';
                        }
                    },
                },
            ],
        });
    };

    function isColorAttr(type, value) {
        if (type == 'color' && value.indexOf('#') == 0) {
            return true;
        }
    }

    showPaymentDetail = function (id) {
        var msg = getPaymentDetailHTML(id);

        BootstrapDialog.show({
            title: 'Payment Detail',
            message: msg,
            size: BootstrapDialog.SIZE_WIDE,
            onshown: function (dialogRef) {
                initPaymentDetail(id);
            },
        });
    };

    function initTableOrders() {
        tableOrders = $('#ordersTable').DataTable({
            dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
            stateSave: true,
            deferRender: true,
            ajax: {
                url: SERVER_PATH + 'shopProducts/ordersEditor',
                type: 'POST',
                headers: {	
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name
                },
                data: {},
                dataType: 'json',
                complete: function (response) {
                    console.log('complete');
                },
                error: function (xhr, status, error) {
                }
            },
            language: {
                info: "Showing _START_ to _END_ of _TOTAL_ products",
                infoEmpty: "Showing 0 to 0 of 0 products",
                lengthMenu: "Show _MENU_ products",
                select: {
                    rows: {
                        "_": "You have selected %d products",
                        "0": "Click an product to select",
                        "1": "1 product selected"
                    },
                },
                paginate: {
                    previous: '<i class="fa fa-chevron-left"></i>',
                    next: '<i class="fa fa-chevron-right"></i>'
                }
            },
            columns: [
                {
                    title: "Created At",
                    data: "invoices.invoice_date",
                },
                {
                    title: "Payment No.",
                    data: "invoices.invoice_number",
                },
                {
                    title: "Customer",
                    data: "parens.surname",
                    render: function (data, type, row) {
                        return `${row.parens.surname} ${row.parens.other_name}`;
                    }
                },
                {
                    title: "Transaction ID",
                    data: "invoices.invoice_identification"
                },
                {
                    title: "Amount",
                    data: "invoices.amount",
                },
                {
                    title: "Status",
                    data: "invoices.status",
                },
                {
                    title: "Shipping fee",
                    data: 'invoices.metadata.shipping.fee',
                },
                {
                    title: "Shipping info",
                    data: 'invoices.metadata.shipping.info',
                    render: function (data, type, row) {
                        return `<b>Name:</b> ${data.name}<br><b>Address:</b> ${data.address}<br><b>Phone:</b> ${data.phone}`;
                    }
                },
                {
                    title: "Discount",
                    data: 'invoices.metadata.discount',
                    render: function (data, type, row) {
                        if (data.code == '') return 'No discount';
                        return `<b>Code:</b> ${data.code}<br><b>Discounted:</b> ${data.value}`;
                    }
                },

                {
                    title: "Details",
                    data: 'invoices.id',
                    render: function (data, type, row) {
                        var button = `<button class="btn btn-primary btn-sm" data-toggle="modal" data-target="#paymentDetailModal" onclick="showPaymentDetail('${data}')">Detail</button>`;

                        return button;
                    }
                },
            ],
            select: {
                style: 'single',
            },
            lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "All"]],
            buttons: [
                {
                    text: 'Export to Excel',
                    action: function (e, dt, node, config) {
                        // call to ajax api  shopProducts/exportOrdersExcel
                        $.ajax({
                            url: SERVER_PATH + 'shopProducts/exportOrdersExcel',
                            type: 'POST',
                            headers: {	
                                'x-user-id': $rootScope.user_id,
                                'x-user-email': $rootScope.user_name
                            },
                            data: {},
                            dataType: 'json',
                            complete: function (response) {
                                console.log('complete');
                                response = JSON.parse(response.responseText);
                                if (response.status == 'OK') {
                                    let url = UPLOAD_FILE_PATH + response.filename;
                                    // download file
                                    window.open(url, '_blank');
                                }
                            },
                            error: function (xhr, status, error) {
                            }
                        });
                    }
                },
            ]
        });
    }
})
