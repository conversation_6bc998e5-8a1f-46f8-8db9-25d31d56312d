app.controller(
    'offlineRegistrationSummerSchemeCtrl',
    function ($scope, $rootScope, $routeParams, $http) {
        var event_id = $routeParams.id;
        var player_table;
        $scope.selectedCourses = [];
        $scope.size = { player: 0, parent: 0 };
        $scope.selectedPlayer = [];
        $scope.all_fieldset = [];
        $scope.parent_for_class = null;
        var clicked = false;
        $scope.shoppingCart = [];
        $scope.registrationSummary = [];
        $scope.shipping = {
            type: 'Self pick up',
            address: '',
        };
        $scope.regexAddress =
            /^[a-zA-Z0-9\u3400-\u9fa5\u00C0-\u1EF9\ufeff\u00A0\u202F\uFF0C\uFF09\uFF08\uFF1A\u30FB\u3001\u2019\u3002\uFF0F\u2236]+[a-zA-Z 0-9\u3400-\u9fa5\ufeff\u00C0-\u1EF9\ufeff\u00A0\u202F\uFF0C\uFF09\uFF08\uFF1A\u30FB\u3001\u2019\u3002\uFF0F\u2236\'\;\:\"\,\/\.\-\(\)\&\'\[\]\|\#]*$/;
        $scope.regexEmail = /^[a-z]+[a-z0-9._]+@[a-z]+\.[a-z.]{2,10}$/i;
        $scope.regexEnglishName = /^[a-z]+(([\',. -][ ]?[a-z])?[a-z]*)*$/i;
        $scope.regexChineseName =
            /^[a-zA-Z \u3400-\u9fa5\ufeff\u00A0\u202F\uFF0C]*$/;
        $scope.regexMobile = /^[0-9]{8}$/;
        $scope.regexHKID = /^[A-Za-z]{1,2}[0-9]{6}[A0-9]$/;
        $scope.regexName =
            /^[a-zA-Z\u3400-\u9fa5\u00C0-\u1EF9\ufeff\u00A0\u202F\uFF0C]+(([\',. -][a-z \u3400-\u9fa5\u00C0-\u1EF9\ufeff\u00A0\u202F\uFF0C])?[a-z \u3400-\u9fa5\u00C0-\u1EF9\ufeff\u00A0\u202F\uFF0C]*)*$/i;

        var prototypeCourse = {
            normal: {
                title: 'Training class',
                type: 'normal',
                course: [],
                disabled: false,
                class_selected: [],
                sub_title: ['Boys 男子: 4-13 歲', 'Girls 女子: 4-15 歲'],
            },
            sibling: {
                title: 'Sibling class',
                type: 'sibling',
                course: [],
                disabled: false,
                class_selected: [],
                sub_title: ['Boys 男子: 6-8 10-13 歲', 'Girls 女子: 6-8 10-13 歲'],
            },
            parent: {
                title: 'Parent class',
                type: 'parent',
                course: [],
                disabled: false,
                class_selected: [],
                sub_title: ['Trainee 學員 3-7 歲', 'Parent 家長: 18-65 歲'],
                input: [
                    {
                        title: 'Parent Name',
                        name: 'parent_name',
                        type: 'text',
                        placeholder: 'Parent Name',
                        value: '',
                        required: true,
                        pattern: $scope.regexName,
                        validate: 'name',
                        error_message: '',
                    },
                    {
                        title: 'Parent Dob',
                        name: 'parent_dob',
                        type: 'date',
                        placeholder: 'Parent Dob',
                        value: '',
                        required: true,
                        pattern: '',
                        validate: 'dob',
                        error_message: '',
                        max: moment(
                            new Date(
                                new Date().getFullYear(),
                                11,
                                31
                            ).setFullYear(new Date().getFullYear() - 18)
                        ).format('YYYY-MM-DD'),
                        min: moment(
                            new Date(
                                new Date().getFullYear(),
                                0,
                                1
                            ).setFullYear(new Date().getFullYear() - 65)
                        ).format('YYYY-MM-DD'),
                    },
                    {
                        title: 'Parent HKID',
                        name: 'parent_hkid',
                        type: 'text',
                        placeholder: 'Parent HKID',
                        value: '',
                        required: true,
                        pattern: $scope.regexHKID,
                        validate: 'hkid_no',
                        error_message: '',
                    },
                ],
            },
            goalkeeper: {
                title: 'Goalkeeper class',
                type: 'goalkeeper',
                course: [],
                disabled: false,
                class_selected: [],
                sub_title: ['Boys 男子: 10-13 歲', 'Girls 女子: 13-15 歲'],
            },
        };

        //clone the object - deep copy
        function deep(value) {
            if (typeof value !== 'object' || value === null) {
                return value;
            }
            if (Array.isArray(value)) {
                return deepArray(value);
            }
            //check if value is regexp
            if (value instanceof RegExp) {
                return value;
            }
            return deepObject(value);
        }
        function deepObject(source) {
            const result = {};
            Object.keys(source).forEach((key) => {
                const value = source[key];
                result[key] = deep(value);
            }, {});
            return result;
        }
        function deepArray(source) {
            return source.map((value) => deep(value));
        }

        $scope.checkboxCourse = deep(prototypeCourse);

        $rootScope.shipping_setting = {
            show_self_pickup: false,
        };

        $scope.player_id = 0;

        const DOMstrings = {
            stepsBtnClass: 'multisteps-form__progress-btn',
            stepsBtns: document.querySelectorAll(
                `.multisteps-form__progress-btn`
            ),
            stepsBar: document.querySelector('.multisteps-form__progress'),
            stepsForm: document.querySelector('.multisteps-form__form'),
            stepsFormTextareas: document.querySelectorAll(
                '.multisteps-form__textarea'
            ),
            stepFormPanelClass: 'multisteps-form__panel',
            stepFormPanels: document.querySelectorAll(
                '.multisteps-form__panel'
            ),
            stepPrevBtnClass: 'js-btn-prev',
            stepNextBtnClass: 'js-btn-next',
        };

        jQuery.ajax({
            type: 'POST',
            url: SERVER_PATH + 'event/getEventInfo',
            async: false,
            headers: {	
                'x-user-id': $rootScope.user_id,
                'x-user-email': $rootScope.user_name
            },
            data: {
                event_id: event_id,
            },
            dataType: 'json',
            complete: function (response) {
                var jsonData = JSON.parse(response.responseText);
                var event = jsonData.info;
                event_name = event.name;
                event_type = event.type;
            },
        });

        $rootScope.event_name = event_name;
        $rootScope.event_type = event_type;

        $scope.parent = {
            email: '',
            surname: '',
            othername: '',
            phone_number: '',
            country_code: '',
            create_user: false,
            email_in_system: false,
        };

        $scope.changeEmail = () => {
            if ($scope.parent.email != '') {
                if (!$scope.regexEmail.test($scope.parent.email)) {
                    return;
                }
            }
            $.ajax({
                type: 'POST',
                url: SERVER_PATH + 'paren/checkParentInSystem',
                async: true,
                headers: {	
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name
                },
                data: {
                    email: $scope.parent.email,
                },
                dataType: 'json',
                success: function (response) {
                    var parent = response.data;
                    if (typeof parent == 'undefined') {
                        $scope.parent = {
                            surname: '',
                            othername: '',
                            phone_number: '',
                            country_code: '',
                            create_user: false,
                            email_in_system: false,
                        };
                        return;
                    }

                    $scope.parent.email_in_system = parent.email_in_system;
                    $scope.parent.surname = parent.surname;
                    $scope.parent.othername = parent.othername;

                    if (parent.phone_number != null) {
                        $scope.parent.phone_number =
                            parent.country_code + ' ' + parent.phone_number;
                        $scope.parent.country_code = parent.country_code;
                    } else {
                        $scope.parent.phone_number = '';
                        $scope.parent.country_code = '';
                    }

                    if (!$scope.$$phase) $scope.$apply();
                },
                error: function (xhr, status, error) {
                    console.log('Error: ' + error);
                },
            });
        };

        $scope.isEmailInSystem = () => {
            return $scope.parent.email_in_system;
        };

        // Listen for the form submission event
        $scope.submitCreateParent = function () {
            // set $scope.createParentForm submitted to true
            $scope.createParentForm.$submitted = true;

            console.warn($scope.createParentForm);

            // Check if the form is valid
            if (!$scope.createParentForm.$invalid) {
                // check start of phone number is +86
                var phone_number = $scope.parent.phone_number;

                var iso_code = 'cn';

                if (phone_number.substring(0, 3) == '+86') {
                    iso_code = 'cn';
                    $scope.parent.country_code = '+86';
                    // remove +86 from phone number
                    $scope.parent.phone_number = phone_number.substring(3);
                } else if (phone_number.substring(0, 4) == '+852') {
                    iso_code = 'hk';
                    $scope.parent.country_code = '+852';
                    // remove +852 from phone number
                    $scope.parent.phone_number = phone_number.substring(4);
                }

                // Get the form data
                var formData = {
                    email: $scope.parent.email,
                    surname: $scope.parent.surname,
                    othername: $scope.parent.othername,
                    phone_number: $scope.parent.phone_number,
                    dialCode: $scope.parent.country_code,
                    create_user: $scope.parent.create_user,
                    iso_code: iso_code,
                };

                // Submit the form using AJAX.
                $.ajax({
                    type: 'POST',
                    url: SERVER_PATH + 'paren/submitParent',
                    async: true,
                    headers: {	
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name
                    },
                    data: formData,
                    dataType: 'json',
                    beforeSend: function (xhr) {
                        Swal.fire({
                            title: 'Please Wait!',
                            allowOutsideClick: false,
                            didOpen: () => {
                                Swal.showLoading();
                            },
                        });
                    },
                    success: function (response) {
                        Swal.close();
                        if (response.status == 'OK') {
                            $scope.parent_id = response.data.id;

                            $scope.setActiveStep(1);

                            $scope.setActivePanel(1);

                            // show success message by sweet alert
                            Swal.fire({
                                title: 'Success',
                                html: response.message,
                                icon: 'success',
                                type: 'success',
                                confirmButtonText: 'OK',
                                confirmButtonColor: '#ed1c24',
                            });
                        } else {
                            Swal.fire({
                                title: 'Error',
                                html: response.message,
                                icon: 'error',
                                type: 'error',
                                confirmButtonText: 'OK',
                                confirmButtonColor: '#ed1c24',
                            });
                        }
                    },
                    error: function (xhr, status, error) {
                        console.log('Error: ' + error);
                    },
                });
            }
        };

        $scope.submitShoppingCart = function () {
            if ($scope.shoppingCart.length == 0) {
                Swal.fire({
                    title: 'Error',
                    html: 'Please select at least one product',
                    icon: 'error',
                    type: 'error',
                    confirmButtonText: 'OK',
                    confirmButtonColor: '#ed1c24',
                });
                return;
            }

            $scope.setActiveStep(2);

            $scope.setActivePanel(2);
        };

        $scope.setActiveStep = (step) => {
            //remove active state from all the state
            removeClasses(DOMstrings.stepsBtns, 'js-active');

            //set picked items to active
            DOMstrings.stepsBtns.forEach((elem, index) => {
                if (index <= step) {
                    elem.classList.add('js-active');
                }
            });
        };

        $scope.setActivePanel = (activePanelNum) => {
            $scope.$emit('activetab', activePanelNum);
            //remove active class from all the panels
            removeClasses(DOMstrings.stepFormPanels, 'js-active');

            //show active panel
            DOMstrings.stepFormPanels.forEach((elem, index) => {
                if (index === activePanelNum) {
                    elem.classList.add('js-active');

                    setFormHeight(elem);
                }
            });
        };

        //get active panel
        const getActivePanel = () => {
            let activePanel;

            DOMstrings.stepFormPanels.forEach((elem) => {
                if (elem.classList.contains('js-active')) {
                    activePanel = elem;
                }
            });

            return activePanel;
        };

        const setFormHeight = () => {
            const activePanel = getActivePanel();

            formHeight(activePanel);
        };

        //set form height equal to current panel height
        const formHeight = (activePanel) => {
            const activePanelHeight = activePanel.offsetHeight;

            DOMstrings.stepsForm.style.height = `${activePanelHeight + 800}px`;
        };

        const removeClasses = (elemSet, className) => {
            elemSet.forEach((elem) => {
                elem.classList.remove(className);
            });
        };

        var unsubscribe = $rootScope.$on('activetab', function (event, data) {
            switch (data) {
                case 1: {
                    initPlayersTable($scope.parent_id);
                    break;
                }
                case 2: {
                    $scope.registrationShoppingCart();
                    break;
                }
                default:
                    break;
            }
        });

        function initPlayersTable() {
            var editor = new $.fn.dataTable.Editor({
                ajax: {
                    type: 'POST',
                    url: SERVER_PATH + 'player/setPlayersOfflineRegistration',
                    headers: {	
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name
                    },
                    data: {
                        parent_id: $scope.parent_id,
                        event_id: event_id,
                    },
                    dataType: 'json',
                    complete: function (response) {
                        player_table.ajax.reload();
                    },
                    error: function (xhr, status, error) {},
                },
                idSrc: 'players.id',
                table: '#playerTable',
                formOptions: {
                    main: {
                        onBlur: 'none',
                    },
                },
                i18n: {
                    create: {
                        button: 'New',
                        title: 'Create new player',
                        submit: 'Create',
                    },
                    edit: {
                        button: 'Edit',
                        title: 'Edit player',
                        submit: 'Save',
                    },
                    remove: {
                        button: 'Delete',
                        title: 'Delete player',
                        submit: 'Delete',
                        confirm: {
                            _: 'Are you sure you want to delete these players?',
                            1: 'Are you sure you want to delete this player?',
                        },
                    },
                    error: {
                        system: 'System error, please contact administrator.',
                    },
                },
                fields: [
                    {
                        label: 'Surname:',
                        name: 'players.surname',
                    },
                    {
                        label: 'Other name',
                        name: 'players.other_name',
                    },
                    {
                        label: 'Chinese name',
                        name: 'players.chinese_name',
                    },
                    {
                        label: 'DOB',
                        name: 'players.dob',
                        type: 'datetime',
                        format: 'DD-MMM-YYYY',
                        opts: {
                            minDate: new Date('2000-01-01'),
                        },
                    },
                    {
                        label: 'Gender',
                        name: 'players.gender',
                        type: 'radio',
                        options: [
                            { label: 'Male', value: 'Male' },
                            {
                                label: 'Female',
                                value: 'Female',
                            },
                        ],
                        default: 'Male',
                    },
                    {
                        label: 'Player HKID',
                        name: 'players.hkid_no',
                        type: 'text',
                    },
                    {
                        label: 'HKID/Passport type',
                        name: 'players.hkid_passport_type',
                        type: 'radio',
                        options: ['HKID', 'Passport', 'Mainland Travel Permit'],
                        def: 'HKID',
                    },
                    {
                        label: 'Passport expiry date',
                        name: 'players.passport_expiry_date',
                        type: 'datetime',
                        format: 'DD-MMM-YYYY',
                    },
                    {
                        label: 'Parent_id',
                        default: $scope.parent_id,
                        name: 'players.parent_id',
                        type: 'hidden',
                    },
                ],
            });

            editor.dependent('players.hkid_passport_type', function (val) {
                return val === 'HKID'
                    ? {
                          hide: ['players.passport_expiry_date'],
                      }
                    : {
                          show: ['players.passport_expiry_date'],
                      };
            });

            player_table = $('#playerTable').DataTable({
                dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
                stateSave: true,
                deferRender: true,
                ajax: {
                    url:
                        SERVER_PATH +
                        'registration/getPlayerAndRegistrationOfSummerScheme',
                    type: 'POST',
                    headers: {	
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name
                    },
                    data: {
                        event_id: event_id,
                        parent_id: $scope.parent_id,
                    },
                    dataType: 'json',
                    complete: function (response) {},
                    error: function (xhr, status, error) {},
                },
                language: {
                    info: 'Showing _TOTAL_ total groups',
                    infoEmpty: 'Showing 0 groups',
                    lengthMenu: 'Show _MENU_ groups',

                    paginate: {
                        previous: '<i class="fa fa-chevron-left"></i>',
                        next: '<i class="fa fa-chevron-right"></i>',
                    },
                },
                select: {
                    style: 'single',
                    selector: 'td:not(:last-child)',
                },
                columns: [
                    {
                        data: 'players.player_photo',
                        className: 'avatar',
                        orderable: false,
                        render: function (data) {
                            if (data !== null && data !== '') {
                                return (
                                    '<img src="' +
                                    PRODUCT_IMAGE_PATH +
                                    data +
                                    '">'
                                );
                            } else {
                                return (
                                    '<img src="' +
                                    SYSTEM_IMAGE_PATH +
                                    'favicon.png">'
                                );
                            }
                        },
                    },
                    {
                        data: 'players.other_name',
                        render: function (data, type, row, meta) {
                            return data + ' ' + row.players.surname;
                        },
                    },
                    {
                        data: 'players.chinese_name',
                    },
                    {
                        data: 'players.gender',
                    },
                    {
                        data: 'players.dob',
                        render: function (data, type, row, meta) {
                            return moment(data).format('DD/MM/YYYY');
                        },
                    },
                    {
                        data: 'courses',
                    },
                    {
                        data: null,
                        render: function (data, type, row, meta) {
                            return '<button type="button" data-toggle="modal" data-target="#modal-group" class="btn btn-primary modal-group" style="width:100px">Select</button>';
                        },
                    },
                ],
                buttons: [
                    // add button add
                    { extend: 'create', editor: editor },
                    { extend: 'edit', editor: editor },
                ],
            });

            $('#playerTable').on('click', 'tbody .modal-group', function (e) {
                var player = player_table
                    .row($(this).parents('tr'))
                    .data().players;

                $scope.selectedPlayer = player;

                let suitable = true;

                jQuery.ajax({
                    type: 'POST',
                    url:
                        SERVER_PATH +
                        'registration/checkPlayerSuitableForRegister',
                    async: false,
                    headers: {	
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name
                    },
                    data: {
                        event_id: event_id,
                        player_id: player.id,
                    },
                    dataType: 'json',
                    complete: function (response) {
                        var response = JSON.parse(response.responseText);
                        if (response.status == 'ERROR') {
                            Swal.fire({
                                title: 'Error!',
                                text: 'This player don\'nt have enough information to register for this event. Please use action "Edit" to update player information.',
                                icon: 'error',
                                confirmButtonText: 'OK',
                            });
                            suitable = false;
                        }
                    },
                });

                if (!suitable) {
                    return;
                }

                $scope.getCourseRegistered(player);

                for (const key in $scope.checkboxCourse) {
                    $scope.checkboxCourse[key].class_selected = [];

                    $scope.getCourseForPlayer(
                        $scope.checkboxCourse[key],
                        player
                    );
                }

                $scope.getAllShirts();

                // Show dialog
                showmodal(player);
            });
        }

        $scope.getCourseRegistered = function (player) {
            for (const key in $scope.checkboxCourse) {
                $scope.checkboxCourse[key].disabled = false;
            }

            var data = new FormData();

            data.append('event_id', event_id);
            data.append('player_id', player.id);

            $http({
                url: SERVER_PATH + 'summer-scheme/getTypeCourseRegistered',
                method: 'POST',
                headers: {	
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name
                },
                data: data,
                headers: { 'Content-Type': undefined },
            }).then(function (response) {
                if (response.data.status == 'OK') {
                    if (response.data.info.length > 0) {
                        for (var i = 0; i < response.data.info.length; i++) {
                            $scope.checkboxCourse[
                                response.data.info[i]
                            ].disabled = true;
                        }
                    } else {
                        for (const key in $scope.checkboxCourse) {
                            $scope.checkboxCourse[key].disabled = false;
                        }
                    }
                }

                if (typeof $scope.shoppingCart[player.id] !== 'undefined') {
                    let courses = $scope.shoppingCart[player.id].courses;

                    for (var j = 0; j < courses.length; j++) {
                        $scope.checkboxCourse[
                            courses[j].train_type
                        ].disabled = true;
                    }
                }
            });
        };

        $scope.getCourseForPlayer = function (course_type, player) {
            var birth_year = moment(player.dob).format('YYYY');

            var data = new FormData();

            data.append('event_id', event_id);
            data.append('language', 'en');
            data.append('gender', player.gender);
            data.append('birth_year', birth_year);
            data.append('course_type', course_type.type);
            data.append('offline_registration', 'true');
            data.append('player_id', player.id);

            $http({
                url: SERVER_PATH + 'summer-scheme/getCourseSummerSchemeEvent',
                method: 'POST',
                headers: {	
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name
                },
                data: data,
                headers: { 'Content-Type': undefined },
            }).then(function (response) {
                course_type.course = response.data.info;
            });
        };

        function getSettingsSystem() {
            $http({
                url: SERVER_PATH + 'setting/getSettingsSystem',
                method: 'POST',
                headers: { 
                    'Content-Type': undefined ,
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name 
                },
            }).then(
                function (response) {
                    console.log(response.data);
                    var data = response.data.data;
                    if (data.length > 0) {
                        console.log(data[4].settings.value);
                        $rootScope.shipping_setting.show_self_pickup =
                            data[4].settings.value == 'Yes' ? true : false;
                    }
                },
                function (response) {
                    // this function handles error
                    console.log(response);
                }
            );
        }

        $scope.getAllShirts = function () {
            var data = new FormData();
            data.append('event_id', event_id);

            $http({
                url: SERVER_PATH + 'summer-scheme/getAllShirts',
                method: 'POST',
                data: data,
                headers: {
                    'Content-Type': undefined,
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name 
                },
            }).then(function (response) {
                if (response.data.status == 'OK') {
                    $scope.shirt_sizes = response.data.info.shirt_sizes;
                }
            });
        };

        $scope.selectCourse = function (course, type) {
            //check course in $scope.checkboxCourse[type].class_selected

            var index = $scope.checkboxCourse[type].class_selected.findIndex(
                (x) => x.id == course.id
            );

            if (index == -1) {
                $scope.checkboxCourse[type].class_selected.push({
                    id: course.id,
                    code: course.class_code,
                });
            } else {
                $scope.checkboxCourse[type].class_selected.splice(index, 1);
            }

            console.warn($scope.checkboxCourse[type].class_selected);
        };

        var current_Messages = {
            name: [
                { type: 'required', message: 'Name is required' },
                { type: 'pattern', message: 'Please enter a valid Name' },
            ],
            email: [
                { type: 'required', message: 'Email is required' },
                { type: 'email', message: 'Email is invalid' },
                { type: 'pattern', message: 'Please enter a valid Email' },
            ],
            surname: [
                { type: 'required', message: 'Please enter a surname' },
                {
                    type: 'pattern',
                    message: 'Please enter a valid English surname',
                },
                {
                    type: 'maxlength',
                    message: 'Surname must be less than 50 characters',
                },
            ],
            other_name: [
                { type: 'required', message: 'Please enter a other name' },
                {
                    type: 'pattern',
                    message: 'Please enter a valid English other name',
                },
                {
                    type: 'maxlength',
                    message: 'Other name must be less than 50 characters',
                },
            ],
            chinese_name: [
                { type: 'required', message: 'Please enter a chinese name' },
                {
                    type: 'pattern',
                    message: 'Please enter a valid Chinese name',
                },
                {
                    type: 'maxlength',
                    message: 'Chinese name must be less than 100 characters',
                },
            ],
            gender: [{ type: 'required', message: 'Please select gender' }],
            dob: [
                { type: 'required', message: 'Please select date of birth' },
                { type: 'date', message: 'Please enter a valid date of birth' },
                { type: 'max', message: 'This person not enough age' },
                { type: 'min', message: 'This person is over the age' },
            ],
            phone: [
                { type: 'required', message: 'Please enter a mobile number' },
                { type: 'pattern', message: 'Mobile number must be 8 digits' },
            ],
            hkid_no: [
                { type: 'required', message: 'Please enter a HKID No' },
                {
                    type: 'pattern',
                    message: 'Please enter a valid HKID! e.g.A1234567',
                },
            ],
        };

        $scope.enMsg = JSON.parse(JSON.stringify(current_Messages));

        $scope.toggleCourse = function (course, courseInfor) {
            var index = $scope.selectedCourses.indexOf(course);

            if (index == -1) {
                $scope.selectedCourses.push(course);

                if (course == 'parent') {
                    turnOnValidateParentCourse(courseInfor);
                } else {
                    if ($scope.selectedCourses.indexOf('parent') == -1) {
                        turnOffValidateParentCourse(courseInfor);
                    }
                }
            } else {
                $scope.selectedCourses.splice(index, 1);
                $scope.checkboxCourse[course].class_selected = [];
                if (course == 'parent') {
                    turnOffValidateParentCourse(courseInfor);
                }
            }
        };

        function turnOffValidateParentCourse(courseInfor) {
            for (i in $scope.checkboxCourse.parent.input) {
                let input = $scope.checkboxCourse.parent.input[i];
                courseInfor[input.name].$setViewValue('');
                courseInfor[input.name].$render();
            }
        }

        function turnOnValidateParentCourse(courseInfor) {
            courseInfor.$setPristine();
        }

        function showmodal() {
            getSettingsSystem();

            $('#myModal').modal({
                backdrop: 'static',
                keyboard: false,
            });

            //on open modal set all input type date to current date
            $('#myModal')
                .on('shown.bs.modal', function () {
                    //
                })
                .on('hidden.bs.modal', function () {
                    $scope.reloadForm(); //reload form
                });

            $('.previous').click(function () {
                current_fs = $(this).parent();
                previous_fs = $(this).parent().prev();

                //Remove class active
                $('#progressbar li')
                    .eq($('fieldset').index(current_fs))
                    .removeClass('active');

                //show the previous fieldset
                previous_fs.show();

                //hide the current fieldset with style
                current_fs.animate(
                    { opacity: 0 },
                    {
                        step: function (now) {
                            // for making fielset appear animation
                            opacity = 1 - now;

                            current_fs.css({
                                display: 'none',
                                position: 'relative',
                            });
                            previous_fs.css({ opacity: opacity });
                        },
                        duration: 600,
                    }
                );
            });

            $('.radio-group .radio').click(function () {
                $(this).parent().find('.radio').removeClass('selected');
                $(this).addClass('selected');
            });

            $('.submit').click(function () {
                return false;
            });
        }

        $scope.submitCourse = async function ($event, fieldset, goodsInfo) {
            fieldset['parent_hkid'].$setValidity('editor', true);
            $scope.enMsg = JSON.parse(JSON.stringify(current_Messages));
            $scope.all_fieldset[0] = fieldset;
            fieldset.$setDirty(true);

            console.warn('fieldset', fieldset);

            if ($scope.selectedCourses.indexOf('parent') == -1) {
                if ($scope.size.player > 0) {
                    check_size = true;
                } else {
                    check_size = false;
                }
            } 

            if (fieldset.$valid) {
                var check = $scope.selectedCourses.filter(
                    (element) =>
                        $scope.checkboxCourse[element].class_selected.length ==
                        0
                );

                if (check.length == 0) {
                    if (goodsInfo.$error.required) {
                        goodsInfo.$setPristine();
                    }

                    $scope.parent_for_class = fieldset;

                    $scope.selectedPlayer['parent'] = {
                        name: fieldset.parent_name.$viewValue,
                        dob: fieldset.parent_dob.$viewValue,
                        hkid: fieldset.parent_hkid.$viewValue,
                    };

                    $courses = [];

                    for (const key in $scope.checkboxCourse) {
                        if (
                            $scope.checkboxCourse[key].class_selected.length > 0
                        ) {
                            $scope.checkboxCourse[key].class_selected.forEach(
                                (course) => {
                                    $courses.push({
                                        ...course,
                                        train_type: key,
                                    });
                                }
                            );
                        }
                    }

                    $scope.selectedPlayer['courses'] = $courses;

                    $scope.nextStep($event, fieldset);

                    setTimeout(function () {
                        clicked = false;
                    }, 1500);
                }
            } else {
                fieldset.parent_name.$setDirty(true);
                fieldset.parent_dob.$setDirty(true);
                fieldset.parent_hkid.$setDirty(true);
            }
        };

        $scope.nextStep = function ($event, fieldset) {
            if (fieldset.$valid && clicked == false) {
                clicked = true;
                current_fs = $($event.currentTarget).parent();
                next_fs = $($event.currentTarget).parent().next();

                //show the next fieldset
                setTimeout(function () {
                    $('#progressbar li')
                        .eq($('fieldset').index(next_fs))
                        .addClass('active');

                    next_fs.show();

                    //hide the current fieldset with style
                    current_fs.animate(
                        { opacity: 0 },
                        {
                            step: function (now) {
                                // for making fielset appear animation
                                opacity = 1 - now;

                                current_fs.css({
                                    display: 'none',
                                    position: 'relative',
                                });
                                next_fs.css({ opacity: opacity });
                            },
                            duration: 1200,
                        }
                    );
                }, 1000);
            } else {
                return false;
            }
        };

        $scope.checkDulicateHKID = function (hkid, name = '', dob = '') {
            var data = new FormData();
            data.append('hkid', hkid);
            data.append('name', name);
            data.append('dob', dob);

            return $http({
                url: SERVER_PATH + 'summer-scheme/checkDulicateHKID',
                data: data,
                method: 'POST',
                headers: {
                    'Content-Type': undefined,
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name   
                },
            }).then(function (response) {
                console.log(response.data);
                return response.data;
            });
        };

        $scope.submitGoods = function ($event, fieldset, goodsInfo) {
            let check_size = false;
            if ($scope.selectedCourses.indexOf('parent') == -1) {
                if ($scope.size.player > 0) {
                    check_size = true;
                } else {
                    check_size = false;
                }
            } else {
                if ($scope.size.parent > 0 && $scope.size.player > 0) {
                    check_size = true;
                } else {
                    check_size = false;
                }
            }

            if (fieldset.$valid && check_size) {
                // // set to $scope.selectedPlayer
                // $scope.selectedPlayer['goods'] = {
                //     player_size: $scope.size.player,
                //     parent_size: $scope.size.parent,
                // };

                $scope.selectedPlayer['courses'].forEach((element) => {
                    if (element.train_type != 'parent') {
                        element['goods'] = {
                            player_size: $scope.size.player,
                        };
                    } else {
                        element['goods'] = {
                            player_size: $scope.size.player,
                            parent_size: $scope.size.parent,
                        };
                    }
                });

                // check if player already in cart
                if (
                    typeof $scope.shoppingCart[$scope.selectedPlayer.id] ==
                    'undefined'
                ) {
                    $scope.shoppingCart[$scope.selectedPlayer.id] = deep(
                        $scope.selectedPlayer
                    );
                } else {
                    $scope.selectedPlayer['courses'].forEach((element) => {
                        $scope.shoppingCart[$scope.selectedPlayer.id][
                            'courses'
                        ].push(element);
                    });
                }

                // show success message
                Swal.fire({
                    title: 'Success!',
                    text: 'Added to cart',
                    icon: 'success',
                    confirmButtonText: 'OK',
                }).then((result) => {
                    console.warn($scope.shoppingCart);
                    $('#myModal').modal('hide');
                });
            }
        };

        $scope.reloadForm = function () {
            //set current_fs variable to the first fieldset
            current_fs = $('fieldset:first');
            //set opacity of fieldset to 1
            current_fs.css({ opacity: 1 });
            //show the first fieldset
            current_fs.show();
            //hide the rest of the fieldset
            $('fieldset:not(:first)').hide();
            //reset progressbar
            $('#progressbar li:not(:first)').removeClass('active');
            $('#msform')[0].reset();
            $scope.enMsg = JSON.parse(JSON.stringify(current_Messages));
            $scope.all_fieldset.forEach(function (fieldset) {
                resetForm(fieldset);
            });

            $scope.selectedPlayer = {};

            $scope.selectedCourses = [];

            $scope.checkboxCourse = deep(prototypeCourse);

            $scope.size = {
                player: 0,
                parent: 0,
            };

            $scope.parent_for_class = null;
        };

        function resetForm(fieldset) {
            if (fieldset) {
                console.log('FIELD SET: ');
                console.log(fieldset);
                for (const e in fieldset) {
                    if (!e.includes('$')) {
                        // console.log(e);
                        fieldset[e].$setViewValue('');
                        fieldset[e].$setValidity('editor', true);
                    }
                }
                fieldset.$setPristine();
            }
        }

        $scope.submitShipping = function () {
            // check form createShippingForm is valid
            if ($scope.createShippingForm.$valid) {
                let data = [];

                $scope.shoppingCart.forEach((element) => {
                    element['courses'].forEach((course) => {
                        data.push({
                            player: {
                                player_id: element.id,
                                parent_dob: element.parent.dob,
                                parent_hkid_No: element.parent.hkid,
                                parent_name: element.parent.name,
                                parent_selected_shirt: course.goods.parent_size,
                                selected_shirt: course.goods.player_size,
                            },
                            course: {
                                id: course.id,
                                train_type: course.train_type,
                            },
                        });
                    });
                });

                $.ajax({
                    type: 'POST',
                    url:
                        SERVER_PATH +
                        'registration/registrationOfflineSummerScheme',
                    async: true,
                    headers: {	
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name
                    },
                    data: {
                        user_id: $rootScope.user_id,
                        res_data: JSON.stringify(data),
                        shipping_type: $scope.shipping.type,
                        shipping_value: $scope.shipping.address
                            ? $scope.shipping.address
                            : '',
                        parent_id: $scope.parent_id,
                    },
                    dataType: 'json',
                    beforeSend: function (xhr) {
                        Swal.fire({
                            title: 'Please Wait!',
                            allowOutsideClick: false,
                            didOpen: () => {
                                Swal.showLoading();
                            },
                        });
                    },
                    success: function (response) {
                        Swal.close();
                        if (response.status == 'OK') {
                            Swal.fire({
                                title: 'Success!',
                                text: 'Registration success',
                                icon: 'success',
                                confirmButtonText: 'OK',
                            }).then((result) => {
                                $scope.resetOfflineRegistration();
                            });
                        } else {
                            Swal.fire({
                                title: 'Error!',
                                text: response.message,
                                icon: 'error',
                                confirmButtonText: 'OK',
                            });
                        }
                    },
                    error: function (xhr, status, error) {
                        console.log('Error: ' + error);
                    },
                });
            }
        };

        $scope.resetOfflineRegistration = function () {
            player_table.destroy();
            player_table = $('#player_table').DataTable();
            $scope.selectedCourses = [];
            $scope.size = { player: 0, parent: 0 };
            $scope.selectedPlayer = [];
            $scope.all_fieldset = [];
            $scope.parent_for_class = null;
            clicked = false;
            $scope.shoppingCart = [];
            $scope.registrationSummary = [];
            $scope.shipping = {
                type: 'Self pick up',
                address: '',
            };

            $scope.reloadForm();

            $scope.setActiveStep(0);

            $scope.setActivePanel(0);

            $scope.$apply();
        };

        $scope.transferCoursesToText = function (courses) {
            let text = '';
            courses.forEach((course) => {
                text += course.code + ', ';
            });

            // remove last ', ' and return
            text = text.substring(0, text.length - 2);

            return text;
        };

        $scope.registrationShoppingCart = function () {
            $scope.registrationSummary = [];

            $scope.shoppingCart.forEach((element) => {
                // foreach courses
                element['courses'].forEach((course) => {
                    let shirt_sizes =
                        'Player: ' +
                        $scope.shirt_sizes.find(
                            (item) => item.id == course.goods.player_size
                        ).size_name;
                    if (course.goods.parent_size) {
                        shirt_sizes +=
                            ', Parent: ' +
                            $scope.shirt_sizes.find(
                                (item) => item.id == course.goods.parent_size
                            ).size_name;
                    }
                    $scope.registrationSummary.push({
                        player: element.surname + ' ' + element.other_name,
                        goods: shirt_sizes,
                        course: course.code,
                    });
                });
            });

            return $scope.registrationSummary;
        };

        $scope.$on('$destroy', unsubscribe);
    }
)
    .directive('validateEmail', function () {
        var EMAIL_REGEXP = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/i;
        return {
            require: 'ngModel',
            link: function (scope, elm, attrs, ctrl) {
                // add a custom validator to the ngModel controller
                ctrl.$validators.email = function (modelValue, viewValue) {
                    if (ctrl.$isEmpty(modelValue)) {
                        // treat empty value as valid
                        return true;
                    }

                    if (EMAIL_REGEXP.test(viewValue)) {
                        // valid email
                        return true;
                    }

                    // invalid email
                    return false;
                };
            },
        };
    })
    .directive('validateEnglishName', function () {
        var NAME_REGEXP = /^[A-Za-z\s]+$/;
        return {
            require: 'ngModel',
            link: function (scope, elm, attrs, ctrl) {
                // add a custom validator to the ngModel controller
                ctrl.$validators.englishName = function (
                    modelValue,
                    viewValue
                ) {
                    if (ctrl.$isEmpty(modelValue)) {
                        // treat empty value as valid
                        return true;
                    }

                    if (NAME_REGEXP.test(viewValue)) {
                        // valid English name
                        return true;
                    }

                    // invalid English name
                    return false;
                };
            },
        };
    })
    .directive('validateChineseName', function () {
        var NAME_REGEXP = /^[a-zA-Z \u3400-\u9fa5\ufeff\u00A0\u202F\uFF0C]*$/;
        return {
            require: 'ngModel',
            link: function (scope, elm, attrs, ctrl) {
                // add a custom validator to the ngModel controller
                ctrl.$validators.chineseName = function (
                    modelValue,
                    viewValue
                ) {
                    if (ctrl.$isEmpty(modelValue)) {
                        // treat empty value as valid
                        return true;
                    }

                    if (NAME_REGEXP.test(viewValue)) {
                        // valid Chinese name
                        return true;
                    }

                    // invalid Chinese name
                    return false;
                };
            },
        };
    })
    .directive('validateLength', function () {
        return {
            require: 'ngModel',
            link: function (scope, element, attrs, ngModel) {
                ngModel.$validators.length = function (modelValue, viewValue) {
                    var value = modelValue || viewValue;
                    if (value && value.length <= 50) {
                        return true;
                    }
                    return false;
                };
            },
        };
    }).directive('validateEnglishChineseName', function () {
        var NAME_REGEXP = /^[a-zA-Z0-9\u3400-\u9fa5\u00C0-\u1EF9\ufeff\u00A0\u202F\uFF0C\uFF09\uFF08\uFF1A\u30FB\u3001\u2019\u3002\uFF0F\u2236]+[a-zA-Z 0-9\u3400-\u9fa5\ufeff\u00C0-\u1EF9\ufeff\u00A0\u202F\uFF0C\uFF09\uFF08\uFF1A\u30FB\u3001\u2019\u3002\uFF0F\u2236\'\;\:\"\,\/\.\-\(\)\&\'\[\]\|\#]*$/;
        return {
            require: 'ngModel',
            link: function (scope, elm, attrs, ctrl) {
                // add a custom validator to the ngModel controller
                ctrl.$validators.englishChineseName = function (
                    modelValue,
                    viewValue
                ) {
                    if (ctrl.$isEmpty(modelValue)) {
                        // treat empty value as valid
                        return true;
                    }

                    if (NAME_REGEXP.test(viewValue)) {
                        // valid Chinese name
                        return true;
                    }

                    // invalid Chinese name
                    return false;
                };
            },
        };
    });
