<style>
  #saveNotice {
    position: absolute;
    display: none;
    margin-top: 5em;
    margin-left: 10em;
    width: 50%;
    z-index: 2;
    cursor: pointer;
  }

  .colorSquare {
    width: 20px;
    height: 20px;
    margin: auto;
    border: 1px solid rgba(0, 0, 0, 0.2);
  }

  table.dataTable tbody th.dt-body-center,
  table.dataTable tbody td.dt-body-center {
    text-align: center;
  }
</style>
<div class="row" id="tab-page">
    <div class="row">
        <div class="col-lg-12">
            <ol class="breadcrumb">
                <li>Setup</li>
                <li class="active"><span>Clubs</span></li>
            </ol>
        </div>
    </div>
</div>
<div class="row">
  <div class="col-lg-12">
    <div class="clearfix">
      <h1 class="pull-left">Clubs</h1>
    </div>
  </div>
</div>

<div id="saveNotice" class="alert alert-warning" style="display: none">
  <i class="fa fa-check-circle fa-fw fa-lg"></i>
  <span id="exporting"> Exporting... !</span>
</div>

<div class="row">
  <div class="col-lg-12">
    <div class="main-box clearfix">
      <div class="main-box-body clearfix">
        <div class="table-responsive">
          <table id="club_table" class="table table-striped table-bordered table-hover" cellspacing="0" width="100%">
            <thead>
              <tr>
                <th></th>
                <th>Logo</th>
                <th>Home color</th>
                <th>Away color</th>
                <th>English name</th>
                <th>Chinese name</th>
                <th>Code</th>
                <th>Chinese code</th>
                <th>Active</th>
                <!-- <th>Color</th> -->
                <!-- <th>Email</th>
                                <th>Phone</th>
                                <th>Address</th> -->
              </tr>
            </thead>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>

<script type="text/javascript" language="javascript" class="init">
  function format(d) {
    // `d` is the original data object for the row
    $content = '<h2 class="pull-left">Club Managers</h2><br/>';
    $content +=
      '<div class="table-responsive">' +
      '<table id="managersTable_' +
      d.id +
      '" class="table table-striped table-bordered table-hover" cellspacing="0" width="100%">' +
      '<thead><tr>' +
      '<th>Name</th>' +
      '<th>Email</th>' +
      '</tr></thead>' +
      '</table>' +
      '</div>';

    return $content;
  }

  $(document).ready(function () {
    var table;
    //plugin
    user_id = localStorage.getItem('hkjflApp.user_id');
		user_name = localStorage.getItem('hkjflApp.user_name');

    var editor = new $.fn.dataTable.Editor({
      ajax: {
        type: 'POST',
        url: SERVER_PATH + 'club/setClubs',
        headers: {	
            'x-user-id': user_id,
            'x-user-email': user_name
        },
        data: {
          // "pgroup_id": pgroup_id
        },
        dataType: 'json',
        complete: function (response) {
          var jsonData = JSON.parse(response.responseText);
          // --- may need to reload
          if (DEVELOPMENT_ENVIRONMENT)
            console.log('status = ' + jsonData.status);
          if (jsonData.status == 'OK') {
            if (DEVELOPMENT_ENVIRONMENT) console.log('Before reload');
            table.ajax.reload();
          }
        },
        error: function (xhr, status, error) { },
      },
      table: '#club_table',
      formOptions: {
        main: {
          onBlur: 'none',
        },
      },
      i18n: {
        create: {
          button: 'New',
          title: 'Create new club',
          submit: 'Create',
        },
        edit: {
          button: 'Edit',
          title: 'Edit club',
          submit: 'Save',
        },
        remove: {
          button: 'Delete',
          title: 'Delete club',
          submit: 'Delete',
          confirm: {
            _: 'Are you sure you want to delete these club?',
            1: 'Are you sure you want to delete this club?',
          },
        },
        error: {
          system: 'System error, please contact administrator.',
        },
      },
      fields: [
        {
          label: 'id',
          name: 'id',
          type: 'hidden'
        },
        {
          label: 'Name:',
          name: 'name',
        },
        {
          label: 'Chinese Name',
          name: 'chinese_name',
        },
        {
          label: 'Code:',
          name: 'code',
          // }, {
          //     label: "Email:",
          //     name: "email"
          // }, {
          //     label: "Phone:",
          //     name: "phone"
          // }, {
          //     label: "Address:",
          //     name: "address"
        },
        {
          label: 'Chinese Code',
          name: 'chinese_code',
        },

        {
          label: 'Home color',
          name: 'home_color',
          type: 'colorpicker',
        },

        {
          label: 'Away color',
          name: 'away_color',
          type: 'colorpicker',
        },

        {
          label: 'Logo',
          name: 'logo',
          type: 'upload',
          display: function (data) {
            return (
              '<div style="text-align: center"><img style="background-color:transparent" src="' +
              PRODUCT_IMAGE_PATH +
              data +
              '" width="100%"></div>'
            );
          },
          clearText: 'Clear',
          noImageText: 'No image',
        },
        // {
        //     label: "Home kit",
        //     name: "homekit",
        //     type: "upload",
        //     display: function (data) {
        //         return '<div style="text-align: center"><img style="background-color:transparent " src="' + PRODUCT_IMAGE_PATH + data + '" width="100%"></div>';
        //     },
        //     clearText: "Clear",
        //     noImageText: 'No image'
        // },
        // {
        //     label: "Away kit",
        //     name: "awaykit",
        //     type: "upload",
        //     display: function (data) {
        //         return '<div style="text-align: center"><img style="background-color:transparent " src="' + PRODUCT_IMAGE_PATH + data + '" width="100%"></div>';
        //     },
        //     clearText: "Clear",
        //     noImageText: 'No image'
        // }
      ],
    });

    function addDetailEvent() {
      $('#club_table tbody').off('click', 'td.details-control');
      // Add event listener for opening and closing details
      $('#club_table tbody').on('click', 'td.details-control', function () {
        var tr = $(this).closest('tr');
        var row = table.row(tr);

        if (row.child.isShown()) {
          // This row is already open - close it
          row.child.hide();
          tr.removeClass('shown');
        } else {
          // Open this row
          row.child(format(row.data())).show();
          tr.addClass('shown');

          // add class for next tr (child row)
          $(this).closest('tr').next().addClass('child-row-detail');

          var editor = new $.fn.dataTable.Editor({
            ajax: {
              url: SERVER_PATH + 'club/setClubManagers',
              type: 'POST',
              headers: {	
                  'x-user-id': user_id,
                  'x-user-email': user_name
              },
              data: {
                club_id: row.data().id,
              },
              dataType: 'json',
              complete: function (response) { },
              error: function (xhr, status, error) { },
            },
            table: '#managersTable_' + row.data().id,
            formOptions: {
              main: {
                onBlur: 'none',
              },
            },
            fields: [
              {
                type: 'hidden',
                name: 'club_managers.club_id',
                default: row.data().id,
              },
              {
                label: 'Club Manager:',
                name: 'club_managers.manager_id',
                type: 'select2',
                opts: {
                  placeholder: 'Select a Club Manager',
                },
              },
            ],
            i18n: {
              create: {
                button: 'New',
                title: 'Add new club manager',
                submit: 'Create',
              },
              edit: {
                button: 'Edit',
                title: 'Edit club manager',
                submit: 'Save',
              },
              remove: {
                button: 'Delete',
                title: 'Delete club manager',
                submit: 'Delete',
                confirm: {
                  _: 'Are you sure you want to delete these club manager?',
                  1: 'Are you sure you want to delete this club manager?',
                },
              },
              error: {
                system: 'System error, please contact administrator.',
              },
            },
          });

          $('#managersTable_' + row.data().id).DataTable({
            dom: '<"row"B>rt<"row"i>',
            stateSave: true,
            deferRender: true,
            ajax: {
              url: SERVER_PATH + 'club/getClubManagers',
              type: 'POST',
              headers: {	
                  'x-user-id': user_id,
                  'x-user-email': user_name
              },
              data: {
                club_id: row.data().id,
              },
              dataType: 'json',
              complete: function (response) { },
              error: function (xhr, status, error) { },
            },
            language: {
              info: 'Showing _TOTAL_ total club mannagers',
              infoEmpty: 'Showing 0 club mannagers',
              lengthMenu: 'Show _MENU_ club mannagers',
              select: {
                rows: {
                  _: 'You have selected %d club mannagers',
                  0: 'Click a manager to select',
                  1: '1 manager selected',
                },
              },
              paginate: {
                previous: '<i class="fa fa-chevron-left"></i>',
                next: '<i class="fa fa-chevron-right"></i>',
              },
            },
            columns: [
              {
                data: null,
                render: function (data, type, row) {
                  // Combine the first and last names into a single table field
                  return data.parens.surname + ' ' + data.parens.other_name;
                },
              },
              { data: 'parens.email' },
            ],
            select: {
              style: 'single',
              // selector: 'td:first-child',
            },
            order: [[0, 'asc']],
            displayLength: -1,
            buttons: [
              { extend: 'create', editor: editor },
              // { extend: "edit",   editor: editor },
              { extend: 'remove', editor: editor },
            ],
          });
        }
      });
    }

    var colStatus = [
      { id: 0, name: 'details', status: true },
      { id: 1, name: 'logo', status: true },
      { id: 2, name: 'homekit', status: true },
      { id: 3, name: 'awaykit', status: true },
      { id: 4, name: 'name', status: true },
      { id: 5, name: 'chinese_name', status: true },
      { id: 6, name: 'code', status: true },
      { id: 7, name: 'chinese_code', status: true },
    ];

    $('#club_table').off('click', 'tbody td .onoffswitch-checkbox');

    $('#club_table').on('click', 'tbody td .onoffswitch-checkbox', function (e, row) {
      var $row = $(this).closest('tr');
      var data = table.row($row).data();
      var status = data.isactive == 1 ? '0' : '1';

      changStatus(data.id, data.isactive);
    })

    function changStatus(clubId, status) {
      var action = status == 1 ? 'unactive' : 'active';
      var title = 'Club';
      var message = 'Are you sure you want to ' + action + ' this club';
      var dialog = BootstrapDialog.confirm(title, message, function (result) {
        if (result) {
          dialog.close();
          jQuery.ajax({
            type: 'POST',
            url: SERVER_PATH + "club/updateStatusClubs",
            async: false,
            headers: {	
                'x-user-id': user_id,
                'x-user-email': user_name
            },
            data: {
              'club_id': clubId,
              'is_active': status
            },
            dataType: 'json',
            complete: function (response) {
              var jsonData = JSON.parse(response.responseText);
              BootstrapDialog.show({
                title: jsonData.status,
                type:
                  jsonData.status == 'OK'
                    ? BootstrapDialog.TYPE_SUCCESS
                    : BootstrapDialog.TYPE_WARNING,
                message: jsonData.message,
                onhide: function (dialogRef) {
                  table.ajax.reload();
                },
              })
            }
          })
        } else {
          table.ajax.reload();
        }
      })
    }

    var table = $('#club_table').DataTable({
      dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
      stateSave: true,
      deferRender: true,
      ajax: {
        url: SERVER_PATH + 'club/getClubs',
        type: 'POST',
        headers: {	
            'x-user-id': user_id,
            'x-user-email': user_name
        },
        data: {
          // "pgroup_id": pgroup_id
        },
        dataType: 'json',
        complete: function (response) {
          addDetailEvent();
        },
        error: function (xhr, status, error) { },
      },

      language: {
        info: 'Showing _START_ to _END_ of _TOTAL_ club',
        infoEmpty: 'Showing 0 to 0 of 0 clubs',
        lengthMenu: 'Show _MENU_ clubs',
        select: {
          rows: {
            _: 'You have selected %d clubs',
            0: 'Click a club to select',
            1: '1 club selected',
          },
        },
        paginate: {
          previous: '<i class="fa fa-chevron-left"></i>',
          next: '<i class="fa fa-chevron-right"></i>',
        },
      },
      columns: [
        {
          className: 'details-control',
          orderable: false,
          data: null,
          defaultContent: '',
        },
        {
          data: 'logo',
          render: function (data) {
            if (data !== null && data !== '') {
              return (
                '<img class="club-logo" src="' +
                PRODUCT_IMAGE_PATH +
                data +
                '">'
              );
            } else {
              return (
                '<img class="club-logo" src="' +
                SYSTEM_IMAGE_PATH +
                'logo.png">'
              );
            }
          },
          visible: colStatus['logo'],
        },

        {
          data: 'home_color',
          render: function (data, type, row) {
            if (type === 'display') {
              return (
                '<div class="colorSquare" style="background:' +
                data +
                ';"></div>'
              );
            }
            return data;
          },
          className: 'dt-body-center',
        },

        {
          data: 'away_color',
          render: function (data, type, row) {
            if (type === 'display') {
              return (
                '<div class="colorSquare" style="background:' +
                data +
                ';"></div>'
              );
            }
            return data;
          },
          className: 'dt-body-center',
        },

        // {
        //     data: "homekit",
        //     render: function (data) {
        //         if (data !== null && data !== '') {
        //             return '<img  class="club-logo"  style="background-color:transparent " src="' + PRODUCT_IMAGE_PATH + data + '">';
        //         } else {
        //             return '';
        //         }
        //     },
        //     visible: colStatus['homekit']
        // },

        // //<img class="club-logo"  style="background-color:transparent " src="' + SYSTEM_IMAGE_PATH + 'homekit.png">
        // {
        //     data: "awaykit",
        //     render: function (data) {
        //         if (data !== null && data !== '') {
        //             return '<img class="club-logo"  style="background-color:transparent " src="' + PRODUCT_IMAGE_PATH + data + '">';
        //         } else {
        //             return '';
        //         }
        //     },
        //     visible: colStatus['awaykit']
        // },

        { data: 'name' },
        {
          data: 'chinese_name',
          visible: colStatus['chinese_name'],
        },
        { data: 'code' },
        {
          data: 'chinese_code',
          visible: colStatus['chinese_code'],
        },
        {
          data: null, render: function (data, type, full, meta) {
            var checked = data.isactive == 0 ? '' : 'checked';
            return `<div class="onoffswitch onoffswitch-danger">
								<input type="checkbox" name="onoffswitch2" class="onoffswitch-checkbox" id="switch${data.id}" ${checked}>
								<label class="onoffswitch-label" for="switch${data.id}">
									<div class="onoffswitch-inner"></div>
									<div class="onoffswitch-switch"></div>
								</label>
							</div>
						`;
          }
        }
        // { data: "email" },
        // { data: "phone" },
        // { data: "address" }
      ],
      select: {
        style: 'single',
        // selector: 'td:first-child',
      },

      order: [[4, 'asc']],

      columnDefs: [
        { type: 'mysort', targets: 5 },
        { type: 'mysort', targets: 7 },
      ],

      lengthMenu: [
        [10, 25, 50, 100, -1],
        [10, 25, 50, 100, 'All'],
      ],
      buttons: [
        { extend: 'create', editor: editor },
        { extend: 'edit', editor: editor },
        { extend: 'remove', editor: editor },
        { extend: 'colvis', text: 'Columns' },
        {
          extend: 'pdf',
          name: 'PDF',
          text: 'Export PDF',
          action: function (e, dt, node, config) {
            var hiddenCol = array_getHideColumn();
            var editor = new $.fn.dataTable.Editor({
              ajax: {
                type: 'POST',
                url: SERVER_PATH + 'club/exportClubPdf',
                data: {
                  hiddenCol: hiddenCol,
                },
                headers: {	
                    'x-user-id': user_id,
                    'x-user-email': user_name
                },
                dataType: 'json',
                beforeSend: function () {
                  var element = document.getElementById('saveNotice');
                  element.style.display = 'block';
                },
                complete: function (response) {
                  var element = document.getElementById('saveNotice');
                  element.style.display = 'none';

                  var jsonData = JSON.parse(response.responseText);
                  var fileName = jsonData.info;
                  downloadFile(
                    PRODUCT_IMAGE_PATH + fileName.split(' ').join('%20'),
                    fileName
                  );
                  // window.open(PRODUCT_IMAGE_PATH + fileName.split(' ').join('%20'));
                  // console.log(PRODUCT_IMAGE_PATH + fileName.split(' ').join('%20'));
                  BootstrapDialog.show({
                    size: BootstrapDialog.SIZE_NORMAL,
                    type: BootstrapDialog.TYPE_SUCCESS,
                    message: jsonData.message,
                  });
                },
                error: function (xhr, status, error) {
                  console.log(error);
                },
              },
              formOptions: {
                main: {
                  onBlur: 'none',
                },
              },
              fields: [],
            });

            editor.create(false).submit();
          },
        },
      ],
    });

    // table.columns([0,1,2,3,4,5,6,7]).visible(true);
    // table.columns.adjust().draw( false ); // adjust column sizing and redraw

    // console.log( );

    function array_getHideColumn() {
      var hidColumns = [];

      for (var i = 0; i < colStatus.length; i++) {
        var status = table.column(i).visible();
        if (!status) hidColumns.push(colStatus[i]);
      }

      return hidColumns;
    }

    function downloadFile(urlToSend, fileName) {
      var req = new XMLHttpRequest();
      req.open('GET', urlToSend, true);
      req.responseType = 'blob';
      req.onload = function (event) {
        var blob = req.response;
        //if you have the fileName header available
        var link = document.createElement('a');
        link.href = window.URL.createObjectURL(blob);
        link.download = fileName;
        link.click();
      };

      req.send();
    }
  });
</script>