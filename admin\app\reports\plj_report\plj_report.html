<div class="row" id="tab-page">
    <div class="row">
        <div class="col-lg-12">
            <ol class="breadcrumb">
                <li>Events</li>
                <li><a href="" ng-click="goBack()">{{event_type}}</a></li>
                <li>{{event_name}}</li>
                <li class="active"><span>Report</span></li>
            </ol>
        </div>
    </div>
</div>
<div class="row">
    <div class="col-lg-12">
        <div class="clearfix">
            <h1 class="pull-left">Report</h1>
        </div>
    </div>
    <!-- <div class="col-lg-6 form-group form-group-select2">
        <label for="selEvent">Select event</label>
        <select style="width: 100%" id="select_event_plj" ng-model="selectedEvent"
            ng-options="event.name for event in events" name="selEvent">
        </select>
    </div> -->
</div>
<div>
    <div class="row">
        <div class="col-lg-12">
            <div class="main-box clearfix">
                <div class="tabs-wrapper profile-tabs">
                    <ul class="nav nav-tabs">
                        <li class="active">
                            <a showtab="" data-target="#tab-registration" data-toggle="tab">Registration</a>
                        </li>
                        <li>
                            <a showtab="" data-target="#tab-coach-attendance" data-toggle="tab">Coach Attendance</a>
                        </li>
                    </ul>
                    <div class="tab-content">
                        <div class="tab-pane fade in active" id="tab-registration">
                            <div class="row">
                                <div class="col-lg-6">
                                    <header class="main-box-header clearfix">
                                        <h2>Summary</h2>
                                    </header>

                                    <div class="main-box-body clearfix">
                                        <table class="table table-responsive table-bordered">
                                            <thead>
                                                <tr>
                                                    <th class="text-left">Payment status</th>
                                                    <th class="text-right">Number of registrations</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr ng-repeat="(key, value) in registration_invoices">
                                                    <td class="text-left">{{value.status}}</td>
                                                    <td class="text-right">{{value.count}}</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <header class="main-box-header clearfix">
                                        <h2>Completed Registrations</h2>
                                    </header>

                                    <div class="main-box-body clearfix">
                                        <table class="table table-responsive table-bordered">
                                            <thead>
                                                <tr>
                                                    <th class="text-left">Number of courses in one
                                                        registration</th>
                                                    <th class="text-right">Number of registrations</th>
                                                    <th class="text-right">Total</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr ng-repeat="(key, value) in registration_courses">
                                                    <td class="text-left">{{value.number_course}}</td>
                                                    <td class="text-right">{{value.total}}</td>
                                                    <td class="text-right"><b>{{value.number_course *
                                                            value.total}}</b></td>
                                                </tr>
                                            </tbody>
                                            <tfoot>
                                                <tr>
                                                    <th colspan="2">Total</th>
                                                    <th class="text-right">{{total_registrations}}</th>
                                                </tr>
                                            </tfoot>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="tab-coach-attendance">
                            <div class="row">
                                <div class="col-lg-12">
                                    <div class="main-box-body clearfix">
                                        <form role="form">
                                            <div class="row">
                                                <div class="col-md-6 col-lg-3">
                                                    <label>Date range</label>
                                                    <div class="input-group">
                                                        <span class="input-group-addon"><i
                                                                class="fa fa-calendar-o"></i></span>
                                                        <input type="text" name="dateFilter" class="form-control" />
                                                    </div>
                                                </div>
                                                <div class="col-md-6 col-lg-2">
                                                    <div class="form-group form-group-select2" id="district_filter">
                                                    </div>
                                                </div>
                                                <div class="col-md-6 col-lg-2">
                                                    <div class="form-group form-group-select2" id="group_filter"></div>
                                                </div>
                                                <div class="col-md-6 col-lg-3">
                                                    <div class="form-group form-group-select2" id="coach_filter"></div>
                                                </div>
                                                <div class="col-md-6 col-lg-2">
                                                    <div class="form-group form-group-select2"
                                                        id="approval_status_filter">
                                                    </div>
                                                </div>
                                            </div>
                                        </form>

                                        <div id="attendanceContent"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>