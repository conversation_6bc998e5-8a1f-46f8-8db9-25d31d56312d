
(function ($, DataTable) {

    if (!DataTable.ext.editorFields) {
        DataTable.ext.editorFields = {};
    }

    var Editor = DataTable.Editor;
    var _fieldTypes = DataTable.ext.editorFields;

    _fieldTypes.colorpicker = {
        create: function (conf) {
            var that = this;

            conf._enabled = true;

            // Create the elements to use for the input
            conf._input = $('<input/>')
                .css({"width":"10%", "padding":0})
                .attr($.extend({
                    id: conf.id,
                    type: 'color'
                }, conf.attr || {}))
              

            // Use the fact that we are called in the Editor instance's scope to call
            // input.ClassName
            return conf._input[0];
        },

        get: function (conf) {
            console.log("get val "+conf);
            return conf._input.val();
        },

        set: function (conf, val) {
         
           
        
            conf._input.val(val)
        },

        enable: function (conf) {
            conf._input.prop('disabled', true);
        },

        disable: function (conf) {
            conf._input.prop('disabled', false);
        }
    };
})(jQuery, jQuery.fn.dataTable);

