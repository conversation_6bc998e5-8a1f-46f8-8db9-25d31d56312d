/**
 * SYSTEM
 */
var IS_PAYMENT = true;
var appName = 'HKFA Grassroots Football';
var pathname = window.location.pathname; // Returns path only
var rurl = window.location.href; // Returns full URL
console.log('config.js - pathname, url = ' + pathname + ', ' + rurl);

var appColor = 'white';
localStorage.setItem('background', appColor);
/**
 * SET ENViRONMENT
 */
var PRODUCTION_ENVIRONMENT = false;
var DEVELOPMENT_ENVIRONMENT = false;
var DEBUG_ENVIRONMENT = false;

var ROOT_PATH = '';

var CHANNEL_ENVIRONMENT='development';

if (rurl.indexOf('localhost') > 0 || rurl.indexOf('ngrok') > 0) {
    /**
     * LOCALHOST MODE
     */
    ROOT_PATH = 'http://localhost/ezactivevn/hkfa/';
    WS_PATH = 'ws://ws.localhost/ws/';
    DEVELOPMENT_ENVIRONMENT = true;
    DEBUG_ENVIRONMENT = true;
    UTC_OFFSET = 7;
    CHANNEL_ENVIRONMENT='development';
} else if (rurl.indexOf('hkfa_staging') > 0) {
    // Redirect http to https
    if (rurl.indexOf('http://') == 0) {
        window.location.href = rurl.replace('http://', 'https://');
    }

    /**
     * WEBHOST MODE
     */
    ROOT_PATH = 'https://hkfa.ezactive.com/hkfa_staging/';
    WS_PATH = 'wss://www.ezactive.com/ws/';
    DEVELOPMENT_ENVIRONMENT = false;
    DEBUG_ENVIRONMENT = true;
    UTC_OFFSET = 8;
    CHANNEL_ENVIRONMENT='staging';
} else {
    // Redirect http to https
    if (rurl.indexOf('http://') == 0) {
        window.location.href = rurl.replace('http://', 'https://');
    }
    /**
     * WEBHOST MODE
     */
    ROOT_PATH = 'https://hkfa.ezactive.com/grassroots/';
    WS_PATH = 'wss://www.ezactive.com/ws/';
    PRODUCTION_ENVIRONMENT = true;
    UTC_OFFSET = 8;
    CHANNEL_ENVIRONMENT='production';
}

/**
 * DEFINE PATH
 */
var SERVER_PATH = ROOT_PATH + 'server/admin/';
var SERVER_CLIENT_PATH = ROOT_PATH + 'server/client/';
var FAKE_PATH = 'http://localhost:3000/';
var SYSTEM_IMAGE_PATH = ROOT_PATH + 'images/system/';
var PRODUCT_IMAGE_PATH = ROOT_PATH + 'images/product/';
var UPLOAD_FILE_PATH = ROOT_PATH + 'uploads/files/';

/**
 * DEFINE CONSTANTS
 */
var USER_PARENT = 1;
var USER_TEAM_COACH = 2;
var USER_CLUB_MANAGER = 3;
var USER_FINANCE = 4;
var USER_SHIPPING = 5;
var USER_SUPERVISOR = 6;
var USER_MANAGERIAL_COACH = 7;
var USER_LEAGUE_ADMIN = 8;
var USER_SUPER_ADMIN = 9;
var USER_GRASSROOTS_FINANCE = 10;
var USER_SUPERVISOR_COACH = 206;

var TYPE_PARENT = 'Parent';
var TYPE_COACH = 'Coach';
var TYPE_CLUB_ADMINISTRATOR = 'Club Manager';
var TYPE_FINANCE = 'Finance';
var TYPE_SHIPPING = 'Shipping';
var TYPE_SUPERVISOR = 'Supervisor';
var TYPE_MANAGERIAL_COACH = 'Managerial Coach';
var TYPE_LEAGUE_ADMIN = 'League Admin';
var TYPE_SUPER_ADMIN = 'Super Admin';
var TYPE_GRASSROOTS_FINANCE = 'Grassroots Finance';
var TYPE_SUPERVISOR_COACH = 'Supervisor Coach';

/* Define status of District Session*/
var SESSION_STATUS_CANCELED = 'Cancelled';
var SESSION_STATUS_ACTIVE = 'Active';

/* Define Coach certifycate*/
var SALARY_LIMIT_BY_CERTIFICATE = -1;
var COACH_CERTIFICATE = [
    { name: 'A', order: 1 },
    { name: 'B', order: 2 },
    { name: 'C', order: 3 },
    { name: 'D', order: 4 },
    { name: 'GK', order: 5 },
    { name: 'GK + A', order: 6 },
    { name: 'GK + B', order: 7 },
    { name: 'GK + Other', order: 8 },
    { name: 'YFL', order: 9 },
    { name: 'TBD', order: 10 },
];

var ROLE_CERTIFICATE = [
    {
        role: ROLE_HEAD_COACH,
        certifications: '0,1,2',
        salary_limit: SALARY_LIMIT_BY_CERTIFICATE,
    },
    {
        role: ROLE_ASSITANT_COACH,
        certifications: '0,1,2,3,4,5',
        salary_limit: 3,
    },
    {
        role: ROLE_GOALKEEPER_COACH,
        certifications: '0,1,2,3,4,5',
        salary_limit: SALARY_LIMIT_BY_CERTIFICATE,
    },
];

// define attendance status map

var COACH_ATTENDANCE_STATUS_PENDING = 0;
var COACH_ATTENDANCE_STATUS_ATTENDED = 1;
var COACH_ATTENDANCE_STATUS_CANCELLED = 2;
var COACH_ATTENDANCE_STATUS_DID_NOT_ATTEND = 3;
var COACH_ATTENDANCE_STATUS_SUBSTITUTED = 4;

var ATTENDANCE_STATUS = new Map();
ATTENDANCE_STATUS.set(COACH_ATTENDANCE_STATUS_PENDING, 'Pending');
ATTENDANCE_STATUS.set(COACH_ATTENDANCE_STATUS_ATTENDED, 'Attended');
ATTENDANCE_STATUS.set(COACH_ATTENDANCE_STATUS_CANCELLED, 'Cancelled');
ATTENDANCE_STATUS.set(COACH_ATTENDANCE_STATUS_DID_NOT_ATTEND, 'Did not attend');
ATTENDANCE_STATUS.set(COACH_ATTENDANCE_STATUS_SUBSTITUTED, 'Substituted');

// define session approval status
var SESSION_APPROVAL_STATUS = [
    { name: 'ACCEPT', role: USER_SUPER_ADMIN },
    { name: 'DECLINE', role: USER_SUPER_ADMIN },
    { name: 'ACCEPT', role: USER_LEAGUE_ADMIN },
    { name: 'DECLINE', role: USER_LEAGUE_ADMIN },
    { name: 'PENDING FOR PAYMENT', role: USER_FINANCE },
    { name: 'APPROVE', role: USER_FINANCE },
    { name: 'REJECT', role: USER_FINANCE },
    { name: 'PAID', role: USER_FINANCE },
    { name: 'REFUNDED', role: USER_FINANCE },
    { name: 'ACCEPT', role: USER_GRASSROOTS_FINANCE },
];

// define list of approval status can change by role
var APPROVAL_STATUS_CAN_CHANGE = new Map();
APPROVAL_STATUS_CAN_CHANGE.set(USER_SUPER_ADMIN, [
    '',
    'ACCEPT',
    'DECLINE',
    'REJECT',
    'PENDING',
]);
APPROVAL_STATUS_CAN_CHANGE.set(USER_LEAGUE_ADMIN, [
    '',
    'ACCEPT',
    'DECLINE',
    'REJECT',
    'PENDING',
]);
APPROVAL_STATUS_CAN_CHANGE.set(USER_FINANCE, [
    'ACCEPT',
    'PENDING FOR PAYMENT',
    'APPROVE',
    'REJECT',
    'REFUNDED',
]);
APPROVAL_STATUS_CAN_CHANGE.set(USER_GRASSROOTS_FINANCE, [
    '',
    'ACCEPT',
    'DECLINE',
    'REJECT',
    'PENDING',
]);

var USER_PARENT_PERMISSION = [
    'dashboardCtrl',
    'goldenAgeCtrl',
    'summerSchemeCtrl',
    'plJuniorCtrl',
    'beginnerCtrl',
    'districtCtrl',
    'regionalCtrl',
    'profileCtrl',
    'mainCtrl',
    'logoutCtrl',
    'languageCtrl',
];
var USER_TEAM_COACH_PERMISSION = [
    'dashboardCtrl',
    'goldenAgeCtrl',
    'summerSchemeCtrl',
    'plJuniorCtrl',
    'beginnerCtrl',
    'districtCtrl',
    'regionalCtrl',
    'profileCtrl',
    'mainCtrl',
    'logoutCtrl',
    'clubcoachCtrl',
    'club_coachesCtrl',
    'paymentTeamsCtrl',
    'cgroupsCtrl',
    'teamSheetCtrl',
    'courseCtrl',
    'languageCtrl',
    'messagesCourseCoachCtrl',
    'messagesCourseCoachDetailCtrl',
    'teamDistrictCtrl',
    'teamDistrictDetailsCtrl',
    'registrationsDistrictCtrl',
    'messageTeamCoachCtrl',
    'cdateCtrl',
    'teamGoldenAgeCtrl',
    'teamGoldenAgePlayerCtrl',
    'registrationsBeginnerCtrl',
];
var USER_CLUB_MANAGER_PERMISSION = [
    'dashboardCtrl',
    'goldenAgeCtrl',
    'summerSchemeCtrl',
    'plJuniorCtrl',
    'beginnerCtrl',
    'districtCtrl',
    'regionalCtrl',
    'profileCtrl',
    'mainCtrl',
    'logoutCtrl',
    'clubsCtrl',
    'clubcoachCtrl',
    'club_coachesCtrl',
    'paymentClubsCtrl',
    'attendanceDistrictCtrl',
    'cgroupsCtrl',
    'teamSheetCtrl',
    'languageCtrl',
];

var USER_LEAGUE_ADMIN_PERMISSION = [
    'dashboardCtrl',
    'goldenAgeCtrl',
    'summerSchemeCtrl',
    'plJuniorCtrl',
    'beginnerCtrl',
    'districtCtrl',
    'regionalCtrl',
    'profileCtrl',
    'mainCtrl',
    'logoutCtrl',
    'registrationsCtrl',
    'paymentsCtrl',
    'seasonsCtrl',
    'shopProductsCtrl',
    'shopProducDetailsCtrl',
    'leaguesGroupsCtrl',
    'tablesCtrl',
    'teamSheetCtrl',
    'messagesCtrl',
    'messagesDetailCtrl',
    'reportsCtrl',
    'systemCtrl',
    'sponsorCtrl',
    'notification_app_startCtrl',
    'leagueDetailCtrl',
    'leagueListCtrl',
    'venuesCtrl',
    'languageCtrl',
    'leagueTournamentCtrl',
    'TournamentGroupStageCtrl',
    'TournamentKnockoutStageCtrl',
    'settingsCtrl',
    'generalSettingsCtrl',
    'cgroupsAdminCtrl',
    'admin_clubsCtrl',
    'respectCtrl',
    'trainingSchemeCtrl',
    'courseCtrl',
    'cdateCtrl',
    'trainingSchemeExerciseCtrl',
    'registrationsTrainingSchemeCtrl',
    'registrationsSummerSchemeCtrl',
    'productsCtrl',
    'paymentsDistrictCtrl',
    'paymentsSummerSchemeCtrl',
    'paymentsPLJuniorCtrl',
    'shippingCtrl',
    'shippingPLJCtrl',
    'supervisorCtrl',
    'supervisorCoachCtrl',
    'certificateCtrl',
    'certificateCreateCtrl',
    'certificatePlayerCtrl',
    'certificatePrintCtrl',
    'documentsCtrl',
    'documentSectionsCtrl',
    'attendanceDistrictCtrl',
    'attendanceAcceptanceCtrl',
    'registrationsDistrictCtrl',
    'teamDistrictCtrl',
    'teamDistrictDetailsCtrl',
    'activityLogEachPlayerCtrl',
    'districtReportsCtrl',
    'regionalReportsCtrl',
    'beginnerReportsCtrl',
    'shippingUserCtrl',
    'selfPickUpCtrl',
    'summerSchemeReportsCtrl',
    'offlineRegistrationSummerSchemeCtrl',  
    'registrationsPLJuniorCtrl',
    'PLJReportsCtrl',
    'registrationsBeginnerCtrl',
    'ElderlyReportsCtrl',
    'teamGoldenAgeCtrl',
    'teamGoldenAgePlayerCtrl',
    'ManagerialCoachCtrl',
    'coachCertificateCtrl',
    'eventEditorCtrl',
    'questionnaireCtrl',
    'responsesCtrl',
    'playerCtrl',
    'emailManagerCtrl'
];

var USER_SUPER_ADMIN_PERMISSION = [
    'dashboardCtrl',
    'goldenAgeCtrl',
    'summerSchemeCtrl',
    'plJuniorCtrl',
    'beginnerCtrl',
    'districtCtrl',
    'regionalCtrl',
    'profileCtrl',
    'mainCtrl',
    'logoutCtrl',
    'registrationsCtrl',
    'paymentsCtrl',
    'seasonsCtrl',
    'shopProductsCtrl',
    'shopProducDetailsCtrl',
    'leaguesGroupsCtrl',
    'tablesCtrl',
    'logsCtrl',
    'leagueAdminCtrl',
    'superAdminCtrl',
    'usersCtrl',
    'teamSheetCtrl',
    'messagesCtrl',
    'messagesDetailCtrl',
    'reportsCtrl',
    'systemCtrl',
    'sponsorCtrl',
    'notification_app_startCtrl',
    'leagueDetailCtrl',
    'leagueListCtrl',
    'venuesCtrl',
    'languageCtrl',
    'leagueTournamentCtrl',
    'TournamentGroupStageCtrl',
    'TournamentKnockoutStageCtrl',
    'settingsCtrl',
    'generalSettingsCtrl',
    'cgroupsAdminCtrl',
    'admin_clubsCtrl',
    'respectCtrl',
    'trainingSchemeCtrl',
    'courseCtrl',
    'cdateCtrl',
    'trainingSchemeExerciseCtrl',
    'registrationsTrainingSchemeCtrl',
    'registrationsSummerSchemeCtrl',
    'productsCtrl',
    'paymentsDistrictCtrl',
    'paymentsSummerSchemeCtrl',
    'paymentsPLJuniorCtrl',
    'shippingCtrl',
    'shippingPLJCtrl',
    'supervisorCtrl',
    'supervisorCoachCtrl',
    'certificateCtrl',
    'certificateCreateCtrl',
    'certificatePlayerCtrl',
    'certificatePrintCtrl',
    'documentsCtrl',
    'documentSectionsCtrl',
    'attendanceDistrictCtrl',
    'attendanceAcceptanceCtrl',
    'registrationsDistrictCtrl',
    'teamDistrictCtrl',
    'teamDistrictDetailsCtrl',
    'activityLogEachPlayerCtrl',
    'districtReportsCtrl',
    'regionalReportsCtrl',
    'beginnerReportsCtrl',
    'financeTeamCtrl',
    'grassrootsFinanceCtrl',
    'shippingUserCtrl',
    'selfPickUpCtrl',
    'summerSchemeReportsCtrl',
    'offlineRegistrationSummerSchemeCtrl',
    'registrationsPLJuniorCtrl',
    'PLJReportsCtrl',
    'registrationsBeginnerCtrl',
    'ElderlyReportsCtrl',
    'teamGoldenAgeCtrl',
    'teamGoldenAgePlayerCtrl',
    'ManagerialCoachCtrl',
    'coachCertificateCtrl',
    'eventEditorCtrl',
    'questionnaireCtrl',
    'responsesCtrl',
    'playerCtrl',
    'emailManagerCtrl'
];

var USER_FINANCE_PERMISSION = [
    'dashboardCtrl',
    'goldenAgeCtrl',
    'summerSchemeCtrl',
    'plJuniorCtrl',
    'beginnerCtrl',
    'districtCtrl',
    'regionalCtrl',
    'profileCtrl',
    'mainCtrl',
    'logoutCtrl',
    'paymentsSummerSchemeCtrl',
    'paymentsDistrictCtrl',
    'paymentsPLJuniorCtrl',
    'reportsCtrl',
    'districtReportsCtrl',
    'regionalReportsCtrl',
    'beginnerReportsCtrl',
    'summerSchemeReportsCtrl',
    'PLJReportsCtrl',
    'ElderlyReportsCtrl',
];

var USER_GRASSROOTS_FINANCE_PERMISSION = [
    'dashboardCtrl',
    'goldenAgeCtrl',
    'summerSchemeCtrl',
    'plJuniorCtrl',
    'beginnerCtrl',
    'districtCtrl',
    'regionalCtrl',
    'profileCtrl',
    'mainCtrl',
    'logoutCtrl',
    'paymentsSummerSchemeCtrl',
    'paymentsDistrictCtrl',
    'paymentsPLJuniorCtrl',
    'reportsCtrl',
    'districtReportsCtrl',
    'regionalReportsCtrl',
    'beginnerReportsCtrl',
    'summerSchemeReportsCtrl',
    'PLJReportsCtrl',
    'ElderlyReportsCtrl'
];

var USER_SHIPPING_PERMISSION = [
    'dashboardCtrl',
    'goldenAgeCtrl',
    'summerSchemeCtrl',
    'plJuniorCtrl',
    'beginnerCtrl',
    'districtCtrl',
    'regionalCtrl',
    'selfPickUpCtrl',
    'profileCtrl',
    'mainCtrl',
    'logoutCtrl',
    'languageCtrl',
];

var USER_SUPERVISOR_PERMISSION = [
    'dashboardCtrl',
    'goldenAgeCtrl',
    'summerSchemeCtrl',
    'plJuniorCtrl',
    'beginnerCtrl',
    'districtCtrl',
    'regionalCtrl',
    'profileCtrl',
    'courseCtrl',
    'teamDistrictCtrl',
    'teamDistrictDetailsCtrl',
    'mainCtrl',
    'logoutCtrl',
    'languageCtrl',
];

var USER_SUPERVISOR_COACH_PERMISSION = [
    'dashboardCtrl',
    'goldenAgeCtrl',
    'summerSchemeCtrl',
    'plJuniorCtrl',
    'beginnerCtrl',
    'districtCtrl',
    'regionalCtrl',
    'profileCtrl',
    'mainCtrl',
    'logoutCtrl',
    'clubcoachCtrl',
    'club_coachesCtrl',
    'paymentTeamsCtrl',
    'cgroupsCtrl',
    'teamSheetCtrl',
    'courseCtrl',
    'languageCtrl',
    'messagesCourseCoachCtrl',
    'messagesCourseCoachDetailCtrl',
    'teamDistrictCtrl',
    'teamDistrictDetailsCtrl',
    'registrationsDistrictCtrl',
    'messageTeamCoachCtrl',
    'cdateCtrl',
    'teamGoldenAgeCtrl',
    'teamGoldenAgePlayerCtrl',
    'registrationsBeginnerCtrl',
];

var SHIPPING_STATUS_CANCELLED = 'Cancelled';
var SHIPPING_STATUS_CONFIRMED = 'Confirmed';
var SHIPPING_STATUS_IN_PROGRESS = 'In Progress';
var SHIPPING_STATUS_COMPLETED = 'Completed';

var APPROVAL_STATUS_Register = 'Registered';
var APPROVAL_STATUS_Approve = 'Approved';
var APPROVAL_STATUS_Waiting = 'Waiting';
var APPROVAL_STATUS_Canceled = 'Canceled';
var APPROVAL_STATUS_Reject = 'Reject';

var EVENT_ACTIVE = 'Active';
var EVENT_UPCOMING = 'Upcoming';
var EVENT_ARCHIVED = 'Archived';
var EVENT_COMPLETED = 'Completed';

var VALIDATE_STATUS_Pending = 'Pending';
var VALIDATE_STATUS_Invalid = 'Awaiting update';
var VALIDATE_STATUS_Updated = 'Updated';
var VALIDATE_STATUS_Validated = 'Validated';

var FIELD_PLAYER_surname = 'surname';
var FIELD_PLAYER_other_name = 'other_name';
var FIELD_PLAYER_gender = 'gender';
var FIELD_PLAYER_dob = 'dob';
var FIELD_PLAYER_hkid_passport_photo = 'hkid_passport_photo';
var FIELD_PLAYER_player_photo = 'player_photo';
var FIELD_PLAYER_club = 'club';

var PHOTO_WIDTH = 512;
var PHOTO_HEIGHT = 512;
var HKID_PHOTO_WIDTH = 720;
var HKID_PHOTO_HEIGHT = 480;

var TEAM_MIXED = 'Mixed';
var TEAM_BOYS = 'Boys';
var TEAM_GIRLS = 'Girls';

var TABLE_REGISTRATION_SUMMER_SCHEME = 'summer_registration';
var TABLE_PAYMENT_SUMMER_SCHEME = 'summer_payment';
var TABLE_SHIPPING_SUMMER_SCHEME = 'summer_shipping';

var SINGLE_ROUND_ROBIN = 1;
var DOUBLE_ROUND_ROBIN = 2;
var TRIPLE_ROUND_ROBIN = 3;

var EVENT_SEASON = 'Season';
var EVENT_GOLDENAGE = 'Golden Age';
var EVENT_TRAINING_SCHEME = 'Training Scheme';
var EVENT_SUMMER_SCHEME = 'Summer Scheme';
var EVENT_DISTRICT = 'District';
var EVENT_PL_JUNIOR = 'PL Junior';
var EVENT_REGIONAL = 'Regional';
var EVENT_BEGINNER = 'Beginner';
var EVENT_ELDERLY = 'Elderly';

var TYPE_LEAGUE = {
    text: 'League',
    value: 0,
};

var TYPE_TOURNAMENT = {
    text: 'Tournament',
    value: 1,
};

// payment status
var STATUS_PROCESSING = 'Processing';
var STATUS_SUCCEEDED = 'Accepted';
var STATUS_OFFLINE_REGISTRATION = 'Offline registration';
var STATUS_WAITING_PAYMENT = 'Waiting Payment';
var STATUS_REQUEST_REFUND = 'RequestRefund';
var STATUS_REFUND = 'Refunded';
var STATUS_PARTIAL_REFUND = 'PartialRefunded';
var STATUS_OFFLINE_PAYMENT = 'Offline Payment';
var STATUS_OFFLINE_REJECTED = 'Rejected';
var STATUS_FREE = 'Free';
var STATUS_REQUEST_PARTIAL_REFUND='RequestPartialRefund';

var SHOP_CATEGORY = {
    SHOES: 1,
    SHIRTS: 2,
    SOCKS: 3,
    SHORTS: 4,
    BALLS: 5,
};

// define district payment status label
var DISTRICT_PAYMENT_STATUS = [];

DISTRICT_PAYMENT_STATUS[STATUS_PROCESSING] = 'Processing'; // Pending
DISTRICT_PAYMENT_STATUS[STATUS_WAITING_PAYMENT] = 'Outstanding'; // Pending
DISTRICT_PAYMENT_STATUS[STATUS_SUCCEEDED] = 'Paid'; // Accepted
DISTRICT_PAYMENT_STATUS[STATUS_OFFLINE_PAYMENT] = 'Paid offline'; // Offline Payment
DISTRICT_PAYMENT_STATUS[STATUS_REFUND] = 'Refunded'; // Refunded
DISTRICT_PAYMENT_STATUS[STATUS_FREE] = 'Free'; // Free
DISTRICT_PAYMENT_STATUS[STATUS_REQUEST_PARTIAL_REFUND] = 'Request Partial Refund';
DISTRICT_PAYMENT_STATUS[STATUS_REQUEST_REFUND] = 'Request Refund'; // RequestRefund

/**
 * Le Quyet Tien 03/01/2018
 * Check device mobile or pc for change type select attribute in Datatable
 */
console.log(navigator.userAgent);
if (
    /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
        navigator.userAgent
    )
) {
    // is mobile
    var SELECT_MODE = 'multi';
} else {
    // is pc
    var SELECT_MODE = 'os';
}

var SEND_TO_ALL = 0;
var SEND_TO_GROUPS = 1;
var SEND_TO_CLUBS = 2;
var SEND_TO_GROUPS_IN_CLUBS = 3;
var SEND_TO_PARENT_OF_PLAYER = 5;
var SEND_TO_SELF_PICK_UP = 7;
var SEND_TO_COURSE = 8;
var SEND_TO_VENUE = 9;
var SEND_TO_SHIPPING_TYPE = 10;
var SEND_FROM_COACH_TO_PLAYER = 6;
var SEND_TO_TEAM = 11;

var SEND_VIA_EMAIL = 0;
var SEND_VIA_NOTIFICAION = 1;
var SEND_VIA_EMAIL_AND_NOTIFICAION = 2;

var REPORT_ID_REGISTRATION = 1;

let title_el = document.querySelector('title');
let documentTitle = document.title;
if (title_el) {
    console.log('found title ! ' + documentTitle);
    if (documentTitle.includes('Login')) {
        title_el.innerHTML = appName + ' - Login';
    }
    if (documentTitle.includes('Dashboard')) {
        title_el.innerHTML = appName;
    }

    if (documentTitle.includes('ERROR-404')) {
        title_el.innerHTML = appName + ' - ERROR-404';
    }

    if (documentTitle.includes('ERROR-500')) {
        title_el.innerHTML = appName + ' - ERROR-500';
    }

    if (documentTitle.includes('Reset password')) {
        title_el.innerHTML = appName + ' - Reset password';
    }

    if (documentTitle.includes('Sign up')) {
        title_el.innerHTML = appName + ' - Sign up';
    }
}

var MATCH_POSTPONED = 1;
var MATCH_CANCELLED = 2;
var MATCH_ABANDONED = 3;

var ROLE_HEAD_COACH = 'Head Coach';
var ROLE_ASSITANT_COACH = 'Assistant Coach';
var ROLE_GOALKEEPER_COACH = 'Goalkeeper Coach';

// define boolean value
var BOOLEAN_TRUE = 1;
var BOOLEAN_FALSE = 0;

// define payment method
var ASIA_PAY = 'AsiaPay';
var OFFLINE_PAYMENT = 'Offline Payment';

var COMMENT_SUGGESTION_OPTIONS = [
    {
        value: 1,
        text: 'Player photo',
    },
    {
        value: 2,
        text: 'HKID/Passport type',
    },
    {
        value: 3,
        text: 'HKID/Passport photo',
    },
];

// session type
var DISTRICT_SESSION_TYPE = [
    {
        value: 0,
        label: 'Selection',
    },
    {
        value: 1,
        label: 'Training',
    },
    {
        value: 2,
        label: 'Match',
    },
];

// translate config
var EN = {
    DASHBOARD: 'Dashboard',
    GOLDEN_AGE: 'Golden Age',
    SUMMER_SCHEME: 'Summer Scheme',
    PL_JUNIOR: 'PL Junior',
    DISTRICT: 'District',
    BEGINNER: 'Beginner',
    REGIONAL: 'Regional',
    REGISTRATION: 'Registration',
    SEASON: 'Season',
    LEAGUE: 'League',
    TEAM_SHEET: 'Team sheet',
    MESSAGES: 'Messages',
    REPORT: 'Report',
    TABLES: 'Tables',
    SETTINGS: 'Settings',
    TEAMS: 'Teams',
    CLUBS: 'Club',
    SHOP: 'Shop',
    COURSE: 'Course/Team',
    PAYMENTS: 'Payments',
    YEAR_GROUPS: 'Year Group',
    RESPECT: 'Respect',
    DOCUMENT: 'Document',
    TRAINING: 'Training',
    TRAINING_SCHEME: 'Training Scheme',
    TRAINING_SCHEDULE: 'Training Schedule',
    CERTIFICATE: 'Certificate',
    SHIPPING: 'Shipping',
    SELF_PICK_UP: 'Self Pick Up',
    QUESTIONNAIRE: 'Questionnaire',
    ATTENDANCE: 'Attendance',
    EDUCATION: 'Education',
    TABLES_LIST: {
        EVENTS: 'Events',
        CLUBS: 'Clubs',
        PLAYERS: 'Players',
        PARENTS: 'Parents',
        CLUB_MANAGERS: 'Club Managers',
        LEAGUE_ADMINS: 'League Admins',
        SUPER_ADMINS: 'Super Admins',
        USERS: 'Users',
        VENUES: 'Venues',
        COACHES: 'Coaches',
        SPONSORS: 'Sponsors',
        PRODUCTS: 'Products',
        FINANCE_TEAM: 'Finance Teams',
        GRASSROOTS_FINANCE: 'Grassroots Finance',
        SHIPPING: 'Shipping Users',
        SUPERVISOR: 'Supervisor',
        SUPERVISOR_COACH: 'Supervisor Coach',
        NON_TRAINING_DAYS: 'Non Training Days',
        MANAGERIAL_COACH: 'Managerial Coach',
        COACH_CERTIFICATE: 'Coach Certificate',
        LOG: 'Log'
    },
    SETTINGS_LIST: {
        SYSTEM: 'System',
        NOTIFICATION_APP_START: 'Notification (App start)',
        COMMENT_SUGGESTION: 'Comment Suggestion',
        EMAIL_TEMPLATE: 'Email Editor',
    },
};

var HANS = {
    DASHBOARD: '摘要',
    GOLDEN_AGE: 'Golden Age',
    SUMMER_SCHEME: 'Summer Scheme',
    PL_JUNIOR: 'PL Junior',
    DISTRICT: 'District',
    BEGINNER: 'Beginner',
    REGIONAL: 'Regional',
    REGISTRATION: '注册',
    SEASON: '球节',
    LEAGUE: '联赛',
    TEAM_SHEET: '球员名单',
    MESSAGES: '讯息',
    REPORT: '报告',
    TABLES: '列表',
    SETTINGS: '设定',
    TEAMS: 'Teams',
    SHOP: '商店',
    CLUBS: '球会',
    COURSE: '课程/队伍',
    PAYMENTS: '付款',
    YEAR_GROUPS: '组别',
    RESPECT: '尊重',
    DOCUMENT: '文档',
    TRAINING: 'Training',
    TRAINING_SCHEME: '訓練項目',
    TRAINING_SCHEDULE: '訓練時間表',
    CERTIFICATE: '證書',
    SHIPPING: '船運',
    SUPERVISOR: '督導',
    SUPERVISOR_COACH: '督導教練',
    SELF_PICK_UP: '自取',
    QUESTIONNAIRE: '問卷調查',
    ATTENDANCE: '出席',
    EDUCATION: '教育', 
    TABLES_LIST: {
        EVENTS: '项目',
        CLUBS: '球会',
        PLAYERS: '球员',
        PARENTS: '家长',
        CLUB_MANAGERS: '球会教练',
        LEAGUE_ADMINS: '联赛管理',
        SUPER_ADMINS: '超级管理员',
        USERS: '用户',
        VENUES: '场地',
        COACH: '教练',
        SPONSORS: '赞助商',
        PRODUCTS: '產品',
        FINANCE_TEAM: '财务团队',
        GRASSROOTS_FINANCE: '草根财务',
        SHIPPING: '船运用户',
        SUPERVISOR: '督导',
        SUPERVISOR_COACH: '督导教练',
        NON_TRAINING_DAYS: '非培训日',
        MANAGERIAL_COACH: 'Managerial Coach',
        COACHES: '教练',
        COACH_CERTIFICATE: '教练证书',
    },
    SETTINGS_LIST: {
        SYSTEM: '系统',
        NOTIFICATION_APP_START: '通知（应用程序启动）',
        COMMENT_SUGGESTION: '评论建议',
        EMAIL_TEMPLATE: 'Email Editor',
    },
};

var HANT = {
    DASHBOARD: '摘要',
    GOLDEN_AGE: 'Golden Age',
    SUMMER_SCHEME: 'Summer Scheme',
    PL_JUNIOR: 'PL Junior',
    DISTRICT: 'District',
    BEGINNER: 'Beginner',
    REGIONAL: 'Regional',
    REGISTRATION: '註冊',
    SEASON: '球季',
    LEAGUE: '聯賽',
    TEAM_SHEET: '球員名單',
    MESSAGES: '信息',
    REPORT: '報告',
    TABLES: '列表',
    SETTINGS: '設定',
    TEAMS: 'Teams',
    CLUBS: '球會',
    COURSE: '課程/團隊',
    PAYMENTS: '付款',
    YEAR_GROUPS: '組別',
    RESPECT: '尊重',
    SHOP: '商店',
    DOCUMENT: '文檔',
    TRAINING: '培訓',
    TRAINING_SCHEME: '訓練項目',
    TRAINING_SCHEDULE: '培訓時間表',
    CERTIFICATE: '證書',
    SHIPPING: '船運',
    SELF_PICK_UP: '自取',
    ATTENDANCE: '出席',
    EDUCATION: '教育',
    TABLES_LIST: {
        EVENTS: '項目',
        CLUBS: '球會',
        PLAYERS: '球員',
        PARENTS: '家長',
        CLUB_MANAGERS: '球會教練',
        LEAGUE_ADMINS: '聯賽管理',
        SUPER_ADMINS: '超級管理員',
        USERS: '用戶',
        VENUES: '場地',
        COACH: '教练',
        SPONSORS: '贊助商',
        PRODUCTS: '產品',
        FINANCE_TEAM: '財務團隊',
        GRASSROOTS_FINANCE: '草根財務',
        SHIPPING: '船運',
        NON_TRAINING_DAYS: '非培訓日',
        MANAGERIAL_COACH: '管理教練',
        COACHES: '教練',
        SUPERVISOR: '督導',
        SUPERVISOR_COACH: '督導教練',
        COACH_CERTIFICATE: '教練證書',
        LOG: '日誌'
    },
    SETTINGS_LIST: {
        SYSTEM: '系統',
        NOTIFICATION_APP_START: '通知',
        COMMENT_SUGGESTION: '評論建議',
        EMAIL_TEMPLATE: 'Email Editor',
    },
};

const TASK_ID_ADMINISTRATOR_EDITOR = 1;

const TASK_ID_NOT_ADMINISTRATOR_EDITOR = 0;

const SOCKET_URL='https://websocket-if4t.onrender.com';
