.dropdown-menu > .active > a,
.dropdown-menu > .active > a:hover,
.dropdown-menu > .active > a:focus {
    background-color: #2980b9;
}

#myModal .modal-header {
    color: white;
    background-color: #2980b9;
}

input[type='radio']:checked + label {
    font-weight: bold;
}

.nav-tabs > li.active > a,
.nav-tabs > li.active > a:hover,
.nav-tabs > li.active > a:focus {
    cursor: default !important;
}

.nav-tabs > li > a,
.nav-tabs > li > a:hover,
.nav-tabs > li > a:focus {
    cursor: pointer !important;
}

table.dataTable tbody tr.selected {
    background-color: #2980b9 !important;
}

.table tbody td.avatar {
    max-width: 85px;
    width: 50px;
}

.table tbody td.avatar img {
    text-align: center;
    margin-right: 0;
}

.table tbody td.trainingSchemeGroups img {
    max-width: 200px;
    max-height: 200px;
}

img.club-logo {
    width: 32px;
    margin: 8px;
    background-color: transparent !important;
}

.align-left {
    text-align: left !important;
}
.align-right {
    text-align: right !important;
}
.error {
    color: RED;
}

.center {
    text-align: center !important;
}

.DTE_Form_Content .control-label {
    text-align: left !important;
}

/* CKeditor 5 */
.ck.ck-mentions {
    padding-left: 0px;
    max-height: var(--ck-mention-list-max-height);
    overflow-x: hidden;
    overflow-y: auto;
    overscroll-behavior: contain;
}

.ck.ck-list__item {
    cursor: default;
    min-width: fit-content;
}

.ck-content .mention {
    /* background:rgb(25 95 220 / 76%); */
    color: #ff2c2c;
    border-radius: 25px;
    padding: 5px;
}

.ck.ck-splitbutton {
    font-size: inherit;
    display: flex;
    flex-flow: row;
}

.ck.ck-list {
    padding: 0;
}

.ck-editor__main h1,
h2,
h3,
h4 {
    margin: 0;
    padding: 0;
}

.ck-editor__main h1,
h2,
h3,
h4 {
    border-bottom: 0;
}

@media (max-width: 767px) {
    .dataTables_length,
    .dataTables_filter {
        display: block;
        position: relative;
        width: 100%;
        margin: 8px;
        left: 0;
        right: 0;
        text-align: center;
    }
}

/* Format telephone field with intl-tel-input in Editor*/
.iti.iti--allow-dropdown {
    width: 100%;
}

.width-100 {
    width: 100%;
}

.center {
    text-align: center;
}

tr.group,
tr.group:hover {
    background-color: #ddd !important;
}

.sidemenu-icon {
    width: 22px;
    height: 22px;
}

.sidemenu-label {
    margin-left: 12px !important;
}

.split-group {
    padding: 0.5rem 0;
    border-top: 1px solid #919191;
}

.split-label {
    margin-left: 1rem;
    margin: 1rem 0 1rem 1rem;
    color: #bfbfbf;
    text-transform: uppercase;
    font-size: 11px;
}

/* STYLE FOR MANAGE USERS TABLE */

.manage-users-wrapper {
    display: flex;
    flex-direction: column;
    gap: 2.5rem;
}

.manage-users-wrapper .alert-container {
    display: flex;
    align-items: start;
    justify-content: start;
    gap: 1rem;
    background: rgba(75, 75, 75, 0.16);
    padding: 1rem;
    border-radius: 8px;
}

.manage-users-wrapper .alert-container .alert-icon {
    padding: 8px;
    background: white;
    border-radius: 6px;
    display: flex;
    gap: 0.5rem;
}

.alert-container .alert-icon i {
    font-size: 20px;
}

.alert-container .alert-content {
    display: flex;
    flex-direction: column;
    gap: 0.125rem;
}

.alert-container .alert-content .title {
    font-size: 16px;
    color: rgba(75, 75, 75, 1);
    font-weight: 600;
    margin: 0;
}

.alert-container .alert-content .description {
    font-size: 14px;
    color: #6c757d;
    margin: 0;
}

.select-role-wrapper {
    width: 25%;
    display: flex;
    flex-direction: column;
    align-items: start;
    gap: 4px;
}
