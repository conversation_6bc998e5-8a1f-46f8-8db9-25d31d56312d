app.controller(
    'registrationsSummerSchemeCtrl',
    function ($scope, $rootScope, $routeParams, $http, $location) {
        $('#page-wrapper').removeClass('nav-small');
        var types = ['Normal', 'Goalkeeper', 'Parent'];
        $scope.tableCourse = null;
        var clicked = false;
        var submitShipping = false;
        var submitCancel = false;
        var temp_player = null;
        $scope.today = moment(new Date()).format('YYYY-MM-DD');
        //For Modal Offline Registration
        var current_fs, next_fs, previous_fs; //fieldsets
        var opacity;
        $scope.regexAddress =
            /^[a-zA-Z0-9\u3400-\u9fa5\u00C0-\u1EF9\ufeff\u00A0\u202F\uFF0C\uFF09\uFF08\uFF1A\u30FB\u3001\u2019\u3002\uFF0F\u2236]+[a-zA-Z 0-9\u3400-\u9fa5\ufeff\u00C0-\u1EF9\ufeff\u00A0\u202F\uFF0C\uFF09\uFF08\uFF1A\u30FB\u3001\u2019\u3002\uFF0F\u2236\'\;\:\"\,\/\.\-\(\)\&\'\[\]\|\#]*$/;
        $scope.regexEmail = /^[a-z]+[a-z0-9._]+@[a-z]+\.[a-z.]{2,10}$/i;
        $scope.regexEnglishName = /^[a-z]+(([\',. -][ ]?[a-z])?[a-z]*)*$/i;
        $scope.regexChineseName =
            /^[a-zA-Z \u3400-\u9fa5\ufeff\u00A0\u202F\uFF0C]*$/;
        $scope.regexMobile = /^[0-9]{8}$/;
        $scope.regexHKID = /^[A-Za-z]{1,2}[0-9]{6}[A0-9]$/;
        $scope.regexName =
            /^[a-zA-Z\u3400-\u9fa5\u00C0-\u1EF9\ufeff\u00A0\u202F\uFF0C]+(([\',. -][a-z \u3400-\u9fa5\u00C0-\u1EF9\ufeff\u00A0\u202F\uFF0C])?[a-z \u3400-\u9fa5\u00C0-\u1EF9\ufeff\u00A0\u202F\uFF0C]*)*$/i;
        $scope.all_fieldset = [];
        $scope.parent = null;
        $scope.parent_for_class = null;
        $scope.player = null;
        $scope.shirt_sizes = [];
        $scope.selectedCourses = [];
        $scope.size = { player: 0, parent: 0 };
        $scope.edit_parent = {
            value: false,
            title: 'Edit information',
            phone: { disabled: true },
            email: { disabled: true },
            surname: { disabled: true },
            other_name: { disabled: true },
        };
        $scope.create_user = { value: false, title: 'Create User' };
        var shipping_source = {
            type: 'home',
            address: '',
            locker_id: '',
            locker_address: '',
            // self_pickup: false,
            show_self_pickup: false,
        };
        $scope.shipping = JSON.parse(JSON.stringify(shipping_source));
        $scope.area_chi = [];
        $scope.sub_district = [];
        $scope.locker_brand = [];
        $scope.all_locker = [];

        var locker_source = {
            area_chi: '',
            subdistricts_chi: '',
            locker_brand: '',
        };
        $scope.locker_selected = JSON.parse(JSON.stringify(locker_source));

        // Initialize the Course Selector
        var prototypeCourse = {
            normal: {
                title: 'Training class',
                type: 'normal',
                course: [],
                disabled: false,
                class_selected: { id: '', code: '' },
                sub_title: ['Boys 男子: 4-13 歲', 'Girls 女子: 4-15 歲'],
            },
            parent: {
                title: 'Parent class',
                type: 'parent',
                course: [],
                disabled: false,
                class_selected: { id: '', code: '' },
                sub_title: ['Trainee 學員 3-7 歲', 'Parent 家長: 18-65 歲'],
                input: [
                    {
                        title: 'Parent Name',
                        name: 'parent_name',
                        type: 'text',
                        placeholder: 'Parent Name',
                        value: '',
                        required: true,
                        pattern: $scope.regexName,
                        validate: 'name',
                        error_message: '',
                    },
                    {
                        title: 'Parent Dob',
                        name: 'parent_dob',
                        type: 'date',
                        placeholder: 'Parent Dob',
                        value: '',
                        required: true,
                        pattern: '',
                        validate: 'dob',
                        error_message: '',
                        max: moment(
                            new Date(
                                new Date().getFullYear(),
                                11,
                                31
                            ).setFullYear(new Date().getFullYear() - 18)
                        ).format('YYYY-MM-DD'),
                        min: moment(
                            new Date(
                                new Date().getFullYear(),
                                0,
                                1
                            ).setFullYear(new Date().getFullYear() - 65)
                        ).format('YYYY-MM-DD'),
                    },
                    {
                        title: 'Parent HKID',
                        name: 'parent_hkid',
                        type: 'text',
                        placeholder: 'Parent HKID',
                        value: '',
                        required: true,
                        pattern: $scope.regexHKID,
                        validate: 'hkid_no',
                        error_message: '',
                    },
                ],
            },
            goalkeeper: {
                title: 'Goalkeeper class',
                type: 'goalkeeper',
                course: [],
                disabled: false,
                class_selected: { id: '', code: '' },
                sub_title: ['Boys 男子: 10-13 歲', 'Girls 女子: 13-15 歲'],
            },
        };
        $scope.checkboxCourse = deep(prototypeCourse);
        console.log($scope.checkboxCourse);
        $scope.toggleCourse = function (course, courseInfor) {
            var index = $scope.selectedCourses.indexOf(course);
            if (index == -1) {
                $scope.selectedCourses.push(course);
                console.log(course);
                if (course == 'parent') {
                    turnOnValidateParentCourse(courseInfor);
                } else {
                    if ($scope.selectedCourses.indexOf('parent') == -1) {
                        turnOffValidateParentCourse(courseInfor);
                    }
                }
            } else {
                $scope.selectedCourses.splice(index, 1);
                $scope.checkboxCourse[course].class_selected = {
                    id: '',
                    code: '',
                };
                if (course == 'parent') {
                    turnOffValidateParentCourse(courseInfor);
                }
            }
        };

        function turnOffValidateParentCourse(courseInfor) {
            console.log('turn Off');
            for (i in $scope.checkboxCourse.parent.input) {
                let input = $scope.checkboxCourse.parent.input[i];
                courseInfor[input.name].$setViewValue('');
                courseInfor[input.name].$render();
            }
        }
        function turnOnValidateParentCourse(courseInfor) {
            console.log('turn On');
            courseInfor.$setPristine();
        }

        // Initialize the Error Message
        var current_Messages = {
            name: [
                { type: 'required', message: 'Name is required' },
                { type: 'pattern', message: 'Please enter a valid Name' },
            ],
            email: [
                { type: 'required', message: 'Email is required' },
                { type: 'email', message: 'Email is invalid' },
                { type: 'pattern', message: 'Please enter a valid Email' },
            ],
            surname: [
                { type: 'required', message: 'Please enter a surname' },
                {
                    type: 'pattern',
                    message: 'Please enter a valid English surname',
                },
                {
                    type: 'maxlength',
                    message: 'Surname must be less than 50 characters',
                },
            ],
            other_name: [
                { type: 'required', message: 'Please enter a other name' },
                {
                    type: 'pattern',
                    message: 'Please enter a valid English other name',
                },
                {
                    type: 'maxlength',
                    message: 'Other name must be less than 50 characters',
                },
            ],
            chinese_name: [
                { type: 'required', message: 'Please enter a chinese name' },
                {
                    type: 'pattern',
                    message: 'Please enter a valid Chinese name',
                },
                {
                    type: 'maxlength',
                    message: 'Chinese name must be less than 100 characters',
                },
            ],
            gender: [{ type: 'required', message: 'Please select gender' }],
            dob: [
                { type: 'required', message: 'Please select date of birth' },
                { type: 'date', message: 'Please enter a valid date of birth' },
                { type: 'max', message: 'This person not enough age' },
                { type: 'min', message: 'This person is over the age' },
            ],
            phone: [
                { type: 'required', message: 'Please enter a mobile number' },
                { type: 'pattern', message: 'Mobile number must be 8 digits' },
            ],
            hkid_no: [
                { type: 'required', message: 'Please enter a HKID No' },
                {
                    type: 'pattern',
                    message: 'Please enter a valid HKID! e.g.A1234567',
                },
            ],
        };
        $scope.enMsg = JSON.parse(JSON.stringify(current_Messages));

        //End

        $scope.types = types;
        var event_id = $routeParams.id;
        $scope.event_id = $routeParams.id;

        $scope.loadingElement = $('#loading');
        $scope.loadingElement.hide();

        /** Get Event Details
         * @param event_id
         */
        jQuery.ajax({
            type: 'POST',
            url: SERVER_PATH + 'event/getEventInfo',
            headers: {
                'x-user-id': $rootScope.user_id,
                'x-user-email': $rootScope.user_name,
            },
            async: false,
            data: {
                event_id: event_id,
            },
            dataType: 'json',
            complete: function (response) {
                var jsonData = JSON.parse(response.responseText);
                var event = jsonData.info;
                event_name = event.name;
                event_type = event.type;
            },
        });
        $rootScope.event_name = event_name;
        $rootScope.event_type = event_type;

        /**GET Summer scheme registration table
      /**
       * @param registration_id
      summer_scheme_table */

        function getModalGroupTableHtml(registration_id) {
            var str =
                '' +
                '<div id="group_table" class="main-box-body clearfix">' +
                '<div class="table-responsive">' +
                '<table id="tblGroups_' +
                registration_id +
                '" class="table table-striped table-bordered table-hover" cellspacing="0" width="100%">' +
                '<thead>' +
                '<tr>' +
                '<th>Class code</th>' +
                '<th>Venue Name</th>' +
                '<th>Train Type</th>' +
                '<th>Status</th>' +
                '</tr>' +
                '</thead>' +
                '</table>' +
                '</div>' +
                '</div>' +
                '';
            return str;
        }

        function getCourseTable(player_id) {
            return (
                '<div id="course_player" class="main-box-body clearfix">' +
                '<div class="table-responsive">' +
                '<table id="tblCoursePlayer_' +
                player_id +
                '" class="table table-striped table-bordered table-hover" cellspacing="0" width="100%">' +
                '<thead>' +
                '<tr>' +
                '<th>Course id</th>' +
                '<th>Type</th>' +
                '</tr>' +
                '</thead>' +
                '</table>' +
                '</div>' +
                '</div>'
            );
        }

        function initWaitlistTable(registration_id, train_type, dialogRef) {
            group_table = $('#tblGroups_' + registration_id).DataTable({
                dom: '<"row"B>rt<"row"i>',
                stateSave: true,
                deferRender: true,
                ajax: {
                    url: SERVER_PATH + 'summer-scheme/getCoursesWaitlist',
                    type: 'POST',
                    headers: {
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name,
                    },
                    data: {
                        registration_id: registration_id,
                        type: train_type,
                    },
                    dataType: 'json',
                    complete: function (response) {},
                    error: function (xhr, status, error) {},
                },
                language: {
                    info: 'Showing _TOTAL_ total groups',
                    infoEmpty: 'Showing 0 groups',
                    lengthMenu: 'Show _MENU_ groups',

                    paginate: {
                        previous: '<i class="fa fa-chevron-left"></i>',
                        next: '<i class="fa fa-chevron-right"></i>',
                    },
                },

                columns: [
                    { data: 'class_code' },
                    { data: 'venue_name' },
                    { data: 'train_type' },
                    {
                        data: 'status',
                        className: 'center',
                        render: function (data, type, full, meta) {
                            switch (data) {
                                case APPROVAL_STATUS_Approve:
                                    return (
                                        '<span class="label label-success">' +
                                        data +
                                        '</span>'
                                    );
                                case APPROVAL_STATUS_Waiting:
                                    return (
                                        '<span class="label label-warning">' +
                                        data +
                                        '</span>'
                                    );

                                default:
                                    return data;
                            }
                        },
                    },
                ],
                select: {
                    style: 'single',
                    // selector: 'td:first-child',
                },
                order: [
                    [1, 'desc'],
                    [0, 'desc'],
                ],
                displayLength: -1,
                buttons: [
                    {
                        text: 'Approve from waitlist',
                        extend: 'selectedSingle',
                        action: function () {
                            var table_selected = group_table
                                .rows({ selected: true })
                                .data();

                            var first_row = table_selected[0];

                            jQuery.ajax({
                                type: 'POST',
                                url:
                                    SERVER_PATH +
                                    'summer-scheme/approveCoursesFromWaiting',
                                async: false,
                                headers: {
                                    'x-user-id': $rootScope.user_id,
                                    'x-user-email': $rootScope.user_name,
                                },
                                data: {
                                    rcourse_id: first_row['id'],
                                    train_type: train_type,
                                },
                                dataType: 'json',
                                complete: function (response) {
                                    var jsonData = JSON.parse(
                                        response.responseText
                                    );

                                    console.log(jsonData);

                                    if (jsonData.status == 'OK') {
                                        BootstrapDialog.show({
                                            title: 'SUCCESS',
                                            type: BootstrapDialog.TYPE_SUCCESS,
                                            message: jsonData.message,
                                        });
                                        dialogRef.close();
                                    } else {
                                        BootstrapDialog.show({
                                            title: 'Error',
                                            type: BootstrapDialog.TYPE_DANGER,
                                            message: jsonData.message,
                                        });
                                        dialogRef.close();
                                    }
                                },
                            });
                        },
                    },
                ],
            });
        }

        function removeDublicateValue(data) {
            var class_code = data;
            if (class_code == null) {
                return null;
            }

            var class_code_arr = class_code.split(',');
            // filter dublicate data and remove

            if (class_code_arr.length > 1) {
                for (var i = 0; i < class_code_arr.length; i++) {
                    let data1 = class_code_arr[i].trim();
                    // check if data dublicate
                    for (var j = i + 1; j < class_code_arr.length; j++) {
                        let data2 = class_code_arr[j].trim();
                        if (data1 === data2) {
                            class_code_arr.splice(j, 1);
                            j--;
                        }
                    }
                }

                // implode data
                class_code = class_code_arr.join(', ');
            }
            return class_code;
        }

        $scope.init = function () {
            getAllAreaChi();

            setTimeout(() => {
                // check datatable is exist
                if (
                    $.fn.DataTable.isDataTable(
                        '#summer_scheme_table_' + event_id
                    )
                ) {
                    $scope.tableCourse.destroy();
                }

                $scope.tableCourse = $(
                    '#summer_scheme_table_' + event_id
                ).DataTable({
                    dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
                    // stateSave: true,
                    deferRender: true,
                    processing: true,
                    serverSide: true,
                    ajax: {
                        url: SERVER_PATH + 'summer-scheme/getSummerScheme',
                        type: 'POST',
                        headers: {
                            'x-user-id': $rootScope.user_id,
                            'x-user-email': $rootScope.user_name,
                        },
                        data: {
                            event_id: event_id,
                        },

                        dataType: 'json',
                        complete: function (response) {
                            console.log('response complete', response);
                        },
                        error: function (xhr, status, error) {},
                    },
                    language: {
                        paginate: {
                            previous: '<i class="fa fa-chevron-left"></i>',
                            next: '<i class="fa fa-chevron-right"></i>',
                        },
                    },
                    columns: [
                        {
                            data: 'player_name',
                        },
                        {
                            data: 'players.chinese_name',
                        },
                        // { data: 'players.hkid_no', visible: false },
                        { data: 'players.dob' },
                        { data: 'players.gender' },
                        { data: 'parens.phone' },
                        { data: 'parens.email' },
                        {
                            data: 'courses.train_type',
                            visible: false,
                            render: function (data, type, row, meta) {
                                return removeDublicateValue(data);
                            },
                        },
                        {
                            data: 'groups.name',
                            visible: false,
                            render: function (data, type, row, meta) {
                                return removeDublicateValue(data);
                            },
                        },
                        {
                            data: 'courses.class_code',
                            render: function (data, type, row, meta) {
                                if (data == null) {
                                    return '';
                                }
                                if (
                                    data != '' &&
                                    (row.courses.train_type.includes(
                                        'parent'
                                    ) ||
                                        row.courses.train_type.includes(
                                            'mother'
                                        ))
                                ) {
                                    return (
                                        '<a class="open_parent_info">' +
                                        data +
                                        '</a>'
                                    );
                                }
                                return data;
                            },
                        },
                        {
                            data: 'rcourse.status',
                            className: 'center',
                            render: function (data, type, full, meta) {
                                switch (data) {
                                    case APPROVAL_STATUS_Approve:
                                        return (
                                            '<span class="label label-success">' +
                                            data +
                                            '</span>'
                                        );
                                    case APPROVAL_STATUS_Waiting:
                                        return (
                                            '<span class="label label-warning">' +
                                            data +
                                            '</span>'
                                        );
                                    case APPROVAL_STATUS_Canceled:
                                        return (
                                            '<span class="label label-danger">' +
                                            data +
                                            '</span> <br><br> <a class="open_cancel_info">Details</a>'
                                        );
                                    default:
                                        return data;
                                }
                            },
                        },
                        // {
                        //     data: null,
                        //     visible: false,
                        //     render: function (data, type, row, meta) {
                        //         $content = '';
                        //         row.product_details.forEach((item) => {
                        //             $content +=
                        //                 '<p>' +
                        //                 item.quantity +
                        //                 ' ' +
                        //                 item.type +
                        //                 ' (' +
                        //                 item.name +
                        //                 ')</p>';
                        //         });
                        //         return $content;
                        //     },
                        // },
                        { data: 'registrations.registered_date' },
                        { data: 'invoices.invoice_number' },
                        {
                            data: 'invoices.status',
                            render: function (data, type, row, meta) {
                                switch (data) {
                                    case STATUS_SUCCEEDED:
                                        return (
                                            '<span class="badge badge-success">' +
                                            data +
                                            '</span>'
                                        );
                                        break;
                                    case STATUS_PROCESSING:
                                        return (
                                            '<span class="badge badge-warning">' +
                                            data +
                                            '</span>'
                                        );
                                        break;
                                    case STATUS_REQUEST_REFUND:
                                        return (
                                            '<span class="badge badge-danger">' +
                                            data +
                                            '</span>'
                                        );
                                        break;
                                    case STATUS_REFUND:
                                        return (
                                            '<span class="badge badge-danger">' +
                                            data +
                                            '</span>'
                                        );
                                        break;
                                    case STATUS_PARTIAL_REFUND:
                                        return (
                                            '<span class="badge badge-info">' +
                                            data +
                                            '</span>'
                                        );
                                        break;
                                    default:
                                        return (
                                            '<span class="badge badge-light">' +
                                            data +
                                            '</span>'
                                        );
                                        break;
                                }
                            },
                        },
                        {
                            data: 'registrations.registration_from',
                            visible: false,
                        },
                        { data: 'shipping.shipping_type' },
                        {
                            data: 'registrations.emailed',
                            render: function (data, type, row, meta) {
                                // show badge
                                if (data == 0) {
                                    return `Didn't send`;
                                } else if (data == 1 || data == 2) {
                                    return `Can't send(${data})`;
                                } else if (data == 'Done') {
                                    return `Sent`;
                                }
                            },
                        },
                    ],
                    initComplete: function (settings, json) {
                        console.log('init complete');
                        // get response data from ajax
                        // var data = this.api().ajax.json().data;

                        // build the select list
                        var type_column = {
                            orderColumn: 6,
                            elementId: 'type-content',
                            options: json.options['courses.train_type'],
                            selectId: 'selType',
                        };
                        var group_column = {
                            orderColumn: 7,
                            elementId: 'group-content',
                            options: json.options['groups.name'],
                            selectId: 'selGroup',
                        };
                        var course_column = {
                            orderColumn: 8,
                            elementId: 'course-content',
                            options: json.options['courses.class_code'],
                            selectId: 'selCourse',
                        };
                        var shipping_type = {
                            orderColumn: 14,
                            elementId: 'shipping-content',
                            options: json.options['shipping.shipping_type'],
                            selectId: 'selShipping',
                        };
                        var status_type = {
                            orderColumn: 9,
                            elementId: 'status-content',
                            options: json.options['rcourse.status'],
                            selectId: 'selStatus',
                            defaultOption: 'Approved',
                        };
                        var payment_column = {
                            orderColumn: 12,
                            elementId: 'payment-content',
                            options: json.options['invoices.status'],
                            selectId: 'selPayment',
                        };

                        filterColumns = [
                            type_column,
                            group_column,
                            course_column,
                            shipping_type,
                            status_type,
                            payment_column,
                        ];

                        filterColumns.forEach((item) => {
                            this.api()
                                .columns(item.orderColumn)
                                .every(function () {
                                    var column = this;
                                    var select = $(
                                        `<select id="${item.orderColumn}" class="form-control"><option value="">All</option></select>`
                                    )
                                        .appendTo($(`#${item.elementId}`))
                                        .on('change', function () {
                                            if ($(this).val()) {
                                                var val =
                                                    $.fn.dataTable.util.escapeRegex(
                                                        $(this).val()
                                                    );
                                                column
                                                    .search(
                                                        val ? val : '',
                                                        true,
                                                        false
                                                    )
                                                    .draw();
                                            } else {
                                                column
                                                    .search('', true, false)
                                                    .draw();
                                            }
                                        })
                                        .select2();

                                    item.options.forEach(function (d) {
                                        select.append(
                                            `<option value="${d.value}">${d.label}</option>`
                                        );
                                    });

                                    // // set default option
                                    // if (
                                    //     item.defaultOption &&
                                    //     $scope.tableCourse.data().length > 0
                                    // ) {
                                    //     select.val(item.defaultOption).change();
                                    // }
                                });
                        });
                    },
                    select: {
                        style: 'single',
                        selector: 'td:not(:last-child)',
                    },
                    order: [[12, 'desc']],
                    lengthMenu: [
                        [10, 25, 50, 100, 1000],
                        [10, 25, 50, 100, 1000],
                    ],
                    displayLength: 10,
                    buttons: [
                        {
                            extend: 'collection',
                            text: 'Actions <span class="caret"></span>',
                            className: 'btn btn-primary',
                            dataToggle: 'dropdown',
                            autoClose: true,
                            buttons: [
                                // {
                                //     text: '<i class="fa fa-plus"></i> Create Offline Registration',
                                //     // extend: 'selectedSingle',
                                //     action: function () {
                                //         $rootScope.$evalAsync(function () {
                                //             $location.path(
                                //                 '/registrations/registration-summer-scheme/offline-registration/' +
                                //                     event_id
                                //             );
                                //         });
                                //     },
                                // },
                                {
                                    text: '<i class="fa fa-envelope"></i> Resend confirmation email',
                                    extend: 'selectedSingle',
                                    action: function () {
                                        var rows_selected = $scope.tableCourse
                                            .rows({ selected: true })
                                            .data()[0];

                                        if (
                                            rows_selected.invoices.status ==
                                                STATUS_SUCCEEDED ||
                                            rows_selected.invoices.status ==
                                                STATUS_OFFLINE_REGISTRATION
                                        ) {
                                            $scope.loadingElement.show();

                                            let registration_id =
                                                rows_selected.registrations.id;
                                            if (
                                                rows_selected.registrations.approval_status.toLowerCase() ==
                                                APPROVAL_STATUS_Canceled
                                            ) {
                                                BootstrapDialog.show({
                                                    title: 'WARNING',
                                                    type: BootstrapDialog.TYPE_WARNING,
                                                    message:
                                                        'This player is canceled, cannot resend email',
                                                });
                                                return;
                                            } else {
                                                jQuery.ajax({
                                                    type: 'POST',
                                                    url:
                                                        SERVER_PATH +
                                                        'summer-scheme/resendRegistrationEmail',
                                                    async: true,
                                                    headers: {
                                                        'x-user-id':
                                                            $rootScope.user_id,
                                                        'x-user-email':
                                                            $rootScope.user_name,
                                                    },
                                                    data: {
                                                        registration_id:
                                                            registration_id,
                                                    },
                                                    dataType: 'json',
                                                    complete: function (
                                                        response
                                                    ) {
                                                        $scope.loadingElement.hide();

                                                        var jsonData =
                                                            JSON.parse(
                                                                response.responseText
                                                            );
                                                        if (
                                                            jsonData.status ==
                                                            'OK'
                                                        ) {
                                                            BootstrapDialog.show(
                                                                {
                                                                    size: BootstrapDialog.SIZE_WIDE,
                                                                    type: BootstrapDialog.TYPE_DANGER,
                                                                    title: 'Send confirmation email',
                                                                    message:
                                                                        jsonData.message,
                                                                }
                                                            );
                                                            // reload table
                                                            $scope.tableCourse.ajax.reload();
                                                        } else {
                                                            BootstrapDialog.show(
                                                                {
                                                                    size: BootstrapDialog.SIZE_WIDE,
                                                                    type: BootstrapDialog.TYPE_DANGER,
                                                                    title: 'Send confirmation email',
                                                                    message:
                                                                        jsonData.message,
                                                                }
                                                            );
                                                        }
                                                    },
                                                });
                                            }
                                        } else {
                                            BootstrapDialog.show({
                                                title: 'WARNING',
                                                type: BootstrapDialog.TYPE_WARNING,
                                                message:
                                                    'This player has not paid yet',
                                            });
                                            return;
                                        }
                                    },
                                },
                                {
                                    text: '<i class="fa fa-exchange"></i> Change Course',
                                    extend: 'selectedSingle',
                                    action: function () {
                                        var rows_selected = $scope.tableCourse
                                            .rows({ selected: true })
                                            .data()[0];
                                        var player_id =
                                            rows_selected.players.id;

                                        var registration_id =
                                            rows_selected.registrations.id;

                                        var msg = getCourseTable(player_id);
                                        if (
                                            rows_selected.invoices.status ==
                                            STATUS_PROCESSING
                                        ) {
                                            BootstrapDialog.show({
                                                title: 'WARNING',
                                                type: BootstrapDialog.TYPE_WARNING,
                                                message:
                                                    'This player is still processing',
                                            });
                                            return;
                                        } else if (
                                            rows_selected.registrations.approval_status.toLowerCase() ==
                                            APPROVAL_STATUS_Approve.toLowerCase()
                                        ) {
                                            // Show dialog
                                            BootstrapDialog.show({
                                                title: 'List of courses',
                                                message: msg,
                                                onshown: function (dialogRef) {
                                                    initChangeCourse(
                                                        player_id,
                                                        registration_id
                                                    );
                                                },
                                                onhide: function (dialogRef) {
                                                    $scope.tableCourse.ajax.reload();
                                                },
                                            });
                                        } else {
                                            BootstrapDialog.show({
                                                title: 'WARNING',
                                                type: BootstrapDialog.TYPE_WARNING,
                                                message:
                                                    'This player is not approved yet',
                                            });
                                            return;
                                        }
                                    },
                                },
                                {
                                    text: '<i class="fa fa-times"></i> Cancel Registration',
                                    extend: 'selectedSingle',
                                    action: function () {
                                        var rows_selected = $scope.tableCourse
                                            .rows({ selected: true })
                                            .data()[0];
                                        var registration_id =
                                            rows_selected.registrations.id;
                                        console.warn(rows_selected);
                                        if (
                                            rows_selected.registrations.approval_status
                                                .toLowerCase()
                                                .includes(
                                                    APPROVAL_STATUS_Canceled.toLowerCase()
                                                )
                                        ) {
                                            BootstrapDialog.show({
                                                title: 'WARNING',
                                                type: BootstrapDialog.TYPE_WARNING,
                                                message:
                                                    'This player is already canceled',
                                            });
                                            return;
                                        } else if (
                                            rows_selected.invoices.status.toLowerCase() ==
                                            STATUS_PROCESSING.toLowerCase()
                                        ) {
                                            BootstrapDialog.show({
                                                title: 'WARNING',
                                                type: BootstrapDialog.TYPE_WARNING,
                                                message:
                                                    'This registration is still processing',
                                            });
                                            return;
                                        } else {
                                            cancelRegistration(
                                                registration_id,
                                                rows_selected
                                            );
                                        }
                                    },
                                },
                            ],
                        },
                        // {
                        //     text: 'Approve from waitlist ',
                        //     extend: "selectedSingle",
                        //     action: function () {
                        //         var rows_selected = tableCourse.rows({ selected: true }).data();
                        //         let registration_id = rows_selected[0].registrations.id;
                        //         let train_type = rows_selected[0].courses.train_type;
                        //         let approval_status = rows_selected[0].rcourse.status;

                        //         if (approval_status != APPROVAL_STATUS_Waiting) {
                        //             BootstrapDialog.show({
                        //                 title: 'WARNING',
                        //                 type: BootstrapDialog.TYPE_WARNING,
                        //                 message: 'This player is not in waitlist'
                        //             });
                        //             return;
                        //         }

                        //         var msg = getModalGroupTableHtml(registration_id);
                        //         console.log(registration_id);

                        //         BootstrapDialog.show({
                        //             title: 'Waiting list',
                        //             message: msg,
                        //             onshown: function (dialogRef) {
                        //                 initWaitlistTable(registration_id, train_type, dialogRef);
                        //             },
                        //             onhide: function (dialogRef) {
                        //                 tableCourse.ajax.reload();
                        //             },
                        //         });
                        //     },

                        // },
                        {
                            extend: 'excel',
                            name: 'excel',
                            text: '<i class="fa fa-file-excel-o"></i> Export to Excel',
                            titleAttr: 'Export data to an Excel file',
                            filename:
                                'Registrations - ' +
                                $rootScope.event_type +
                                ' ' +
                                $rootScope.event_name,
                            title:
                                'Registrations - ' +
                                $rootScope.event_type +
                                ' ' +
                                $rootScope.event_name,
                            exportOptions: {
                                columns: ':visible',
                                modifier: {
                                    autoFilter: true,
                                    // selected: true
                                },
                            },
                            action: exportAllToExcel,
                        },
                        {
                            extend: 'colvis',
                            text: 'Columns',
                        },
                    ],
                    columnDefs: [
                        {
                            type: 'justNum',
                            targets: 2,
                        },
                    ],
                });

                $('#summer_scheme_table_' + event_id).on(
                    'click',
                    'tbody td a.open_parent_info',
                    function (e) {
                        var $row = $(this).closest('tr');
                        // Get row data
                        var data = $scope.tableCourse.row($row).data();
                        var msg = 'Email: ' + data.registrations.id;

                        //Get parent row data
                        jQuery.ajax({
                            type: 'POST',
                            url: SERVER_PATH + 'summer-scheme/getParentByRegID',
                            async: false,
                            headers: {
                                'x-user-id': $rootScope.user_id,
                                'x-user-email': $rootScope.user_name,
                            },
                            data: {
                                registration_id: data.registrations.id,
                            },
                            dataType: 'json',
                            complete: function (response) {
                                var jsonData = JSON.parse(
                                    response.responseText
                                );
                                console.log(jsonData);
                                if (jsonData.status == 'OK') {
                                    let parents = jsonData.info;
                                    let parents_info = '';
                                    parents.forEach((parent, index) => {
                                        parents_info += `<h4><strong>Parent ${
                                            index + 1
                                        }: ${parent.parent_name}</strong></h4>`;
                                        parents_info += `DOB: ${new Date(
                                            parent.parent_dob
                                        ).toLocaleDateString('zh-HK')}<br>`;
                                        parents_info += `HKID: ${parent.parent_hkid}<br>`;
                                        parents_info += `<hr>`;
                                    });

                                    BootstrapDialog.show({
                                        title: 'Parent info',
                                        type: BootstrapDialog.TYPE_INFO,
                                        message: parents_info,
                                    });
                                } else {
                                    BootstrapDialog.show({
                                        title: 'Error',
                                        type: BootstrapDialog.TYPE_DANGER,
                                        message: jsonData.message,
                                    });
                                }
                            },
                        });

                        // Show dialog
                        //   BootstrapDialog.show({
                        //     title: 'Parent Info',
                        //     message: msg,
                        //   });
                    }
                );

                $('#summer_scheme_table_' + event_id).on(
                    'click',
                    'tbody td a.open_cancel_info',
                    function (e) {
                        var $row = $(this).closest('tr');
                        // Get row data
                        var data = $scope.tableCourse.row($row).data();
                        getcancelRegistrationByID(data.registrations.id);
                    }
                );
            }, 500);
        };

        function onlyUnique(value, index, self) {
            return self.indexOf(value) === index;
        }

        $scope.goBack = function () {
            window.history.back();
        };

        $scope.init();

        function initChangeCourse(player_id, registration_id) {
            var editor = new $.fn.dataTable.Editor({
                ajax: {
                    url: SERVER_PATH + 'summer-scheme/changeCourse',
                    type: 'POST',
                    headers: {
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name,
                    },
                    data: {
                        player_id: player_id,
                        registration_id: registration_id,
                        user_id: $rootScope.user_id,
                        event_id: event_id,
                    },
                    dataType: 'json',
                    complete: function (response) {
                        var jsonData = JSON.parse(response.responseText);
                        if (jsonData.status == 'OK') {
                            Swal.fire({
                                icon: 'success',
                                title: 'Success',
                                text: jsonData.message,
                            });
                            table.ajax.reload();
                        }
                    },
                    error: function (xhr, status, error) {
                        Swal.fire({
                            icon: 'error',
                            title: 'Oops...',
                            text: error,
                        });
                    },
                },
                table: '#tblCoursePlayer_' + player_id,
                formOptions: {
                    main: {
                        onBlur: 'none',
                    },
                },
                fields: [
                    {
                        label: 'Course',
                        name: 'courses.id',
                        type: 'select2',
                        opts: {
                            placeholder: 'Select a course',
                        },
                    },
                ],
                i18n: {
                    edit: {
                        button: 'Change Course',
                        title: 'Change Course',
                        submit: 'Save',
                    },
                },
            });

            table = $('#tblCoursePlayer_' + player_id).DataTable({
                dom: '<"row"B>rt<"row"i>',
                stateSave: true,
                deferRender: true,
                ajax: {
                    url:
                        SERVER_PATH +
                        'summer-scheme/getAllCoursesInRegistration',
                    type: 'POST',
                    headers: {
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name,
                    },
                    data: {
                        registration_id: registration_id,
                    },
                    dataType: 'json',
                    complete: function (response) {},
                    error: function (xhr, status, error) {},
                },
                language: {
                    info: 'Showing _TOTAL_ total courses',
                    infoEmpty: 'Showing 0 courses',
                    lengthMenu: 'Show _MENU_ courses',

                    paginate: {
                        previous: '<i class="fa fa-chevron-left"></i>',
                        next: '<i class="fa fa-chevron-right"></i>',
                    },
                },
                columns: [
                    { data: 'courses.class_code' },
                    { data: 'courses.train_type' },
                ],
                select: {
                    style: 'single',
                },
                order: [[0, 'asc']],
                displayLength: -1,
                buttons: [
                    {
                        extend: 'edit',
                        editor: editor,
                    },
                ],
            });

            editor.on('preSubmit', function (e, data, action) {
                var table_selected = table.rows({ selected: true }).data();

                var first_row = table_selected[0];

                data.course_id_from = first_row.courses.id;
            });
        }

        //----- Cancel Registration-------//
        function cancelRegistration(registration_id, rows_selected) {
            // console.log($('#customForm').html());

            var editor = new $.fn.dataTable.Editor({
                ajax: {
                    type: 'POST',
                    url: SERVER_PATH + 'registration/adminCancelRegistration',
                    // template: '#customForm',
                    headers: {
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name,
                    },
                    data: {
                        registration_id: registration_id,
                    },
                    dataType: 'json',
                    complete: function (response) {
                        submitCancel = false;
                        var jsonData = JSON.parse(response.responseText);
                        console.log(jsonData);
                        if (jsonData.status == 'OK') {
                            BootstrapDialog.show({
                                title: 'Success',
                                type: BootstrapDialog.TYPE_SUCCESS,
                                message: jsonData.message,
                            });
                        } else {
                            if (jsonData.message != null) {
                                BootstrapDialog.show({
                                    title: 'Error',
                                    type: BootstrapDialog.TYPE_DANGER,
                                    message: jsonData.message,
                                });
                            }
                        }
                    },
                    error: function (xhr, status, error) {
                        submitCancel = false;
                        console.log(error);
                    },
                },
                formOptions: {
                    main: {
                        onBlur: 'none',
                    },
                },
                fields: [
                    {
                        label: 'Player Name',
                        name: 'players.name',
                        type: 'readonly',
                        def:
                            rows_selected.players.surname +
                            ' ' +
                            rows_selected.players.other_name,
                    },
                    {
                        label: 'Course',
                        name: 'courses.class_code',
                        type: 'readonly',
                        def: rows_selected.courses.class_code,
                    },
                    {
                        label: 'Reason',
                        name: 'registration_cancellations.reason',
                        type: 'textarea',
                    },
                    {
                        name: 'registration_cancellations.registration_id',
                        type: 'hidden',
                        def: registration_id,
                    },
                    {
                        name: 'registration_cancellations.user_id',
                        type: 'hidden',
                        def: $rootScope.user_id,
                    },
                    {
                        name: 'registration_cancellations.descriptions',
                        type: 'hidden',
                        def: '',
                    },
                ],
            });
            // var template = $('#customForm')
            //     .clone()
            //     .attr('id', 'customForm_' + registration_id);
            //get input have name = refund in template
            // var inputs = template.find('input[name="refund"]');
            // console.log(inputs);
            // inputs.each(function (index, input) {
            //     //get label of input
            //     var label = template.find(
            //         'label[for="' + $(input).attr('id') + '"]'
            //     );
            //     //change label for input
            //     label.attr('for', label.attr('for') + '_' + registration_id);
            //     $(input).attr(
            //         'id',
            //         $(input).attr('id') + '_' + registration_id
            //     );
            // });

            // editor.s.template = template;

            // editor.on('initCreate', function () {
            //     // editor.hide('registration_cancellations.registration_id');
            //     // editor.hide('registration_cancellations.user_id');
            // });
            // editor.on('initSubmit', function () {
            //     // editor.close();
            // });
            editor.on('close', function () {
                if ($scope.tableCourse) $scope.tableCourse.ajax.reload();
            });
            // editor.on('preSubmit', function () {
            //     //get value of input name="refund" in div #customForm

            //     var refund = $(
            //         '#customForm_' +
            //             registration_id +
            //             ' input[name="refund"]:checked'
            //     ).val();
            //     editor.s.ajax.data.refund_type = refund;
            //     // console.log(editor.s.ajax);
            //     // console.log(refund);

            //     //Dialog to confirm cancel registration
            //     if (submitCancel == false) {
            //         BootstrapDialog.show({
            //             title: 'Confirm',
            //             type: BootstrapDialog.TYPE_WARNING,
            //             message:
            //                 'Are you sure you want to cancel this registration?',
            //             buttons: [
            //                 {
            //                     label: 'Yes',
            //                     cssClass: 'btn-warning',
            //                     action: function (dialogItself) {
            //                         dialogItself.close();
            //                         submitCancel = true;
            //                         editor.submit();
            //                     },
            //                 },
            //                 {
            //                     label: 'No',
            //                     cssClass: 'btn-default',
            //                     action: function (dialogItself) {
            //                         dialogItself.close();
            //                         submitCancel = false;
            //                     },
            //                 },
            //             ],
            //         });
            //     }
            //     return submitCancel;
            //     // return confirm('Ask again, are you sure?');
            // });

            editor
                .title('Cancel Registration')
                .buttons({
                    label: 'Submit',
                    fn: function () {
                        this.submit();
                    },
                })
                .create();
        }

        //----- Offline Registration-------//
        $scope.checkEmail = async function (email, fieldset, submit = false) {
            // console.log(fieldset.email);
            if (email && fieldset.email.$valid) {
                return await $http
                    .post(SERVER_PATH + 'summer-scheme/checkEmail', {
                        email: email,
                    })
                    .then(function (response) {
                        // console.log(response);
                        var jsonData = response.data;

                        if (jsonData.status != 'OK') {
                            // console.log(jsonData);
                            if (jsonData.data) {
                                var data = jsonData.data;
                                console.log('email is not available');
                                $scope.edit_parent.value = false;
                                // if (data.type.toLowerCase() == 'parent') {
                                for (field in fieldset) {
                                    if (!field.includes('$')) {
                                        if (!submit) {
                                            $scope.edit_parent[
                                                field
                                            ].disabled = true;
                                            fieldset[field].$setViewValue(
                                                data[field]
                                            );
                                            fieldset[field].$render();
                                            if (
                                                !data[field] ||
                                                fieldset[field].$invalid
                                            ) {
                                                $scope.edit_parent[
                                                    field
                                                ].disabled = false;
                                            }
                                        }
                                    }
                                }
                                $scope.parent = data;
                                return data;
                                // } else {
                                //   console.log('this email from ' + data.type);
                                //   BootstrapDialog.show({
                                //     title: 'Error',
                                //     type: BootstrapDialog.TYPE_DANGER,
                                //     message: 'This email is already registered as ' + data.type,
                                //   });
                                //   return {
                                //     status: -2,
                                //     message: 'This email from ' + data.type,
                                //   };
                                // }
                            } else {
                                $scope.parent = null;
                                console.log('Not found data');

                                return {
                                    status: -1,
                                    message: 'Not found data',
                                };
                            }
                        } else {
                            for (field in fieldset) {
                                if (!field.includes('$')) {
                                    if (fieldset[field].$viewValue == '') {
                                        fieldset[field].$setViewValue('');
                                        fieldset[field].$render();
                                    }
                                }
                            }
                            $scope.edit_parent.value = true;
                            console.log('email is available');
                            return true;
                        }
                    });
            } else if (email == '') {
                for (field in fieldset) {
                    if (!field.includes('$')) {
                        fieldset[field].$setViewValue('');
                        fieldset[field].$render();
                    }
                }
                $scope.edit_parent.value = true;
                return true;
            }
        };

        $scope.submitParent = async function ($event, fieldset) {
            //remove all validation errors in the fieldset
            $scope.all_fieldset[0] = fieldset;
            fieldset.$setPristine();
            $scope.enMsg = JSON.parse(JSON.stringify(current_Messages));
            for (field in fieldset) {
                if (!field.includes('$')) {
                    fieldset[field].$setValidity('editor', true);
                }
            }
            var email = fieldset.email.$modelValue;
            var surname = fieldset.surname.$modelValue;
            var other_name = fieldset.other_name.$modelValue;
            var phone = fieldset.phone.$modelValue;
            var parent_id = 0;
            var action = 'create';
            var data = new FormData();
            var parent_type = 'Parent';
            // fieldset.$setPristine(false);
            // console.log(data);
            if (fieldset.$valid) {
                //send data to server (editor)
                var check_email = await $scope.checkEmail(
                    email,
                    fieldset,
                    true
                );
                // console.log(fieldset);

                // console.log(check_email);
                if (check_email) {
                    if (
                        check_email != true &&
                        check_email.status >= 0 &&
                        check_email.id
                    ) {
                        parent_id = check_email.id;
                        parent_id = 'row_' + parent_id;
                        parent_type = check_email.type;
                        action = 'edit';
                    } else if (check_email != true && check_email.status < 0) {
                        return;
                    }

                    data.append(
                        'data[' + parent_id + '][parens][email]',
                        email
                    );
                    data.append(
                        'data[' + parent_id + '][parens][surname]',
                        surname
                    );
                    data.append(
                        'data[' + parent_id + '][parens][other_name]',
                        other_name
                    );
                    data.append(
                        'data[' + parent_id + '][parens][phone]',
                        phone
                    );
                    data.append(
                        'data[' + parent_id + '][parens][type]',
                        parent_type
                    );
                    data.append('action', action);
                    data.append(
                        'data[' + parent_id + '][create_user]',
                        $scope.create_user.value ? 1 : ''
                    );

                    data.append(
                        'data[' +
                            parent_id +
                            '][parens][phone][nationalNumber]',
                        phone
                    );

                    data.append(
                        'data[' + parent_id + '][parens][phone][isValid]',
                        true
                    );

                    data.append(
                        'data[' + parent_id + '][parens][phone][dialCode]',
                        852
                    );

                    data.append(
                        'data[' + parent_id + '][parens][country_code]',
                        852
                    );

                    data.append(
                        'data[' + parent_id + '][parens][iso_code]',
                        'hk'
                    );

                    data.append(
                        'data[' + parent_id + '][parens][phone][isoCode]',
                        'hk'
                    );

                    data.append(
                        'data[' +
                            parent_id +
                            '][parens][phone][internationalNumber]',
                        '+852' + phone
                    );

                    createParent(data, $event, fieldset, parent_type);
                } else if (check_email.status == 'error') {
                    BootstrapDialog.show({
                        size: BootstrapDialog.SIZE_WIDE,
                        type: BootstrapDialog.TYPE_DANGER,
                        title: 'Error',
                        message: check_email.message,
                    });
                }
            } else {
                fieldset.email.$setDirty(true);
                fieldset.surname.$setDirty(true);
                fieldset.other_name.$setDirty(true);
                fieldset.phone.$setDirty(true);
            }
        };

        $scope.submitPlayer = function ($event, fieldset, courseInfor) {
            //remove all validation errors in the fieldset
            $scope.all_fieldset[1] = fieldset;
            temp_player = null;
            $scope.enMsg = JSON.parse(JSON.stringify(current_Messages));
            for (const e in fieldset) {
                if (!e.includes('$')) {
                    // console.log(e);
                    fieldset[e].$setValidity('editor', true);
                }
            }
            console.log(fieldset.p_gender);
            // console.log(data);

            // console.log(fieldset);
            var surname = fieldset.p_surname.$modelValue;
            var other_name = fieldset.p_other_name.$modelValue;
            var chinese_name = fieldset.p_chinese_name.$modelValue
                ? fieldset.p_chinese_name.$modelValue
                : '';
            var gender = fieldset.p_gender.$modelValue;
            var dob = fieldset.p_dob.$modelValue;
            var hkid_no = fieldset.p_hkid_no.$modelValue;
            var parent_email = '';
            var player_id = 0;
            var action = 'create';
            var data = new FormData();
            var dataCheck = new FormData();
            //format dob to dd-MMM-yyyy by momentjs
            var dob_2_check = moment(dob).format('YYYY-MM-DD');
            dob = moment(dob).format('DD-MMM-YYYY');

            console.log(dob);

            if (fieldset.$valid) {
                if ($scope.parent != null) {
                    parent_email = $scope.parent.email;
                    parent_id = $scope.parent.id;
                }
                //send data to server (editor)
                if (parent_email != '') {
                    dataCheck.append('surname', surname);
                    dataCheck.append('other_name', other_name);
                    dataCheck.append('hkid_no', hkid_no);
                    dataCheck.append('parent_id', parent_id);
                    dataCheck.append('dob', dob_2_check);

                    $http({
                        url: SERVER_PATH + 'player/checkPlayerExist',
                        method: 'POST',
                        data: dataCheck,
                        //assign content-type as undefined, the browser
                        //will assign the correct boundary for us
                        headers: {
                            'Content-Type': undefined,
                            'x-user-id': $rootScope.user_id,
                            'x-user-email': $rootScope.user_name,
                        },
                    }).then(async function (response) {
                        console.log(response);

                        courseInfor.$setPristine();

                        var jsonData = response.data;
                        if (jsonData.status == 'OK') {
                            console.log(jsonData.data);
                            if (jsonData.data) {
                                for (e in fieldset) {
                                    if (!e.includes('$')) {
                                        if (e.includes('dob')) {
                                            fieldset[e].$setViewValue(
                                                jsonData.data.dob.split(' ')[0]
                                            );
                                        } else {
                                            fieldset[e].$setViewValue(
                                                jsonData.data[
                                                    e.replace('p_', '')
                                                ]
                                            );
                                        }
                                        fieldset[e].$render();
                                    }
                                }

                                $scope.nextStep($event, fieldset);
                                player_id = jsonData.data.id;
                                action = 'edit';
                                //save player data
                                $scope.player = jsonData.data;
                            } else {
                                player_id = -1;
                            }
                        }

                        if (player_id == 0) {
                            $scope.player = null;
                            // if (player_id > 0) {
                            //   player_id = 'row_' + player_id;
                            // }
                            data.append('action', action);
                            data.append(
                                'data[' + player_id + '][players][surname]',
                                surname
                            );
                            data.append(
                                'data[' + player_id + '][players][other_name]',
                                other_name
                            );
                            data.append(
                                'data[' +
                                    player_id +
                                    '][players][chinese_name]',
                                chinese_name
                            );

                            data.append(
                                'data[' + player_id + '][players][dob]',
                                dob
                            );
                            data.append(
                                'data[' + player_id + '][players][gender]',
                                gender
                            );
                            data.append(
                                'data[' + player_id + '][players][hkid_no]',
                                hkid_no
                            );
                            data.append(
                                'data[' +
                                    player_id +
                                    '][players][hkid_passport_type]',
                                'HKID'
                            );
                            data.append(
                                'data[' +
                                    player_id +
                                    '][players][passport_expiry_date]',
                                ''
                            );
                            data.append(
                                'data[' + player_id + '][parens][email]',
                                parent_email
                            );
                            temp_player = data;

                            //Check HKID of player
                            var checkHKID = await $scope.checkDulicateHKID(
                                hkid_no
                            );
                            console.log(checkHKID);

                            if (
                                checkHKID.status != 'OK' &&
                                checkHKID.message != ''
                            ) {
                                $scope.enMsg['hkid_no'].push({
                                    type: 'editor',
                                    message: checkHKID.message,
                                });

                                fieldset['p_hkid_no'].$setValidity(
                                    'editor',
                                    false
                                );
                            } else {
                                $scope.nextStep($event, fieldset);
                            }

                            // createPlayer(data, $event, fieldset);
                        } else if (player_id < 0) {
                            BootstrapDialog.show({
                                size: BootstrapDialog.SIZE_WIDE,
                                type: BootstrapDialog.TYPE_DANGER,
                                title: 'Error',
                                message: 'Error creating player',
                            });
                        }
                        $scope.getCourseRegistered();
                        //for in object
                        for (const key in $scope.checkboxCourse) {
                            $scope.checkboxCourse[key].class_selected = {
                                id: '',
                                code: '',
                            };

                            $scope.getCourseForPlayer(
                                $scope.checkboxCourse[key],
                                temp_player
                                    ? temp_player.get(
                                          'data[' +
                                              player_id +
                                              '][players][dob]'
                                      )
                                    : null,
                                temp_player
                                    ? temp_player.get(
                                          'data[' +
                                              player_id +
                                              '][players][gender]'
                                      )
                                    : null
                            );
                        }
                        $scope.selectedCourses = [];
                        $scope.size.parent = 0;
                        $scope.size.player = 0;
                    });
                } else {
                    BootstrapDialog.show({
                        size: BootstrapDialog.SIZE_WIDE,
                        type: BootstrapDialog.TYPE_DANGER,
                        title: 'Error',
                        message: 'No parent selected',
                    });
                }
            } else {
                fieldset.p_surname.$setDirty(true);
                fieldset.p_other_name.$setDirty(true);
                fieldset.p_chinese_name.$setDirty(true);
                fieldset.p_gender.$setDirty(true);
                fieldset.p_dob.$setDirty(true);
                fieldset.p_hkid_no.$setDirty(true);
            }
            $scope.getAllShirts();
        };

        $scope.submitCourse = async function ($event, fieldset, shippingInfor) {
            console.log(fieldset);
            fieldset['parent_hkid'].$setValidity('editor', true);
            $scope.enMsg = JSON.parse(JSON.stringify(current_Messages));
            // console.log($scope.checkboxCourse.parent);
            // console.log($scope.selectedCourses);
            // console.log($scope.selectedCourses[0]);
            // console.log(
            //   $scope.checkboxCourse[$scope.selectedCourses[0]].class_selected
            // );
            $scope.all_fieldset[2] = fieldset;
            fieldset.$setDirty(true);
            let check_size = false;
            console.log($scope.selectedCourses.indexOf('parent'));
            if ($scope.selectedCourses.indexOf('parent') == -1) {
                if ($scope.size.player > 0) {
                    check_size = true;
                } else {
                    check_size = false;
                }
            } else {
                var checkHKID = await $scope.checkDulicateHKID(
                    fieldset.parent_hkid.$viewValue,
                    fieldset.parent_name.$viewValue,
                    fieldset.parent_dob.$viewValue
                );
                if (checkHKID.status != 'OK' && checkHKID.message != '') {
                    $scope.enMsg['hkid_no'].push({
                        type: 'editor',
                        message: checkHKID.message,
                    });

                    fieldset['parent_hkid'].$setValidity('editor', false);

                    // BootstrapDialog.show({
                    //   title: 'Error',
                    //   type: BootstrapDialog.TYPE_DANGER,
                    //   message: checkHKID.message,
                    // });
                }
                console.log(checkHKID);
                if (
                    $scope.size.parent > 0 &&
                    $scope.size.player > 0 &&
                    checkHKID.status == 'OK'
                ) {
                    check_size = true;
                } else {
                    check_size = false;
                }
            }

            console.log(check_size);
            if (fieldset.$valid && check_size) {
                //foreach in array selectedCourses
                var check = $scope.selectedCourses.filter(
                    (element) =>
                        !$scope.checkboxCourse[element].class_selected.id
                );
                console.log('not yet select class: ' + check);
                if (check.length == 0 && $scope.selectedCourses.length > 0) {
                    if (shippingInfor.$error.required) {
                        shippingInfor.$setPristine();
                    }

                    $scope.parent_for_class = fieldset;
                    console.log($scope.parent_for_class);
                    $scope.nextStep($event, fieldset);
                }
            } else {
                fieldset.parent_name.$setDirty(true);
                fieldset.parent_dob.$setDirty(true);
                fieldset.parent_hkid.$setDirty(true);
            }
        };
        $scope.nextStep = function ($event, fieldset) {
            // console.log(fieldset);
            // console.log(fieldset.$valid);
            // fieldset.$setDirty(true);
            if (fieldset.$valid && clicked == false) {
                clicked = true;
                current_fs = $($event.currentTarget).parent();
                // console.log($('#progressbar li').eq($('fieldset').index(next_fs)));
                next_fs = $($event.currentTarget).parent().next();

                //show the next fieldset
                setTimeout(function () {
                    //Add Class Active
                    $('#progressbar li')
                        .eq($('fieldset').index(next_fs))
                        .addClass('active');
                    next_fs.show();
                    //hide the current fieldset with style
                    current_fs.animate(
                        { opacity: 0 },
                        {
                            step: function (now) {
                                // for making fielset appear animation
                                opacity = 1 - now;

                                current_fs.css({
                                    display: 'none',
                                    position: 'relative',
                                });
                                next_fs.css({ opacity: opacity });
                            },
                            duration: 1200,
                        }
                    );
                }, 1000);

                setTimeout(function () {
                    clicked = false;
                }, 1500);
                return true;
            } else {
                return false;
            }
        };

        $scope.reloadForm = function () {
            //set current_fs variable to the first fieldset
            current_fs = $('fieldset:first');
            //set opacity of fieldset to 1
            current_fs.css({ opacity: 1 });
            //show the first fieldset
            current_fs.show();
            //hide the rest of the fieldset
            $('fieldset:not(:first)').hide();
            //reset progressbar
            $('#progressbar li:not(:first)').removeClass('active');
            $('#msform')[0].reset();
            $scope.enMsg = JSON.parse(JSON.stringify(current_Messages));
            $scope.all_fieldset.forEach(function (fieldset) {
                resetForm(fieldset);
            });
            $scope.create_user.value = false;
            temp_player = null;
            $scope.parent = null;
            $scope.player = null;
            $scope.checkboxCourse = deep(prototypeCourse);
            $scope.selectedCourses = [];
            $scope.sub_district = [];
            $scope.locker_brand = [];
            $scope.all_locker = [];
            $scope.shipping = JSON.parse(JSON.stringify(shipping_source));
            $scope.locker_selected = JSON.parse(JSON.stringify(locker_source));
            $scope.parent_for_class = null;
            $scope.edit_parent.value = false;
            //reload $scope.tableCourse
            if ($scope.tableCourse != null) {
                $scope.tableCourse.ajax.reload();
            }
            $scope.size.parent = 0;
            $scope.size.player = 0;
            getSettingsSystem();
        };
        // show modal
        function showmodal() {
            getSettingsSystem();
            $('#myModal').modal({
                backdrop: 'static',
                keyboard: false,
            });
            //on open modal set all input type date to current date
            $('#myModal')
                .on('shown.bs.modal', function () {
                    //
                })
                .on('hidden.bs.modal', function () {
                    $scope.reloadForm(); //reload form
                });

            $('.previous').click(function () {
                current_fs = $(this).parent();
                previous_fs = $(this).parent().prev();

                //Remove class active
                $('#progressbar li')
                    .eq($('fieldset').index(current_fs))
                    .removeClass('active');

                //show the previous fieldset
                previous_fs.show();

                //hide the current fieldset with style
                current_fs.animate(
                    { opacity: 0 },
                    {
                        step: function (now) {
                            // for making fielset appear animation
                            opacity = 1 - now;

                            current_fs.css({
                                display: 'none',
                                position: 'relative',
                            });
                            previous_fs.css({ opacity: opacity });
                        },
                        duration: 600,
                    }
                );
            });

            $('.radio-group .radio').click(function () {
                $(this).parent().find('.radio').removeClass('selected');
                $(this).addClass('selected');
            });

            $('.submit').click(function () {
                return false;
            });
        }

        function resetForm(fieldset) {
            if (fieldset) {
                console.log('FIELD SET: ');
                console.log(fieldset);
                for (const e in fieldset) {
                    if (!e.includes('$')) {
                        // console.log(e);
                        fieldset[e].$setViewValue('');
                        fieldset[e].$setValidity('editor', true);
                    }
                }
                fieldset.$setPristine();
            }
        }

        function createParent(data, $event, fieldset, parent_type) {
            $scope.parent = null;
            var url;
            switch (parent_type) {
                case TYPE_PARENT:
                    url = 'paren/setParens';
                    break;
                case TYPE_SUPER_ADMIN:
                    url = 'paren/setSuperAdmins';
                    break;
                case TYPE_LEAGUE_ADMIN:
                    url = 'paren/setLeagueAdmins';
                    break;
                case TYPE_CLUB_ADMINISTRATOR:
                    url = 'paren/setClubManagers';
                    break;
                case TYPE_COACH:
                    url = 'paren/setCoachs';
                    break;
                default:
                    url = 'paren/setParens';
                    break;
            }

            $http({
                url: SERVER_PATH + url,
                method: 'POST',
                data: data,
                //assign content-type as undefined, the browser
                //will assign the correct boundary for us
                headers: {
                    'Content-Type': undefined,
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name,
                },
            }).then(
                function (response) {
                    // console.log(response);
                    // console.log(response.data.data.length);
                    //check response.data.fieldErrors is exist or not
                    if (response.data.fieldErrors) {
                        var fEr = response.data.fieldErrors;
                        // console.log(fEr);
                        //for each fieldErrors, assign to the correct form field
                        for (var k in fEr) {
                            var field_name = fEr[k].name.split('.')[1];
                            console.log(field_name);
                            //add the error message to the $scope.enMsg[field_name]
                            $scope.enMsg[field_name].push({
                                type: 'editor',
                                message: fEr[k].status,
                            });

                            fieldset[field_name].$setValidity('editor', false);
                            fieldset[field_name].$setDirty(true);
                        }
                    } else if (response.data.data.length > 0) {
                        // console.log(response.data.data);
                        $scope.parent = response.data.data[0].parens;
                        // console.log($scope.parent);
                        $scope.nextStep($event, fieldset);
                    }
                },
                function (response) {
                    // this function handles error
                    console.log(response);
                }
            );
        }

        function createPlayer(data, $event, fieldset) {
            return $http({
                url: SERVER_PATH + 'player/setPlayers',
                method: 'POST',
                data: data,
                headers: {
                    'Content-Type': undefined,
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name,
                },
            }).then(
                function (response) {
                    console.log(response);
                    // console.log(response.data.data.length);

                    //check response.data.fieldErrors is exist or not
                    if (response.data.fieldErrors) {
                        var fEr = response.data.fieldErrors;
                        // console.log(fEr);
                        mess = '';
                        //for each fieldErrors, assign to the correct form field
                        for (var k in fEr) {
                            var field_name = fEr[k].name.split('.')[1];
                            console.log(field_name);
                            mess +=
                                '<p>' +
                                field_name +
                                ': ' +
                                fEr[k].status +
                                '</p>';
                            // if (fEr[k].status) {
                            //   if (
                            //     fEr[k].status.toLowerCase().includes('hkid is duplicated')
                            //   ) {
                            //     console.log('duplicated');
                            //   }
                            // }

                            // //add the error message to the $scope.enMsg[field_name]
                            // $scope.enMsg[field_name].push({
                            //   type: 'editor',
                            //   message: fEr[k].status,
                            // });
                            // console.log($scope.enMsg[field_name]);

                            // fieldset['p_' + field_name].$setValidity('editor', false);
                            // fieldset['p_' + field_name].$setDirty(true);
                            // console.log(fieldset['p_' + field_name]);
                        }
                        BootstrapDialog.show({
                            title: 'Error',
                            type: BootstrapDialog.TYPE_DANGER,
                            message: mess,
                        });
                        return false;
                    } else if (response.data.data.length > 0) {
                        // console.log(response.data.data);
                        $scope.player = response.data.data[0].players;
                        console.log($scope.player);
                        return $scope.player;
                        // $scope.getCourseRegistered();

                        //for in object
                        // for (const key in $scope.checkboxCourse) {
                        //   $scope.checkboxCourse[key].class_selected = { id: '', code: '' };
                        //   $scope.getCourseForPlayer($scope.checkboxCourse[key]);
                        // }
                        // $scope.nextStep($event, fieldset);
                    }
                },
                function (response) {
                    // this function handles error
                    console.log(response);
                }
            );
        }

        $scope.getCourseForPlayer = function (
            course_type,
            dob = null,
            gender = null
        ) {
            if ($scope.player || dob) {
                //format string dob of player to yyyy
                var birth_year = dob
                    ? moment(dob).format('YYYY')
                    : moment($scope.player.dob).format('YYYY');
                console.log('birth_year: ' + birth_year);
                var data = new FormData();
                data.append('event_id', event_id);
                data.append('language', 'en');
                data.append('gender', gender ? gender : $scope.player.gender);
                data.append('birth_year', birth_year);
                data.append('course_type', course_type.type);
                data.append('offline_registration', 'true');
                $http({
                    url:
                        SERVER_PATH +
                        'summer-scheme/getCourseSummerSchemeEvent',
                    method: 'POST',
                    data: data,
                    headers: {
                        'Content-Type': undefined,
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name,
                    },
                }).then(
                    function (response) {
                        // console.log(response.data);
                        course_type.course = response.data.info;
                        // console.log(course_type.course);
                    },
                    function (response) {
                        // this function handles error
                        console.log(response);
                    }
                );
            }
        };

        $scope.getCourseRegistered = function () {
            for (const key in $scope.checkboxCourse) {
                $scope.checkboxCourse[key].disabled = false;
            }
            if ($scope.player != null) {
                var data = new FormData();
                data.append('event_id', event_id);
                data.append('player_id', $scope.player.id);
                $http({
                    url: SERVER_PATH + 'summer-scheme/getTypeCourseRegistered',
                    method: 'POST',
                    data: data,
                    headers: {
                        'Content-Type': undefined,
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name,
                    },
                }).then(
                    function (response) {
                        console.log(response);
                        if (response.data.status == 'OK') {
                            console.log(response.data);
                            if (response.data.info.length > 0) {
                                for (
                                    var i = 0;
                                    i < response.data.info.length;
                                    i++
                                ) {
                                    $scope.checkboxCourse[
                                        response.data.info[i]
                                    ].disabled = true;
                                    console.log(
                                        $scope.checkboxCourse[
                                            response.data.info[i]
                                        ]
                                    );
                                }
                            } else {
                                for (const key in $scope.checkboxCourse) {
                                    $scope.checkboxCourse[key].disabled = false;
                                }
                            }
                        }
                    },
                    function (response) {
                        // this function handles error
                        console.log(response);
                    }
                );
            }
        };

        $scope.getAllShirts = function () {
            var data = new FormData();
            data.append('event_id', event_id);
            $http({
                url: SERVER_PATH + 'summer-scheme/getAllShirts',
                method: 'POST',
                data: data,
                headers: {
                    'Content-Type': undefined,
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name,
                },
            }).then(
                function (response) {
                    console.log(response);
                    if (response.data.status == 'OK') {
                        console.log(response.data);
                        $scope.shirt_sizes = response.data.info.shirt_sizes;
                    }
                },
                function (response) {
                    // this function handles error
                    console.log(response);
                }
            );
        };

        $scope.selectCourse = function (course, type) {
            $scope.checkboxCourse[type].class_selected = {
                id: course.id,
                code: course.class_code,
            };
            console.log($scope.checkboxCourse[type].class_selected);
        };

        function getcancelRegistrationByID(registration_id) {
            var data = new FormData();
            data.append('registration_id', registration_id);
            $http({
                url: SERVER_PATH + 'registration/getcancelRegistrationByID',
                method: 'POST',
                data: data,
                headers: {
                    'Content-Type': undefined,
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name,
                },
            }).then(
                function (response) {
                    console.log(response);
                    if (response.statusText == 'OK') {
                        console.log(response.data.data);
                        let data = response.data.data;
                        if (!data.reason) {
                            data.reason = 'Cancel';
                        }
                        if (!data.descriptions) {
                            data.descriptions = '';
                        } else {
                            //parse to json
                            data.descriptions = JSON.parse(data.descriptions);
                            data.descriptions =
                                data.descriptions.status +
                                ' - ' +
                                data.descriptions.message;
                            console.log(data.descriptions);
                        }

                        let user_name = data.other_name
                            ? data.other_name +
                              ' ' +
                              data.surname +
                              ' (' +
                              data.email +
                              ')'
                            : 'This user is not exist or deleted';

                        BootstrapDialog.show({
                            title: 'Cancel details',
                            type: BootstrapDialog.TYPE_INFO,
                            message:
                                'Canceler: <strong>' +
                                user_name +
                                '</strong><br><br>Reason: <strong>' +
                                data.reason +
                                '</strong><br><br>Cancel at: <strong>' +
                                data.cancel_date +
                                '</strong>',
                        });
                    } else {
                        BootstrapDialog.show({
                            title: 'Error',
                            type: BootstrapDialog.TYPE_DANGER,
                            message: jsonData.message,
                        });
                    }
                },
                function (response) {
                    // this function handles error
                    console.log(response);
                }
            );
        }

        function getSettingsSystem() {
            $http({
                url: SERVER_PATH + 'setting/getSettingsSystem',
                method: 'POST',
                headers: {
                    'Content-Type': undefined,
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name,
                },
            }).then(
                function (response) {
                    console.log(response.data);
                    var data = response.data.data;
                    if (data.length > 0) {
                        console.log(data[4].settings.value);
                        $scope.shipping.show_self_pickup =
                            data[4].settings.value == 'Yes' ? true : false;
                    }
                },
                function (response) {
                    // this function handles error
                    console.log(response);
                }
            );
        }

        //clone the object - deep copy
        function deep(value) {
            if (typeof value !== 'object' || value === null) {
                return value;
            }
            if (Array.isArray(value)) {
                return deepArray(value);
            }
            //check if value is regexp
            if (value instanceof RegExp) {
                return value;
            }
            return deepObject(value);
        }
        function deepObject(source) {
            const result = {};
            Object.keys(source).forEach((key) => {
                const value = source[key];
                result[key] = deep(value);
            }, {});
            return result;
        }
        function deepArray(source) {
            return source.map((value) => deep(value));
        }

        //---- LOCKER ----//
        function getAllAreaChi() {
            $http({
                url: SERVER_PATH + 'shipping/getAllAreaChi',
                method: 'POST',
                headers: {
                    'Content-Type': undefined,
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name,
                },
            }).then(function (response) {
                console.log(response);
                if (response.data.status == 'OK') {
                    console.log(response.data);
                    $scope.area_chi = response.data.info;
                }
            });
        }
        $scope.getAllSubDistrict = function () {
            var data = new FormData();
            // console.log($scope.locker_selected.area_chi);
            if ($scope.locker_selected.area_chi != '') {
                data.append('area_chi', $scope.locker_selected.area_chi);
                $http({
                    url: SERVER_PATH + 'shipping/getAllSubDistrict',
                    method: 'POST',
                    data: data,
                    headers: {
                        'Content-Type': undefined,
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name,
                    },
                }).then(function (response) {
                    console.log(response);
                    if (response.data.status == 'OK') {
                        console.log(response.data);
                        $scope.locker_selected.subdistricts_chi = '';
                        $scope.locker_selected.locker_brand = '';
                        $scope.sub_district = response.data.info;
                        $scope.all_locker = [];
                    }
                });
            }
        };

        $scope.getAllLockerBrand = function () {
            var data = new FormData();
            if (
                $scope.locker_selected.subdistricts_chi != '' &&
                $scope.locker_selected.area_chi != ''
            ) {
                data.append(
                    'subdistricts_chi',
                    $scope.locker_selected.subdistricts_chi
                );
                data.append('area_chi', $scope.locker_selected.area_chi);
                $http({
                    url: SERVER_PATH + 'shipping/getAllLockerBrand',
                    data: data,
                    method: 'POST',
                    headers: {
                        'Content-Type': undefined,
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name,
                    },
                }).then(function (response) {
                    console.log(response);
                    if (response.data.status == 'OK') {
                        console.log(response.data);
                        $scope.locker_selected.locker_brand = '';
                        $scope.locker_brand = response.data.info;
                        $scope.all_locker = [];
                    }
                });
            }
        };

        $scope.getAllLocker = function () {
            var data = new FormData();
            if (
                $scope.locker_selected.subdistricts_chi != '' &&
                $scope.locker_selected.area_chi != '' &&
                $scope.locker_selected.locker_brand != ''
            ) {
                data.append(
                    'subdistricts_chi',
                    $scope.locker_selected.subdistricts_chi
                );
                data.append('area_chi', $scope.locker_selected.area_chi);
                data.append(
                    'locker_brand',
                    $scope.locker_selected.locker_brand
                );
                $http({
                    url: SERVER_PATH + 'shipping/getAllLockerClient',
                    data: data,
                    method: 'POST',
                    headers: {
                        'Content-Type': undefined,
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name,
                    },
                }).then(function (response) {
                    console.log(response);
                    if (response.data.status == 'OK') {
                        console.log(response.data);
                        $scope.all_locker = response.data.info;
                        console.log($scope.all_locker);
                    } else {
                        BootstrapDialog.show({
                            title: 'Error',
                            type: BootstrapDialog.TYPE_DANGER,
                            message: 'No locker found',
                        });
                    }
                });
            }
        };

        $scope.selectLocker = function (locker) {
            $scope.shipping.locker_address = locker.address_chi;
            $scope.shipping.locker_id = locker.id;
        };
        $scope.submitShipping = async function ($event, shippingInfor) {
            // console.log($scope.locker_selected.locker_brand);
            if (shippingInfor.$valid && submitShipping == false) {
                submitShipping = true;
                var res;
                if (temp_player) {
                    res = await createPlayer(temp_player);
                }
                console.log('RES: ');
                console.log(res);
                if (res || $scope.player) {
                    $scope.registerOldPlayerSummerScheme($event, shippingInfor);
                }

                setTimeout(function () {
                    submitShipping = false;
                }, 1500);
            }
        };

        $scope.registerOldPlayerSummerScheme = function ($event, fieldset) {
            console.log('PLAYER:');
            console.log($scope.player);
            var parent_id = '';
            var player_id = '';
            var parent_hkid = '';
            var parent_name = '';
            var parent_dob = '';
            var course_id = 'null';
            var parent_course_id = 'null';
            var goalkeeper_course_id = 'null';
            var selected_shirt = '';
            var parent_selected_shirt = 'null';
            var shipping_type = '';
            var res_address = '';
            var select_locker = 'null';
            var data = new FormData();

            if ($scope.parent.id) {
                parent_id = $scope.parent.id;
            }
            if ($scope.player.id) {
                player_id = $scope.player.id;
            }
            $scope.selectedCourses.forEach((course) => {
                switch (course) {
                    case 'normal':
                        if ($scope.checkboxCourse[course].class_selected.id) {
                            course_id =
                                $scope.checkboxCourse[course].class_selected.id;
                        }
                        break;
                    case 'goalkeeper':
                        if ($scope.checkboxCourse[course].class_selected.id) {
                            goalkeeper_course_id =
                                $scope.checkboxCourse[course].class_selected.id;
                        }
                        break;
                    case 'parent':
                        if ($scope.checkboxCourse[course].class_selected.id) {
                            parent_course_id =
                                $scope.checkboxCourse[course].class_selected.id;
                        }
                        break;
                }
            });
            if ($scope.size.player != 0) {
                selected_shirt = $scope.size.player;
            }
            if ($scope.size.parent != 0) {
                parent_selected_shirt = $scope.size.parent;
            }

            var check_address = false;
            switch ($scope.shipping.type) {
                case 'home':
                    shipping_type = 'home';
                    if ($scope.shipping.address != '') {
                        res_address = $scope.shipping.address;
                        $scope.shipping.locker_id = '';
                        check_address = true;
                    }
                    break;
                case 'locker':
                    shipping_type = 'locker';
                    if ($scope.shipping.locker_id != '') {
                        $scope.shipping.address = '';
                        select_locker = $scope.shipping.locker_id;
                        check_address = true;
                    }
                    break;
                case 'self_pickup':
                    shipping_type = 'Self pick up';
                    check_address = true;
            }

            if ($scope.parent_for_class != null) {
                parent_hkid = $scope.parent_for_class.parent_hkid.$modelValue;
                parent_name = $scope.parent_for_class.parent_name.$modelValue;
                parent_dob = $scope.parent_for_class.parent_dob.$modelValue;
                parent_dob = moment(parent_dob).format('YYYY-MM-DD');
            }
            if (check_address) {
                data.append('parent_id', parent_id);
                data.append('player_id', player_id);
                data.append('parent_hkid', parent_hkid);
                data.append('parent_name', parent_name);
                data.append('parent_dob', parent_dob);
                data.append('course_id', course_id);
                data.append('res_address', res_address);
                data.append('parent_course_id', parent_course_id);
                data.append('goalkeeper_course_id', goalkeeper_course_id);
                data.append('selected_shirt', selected_shirt);
                data.append('parent_selected_shirt', parent_selected_shirt);
                data.append('shipping_type', shipping_type);
                data.append('select_locker', select_locker);
                data.append('offline_registration', 'true');
                $http({
                    url:
                        SERVER_PATH +
                        'summer-scheme/createNewRegistrationForSummerScheme',
                    method: 'POST',
                    data: data,
                    headers: {
                        'Content-Type': undefined,
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name,
                    },
                }).then(function (response) {
                    console.log(response);
                    if (response.data.status == 'OK') {
                        console.log(fieldset);
                        $scope.nextStep($event, fieldset);
                        console.log(response.data);
                    } else {
                        BootstrapDialog.show({
                            type: BootstrapDialog.TYPE_DANGER,
                            title: 'Error',
                            message: response.data.message,
                        });
                    }
                });
            } else {
                BootstrapDialog.show({
                    type: BootstrapDialog.TYPE_DANGER,
                    title: 'Error',
                    message: 'Please select shipping address',
                });
            }
        };

        $scope.checkDulicateHKID = function (hkid, name = '', dob = '') {
            var data = new FormData();
            data.append('hkid', hkid);
            data.append('name', name);
            data.append('dob', dob);
            return $http({
                url: SERVER_PATH + 'summer-scheme/checkDulicateHKID',
                data: data,
                method: 'POST',
                headers: {
                    'Content-Type': undefined,
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name,
                },
            }).then(function (response) {
                console.log(response.data);
                return response.data;
            });
        };
    }
);
