<?xml version='1.0' encoding='utf-8'?>
<widget version="1.0.0" xmlns="http://www.w3.org/ns/widgets" xmlns:cdv="http://cordova.apache.org/ns/1.0">
  <access origin="*" />
  
  <feature name="PhotoViewer">
    <param name="ios-package" value="PhotoViewer"/>
  </feature>

  <feature name="ActionSheet">
    <param name="ios-package" value="ActionSheet"/>
  </feature>

  <feature name="BackgroundMode">
    <param name="ios-package" value="APPBackgroundMode" onload="true"/>
    <param name="onload" value="true"/>
  </feature>

  <feature name="Camera">
    <param name="ios-package" value="CDVCamera"/>
  </feature>

  <feature name="Chooser">
    <param name="ios-package" value="Chooser"/>
  </feature>

  <feature name="Device">
    <param name="ios-package" value="CDVDevice"/>
  </feature>

  <feature name="Notification">
    <param name="ios-package" value="CDVNotification"/>
  </feature>

  <feature name="File">
    <param name="ios-package" value="CDVFile"/>
    <param name="onload" value="true"/>
  </feature>

  <feature name="FileOpener2">
    <param name="ios-package" value="FileOpener2"/>
  </feature>

  <feature name="FileTransfer">
    <param name="ios-package" value="CDVFileTransfer"/>
  </feature>

  <feature name="Geolocation">
    <param name="ios-package" value="CDVLocation"/>
  </feature>

  <feature name="InAppBrowser">
    <param name="ios-package" value="CDVWKInAppBrowser"/>
    <param name="onload" value="true"/>
  </feature>

  <feature name="NetworkStatus">
    <param name="ios-package" value="CDVConnection"/>
  </feature>

  <feature name="CDVOrientation">
    <param name="ios-package" value="CDVOrientation"/>
  </feature>

  <feature name="AppVersion">
    <param name="ios-package" value="AppVersion"/>
  </feature>

  <feature name="Market">
    <param name="ios-package" value="CDVMarket"/>
  </feature>

  
</widget>