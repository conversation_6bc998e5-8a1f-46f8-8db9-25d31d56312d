<link rel="stylesheet"
  href="app/registrations/registration-summer-scheme/offline-registration/offline-registration.css">
<div class="row" id="tab-page">
  <div class="row">
    <div class="col-lg-12">
      <ol class="breadcrumb">
        <li>Home</li>
        <li>Registrations</li>
        <li>{{event_name}}</li>
        <li>{{event_type}}</li>
        <li class="active"><span>Registrations</span></li>
      </ol>
    </div>
  </div>
</div>
<div class="row">
  <div class="col-lg-12">
    <div class="clearfix">
      <h1 class="pull-left">Offline Registrations</h1>
    </div>
  </div>
</div>

<section class="multisteps-form">
  <div class="row">
    <div class="col-12 col-lg-8 ml-auto mr-auto mb-4 width-100">
      <div class="multisteps-form__progress">
        <button class="multisteps-form__progress-btn js-active" type="button" title="User Info">Parent</button>
        <button class="multisteps-form__progress-btn" type="button" title="Address">Player</button>
        <button class="multisteps-form__progress-btn" type="button" title="Order Info">Shipping</button>
      </div>
    </div>
  </div>
  <div class="row">
    <div class="col-lg-1"></div>
    <div class="col-12 col-lg-10 m-auto">
      <form class="multisteps-form__form">
        <!--single form panel-->
        <div class="multisteps-form__panel shadow p-4 rounded bg-white js-active" data-animation="scaleIn">
          <h2 class="multisteps-form__title center">Parent Information</h2>
          <div class="multisteps-form__content">
            <div class="form-row mt-4" id="parent_info">
              <ng-form class="parent_form" name="createParentForm" id="createParentForm"
                ng-submit="submitCreateParent()">
                <div class="form-group">
                  <label for="parent-email">Parent Email:</label>
                  <input type="email" class="form-control" id="parent-email" placeholder="Enter parent email"
                    ng-model="parent.email" name="email" ng-change="changeEmail()" ng-model-options="{ debounce: 1000 }"
                    required validate-email>
                  <span class="error"
                    ng-show="createParentForm.email.$error.required && createParentForm.$submitted">Please
                    enter your email address.</span>
                  <span class="error"
                    ng-show="createParentForm.email.$error.email && createParentForm.$submitted">Please
                    enter a valid email address.</span>
                </div>
                <div class="form-group">
                  <label for="english-surname">English Surname:</label>
                  <input type="text" class="form-control" id="english-surname" name="surname"
                    placeholder="Enter English surname" ng-model="parent.surname"
                    required validate-english-chinese-name
                    validate-length>
                  <span class="error"
                    ng-show="createParentForm.surname.$error.required && createParentForm.$submitted">Please
                    enter your surname.</span>
                  <span class="error"
                    ng-show="createParentForm.surname.$error.englishChineseName && createParentForm.$submitted">Please
                    enter valid surname.</span>
                  <span class="error"
                    ng-show="createParentForm.surname.$error.length && createParentForm.$submitted">Please
                    enter valid surname.</span>
                </div>
                <div class="form-group">
                  <label for="english-other-name">English Other Name:</label>
                  <input type="text" class="form-control" id="english-other-name" name="othername"
                    placeholder="Enter English other name" ng-model="parent.othername"
                    required validate-english-chinese-name
                    validate-length>
                  <span class="error"
                    ng-show="createParentForm.othername.$error.required  && createParentForm.$submitted">Please
                    enter your other name.</span>
                  <span class="error"
                    ng-show="createParentForm.surname.$error.englishChineseName && createParentForm.$submitted">Please
                    enter valid other name.</span>
                  <span class="error"
                    ng-show="createParentForm.othername.$error.length && createParentForm.$submitted">Please
                    enter valid other name.</span>
                </div>
                <div class="form-group flex-nowrap">
                  <label for="phone-number">Phone Number:</label>
                  <input type="tel" class="form-control" id="phone-number" name="phone_number"
                    placeholder="Enter phone number" ng-model="parent.phone_number" required
                    ng-change="changePhoneNumber()" intl-tel-input>
                  <span class="error"
                    ng-show="createParentForm.phone_number.$error.required && createParentForm.$submitted">Please
                    enter your phone number.</span>
                  <span class="error"
                    ng-show="createParentForm.phone_number.$error.phone && createParentForm.$submitted">Please
                    enter valid phone number.</span>
                </div>
                <div class="create-player">
                  <input type="checkbox" class="form-check-input" id="create-user-checkbox"
                    ng-model="parent.create_user">
                  <label class="form-check-label" for="create-user-checkbox">Create user</label>
                </div>
                <div class="form-group">
                  <button class="btn btn-primary ml-auto btn-block" type="submit"
                    ng-click="submitCreateParent()">Submit</button>
                </div>
              </ng-form>
            </div>
          </div>
        </div>
        <!--single form panel-->
        <div class="multisteps-form__panel shadow p-4 rounded bg-white" data-animation="scaleIn">
          <h1 class="multisteps-form__title">Players</h1>
          <div class="multisteps-form__content">
            <div class="button-row d-flex mt-4">
              <div class="row">
                <div class="col-lg-12">
                  <div class="main-box clearfix">
                    <div class="main-box-body clearfix">
                      <div class="table-responsive">
                        <table id="playerTable" class="table table-striped table-bordered table-hover" cellspacing="0"
                          width="100%">
                          <thead>
                            <tr>
                              <th>Player photo</th>
                              <th>Player name</th>
                              <th>Chinese name</th>
                              <th>Gender</th>
                              <th>DOB</th>
                              <th>Courses</th>
                              <th>Actions</th>
                            </tr>
                          </thead>
                        </table>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="button-row d-flex mt-4">
              <button class="btn btn-primary ml-auto btn-block" type="button" title="Next"
                ng-click="submitShoppingCart()">Next</button>
            </div>
          </div>
        </div>
        <!--single form panel-->
        <div class="multisteps-form__panel shadow p-4 rounded bg-white" data-animation="scaleIn">
          <h1 class="multisteps-form__title">Shipping</h1>
          <div class="multisteps-form__content">
            <div class="form-row mt-4">
              <div class="row">
                <div class="col-lg-2"></div>
                <div class="col-lg-8" id="shipping_info">
                  <ng-form class="shipping_form" name="createShippingForm">
                    <h4 style="margin-top: 10px;">Registrations</h4>
                    <table class="table table-bordered">
                      <thead class="thead-dark">
                        <tr>
                          <th>Player name</th>
                          <th>Courses</th>
                          <th>Goods</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr ng-repeat="item in registrationSummary">
                          <td>{{item.player}}</td>
                          <td>{{item.course}}</td>
                          <td>{{item.goods}}</td>
                        </tr>
                      </tbody>
                    </table>
                    <h4 style="margin-top: 10px;">Shipping type</h4>
                    <div class="shipping_method_item">
                      <input id="home" type="radio" value="home" ng-model="shipping.type" ng-required="!shipping"
                        name="type" />
                      <label for="home">Door</label>
                    </div>
                    <div class="shipping_method_item">
                      <input id="self_pickup" type="radio" value="Self pick up" ng-model="shipping.type"
                        ng-required="!shipping" name="type" />
                      <label for="self_pickup">Self Pick-up HKFA</label>
                    </div>
                    <div ng-show="shipping.type == 'home'">
                      <input ng-trim="true" class="form-control" type="text" placeholder="Delivery address"
                        ng-model="shipping.address" name="address" ng-pattern="shipping.type=='home'?regexAddress :''"
                        maxlength="150" ng-required="shipping.type=='home'" />
                      <div class="errorContainer">
                        <div ng-show="createShippingForm.address.$dirty && shipping.address == ''" class="text-danger">
                          <i class="fa fa-times"></i>
                          <span> Please enter the address </span>
                        </div>
                        <div
                          ng-show="createShippingForm.address.$dirty && shipping.address != '' && createShippingForm.address.$error.pattern"
                          class="text-danger">
                          <i class="fa fa-times"></i>
                          <span> Please enter the valid address </span>
                        </div>
                      </div>
                    </div>
                    <div class="button-row d-flex">
                      <button class="btn btn-primary ml-auto btn-block" type="button" title="Next"
                        ng-click="submitShipping()" ng-disabled="createShippingForm.$invalid">Next</button>
                    </div>
                  </ng-form>
                </div>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>
</section>

<ng-include
  src="'app/registrations/registration-summer-scheme/offline-registration/select-course/select-course.html'"></ng-include>