<?xml version='1.0' encoding='utf-8'?>
<widget version="1.0.0" xmlns="http://www.w3.org/ns/widgets" xmlns:cdv="http://cordova.apache.org/ns/1.0">
  <access origin="*" />
  
  <feature name="PhotoViewer">
    <param name="android-package" value="com.sarriaroman.PhotoViewer.PhotoViewer"/>
  </feature>

  <feature name="ActionSheet">
    <param name="android-package" value="nl.xservices.plugins.actionsheet.ActionSheet"/>
  </feature>

  <feature name="Camera">
    <param name="android-package" value="org.apache.cordova.camera.CameraLauncher"/>
  </feature>

  <feature name="Chooser">
    <param name="android-package" value="com.cyph.cordova.Chooser"/>
  </feature>

  <feature name="Device">
    <param name="android-package" value="org.apache.cordova.device.Device"/>
  </feature>

  <feature name="Notification">
    <param name="android-package" value="org.apache.cordova.dialogs.Notification"/>
  </feature>

  <feature name="File">
    <param name="android-package" value="org.apache.cordova.file.FileUtils"/>
    <param name="onload" value="true"/>
  </feature>

  <feature name="FileOpener2">
    <param name="android-package" value="io.github.pwlin.cordova.plugins.fileopener2.FileOpener2"/>
  </feature>

  <feature name="FileTransfer">
    <param name="android-package" value="org.apache.cordova.filetransfer.FileTransfer"/>
  </feature>

  <feature name="FileChooser">
    <param name="android-package" value="com.megster.cordova.FileChooser"/>
  </feature>

  <feature name="FilePath">
    <param name="android-package" value="com.hiddentao.cordova.filepath.FilePath"/>
    <param name="onload" value="true"/>
  </feature>

  <feature name="Geolocation">
    <param name="android-package" value="org.apache.cordova.geolocation.Geolocation"/>
  </feature>

  <feature name="InAppBrowser">
    <param name="android-package" value="org.apache.cordova.inappbrowser.InAppBrowser"/>
  </feature>

  <feature name="NetworkStatus">
    <param name="android-package" value="org.apache.cordova.networkinformation.NetworkManager"/>
  </feature>

  <feature name="CDVOrientation">
    <param name="android-package" value="cordova.plugins.screenorientation.CDVOrientation"/>
  </feature>

  <feature name="AppVersion">
    <param name="android-package" value="uk.co.whiteoctober.cordova.AppVersion"/>
  </feature>

  <feature name="Market">
    <param name="android-package" value="com.xmartlabs.cordova.market.Market"/>
  </feature>

  
</widget>