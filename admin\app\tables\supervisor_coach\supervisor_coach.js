app.controller('supervisorCoachCtrl', function ($scope, $rootScope, $http, localStorageService,$routeParams,$location ) {
    var table;
    $scope.init = function () {
        var editor = new $.fn.dataTable.Editor({
            ajax: {
                type: 'POST',
                url: SERVER_PATH + "paren/setSupervisorCoaches",
                headers: {	
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name
                },
                data: {},
                dataType: 'json',
                complete: function (response) {
                    var jsonData = JSON.parse(response.responseText);
                    // --- may need to reload
                    if (DEVELOPMENT_ENVIRONMENT) console.log('status = ' + jsonData.status);
                    if (jsonData.status == 'OK') {
                        if (DEVELOPMENT_ENVIRONMENT) console.log('Before reload');
                        table.ajax.reload();
                    }
                },
                error: function (xhr, status, error) {
                },
            },
            table: "#supervisorCoachTable",
            formOptions: {
                main: {
                    onBlur: 'none'
                }
            },
            i18n: {
                create: {
                    button: "New",
                    title: "Create new supervisor coach",
                    submit: "Create"
                },
                edit: {
                    button: "Edit",
                    title: "Edit supervisor coach",
                    submit: "Save"
                },
                remove: {
                    button: "Delete",
                    title: "Delete supervisor coach",
                    submit: "Delete",
                    confirm: {
                        _: "Are you sure you want to delete these supervisor coaches?",
                        1: "Are you sure you want to delete this supervisor coach?"
                    }
                },
                error: {
                    system: "System error, please contact supervisor coach."
                },
            },
            fields: [
                {
                    label: "Surname:",
                    name: "parens.surname"
                }, {
                    label: "Other name",
                    name: "parens.other_name"
                }, {
                    label: "Chinese name",
                    name: "parens.chinese_name"
                }, {
                    label: "Email",
                    name: "parens.email"
                }, {
                    label: "Coach ID",
                    name: "parens.coach_id_no"
                }, {
                    name: "parens.type",
                    type: "hidden",
                    def: TYPE_SUPERVISOR_COACH
                },
                {
                    label: "Phone number",
                    name: "parens.phone",
                    type: "telephone",
                    opts: {
                        preferredCountries: ['hk', 'cn'],
                        initialCountry: 'hk'
                    }
                },
                {
                    label: "Create user:",
                    name: "create_user",
                    type: "checkbox",
                    separator: "|",
                    options: [
                        { label: '', value: 1 }
                    ]
                }
            ]
        });

        // hide the create user checkbox for edit
        editor.on('open', function (e, mode, action) {
            if (action == 'edit') {
                editor.field('create_user').hide();
            } else {
                editor.field('create_user').show();
            }
        });

        table= $('#supervisorCoachTable').DataTable({
            dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
            stateSave: true,
            deferRender: true,
            ajax: {
                url : SERVER_PATH + 'paren/getSupervisorCoaches',
                type : 'POST',
                headers: {	
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name
                },
                data:{},
                dataType: 'json',
                complete: function (response) {
                },
                error: function (xhr, status, error) {
                    // show error message

                }
            },
            language:{
                info: "Showing _START_ to _END_ of _TOTAL_ supervisor coaches",
                infoEmpty: "Showing 0 to 0 of 0 supervisor coaches",
                lengthMenu: "Show _MENU_ supervisor coaches",
                select: {
                    rows: {
                        "_": "You have selected %d supervisor coaches",
                        "0": "Click an supervisor coach to select",
                        "1": "1 supervisor coach selected"
                    },
                },
                paginate: {
                    previous: '<i class="fa fa-chevron-left"></i>',
                    next: '<i class="fa fa-chevron-right"></i>'
                }
            },
            columns: [
                { data: "parens.surname" },
                { data: "parens.other_name" },
                { data: "parens.chinese_name" },
                { data: "parens.email" },
                { data: "parens.phone", className: "center" },
                { data: "parens.coach_id_no", className: "center" },
                {
                    data: null, render: function (data, type, row) {
                        return '<button type="button" data-toggle="modal" data-target="#modal-coaches" class="btn btn-primary modal-coaches" >Manage Certificates</button>';
                    }
                },
            ],
            select: {
                style: 'single',
                selector: 'td:first-child',
            },
            order: [[1, 'asc']],
            lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "All"]],
            buttons: [
                { extend: "create", editor: editor },
                { extend: "edit", editor: editor },
                { extend: "remove", editor: editor },
            ]
        });

        $('#supervisorCoachTable').on(
            'click',
            'tbody .modal-coaches',
            function () {
                let row = $(this).closest('tr');
                let data = table.row(row).data();
                console.log(data);
                var msg = getModalLevelTableHtml(data.parens.id);
                // Show dialog
                BootstrapDialog.show({
                    title: "Coach's Certification History - " + data.parens.other_name + ' ' + data.parens.surname + ' (' + data.parens.email + ')',
                    message: msg,
                    size: BootstrapDialog.SIZE_WIDE,
                    onshown: function (dialogRef) {
                        initLevelHistoryTbl(data);
                    },
                });
            }
        );

        function getModalLevelTableHtml(id) {
            var str = '' +
                '<div class="main-box-body clearfix">' +
                '<div class="table-responsive">' +
                // '<a data-match-route="/tables/coach" href="#/tables/coach" class="active">Add new Coach</a>'+
                '<table id="levelHistory_' + id + '" class="table table-striped table-bordered table-hover" cellspacing="0" width="100%">' +
                '<thead>' +
                '<tr>' +
                '<th>Level</th>' +
                '<th>From date</th>' +
                '</tr>' +
                '</thead>' +
                '</table>' +
                '</div>' +
                '</div>' +
                '';
            return str;
        }

        function initLevelHistoryTbl(d) {
            // console.log(d);
            editorLevels = new $.fn.dataTable.Editor({
                ajax: {
                    type: 'POST',
                    url: SERVER_PATH + "paren/getCoachLevels",
                    data: {
                        coach_id: d.parens.id
                    },
                    headers: {	
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name
                    },
                    async: false,
                    dataType: 'json',
                    complete: function (response) {
                        // reload table
                        tableLevels.ajax.reload();
                    },
                    error: function (xhr, status, error) { },
                },
                table: '#levelHistory_' + d.parens.id,
                formOptions: {
                    main: {
                        onBlur: 'none',
                    },
                },
                i18n: {
                    create: {
                        title: 'Add new level',
                        submit: 'Add',
                    },
                    edit: {
                        title: 'Edit level',
                        submit: 'Update',
                    },
                    error: {
                        system: 'System error, please contact administrator.',
                    },
                },
                fields: [
                    {
                        name: 'coach_levels.coach_id',
                        type: 'hidden',
                        def: d.parens.id,
                    },
                    {
                        name: 'coach_levels.level',
                        label: 'Level',
                        type: 'select',
                    },
                    {
                        name: 'coach_levels.from_date',
                        label: 'From date',
                        type: 'datetime',
                        format: 'DD-MMM-YYYY'
                    }
                ],
            });

            tableLevels = $('#levelHistory_' + d.parens.id).DataTable({
                dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
                stateSave: false,
                deferRender: true,
                ajax: {
                    url: SERVER_PATH + "paren/getCoachLevels",
                    type: 'POST',
                    headers: {	
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name
                    },
                    data: {
                        coach_id: d.parens.id
                    },
                    dataType: 'json',
                    complete: function (response) { },
                    error: function (xhr, status, error) { },
                },
                language: {
                    paginate: {
                        previous: '<i class="fa fa-chevron-left"></i>',
                        next: '<i class="fa fa-chevron-right"></i>',
                    },
                },

                columns: [
                    {
                        data: 'coach_levels.level', render: function (data, type, row) {
                            console.log(data);
                            let coach_cert = COACH_CERTIFICATE.find(x => x.order == data);
                            if (coach_cert) {
                                return coach_cert.name;
                            } else {
                                return data;
                            }
                        }
                    },
                    { data: 'coach_levels.from_date' }
                ],
                select: {
                    style: 'single',
                    selector: 'td:not(:last-child)',
                },
                order: [[0, 'asc']],
                buttons: [
                    {
                        extend: 'create',
                        editor: editorLevels,
                    },
                    {
                        extend: 'remove',
                        editor: editorLevels,
                    },
                    {
                        extend: 'colvis',
                        text: 'Columns',
                    },
                ],
            });
        }
    }
});