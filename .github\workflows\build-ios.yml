# create action to build production and upload to google play (ionic appflow) when call by cmd

name: Build and upload to TestFlight
on:
  workflow_dispatch:
jobs:
  build:
    runs-on: macos-latest
    steps:
      - uses: actions/checkout@v3
      # show current branch
      - name: Show current branch
        run: echo ${{ github.event.inputs.branch }}
      - name: Setup Node.js environment
        uses: actions/setup-node@v3
        with:
          node-version: 16.16.0
      - uses: actions/cache@v2
        with:
          path: Pods
          key: ${{ runner.os }}-pods-${{ hashFiles('**/Podfile.lock') }}
          restore-keys: |
            ${{ runner.os }}-pods-
      - run: npm i -g @ionic/cli
      - run: npm install -g @capacitor/cli
      - run: cd client && npm i --force && npm run build-ios-prod
      - name: Set up Xcode
        uses: maxim-lobanov/setup-xcode@v1
        with:
          xcode-version: latest-stable
      - name: Install cocoapods
        run: sudo gem install cocoapods
      - run: sudo gem install cocoapods-dependencies
      - name: Install pods
        run: cd client && cd ios && cd App && rm -rf ~/Library/Developer/Xcode/DerivedData/ && pod cache clean --all && pod deintegrate && pod dependencies && pod install
      - name: Install the Apple certificate and provisioning profile
        env:
          BUILD_CERTIFICATE_BASE64: ${{ secrets.BUILD_CERTIFICATE_BASE64 }}
          P12_PASSWORD: ${{ secrets.P12_PASSWORD }}
          BUILD_PROVISION_PROFILE_BASE64: ${{ secrets.BUILD_PROVISION_PROFILE_BASE64 }}
          KEYCHAIN_PASSWORD: ${{ secrets.KEYCHAIN_PASSWORD }}
        run: |
          # create variables
          CERTIFICATE_PATH=$RUNNER_TEMP/build_certificate.p12
          PP_PATH=$RUNNER_TEMP/build_pp.mobileprovision
          KEYCHAIN_PATH=$RUNNER_TEMP/app-signing.keychain-db

          # import certificate and provisioning profile from secrets
          echo -n "$BUILD_CERTIFICATE_BASE64" | base64 --decode --output $CERTIFICATE_PATH
          echo -n "$BUILD_PROVISION_PROFILE_BASE64" | base64 --decode --output $PP_PATH

          # create temporary keychain
          security create-keychain -p "$KEYCHAIN_PASSWORD" $KEYCHAIN_PATH
          security set-keychain-settings -lut 21600 $KEYCHAIN_PATH
          security unlock-keychain -p "$KEYCHAIN_PASSWORD" $KEYCHAIN_PATH
          # import certificate to keychain
          security import $CERTIFICATE_PATH -P "$P12_PASSWORD" -A -t cert -f pkcs12 -k $KEYCHAIN_PATH
          security list-keychain -d user -s $KEYCHAIN_PATH
          # apply provisioning profile
          mkdir -p ~/Library/MobileDevice/Provisioning\ Profiles
          cp $PP_PATH ~/Library/MobileDevice/Provisioning\ Profiles
      - name: Show available certificates
        run: security find-identity -v -p codesigning
      - name: Show available provisioning profiles
        run: ls ~/Library/MobileDevice/Provisioning\ Profiles/
      - name: Build the app
        run: cd client && cd ios && cd App && xcodebuild clean -project App.xcodeproj -configuration  ReleaseAdhoc  -alltargets
      - name: Archive your app
        run: cd client && cd ios && cd App && xcodebuild -workspace App.xcworkspace -scheme App -configuration Release -archivePath App.xcarchive archive
      - name: Export the archive
        run: cd client && cd ios && cd App && xcodebuild -exportArchive -archivePath App.xcarchive -exportOptionsPlist ExportOptions.plist -exportPath App.ipa
      - name: Upload the app to App Store Connect
        env:
          APPLE_ID: ${{ secrets.APP_STORE_CONNECT_USERNAME }}
          APPLE_ID_PASSWORD: ${{ secrets.APP_STORE_CONNECT_PASSWORD }}
        run: |
          cd client && cd ios && cd App && xcrun altool --upload-app -f App.ipa/App.ipa -t ios -u $APPLE_ID -p $APPLE_ID_PASSWORD
      - name: Clean up keychain and provisioning profile
        if: ${{ always() }}
        run: |
          security delete-keychain $RUNNER_TEMP/app-signing.keychain-db
          rm ~/Library/MobileDevice/Provisioning\ Profiles/build_pp.mobileprovision
