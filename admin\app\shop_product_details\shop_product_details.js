
app.controller('shopProducDetailsCtrl', function ($scope, $rootScope, $routeParams, $http, $q) {
    $scope.id = $routeParams.id;
    $scope.product = {};
    let tableProductAttr;
    let editorProductAttr;
    let tableProductQuantity;
    let editorProductQuantity;
    $scope.attributes = [];
    $scope.init = function () {
        console.log('shopProducDetailsCtrl', $scope.id);
        initTableProductsAttr();
        initTableQuantity();
        getProduct($scope.id);
    }

    function getProduct(id) {
        jQuery.ajax({
            type: 'POST',
            url: SERVER_PATH + "shopProducts/getProduct",
            async: false,
            headers: {	
                'x-user-id': $rootScope.user_id,
                'x-user-email': $rootScope.user_name
            },
            data: {
                product_id: id
            },
            dataType: 'json',
            complete: function (response) {
                console.log('getProduct', response.responseJSON.info.name);
                $scope.product = response.responseJSON.info;
            }
        });
    }

    function tableAtrrDetailsHtml(row_data) {
        let html = `<div class="table-responsive">
                <table id="valuesTable_${row_data.product_attributes.key_name}" class="table table-striped table-bordered table-hover"
                cellspacing="0" width="100%">
                </table>
            </div>`
        return $(html);
    }

    function initTableProductsAttr() {
        console.log('initTableProductsAttr');
        editorProductAttr = new $.fn.dataTable.Editor({
            ajax: {
                type: 'POST',
                url: SERVER_PATH + "shopProducts/productAttributes",
                headers: {	
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name
                },
                data: {
                    product_id: $scope.id
                },
                async: false,
                dataType: 'json',
                complete: function (response) {
                    tableProductAttr.ajax.reload();
                },
                error: function (xhr, status, error) {
                },
            },
            table: "#productAttrTable",
            formOptions: {
                main: {
                    onBlur: 'none'
                }
            },
            i18n: {
                create: {
                    button: "New",
                    title: "Create new attribute",
                    submit: "Create"
                },
                edit: {
                    button: "Edit",
                    title: "Edit attribute",
                    submit: "Save"
                },
                remove: {
                    button: "Delete",
                    title: "Delete attribute",
                    submit: "Delete",
                },
                error: {
                    system: "System error, please contact developer."
                },
            },
            fields: [
                {
                    name: "product_attributes.product_id",
                    type: "hidden",
                    def: $scope.id
                },
                {
                    label: "Name:",
                    name: "product_attributes.name",
                },
                {
                    label: "Type:",
                    name: "product_attributes.type",
                    type: "select",
                    options: [
                        { label: "Size", value: "size" },
                        { label: "Color", value: "color" },
                        { label: "Input", value: "input" },
                        { label: "Other", value: "other" },
                    ]
                },
                {
                    label: "Require?:",
                    name: "props.required",
                    type: "radio",
                    options: [
                        { label: "Yes", value: "1" },
                        { label: "No", value: "0" },
                    ],
                    def: "1",
                },
                {
                    label: "Max Length (optional):",
                    name: "props.maxLength",
                    attr: {
                        type: "number"
                    },
                },
                {
                    label: "Input Note (optional):",
                    name: "props.description",
                },
                {
                    label: "Value:",
                    name: "temp_value",
                    def: "",
                }
            ]
        });

        editorProductAttr.dependent('product_attributes.type', function (val, data, callback) {
            let action = editorProductAttr.s.action;
            if (action == 'create') {
                if (val === 'color') {
                    editorProductAttr.field('temp_value').input().attr('type', 'color');
                    editorProductAttr.field('temp_value').show();
                } else {
                    editorProductAttr.field('temp_value').input().attr('type', 'text');
                    if (val == 'input') {
                        // hide temp_value field
                        editorProductAttr.field('temp_value').hide();
                    } else {
                        editorProductAttr.field('temp_value').show();
                    }
                }
            }
            let keys = Object.keys(editorProductAttr.s.fields);
            console.log('keys', keys);

            if (val != 'input') {
                // for each props field to hide
                for (let i = 0; i < keys.length; i++) {
                    if (keys[i].includes('props')) {
                        editorProductAttr.field(keys[i]).hide();
                    }
                }
            } else {
                // for each props field to show
                for (let i = 0; i < keys.length; i++) {
                    if (keys[i].includes('props')) {
                        editorProductAttr.field(keys[i]).show();
                    }
                }
            }

            callback(true);
        });

        editorProductAttr.on('initEdit', function (e, node, data) {
            // hide temp_value field
            editorProductAttr.field('temp_value').hide();
            editorProductAttr.field('product_attributes.type').hide();
        });

        editorProductAttr.on('initCreate', function (e, node, data) {
            // show temp_value field
            editorProductAttr.field('temp_value').show();
            editorProductAttr.field('product_attributes.type').show();
        });

        // editorProductAttr.on('postCreate', function (e, data, action) {
        //     // reload table
        //     tableProductAttr.ajax.reload();
        // });

        tableProductAttr = $('#productAttrTable').DataTable({
            dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
            stateSave: true,
            deferRender: true,
            ajax: {
                url: SERVER_PATH + 'shopProducts/productAttributes',
                type: 'POST',
                data: {
                    product_id: $scope.id
                },
                headers: {	
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name
                },
                dataType: 'json',
                complete: function (response) {
                    console.log('complete');
                    tableProductQuantity.ajax.reload();
                },
                error: function (xhr, status, error) {
                }
            },
            language: {
                info: "Showing _START_ to _END_ of _TOTAL_ products",
                infoEmpty: "Showing 0 to 0 of 0 products",
                lengthMenu: "Show _MENU_ products",
                select: {
                    rows: {
                        "_": "You have selected %d products",
                        "0": "Click an product to select",
                        "1": "1 product selected"
                    },
                },
                paginate: {
                    previous: '<i class="fa fa-chevron-left"></i>',
                    next: '<i class="fa fa-chevron-right"></i>'
                }
            },
            createdRow:
                function (row, data, dataIndex) {
                    // console.log('createdRow', row, data, dataIndex);
                    if (data.product_attributes.type === 'input' || data.product_attributes.type === 'default') {
                        //    remove class detail-control
                        let td = $(row).find('td.details-control');
                        td.removeClass('details-control');
                        console.log(td);
                    }
                },
            columns: [
                {
                    class: 'details-control w-5',
                    orderable: false,
                    data: null,
                    defaultContent: ''
                },
                {
                    data: "product_attributes.name",
                    title: "Name",
                },
                // {
                //     data: "product_attributes.key_name",
                //     title: "Key Name",
                // },
                {
                    data: "product_attributes.type",
                    title: "Type",
                },
            ],
            select: {
                style: 'single',
            },
            lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "All"]],
            buttons: [
                { extend: 'create', editor: editorProductAttr },
                { extend: 'edit', editor: editorProductAttr },
                { extend: 'remove', editor: editorProductAttr },
            ]
        });

        const detailRows = [];
        tableProductAttr.on('click', 'tbody td.details-control', function () {
            let tr = event.target.closest('tr');
            let row = tableProductAttr.row(tr);
            let idx = detailRows.indexOf(tr.id);

            // check if type is input
            if (row.data().product_attributes.type != 'input' && row.data().product_attributes.type != 'default') {

                if (row.child.isShown()) {
                    tr.classList.remove('shown');
                    row.child.hide();

                    // Remove from the 'open' array
                    detailRows.splice(idx, 1);
                }
                else {
                    tr.classList.add('shown');
                    row.child(tableAtrrDetailsHtml(row.data())).show();

                    // Add to the 'open' array
                    if (idx === -1) {
                        detailRows.push(tr.id);
                    }
                    let table_id = `valuesTable_${row.data().product_attributes.key_name}`;
                    initTableDetails(table_id, row.data());
                }
            }
        });

    }

    function initTableQuantity() {

        editorProductQuantity = new $.fn.dataTable.Editor({
            ajax: {
                type: 'POST',
                url: SERVER_PATH + "shopProducts/productAttrQty",
                data: {
                    product_id: $scope.id
                },
                headers: {	
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name
                },
                async: false,
                dataType: 'json',
                complete: function (response) {
                },
                error: function (xhr, status, error) {
                },
            },
            table: "#quantityTable",
            formOptions: {
                main: {
                    onBlur: 'none'
                }
            },
            i18n: {
                create: {
                    button: "Add",
                    title: "Add",
                    submit: "Add"
                },
                edit: {
                    button: "Edit",
                    title: "Edit",
                    submit: "Save"
                },
                remove: {
                    button: "Delete",
                    title: "Delete",
                    submit: "Delete",
                },
                error: {
                    system: "System error, please contact developer."
                },
            },
            fields: [
                {
                    label: "Quantity:",
                    name: "product_attr_qty.quantity",
                    attr: {
                        type: "number"
                    },
                    def: 1,
                }
            ]
        });

        tableProductQuantity = $('#quantityTable').DataTable({
            dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
            stateSave: true,
            deferRender: true,
            ajax: {
                url: SERVER_PATH + 'shopProducts/productAttrQty',
                type: 'POST',
                headers: {	
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name
                },
                data: {
                    product_id: $scope.id
                },
                dataType: 'json',
                complete: function (response) {
                    console.log('complete');
                },
                error: function (xhr, status, error) {
                }
            },
            language: {
                info: "Showing _START_ to _END_ of _TOTAL_ products",
                infoEmpty: "Showing 0 to 0 of 0 products",
                lengthMenu: "Show _MENU_ products",
                select: {
                    rows: {
                        "_": "You have selected %d products",
                        "0": "Click an product to select",
                        "1": "1 product selected"
                    },
                },
                paginate: {
                    previous: '<i class="fa fa-chevron-left"></i>',
                    next: '<i class="fa fa-chevron-right"></i>'
                }
            },
            columns: [
                {
                    data: "product_attr_qty.link_attrs",
                    title: "Attriutes",
                },
                {
                    data: "product_attr_qty.quantity",
                    title: "Quantity",
                },
            ],
            select: {
                style: 'os',
            },
            lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "All"]],
            buttons: [
                // { extend: 'create', editor: editorProductQuantity },
                { extend: 'edit', editor: editorProductQuantity },
                // { extend: 'remove', editor: editorProductQuantity },
            ]
        });
    }

    function initTableDetails(id, row_data) {
        console.log('initTableDetails', id);
        console.log('initTableDetails', $(`#${id}`));
        let fields = [
            {
                name: "product_attributes.product_id",
                type: "hidden",
                def: $scope.id
            },
            {
                label: "Name:",
                name: "product_attributes.name",
                type: "hidden",
                def: row_data.product_attributes.name
            },
            {
                name: "product_attributes.key_name",
                type: "hidden",
                def: row_data.product_attributes.key_name
            },
            {
                label: "Type:",
                name: "product_attributes.type",
                type: "hidden",
                def: row_data.product_attributes.type
            },

        ];
        if (row_data.product_attributes.type === 'color') {
            fields.push({
                label: "Value:",
                name: "product_attributes.value",
                attr: {
                    type: "color"
                }
            });
        } else {
            fields.push({
                label: "Value:",
                name: "product_attributes.value",
            });
        }

        let editorDetails = new $.fn.dataTable.Editor({
            ajax: {
                type: 'POST',
                url: SERVER_PATH + "shopProducts/attributeValues",
                headers: {	
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name
                },
                data: {
                    product_id: $scope.id,
                    key_name: row_data.product_attributes.key_name
                },
                async: false,
                dataType: 'json',
                complete: function (response) {
                    tableProductQuantity.ajax.reload();
                },
                error: function (xhr, status, error) {
                },
            },
            table: `#${id}`,
            formOptions: {
                main: {
                    onBlur: 'none'
                }
            },
            i18n: {
                create: {
                    button: "New",
                    title: "Add new value",
                    submit: "Create"
                },
                edit: {
                    button: "Edit",
                    title: "Edit value",
                    submit: "Save"
                },
                remove: {
                    button: "Delete",
                    title: "Delete value",
                    submit: "Delete",
                    confirm: {
                        _: "Are you sure you want to delete these %d values?",
                        1: "Are you sure you want to delete this value?"
                    }
                },
                error: {
                    system: "System error, please contact developer."
                },
            },
            fields: fields
        });

        let tableDetails = $(`#${id}`).DataTable({
            dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i>',
            stateSave: true,
            deferRender: true,
            ajax: {
                url: SERVER_PATH + 'shopProducts/attributeValues',
                type: 'POST',
                headers: {	
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name
                },
                data: {
                    product_id: $scope.id,
                    key_name: row_data.product_attributes.key_name
                },
                dataType: 'json',
                complete: function (response) {
                },
                error: function (xhr, status, error) {
                }
            },

            columns: [
                {
                    data: "product_attributes.id",
                    title: "ID",
                },
                {
                    data: "product_attributes.value",
                    title: "Value",
                    render: function (data, type, row) {
                        if (row_data.product_attributes.type === 'color') {
                            return `<div style="border-radius:50%;width: 30px; height: 30px; background-color: ${data};"></div>`;
                        } else {
                            return data;
                        }
                    }
                },
            ],
            select: {
                style: 'single',
                selector: 'td:first-child',
            },
            lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "All"]],
            buttons: [
                { extend: 'create', editor: editorDetails },
                { extend: 'edit', editor: editorDetails },
                { extend: 'remove', editor: editorDetails },
            ]
        });
    }

    function convertSystemPath2WebPath(systemPath) {
        let web_path = systemPath
        // replace "\" to "/"
        web_path = web_path.replace(/\\/g, "/");
        // get file name in web_path
        let fileName = web_path.split('/').pop();
        web_path = `${UPLOAD_FILE_PATH}${fileName}`;
        return web_path;
    }
})
