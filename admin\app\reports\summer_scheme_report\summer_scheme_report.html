<div class="row" id="tab-page">
    <div class="row">
        <div class="col-lg-12">
            <ol class="breadcrumb">
                <li>Events</li>
                <li><a href="" ng-click="goBack()">{{event_type}}</a></li>
                <li>{{event_name}}</li>
                <li class="active"><span>Report</span></li>
            </ol>
        </div>
    </div>
</div>
<div class="row">
    <div class="col-lg-12">
        <div class="clearfix">
            <h1 class="pull-left">Report</h1>
        </div>
    </div>
    <!-- <div class="col-lg-6 form-group form-group-select2">
        <label for="selEvent">Select event</label>
        <select style="width: 100%" id="select_event_summer_scheme" ng-model="selectedEvent"
            ng-options="event.name for event in events" name="selEvent">
        </select>
    </div> -->
</div>

<div>
    <div class="row">
        <div class="col-lg-12">
            <div class="main-box clearfix">
                <div class="tabs-wrapper profile-tabs">
                    <ul class="nav nav-tabs">
                        <li class="active">
                            <a showtab="" data-target="#tab-registration" data-toggle="tab">Registration</a>
                        </li>
                        <li>
                            <a showtab="" data-target="#tab-payment" data-toggle="tab">Payment</a>
                        </li>
                        <li>
                            <a showtab="" data-target="#tab-coach-attendance" data-toggle="tab">Coach Attendance</a>
                        </li>
                    </ul>
                    <div class="tab-content">
                        <div class="tab-pane fade in active" id="tab-registration">
                            <div class="col-lg-12">
                                <div class="row">
                                    <div class="col-lg-3">
                                        <div class="main-box">
                                            <header class="main-box-header clearfix">
                                                <h2>Registration From</h2>
                                            </header>

                                            <div class="main-box-body clearfix">
                                                <div id="registration-from"
                                                    style="width: 100%; height: 400px; padding: 0px; position: relative;">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-3">
                                        <div class="main-box">
                                            <header class="main-box-header clearfix">
                                                <h2>Payment Status</h2>
                                            </header>

                                            <div class="main-box-body clearfix">
                                                <div id="payment-status"
                                                    style="width: 100%; height: 400px; padding: 0px; position: relative;">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-3">
                                        <div class="main-box">
                                            <header class="main-box-header clearfix">
                                                <h2>Shipping Type</h2>
                                            </header>

                                            <div class="main-box-body clearfix">
                                                <div id="shipping-type"
                                                    style="width: 100%; height: 400px; padding: 0px; position: relative;">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-3">
                                        <div class="main-box">
                                            <header class="main-box-header clearfix">
                                                <h2>Course Slot</h2>
                                            </header>

                                            <div class="main-box-body clearfix">
                                                <div id="course-slot"
                                                    style="width: 100%; height: 400px; padding: 0px; position: relative;">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <div class="row">

                                    <!-- T-shirt -->
                                    <div class="col-lg-6">
                                        <div class="main-box">
                                            <header class="main-box-header clearfix">
                                                <h2>T-shirt</h2>
                                            </header>
                                            <div class="main-box-body clearfix">
                                                <table class="table table-responsive table-bordered">
                                                    <thead>
                                                        <tr>
                                                            <th>Size</th>
                                                            <th class="text-right">Home</th>
                                                            <th class="text-right">Self pick up</th>
                                                            <th class="text-right">Do not need ...</th>
                                                            <th class="text-right">Total</th>
                                                            <th class="text-right">Ordered</th>
                                                            <th class="text-right">In Stock</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <tr ng-repeat="(key, value) in tshirtSummary">
                                                            <td>{{key}}</td>
                                                            <td class="text-right">{{value.home}}</td>
                                                            <td class="text-right">{{value.self_pick_up}}</td>
                                                            <td class="text-right">{{value.do_not_need_kit_and_ball}}</td>
                                                            <td class="text-right"><strong>{{value.home +
                                                                    value.self_pick_up+value.do_not_need_kit_and_ball}}</strong></td>
                                                            <td class="text-right">{{value.ordered}}</td>
                                                            <td class="text-right status"
                                                                ng-class="{'red': (value.home + value.self_pick_up+value.do_not_need_kit_and_ball) > value.ordered, 'yellow': (value.home + value.self_pick_up+value.do_not_need_kit_and_ball)/value.ordered <= 1 && (value.home + value.self_pick_up+value.do_not_need_kit_and_ball)/value.ordered >= 0.8, 'green': (value.home + value.self_pick_up+value.do_not_need_kit_and_ball)/value.ordered < 0.8}">
                                                                {{value.ordered - (value.home + value.self_pick_up +
                                                                value.do_not_need_kit_and_ball)}}
                                                                ({{((value.ordered - (value.home +
                                                                value.self_pick_up +
                                                                value.do_not_need_kit_and_ball))/value.ordered*100).toFixed(0)}}%)
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Ball -->
                                    <div class="col-lg-6">
                                        <div class="main-box">
                                            <header class="main-box-header clearfix">
                                                <h2>Ball</h2>
                                            </header>
                                            <div class="main-box-body clearfix">
                                                <table class="table table-responsive table-bordered">
                                                    <thead>
                                                        <tr>
                                                            <th>Size</th>
                                                            <th class="text-right">Home</th>
                                                            <th class="text-right">Self pick up</th>
                                                            <th class="text-right">Do not need ...</th>
                                                            <th class="text-right">Total</th>
                                                            <th class="text-right">Ordered</th>
                                                            <th class="text-right">In Stock</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <tr ng-repeat="(key, value) in ballSummary">
                                                            <td>{{key}}</td>
                                                            <td class="text-right">{{value.home}}</td>
                                                            <td class="text-right">{{value.self_pick_up}}</td>
                                                            <td class="text-right">{{value.do_not_need_kit_and_ball}}</td>
                                                            <td class="text-right"><strong>{{value.home +
                                                                    value.self_pick_up+value.do_not_need_kit_and_ball}}</strong></td>
                                                            <td class="text-right">{{value.ordered}}</td>
                                                            <td class="text-right status"
                                                                ng-class="{'red': (value.home + value.self_pick_up+value.do_not_need_kit_and_ball) > value.ordered, 'yellow': (value.home + value.self_pick_up+value.do_not_need_kit_and_ball)/value.ordered <= 1 && (value.home + value.self_pick_up+value.do_not_need_kit_and_ball)/value.ordered >= 0.8, 'green': (value.home + value.self_pick_up+value.do_not_need_kit_and_ball)/value.ordered < 0.8}">
                                                                {{value.ordered - (value.home + value.self_pick_up +
                                                                value.do_not_need_kit_and_ball)}}
                                                                ({{((value.ordered - (value.home +
                                                                value.self_pick_up +
                                                                value.do_not_need_kit_and_ball))/value.ordered*100).toFixed(0)}}%)
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Booklet -->
                                    <div class="col-lg-6">
                                        <div class="main-box">
                                            <header class="main-box-header clearfix">
                                                <h2>Booklet</h2>
                                            </header>
                                            <div class="main-box-body clearfix">
                                                <table class="table table-responsive table-bordered">
                                                    <thead>
                                                        <tr>
                                                            <th>Type</th>
                                                            <th class="text-right">Home</th>
                                                            <th class="text-right">Self pick up</th>
                                                            <th class="text-right">Do not need ...</th>
                                                            <th class="text-right">Total</th>
                                                            <th class="text-right">Ordered</th>
                                                            <th class="text-right">In Stock</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <tr ng-repeat="(key, value) in bookletSummary">
                                                            <td>{{key}}</td>
                                                            <td class="text-right">{{value.home}}</td>
                                                            <td class="text-right">{{value.self_pick_up}}</td>
                                                            <td class="text-right">{{value.do_not_need_kit_and_ball}}</td>
                                                            <td class="text-right"><strong>{{value.home +
                                                                    value.self_pick_up+value.do_not_need_kit_and_ball}}</strong></td>
                                                            <td class="text-right">{{value.ordered}}</td>
                                                            <td class="text-right status"
                                                                ng-class="{'red': (value.home + value.self_pick_up+value.do_not_need_kit_and_ball) > value.ordered, 'yellow': (value.home + value.self_pick_up+value.do_not_need_kit_and_ball)/value.ordered <= 1 && (value.home + value.self_pick_up+value.do_not_need_kit_and_ball)/value.ordered >= 0.8, 'green': (value.home + value.self_pick_up+value.do_not_need_kit_and_ball)/value.ordered < 0.8}">
                                                                {{value.ordered - (value.home + value.self_pick_up +
                                                                value.do_not_need_kit_and_ball)}}
                                                                ({{((value.ordered - (value.home +
                                                                value.self_pick_up +
                                                                value.do_not_need_kit_and_ball))/value.ordered*100).toFixed(0)}}%)
                                                            </td>   
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="tab-payment">
                            <div class="col-lg-12">
                                <div class="main-box">
                                    <div class="main-box-body clearfix">
                                        <div id="content-payment-report" class="table-responsive"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="tab-coach-attendance">
                            <div class="row">
                                <div class="col-lg-12">
                                    <div class="main-box-body clearfix">
                                        <form role="form">
                                            <div class="row">
                                                <div class="col-md-6 col-lg-3">
                                                    <label>Date range</label>
                                                    <div class="input-group">
                                                        <span class="input-group-addon"><i
                                                                class="fa fa-calendar-o"></i></span>
                                                        <input type="text" name="dateFilter" class="form-control" />
                                                    </div>
                                                </div>
                                                <div class="col-md-6 col-lg-2">
                                                    <div class="form-group form-group-select2" id="district_filter">
                                                    </div>
                                                </div>
                                                <div class="col-md-6 col-lg-2">
                                                    <div class="form-group form-group-select2" id="group_filter"></div>
                                                </div>
                                                <div class="col-md-6 col-lg-3">
                                                    <div class="form-group form-group-select2" id="coach_filter"></div>
                                                </div>
                                                <div class="col-md-6 col-lg-2">
                                                    <div class="form-group form-group-select2"
                                                        id="approval_status_filter"></div>
                                                </div>
                                            </div>
                                        </form>
                                        <div id="attendanceContent"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>