<div class="row" id="tab-page">
    <div class="row">
        <div class="col-lg-12">
            <ol class="breadcrumb">
                <li>Setup</li>
                <li class="active"><span>Notification</span></li>
            </ol>
        </div>
    </div>
</div>
<div class="row">
	<div class="col-lg-12">
		<div class="clearfix">
			<h1 class="pull-left">Notification (App start)</h1>
		</div>
	</div>
</div>

<div class="row">
	<div class="col-lg-12">
		<div class="main-box clearfix">
			<div class="main-box-body clearfix">
				<div class="table-responsive">
					<table id="tableNotification" class="table table-striped table-bordered table-hover" cellspacing="0"
						width="100%">
						<thead>
							<tr>
								<th>Id</th>
								<th>Type</th>
								<th>Title</th>
								<th>Start date</th>
								<th>End date</th>
							</tr>
						</thead>
					</table>
				</div>
			</div>
		</div>
	</div>
</div>

<script type="text/javascript" language="javascript" class="init">
	$(document).ready(function () {

		user_id = localStorage.getItem('hkjflApp.user_id');
		user_name = localStorage.getItem('hkjflApp.user_name');

		// datatable & editor master
		var tableNotification;

		var editor = new $.fn.dataTable.Editor({
			ajax: {
				type: 'POST',
				url: SERVER_PATH + "notificationAppStart/setNotificationAppStart",
				data: {},
				headers: {	
                    'x-user-id': user_id,
                    'x-user-email': user_name
                },
				dataType: 'json',
				complete: function (response) {
					var jsonData = JSON.parse(response.responseText);
					// --- may need to reload
					if (DEVELOPMENT_ENVIRONMENT) console.log('status = ' + jsonData.status);
					if (jsonData.status == 'OK') {
						tableNotification.ajax.reload();
					}
				},
				error: function (xhr, status, error) {},
			},
			table: "#tableNotification",
			formOptions: {
				main: {
					onBlur: 'none',
				}
			},
			i18n: {
				create: {
					button: "New",
					title: "Create new Notification",
					submit: "Create"
				},
				edit: {
                    button: "Edit",
                    title: "Edit Notification",
                    submit: "Update"
                },
				remove: {
					button: "Delete",
					title: "Delete Notification",
					submit: "Delete",
					confirm: {
						_: "Are you sure you want to delete these Notification?",
						1: "Are you sure you want to delete this Notification?"
					}
				},
				error: {
					system: "System error, please contact administrator."
				},
			},
			fields: [
				{
					label: "Title",
					name: "notification_app_start.title",
				},
				{
					label: "Start date",
					name: "notification_app_start.start_date",
					type: "datetime",
                	def: function () { 
						return new Date(); 
					},
					format: "YYYY/MM/DD"
				},
				{
					label: "End date",
					name: "notification_app_start.end_date",
					type: "datetime",
                	def: function () { 
						return new Date(); 
					},
					format: "YYYY/MM/DD"
				},
				{
					label: "Body",
					name: "notification_app_start.body",
					type: "ckeditor",
				}, 
			]
		});

		var tableNotification = $('#tableNotification').DataTable({
			dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
			stateSave: false,
			deferRender: true,
			ajax: {
				url: SERVER_PATH + "notificationAppStart/getNotificationAppStart",
				type: 'POST',
				headers: {	
                    'x-user-id': user_id,
                    'x-user-email': user_name
                },
				data: {},
				dataType: 'json',
				complete: function (response) {},
				error: function (xhr, status, error) {},
			},
			language: {
				paginate: {
					previous: '<i class="fa fa-chevron-left"></i>',
					next: '<i class="fa fa-chevron-right"></i>'
				}
			},
			columnDefs: [
				{
					"targets": [0],
					"visible": false
				}
			],
			columns: [
				{
					data: 'notification_app_start.id'
				},
				{
					data: 'notification_app_start.type'
				},
				{
					data: 'notification_app_start.title'
				},
				{
					data: 'notification_app_start.start_date',
					className: "center",
				},
				{
					data: 'notification_app_start.end_date',
					className: "center",
				},
			],
			select: {
				style: 'single',
			},
			order: [
				[0, 'asc']
			],
			lengthMenu: [
				[10, 25, 50, 100, -1],
				[10, 25, 50, 100, "All"]
			],
			buttons: [
				{
					extend: "edit",
					editor: editor
				},
				{
					extend: 'colvis',
					text: 'Columns'
				}
			]
		});

	});
</script>
