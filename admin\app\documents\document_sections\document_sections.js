app.controller(
  'documentSectionsCtrl',
  function ($scope, $rootScope, $routeParams, $http) {
    $('#page-wrapper').removeClass('nav-small');

    $scope.documentId = $routeParams.id;
    $scope.documentName = $routeParams.name;
    console.log($scope.documentId);

    var table;

    function getcropperHTML(src) {
      var cropperHTML =
        '<div class="cropper"><img style="height: 600px" class="js-avatar-preview" src="' +
        src +
        '"></div>';
      return cropperHTML;
    }

    var editor = new $.fn.dataTable.Editor({
      ajax: {
        type: 'POST',
        url: SERVER_PATH + 'document/setDocumentSections',
        headers: {	
          'x-user-id': $rootScope.user_id,
          'x-user-email': $rootScope.user_name
        },
        data: {
          document_id: $scope.documentId,
        },
        async: false,
        dataType: 'json',
        complete: function (response) {
          var jsonData = JSON.parse(response.responseText);
          // --- may need to reload
          if (DEVELOPMENT_ENVIRONMENT)
            console.log('status = ' + jsonData.status);
          if (jsonData.status == 'OK') {
            table.ajax.reload();
          }
        },
        error: function (xhr, status, error) {},
      },
      table: '#documentSectionsTable',
      formOptions: {
        main: {
          onBlur: 'none',
        },
      },
      i18n: {
        create: {
          button: 'New',
          title: 'Create new documents',
          submit: 'Create',
        },
        edit: {
          button: 'Edit',
          title: 'Edit documents',
          submit: 'Update',
        },
        remove: {
          button: 'Delete',
          title: 'Delete documents',
          submit: 'Delete',
          confirm: {
            _: 'Are you sure you want to delete these documents?',
            1: 'Are you sure you want to delete this documents?',
          },
        },
        error: {
          system: 'System error, please contact administrator.',
        },
      },
      fields: [
        {
          label: 'Document ID',
          name: 'document_sections.document_id',
          type: 'hidden',
          def: $scope.documentId,
        },
        {
          label: 'Title',
          name: 'document_sections.title',
        },
        {
          label: 'Content',
          name: 'document_sections.content',
          type: 'ckeditor',
        },
        {
          label: 'Photo',
          name: 'document_sections.section_photo',
          type: 'upload',
          display: function (data) {
            return '<img src="' + UPLOAD_FILE_PATH + data + '" width="100%">';
          },
          clearText: 'Clear',
          noImageText: 'No image',
        },
        {
          label: 'PDF',
          name: 'document_sections.section_file',
          type: 'upload',
          display: function (data) {
            return data;
          },
          noFileText: 'No files',
          clearText: 'Clear',
        },
        {
          label: 'order',
          name: 'document_sections.order',
          type: 'hidden',
        },
      ],
    });

    editor.on('preUpload', function (e, fieldName, file, ajaxData) {
      ajaxData.document_id = $scope.documentId;
      console.log('preUpload', ajaxData);
    });

    var table = $('#documentSectionsTable').DataTable({
      dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
      stateSave: true,
      deferRender: true,
      ajax: {
        url: SERVER_PATH + 'document/getDocumentSections',
        type: 'POST',
        headers: {	
          'x-user-id': $rootScope.user_id,
          'x-user-email': $rootScope.user_name
        },
        data: {
          document_id: $scope.documentId,
        },
        dataType: 'json',
        complete: function (response) {
          console.log(response);
        },
        error: function (xhr, status, error) {},
      },
      language: {
        paginate: {
          previous: '<i class="fa fa-chevron-left"></i>',
          next: '<i class="fa fa-chevron-right"></i>',
        },
      },
      columns: [
        {
          data: 'document_sections.order',
        },
        {
          data: 'document_sections.section_photo',
          className: 'avatar',
          orderable: false,
          render: function (data) {
            if (data !== null && data !== '') {
              return '<a class="openImage"><img src="' + UPLOAD_FILE_PATH + data + '" width="100%"></a>';
            } else {
              return '<img src="' + SYSTEM_IMAGE_PATH + 'favicon.png">';
            }
          },
        },
        {
          data: 'document_sections.title',
          sortable: false,
        },
        {
          data: 'document_sections.section_file',
          sortable: false,
          render: function (data, type, full, meta) {
            if (data != null) {
              return `<a href="javascript:window.open('${UPLOAD_FILE_PATH}${data}')">Open</a>`;
            }
            return '';
          },
        },
      ],
      rowReorder: {
        dataSrc: 'document_sections.order',
        editor: editor,
      },
      select: {
        style: 'single',
        selector: 'td:not(:last-child)',
      },
      order: [[0, 'asc']],
      columnDefs: [
        {
          visible: false,
          target: [3],
        },
      ],
      lengthMenu: [
        [10, 25, 50, 100, -1],
        [10, 25, 50, 100, 'All'],
      ],
      buttons: [
        {
          extend: 'create',
          editor: editor,
        },
        {
          extend: 'edit',
          editor: editor,
        },
        {
          extend: 'remove',
          editor: editor,
        },
        {
          extend: 'colvis',
          text: 'Columns',
        },
      ],
    });

    // Open image
    $('#documentSectionsTable').on('click', 'tbody td img', function (e, row) {
      console.log('Click on openImage');
      var $row = $(this).closest('tr');
      var data = table.row($row).data();
      console.log(data.document_sections);
      // show modal
      BootstrapDialog.show({
        title: data.document_sections.title,
        size: BootstrapDialog.SIZE_NORMAL,
        message:
          '<img src="' +
          UPLOAD_FILE_PATH +
          data.document_sections.section_photo +
          '" width="100%">',
      });
    });
  }
);
