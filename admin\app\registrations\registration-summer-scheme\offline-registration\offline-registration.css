* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    font-size: 16px;
    color: #2c2c2c;
}
body a {
    color: inherit;
    text-decoration: none;
}

.header__btn {
    transition-property: all;
    transition-duration: 0.2s;
    transition-timing-function: linear;
    transition-delay: 0s;
    padding: 10px 20px;
    display: inline-block;
    margin-right: 10px;
    background-color: #fff;
    border: 1px solid #2c2c2c;
    border-radius: 3px;
    cursor: pointer;
    outline: none;
}
.header__btn:last-child {
    margin-right: 0;
}
.header__btn:hover,
.header__btn.js-active {
    color: #fff;
    background-color: #2c2c2c;
}

.header {
    max-width: 600px;
    margin: 50px auto;
    text-align: center;
}

.header__title {
    margin-bottom: 30px;
    font-size: 2.1rem;
}

.content {
    width: 95%;
    margin: 0 auto 50px;
}

.content__title {
    margin-bottom: 40px;
    font-size: 20px;
    text-align: center;
}

.content__title--m-sm {
    margin-bottom: 10px;
}

.multisteps-form__progress {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(0, 1fr));
}

.multisteps-form__progress-btn {
    transition-property: all;
    transition-duration: 0.15s;
    transition-timing-function: linear;
    transition-delay: 0s;
    position: relative;
    padding-top: 20px;
    color: rgba(108, 117, 125, 0.7);
    text-indent: -9999px;
    border: none;
    background-color: transparent;
    outline: none !important;
    cursor: pointer;
}
@media (min-width: 500px) {
    .multisteps-form__progress-btn {
        text-indent: 0;
    }
}
.multisteps-form__progress-btn:before {
    position: absolute;
    top: 0;
    left: 50%;
    display: block;
    width: 13px;
    height: 13px;
    content: '';
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
    transition: all 0.15s linear 0s,
        -webkit-transform 0.15s cubic-bezier(0.05, 1.09, 0.16, 1.4) 0s;
    transition: all 0.15s linear 0s,
        transform 0.15s cubic-bezier(0.05, 1.09, 0.16, 1.4) 0s;
    transition: all 0.15s linear 0s,
        transform 0.15s cubic-bezier(0.05, 1.09, 0.16, 1.4) 0s,
        -webkit-transform 0.15s cubic-bezier(0.05, 1.09, 0.16, 1.4) 0s;
    border: 2px solid currentColor;
    border-radius: 50%;
    background-color: #fff;
    box-sizing: border-box;
    z-index: 3;
}
.multisteps-form__progress-btn:after {
    position: absolute;
    top: 5px;
    left: calc(-50% - 13px / 2);
    transition-property: all;
    transition-duration: 0.15s;
    transition-timing-function: linear;
    transition-delay: 0s;
    display: block;
    width: 100%;
    height: 2px;
    content: '';
    background-color: currentColor;
    z-index: 1;
}
.multisteps-form__progress-btn:first-child:after {
    display: none;
}
.multisteps-form__progress-btn.js-active {
    color: #007bff;
}
.multisteps-form__progress-btn.js-active:before {
    -webkit-transform: translateX(-50%) scale(1.2);
    transform: translateX(-50%) scale(1.2);
    background-color: currentColor;
}

.multisteps-form__form {
    position: relative;
}

.multisteps-form__panel {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 0;
    opacity: 0;
    visibility: hidden;
}
.multisteps-form__panel.js-active {
    height: auto;
    opacity: 1;
    visibility: visible;
}
.multisteps-form__panel[data-animation='scaleOut'] {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
}
.multisteps-form__panel[data-animation='scaleOut'].js-active {
    transition-property: all;
    transition-duration: 0.2s;
    transition-timing-function: linear;
    transition-delay: 0s;
    -webkit-transform: scale(1);
    transform: scale(1);
}
.multisteps-form__panel[data-animation='slideHorz'] {
    left: 50px;
}
.multisteps-form__panel[data-animation='slideHorz'].js-active {
    transition-property: all;
    transition-duration: 0.25s;
    transition-timing-function: cubic-bezier(0.2, 1.13, 0.38, 1.43);
    transition-delay: 0s;
    left: 0;
}
.multisteps-form__panel[data-animation='slideVert'] {
    top: 30px;
}
.multisteps-form__panel[data-animation='slideVert'].js-active {
    transition-property: all;
    transition-duration: 0.2s;
    transition-timing-function: linear;
    transition-delay: 0s;
    top: 0;
}
.multisteps-form__panel[data-animation='fadeIn'].js-active {
    transition-property: all;
    transition-duration: 0.3s;
    transition-timing-function: linear;
    transition-delay: 0s;
}
.multisteps-form__panel[data-animation='scaleIn'] {
    -webkit-transform: scale(0.9);
    transform: scale(0.9);
}
.multisteps-form__panel[data-animation='scaleIn'].js-active {
    transition-property: all;
    transition-duration: 0.2s;
    transition-timing-function: linear;
    transition-delay: 0s;
    -webkit-transform: scale(1);
    transform: scale(1);
}

.multisteps-form__title {
    text-align: center;
    text-transform: uppercase;
    letter-spacing: 3px;
    font-size: 32px;
    line-height: 48px;
    color: #5543ca;
    background: #5543ca;
    background: -moz-linear-gradient(left, #f4524d 0%, #5543ca 100%) !important;
    background: -webkit-linear-gradient(
        left,
        #f4524d 0%,
        #5543ca 100%
    ) !important;
    background: linear-gradient(to right, #f4524d 0%, #5543ca 100%) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
}

#parent_info .form-group label{
    font-size: 1.2em;
    font-weight: 600;
    color: #5543ca;
    margin-bottom: 0.5em;
    display: block;
}

#parent_info .form-group input{
    display: block;
    width: 100%;
    height: 36px;
    border-width: 0 0 2px 0;
    border-color: #5543ca;
    font-size: 18px;
    line-height: 26px;
    font-weight: 400;
}

#parent_info #parent_info{
    border: 1px solid #5543ca;
    padding-top: 1em;
    padding-right: 1em;
    padding-left: 1em;
    border-radius: 1em;
    background-color: white;
    /* show box shadow for this block */
    box-shadow: 0 0 10px rgba(0,0,0,0.5);
}

.create-player label{
    font-size: 1.2em;
    font-weight: 600;
    color: #5543ca;
    margin-bottom: 0.5em;
}

#shipping_info{
    background-color: white;
    /* show box shadow for this block */
    box-shadow: 0 0 10px rgba(0,0,0,0.5);
    padding-top: 1em;
    padding-right: 1em;
    padding-left: 1em;
    padding-bottom: 1em;
    border-radius: 1em;
}

#shipping_info button{
    margin-top: 10px;
}