<!DOCTYPE html>
<html ng-app="hkjflApp">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />

    <title>HKFA</title>

    <!-- bootstrap -->
    <link rel="stylesheet" type="text/css"
        href="../../../../framework/node_modules/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" type="text/css"
        href="../../../../framework/node_modules/bootstrap3-dialog/dist/css/bootstrap-dialog.min.css" />

    <!-- RTL support - for demo only -->
    <script src="../cube/demo-rtl.js"></script>

    <!-- libraries -->
    <link rel="stylesheet" type="text/css"
        href="../../../../framework/node_modules/font-awesome/css/font-awesome.min.css" />
    <link rel="stylesheet" type="text/css"
        href="../../../../framework/node_modules/nanoscroller/bin/css/nanoscroller.css" />

    <!-- global styles -->
    <link rel="stylesheet" type="text/css"
        href="../../../../framework/node_modules/zakumi-admin/css/compiled/theme_styles.css" />

    <!-- Favicon -->
    <link type="image/x-icon" href="../../images/favicon.png" rel="shortcut icon" />

    <style>
        #print_training_exercise_page {
            background: none repeat scroll 0 0 white;
        }

        .print-button-container {
            text-align: center;
            padding: 10px;
        }

        .print-button-container {
            text-align: center;
            padding: 10px;
        }

        #print-button {
            background-color: #bfbfbf;
            padding: 8px 50px;
            font-weight: bold;
            box-shadow: 3px 3px #adabab
        }

        #print-button:hover {
            cursor: pointer;
        }

        #print-button:active {
            background-color: #adabab;
            box-shadow: none;
        }
    </style>

</head>

<body id="print_training_exercise_page" ng-controller="print_training_exerciseCtrl">
    <div class="container">
        <div id="training_exercise_content"></div>
        <div class="print-button-container">
            <button ng-click="print()" id="print-button">Print</button>
        </div>
    </div>

</body>

<!-- global scripts -->
<script src="../cube/demo-skin-changer.js"></script> <!-- only for demo -->
<script src="../../../../framework/node_modules/jquery/dist/jquery.min.js"></script>
<script src="../../../../framework/node_modules/jquery/dist/jquery.min.js"></script>
<script src="../../../../framework/node_modules/bootstrap/dist/js/bootstrap.min.js"></script>
<script src="../../../../framework/node_modules/bootstrap3-dialog/dist/js/bootstrap-dialog.min.js"></script>
<script src="../../../../framework/node_modules/nanoscroller/bin/javascripts/jquery.nanoscroller.js"></script>

<!-- theme scripts -->
<script src="../../../../framework/node_modules/zakumi-admin//js/scripts.js"></script>

<!-- angular libs -->
<script src="../../../../framework/node_modules/angular/angular.min.js"></script>
<script src="https://ajax.googleapis.com/ajax/libs/angularjs/1.2.0rc1/angular-route.min.js"></script>


<!-- this page specific inline scripts -->
<script src="../config.js"></script>
<script src="./print_training_exercise.js"></script>

<script>
    $(document).ready(function () {
        window.onafterprint = function () {
            $('#print-button').show();
        }
    });
</script>