app.controller(
    'responsesCtrl',
    function ($scope, $rootScope, $routeParams, $http, $q) {
        var questionnaires_id = $routeParams.id;
        // $scope.title = $routeParams.title;
        $scope.questionnaire = null;

        jQuery.ajax({
            type: 'POST',
            url: SERVER_PATH + 'questionnaire/getquestionnaireById',
            async: false,
            headers: {
                'x-user-id': $rootScope.user_id,
                'x-user-email': $rootScope.user_name,
            },
            data: {
                questionnaires_id: questionnaires_id,
            },
            dataType: 'json',
            complete: function (response) {
                var jsonData = JSON.parse(response.responseText);
                if (jsonData.status == 'OK') {
                    $scope.questionnaire = jsonData.info;
                    $scope.title = jsonData.info.title;
                }
            },
        });

        questionnaireTable = $('#responsesTable').DataTable({
            dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
            stateSave: true,
            deferRender: true,
            ajax: {
                url: SERVER_PATH + 'questionnaire/getResponsesTable',
                type: 'POST',
                headers: {
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name,
                },
                data: {
                    questionnaires_id: questionnaires_id,
                },
                dataType: 'json',
                complete: function (response) {},
                error: function (xhr, status, error) {},
            },
            language: {
                paginate: {
                    previous: '<i class="fa fa-chevron-left"></i>',
                    next: '<i class="fa fa-chevron-right"></i>',
                },
            },
            columns: [
                {
                    data: 'parent_name',
                },
                {
                    data: 'responses.player_ids',
                },
                {
                    data: 'responses.course_name',
                },
                {
                    data: 'responses.status',
                },
                {
                    data: 'responses.created_at',
                },
                {
                    data: 'responses.updated_at',
                },
                {
                    data: null,
                    render: function (data, type, row) {
                        return '<button class="btn btn-primary btn-sm RButton">View answers</button>';
                    },
                },
            ],
            select: {
                style: 'single',
                selector: 'td:not(:last-child)',
            },
            order: [[0, 'asc']],
            lengthMenu: [
                [10, 25, 50, 100, -1],
                [10, 25, 50, 100, 'All'],
            ],
            buttons: [
                {
                    extend: 'colvis',
                    text: 'Columns',
                },
            ],
        });

        function viewPlayerHtml(response_id) {
            return '<table id="ResponsesDetailTable_'+response_id+'" class="table table-striped table-bordered table-hover" cellspacing="0" width="100%"> <thead> <tr> <th></th> <th>Questions</th> <th>Reply Method</th> <th style="width:600px">Answers</th> </tr> </thead> </table>';
        }
        $('#responsesTable tbody').off('click', 'button.RButton');

        $('#responsesTable tbody').on('click', 'button.RButton', function () {
            var data = questionnaireTable.row($(this).parents('tr')).data();

            var responses = data.responses;

            BootstrapDialog.show({
                size: BootstrapDialog.SIZE_WIDE,
                type: BootstrapDialog.TYPE_DANGER,
                closable: true,
                closeByBackdrop: false,
                closeByKeyboard: true,
                title: 'View Answers',
                id: 'view-answers',
                message: viewPlayerHtml(responses.id),
                onshown: function (dialog) {
                    $scope.initresponsesDetailTable(responses.id);
                },
                onhide: function (dialog) {},
                onhidden: function (dialogRef) {},
            });
        });

        $scope.initresponsesDetailTable = function (response_id) {
            if (
                $.fn.DataTable.isDataTable(
                    `#ResponsesDetailTable_` + response_id
                )
            ) {
                $(`#ResponsesDetailTable_` + response_id)
                    .DataTable()
                    .destroy();
            }

            responsesDetailTable = $(
                `#ResponsesDetailTable_` + response_id
            ).DataTable({
                dom: '<"row"<"col-sm-6"B><"col-sm-6"f>>rt<"row"i>',
                stateSave: true,
                deferRender: true,
                ajax: {
                    url: SERVER_PATH + 'questionnaire/getResponsesDetailTable',
                    type: 'POST',
                    headers: {
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name,
                    },
                    data: {
                        response_id: response_id,
                    },
                    dataType: 'json',
                    complete: function (response) {},
                    error: function (xhr, status, error) {},
                },
                language: {
                    paginate: {
                        previous: '<i class="fa fa-chevron-left"></i>',
                        next: '<i class="fa fa-chevron-right"></i>',
                    },
                },
                columns: [
                    {
                        data: 'questions.type',
                        visible: false
                    },
                    {
                        data: 'questions.question',
                    },
                    {
                        data: 'questions.field_type',
                    },
                    {
                        data: 'response_details.answer',
                        render: function (data, type, row) {
                            return '<span style="width: 650px; display: inline-block; word-wrap: break-word; white-space: normal;">' + data + '</span>';
                        }
                    },
                ],
                rowGroup: {
                    order: [['questions.type', 'asc']],
                    dataSrc: function (row) {
                        return row.questions.type == null
                            ? '_'
                            : row.questions.type;
                    }
                },
                buttons: [],
                order: [[0, 'asc']],
                displayLength: -1,
            });
        };
    }
);
