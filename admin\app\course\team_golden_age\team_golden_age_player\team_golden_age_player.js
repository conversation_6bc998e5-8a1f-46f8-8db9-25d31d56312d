app.controller(
    'teamGoldenAgePlayerCtrl',
    function ($scope, $rootScope, $routeParams, $http) {
        var event_id = $routeParams.id;
        $scope.event_id = event_id;
        $scope.team_id = $routeParams.teamId;
        $scope.team_name = $routeParams.teamName;

         // get info event
         jQuery.ajax({
            type: 'POST',
            url: SERVER_PATH + 'event/getEventInfo',
            async: false,
            headers: {	
                'x-user-id': $rootScope.user_id,
                'x-user-email': $rootScope.user_name
            },
            data: {
                event_id: event_id,
            },
            dataType: 'json',
            complete: function (response) {
                var jsonData = JSON.parse(response.responseText);
                var event = jsonData.info;
                $scope.selectedEvent = jsonData.info;
                $scope.selectedEvent.id = event_id;
                event_name = event.name;
                event_type = event.type;
                normalizedType = normalizeEventType(event_type);
            },
        });

        $scope.event_name = event_name;
        $scope.event_type = event_type;

        initPlayerTbl();

        $scope.normalizedType = normalizedType;

        function normalizeEventType(eventType) {
            switch (eventType) {
                case 'Summer Scheme':
                    return 'summer-scheme';
                case 'Regional':
                    return 'regional';
                case 'PL Junior':
                    return 'pl-junior';
                case 'Golden Age':
                    return 'golden-age';
                case 'Beginner':
                    return 'beginner';
                case 'District':
                    return 'district';
                default:
                    return '';
            }
        }

        function initPlayerTbl() {
            tablePlayer = $('#tblGoldenAgePlayer').DataTable({
                dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
                stateSave: false,
                deferRender: true,
                ajax: {
                    url: SERVER_PATH + 'group/getPlayerInTeam',
                    type: 'POST',
                    headers: {	
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name
                    },
                    data: {
                        team_id: $scope.team_id,
                        event_id: $scope.event_id,
                    },
                    dataType: 'json',
                    complete: function (response) {
                        // console.log('res: ',response);
                    },
                    error: function (xhr, status, error) {},
                },
                order: [[1, 'asc']],
                language: {
                    paginate: {
                        previous: '<i class="fa fa-chevron-left"></i>',
                        next: '<i class="fa fa-chevron-right"></i>',
                    },
                },
                columns: [
                    { data: 'player_photo', className: "avatar",
                    orderable: false,
                    render: function(data) {
                        if(data !== null && data !== ''){
                            return '<img src="' + PRODUCT_IMAGE_PATH + data + '">';
                        }else{
                            return '<img src="' + SYSTEM_IMAGE_PATH + 'favicon.png">';
                        }
                    }  },
                    { data: 'player_name' },
                    { data: 'chinese_name' },
                    { data: 'approval_status' },
                ],
                select: {
                    style: SELECT_MODE,
                    selector: 'td:first-child',
                },
                buttons: [
                    
                ]
            });
        }
    }
);