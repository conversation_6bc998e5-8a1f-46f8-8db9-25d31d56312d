let GLOBAL_SCOPE = {};
app.controller(
    'paymentsSummerSchemeCtrl',
    function ($scope, $rootScope, $routeParams, $http) {
        var event_id = $routeParams.id;
        $scope.event_id = event_id;
        GLOBAL_SCOPE = $scope;

        jQuery.ajax({
            type: 'POST',
            url: SERVER_PATH + 'event/getEventInfo',
            async: false,
            headers: {
                'x-user-id': $rootScope.user_id,
                'x-user-email': $rootScope.user_name,
            },
            data: {
                event_id: event_id,
            },
            dataType: 'json',
            complete: function (response) {
                var jsonData = JSON.parse(response.responseText);
                var event = jsonData.info;
                $scope.event_name = event.name;
                $scope.event_type = event.type;
                $scope.normalizedType = normalizeEventType(event.type);
            },
        });

        if ($.fn.daterangepicker) {
            $('input[name="dateFilter"]').daterangepicker('destroy');
        }

        // setTimeout(() => {
        //     $('#paymentDetail').modal('show');
        // }, 400);

        $scope.switchTab = function (tabId) {
            const tabs = document.querySelectorAll('.modal-tab');
            const tabContents = document.querySelectorAll('.tab-content');
            tabs.forEach((tab) => {
                tab.classList.remove('active');
            });

            tabContents.forEach((tabContent) => {
                tabContent.classList.remove('active');
            });

            const tab = document.querySelector(`[data-tabId="${tabId}"]`);
            const tabContent = document.querySelector(`[data-tab="${tabId}"]`);

            tab.classList.add('active');
            tabContent.classList.add('active');
        };
        
        function normalizeEventType(eventType) {
            switch (eventType) {
                case 'Summer Scheme':
                    return 'summer-scheme';
                case 'Regional':
                    return 'regional';
                case 'PL Junior':
                    return 'pl-junior';
                case 'Golden Age':
                    return 'golden-age';
                case 'Beginner':
                    return 'beginner';
                case 'District':
                    return 'district';
                default:
                    return '';
            }
        }
        $('input[name="dateFilter"]').daterangepicker({
            startDate: moment().startOf('month'),
            endDate: moment().endOf('month'),
            locale: {
                format: 'DD/MM/YYYY',
            },
            maxSpan: {
                days: 90,
            },
            ranges: {
                Yesterday: [
                    moment().subtract(1, 'days'),
                    moment().subtract(1, 'days'),
                ],
                Today: [moment(), moment()],
                'Last week': [
                    moment().subtract(1, 'week').startOf('week').add(1, 'days'),
                    moment().subtract(1, 'week').endOf('week').add(1, 'days'),
                ],
                'This week': [
                    // start week on monday
                    moment().startOf('week').add(1, 'days'),
                    moment().endOf('week').add(1, 'days'),
                ],
                'Last Month': [
                    moment().subtract(1, 'month').startOf('month'),
                    moment().subtract(1, 'month').endOf('month'),
                ],
                'This Month': [
                    moment().startOf('month'),
                    moment().endOf('month'),
                ],
            },
        });

        $('input[name="dateFilter"]').on(
            'apply.daterangepicker',
            function (ev, picker) {
                console.log('date change');
                $scope.start_date = picker.startDate.format('YYYY-MM-DD');
                $scope.end_date = picker.endDate.format('YYYY-MM-DD');

                initPaymentTable();
            }
        );

        // Date range picker
        $scope.start_date = $('input[name="dateFilter"]')
            .data('daterangepicker')
            .startDate.format('YYYY-MM-DD');
        $scope.end_date = $('input[name="dateFilter"]')
            .data('daterangepicker')
            .endDate.format('YYYY-MM-DD');

        setTimeout(() => {
            initPaymentTable();
        }, 400);

        initPaymentTable = function () {
            $('#paymentTable_' + event_id)
                .DataTable()
                .destroy();

            $scope.table = $('#paymentTable_' + event_id).DataTable({
                dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
                stateSave: true,
                deferRender: true,
                ajax: {
                    url: SERVER_PATH + 'payment/getSummerSchemePayment',
                    type: 'POST',
                    headers: {
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name,
                    },
                    data: {
                        event_id: event_id,
                        start_date: $scope.start_date,
                        end_date: $scope.end_date,
                    },
                    dataType: 'json',
                    complete: function (response) {},
                    error: function (xhr, status, error) {},
                },
                language: {
                    info: 'Showing _START_ to _END_ of _TOTAL_ payment',
                    infoEmpty: 'Showing 0 to 0 of 0 payments',
                    lengthMenu: 'Show _MENU_ payments',
                    select: {
                        rows: {
                            _: 'You have selected %d payments',
                            0: 'Click a payment to select',
                            1: '1 payment selected',
                        },
                    },
                    paginate: {
                        previous: '<i class="fa fa-chevron-left"></i>',
                        next: '<i class="fa fa-chevron-right"></i>',
                    },
                },
                columns: [
                    {
                        data: 'invoices.invoice_number',
                        visible: false,
                    },
                    {
                        data: null,
                        render: function (data, type, row) {
                            return (
                                data.parens.surname +
                                ' ' +
                                data.parens.other_name
                            );
                        },
                    },
                    {
                        data: null,
                        render: function (data, type, row) {
                            return (
                                data.parens.country_code +
                                ' ' +
                                data.parens.phone
                            );
                        },
                    },
                    {
                        data: 'parens.email',
                    },
                    {
                        data: 'invoices.invoice_identification',
                    },
                    {
                        data: 'invoices.invoice_date',
                    },
                    {
                        data: 'invoices.amount',
                    },
                    {
                        data: 'invoices.status',
                        render: function (data, type, row, meta) {
                            if (
                                data
                                    .toLowerCase()
                                    .includes(STATUS_REFUND.toLowerCase())
                            ) {
                                return (
                                    '<span class="badge badge-info">' +
                                    data +
                                    '</span>'
                                );
                            }

                            if (
                                data
                                    .toLowerCase()
                                    .includes(
                                        STATUS_PARTIAL_REFUND.toLowerCase()
                                    ) ||
                                data
                                    .toLowerCase()
                                    .includes(
                                        STATUS_REQUEST_PARTIAL_REFUND.toLowerCase()
                                    )
                            ) {
                                return (
                                    '<span class="badge badge-info">' +
                                    data +
                                    '</span>'
                                );
                            }

                            switch (data) {
                                case STATUS_SUCCEEDED:
                                    return (
                                        '<span class="badge badge-success">' +
                                        data +
                                        '</span>'
                                    );
                                    break;
                                case STATUS_PROCESSING:
                                    return (
                                        '<span class="badge badge-warning">' +
                                        data +
                                        '</span>'
                                    );
                                    break;
                                case STATUS_REQUEST_REFUND:
                                    return (
                                        '<span class="badge badge-danger">' +
                                        data +
                                        '</span>'
                                    );
                                    break;
                                default:
                                    return (
                                        '<span class="badge badge-light">' +
                                        data +
                                        '</span>'
                                    );
                                    break;
                            }
                        },
                    },
                    {
                        data: 'invoices.type',
                        render: function (data) {
                            if (data == 'change_shipping') {
                                return 'Change Shipping';
                            } else if (data == 'registration') {
                                return 'Registration';
                            } else if (data == 'add_parent') {
                                return 'Add Parent';
                            } else {
                                return 'Unknown';
                            }
                        },
                    },
                    {
                        data: 'invoices.id',
                        render: function (data, type, row) {
                            let payRef = row.invoices.invoice_identification;
                            const invoice_id = row.invoices.id;
                            // button show payment detail
                            let user_id = $rootScope.user_id;
                            var refundButton = `<button class="btn btn-info btn-sm" data-toggle="modal" data-target="#paymentDetailModal" onclick="showPaymentDetail('${data}', ${user_id}, ${payRef})">Refund</button>`;
                            var detailButton = `<button class="btn btn-primary btn-sm" data-toggle="modal" data-target="#paymentDetailModal" onclick="showPaymentItems(${invoice_id})">Detail</button>`;
                            return (
                                '<div style=" display: flex; align-content: center; align-items: center; gap: 10px; ">' +
                                detailButton +
                                refundButton +
                                '</div>'
                            );
                        },
                    },
                ],
                initComplete: function () {
                    // reset html
                    $('#payment-type_filter').html('');

                    var payment_type_collunm = {
                        orderColumn: 8,
                        elementId: 'payment-type_filter',
                        selectId: 'selType',
                    };

                    filterColumns = [payment_type_collunm];

                    filterColumns.forEach((item, index) => {
                        this.api()
                            .columns(item.orderColumn)
                            .every(function () {
                                var column = this;

                                var select = $(
                                    `<select id="${item.orderColumn}" class="form-control"><option value="">All</option></select>`
                                )
                                    .appendTo($(`#${item.elementId}`))
                                    .on('change', function () {
                                        var val =
                                            $.fn.dataTable.util.escapeRegex(
                                                // change '_' to ' '
                                                $(this).val()
                                            );

                                        column
                                            .search(
                                                val ? val : '',
                                                true,
                                                false,
                                                false
                                            )
                                            .draw(false);
                                    })
                                    .select2();
                                var column_data = column.data();
                                var select_data = [];
                                column_data.map((item) => {
                                    if (item != null) {
                                        item.indexOf(', ') > 0
                                            ? (item = item.split(', '))
                                            : (item = [item]);
                                        item.forEach((item) => {
                                            select_data.push(
                                                item.replace('_', ' ')
                                            );
                                        });
                                    }
                                });

                                select_data
                                    .filter(onlyUnique)
                                    .sort()
                                    .map(function (d, j) {
                                        console.warn(d);
                                        if (index == 0) {
                                            select.append(
                                                // lower case and upper case first character
                                                `<option value="${d.replace(
                                                    /\b\w/g,
                                                    (l) => l.toUpperCase()
                                                )}">${d.replace(/\b\w/g, (l) =>
                                                    l.toUpperCase()
                                                )}</option>`
                                            );
                                        }
                                    });
                            });
                    });
                },
                select: {
                    style: SELECT_MODE,
                },
                order: [[0, 'asc']],
                lengthMenu: [
                    [10, 25, 50, 100, -1],
                    [10, 25, 50, 100, 'All'],
                ],
                buttons: [
                    {
                        extend: 'collection',
                        text: 'Actions <i class="fa fa-angle-down"></i>',
                        className: 'btn btn-primary',
                        autoClose: true,
                        buttons: [
                            {
                                text: '<i class="fa fa-refresh"></i> Sync Payment Status',
                                extend: 'selectedSingle',
                                action: function () {
                                    var rows_selected = $scope.table
                                        .rows({ selected: true })
                                        .data()[0];
                                    var payRef =
                                        rows_selected.invoices
                                            .invoice_identification;
                                    var amt = rows_selected.invoices.amount;
                                    var status = rows_selected.invoices.status;
                                    var invoice_id = rows_selected.invoices.id;
                                    console.log('payRef: ' + payRef);
                                    console.log('amount: ' + amt);
                                    console.log('invoice_id: ' + invoice_id);
                                    if (payRef != null) {
                                        console.log('sync payment');
                                        syncPaymentStatus(
                                            payRef,
                                            invoice_id,
                                            $scope.table,
                                            $rootScope.user_id,
                                            $rootScope.user_name
                                        );
                                    }
                                },
                            },
                            // {
                            //     text: '<i class="fa fa-undo"></i> Refund Payment',
                            //     extend: 'selectedSingle',
                            //     action: function () {
                            //         var rows_selected = table
                            //             .rows({ selected: true })
                            //             .data()[0];
                            //         var payRef =
                            //             rows_selected.invoices
                            //                 .invoice_identification;
                            //         var amt = rows_selected.invoices.amount;
                            //         var status = rows_selected.invoices.status;
                            //         var invoice_id =
                            //             rows_selected.invoices.invoice_number;
                            //         console.log('payRef: ' + payRef);
                            //         console.log('amount: ' + amt);
                            //         console.log('invoice_id: ' + invoice_id);
                            //         if (payRef != null) {
                            //             initModalRefund(amt, payRef, table);
                            //         } else if (
                            //             status.toLowerCase() ==
                            //             STATUS_OFFLINE_REGISTRATION.toLowerCase()
                            //         ) {
                            //             BootstrapDialog.show({
                            //                 title: 'WARNING',
                            //                 message:
                            //                     'This payment is offline registration',
                            //                 type: BootstrapDialog.TYPE_WARNING,
                            //                 buttons: [
                            //                     {
                            //                         label: 'Close',
                            //                         action: function (dialog) {
                            //                             dialog.close();
                            //                         },
                            //                     },
                            //                 ],
                            //             });
                            //         } else {
                            //             BootstrapDialog.show({
                            //                 title: 'WARNING',
                            //                 message:
                            //                     'No payment reference found',
                            //                 type: BootstrapDialog.TYPE_WARNING,
                            //                 buttons: [
                            //                     {
                            //                         label: 'Close',
                            //                         action: function (dialog) {
                            //                             dialog.close();
                            //                         },
                            //                     },
                            //                 ],
                            //             });
                            //         }
                            //     },
                            // },
                        ],
                    },
                    {
                        extend: 'excel',
                        name: 'excel',
                        text: '<i class="fa fa-file-excel-o"></i>&emsp;Export to Excel',
                        titleAttr: 'Export data to an Excel file',
                        filename: `HKFA Grassroots - ${$scope.event_type} ${$scope.event_name} - Payments`,
                        exportOptions: {
                            columns: ':visible',
                            modifier: {
                                autoFilter: true,
                                // selected: true
                            },
                        },
                    },
                    {
                        extend: 'colvis',
                        text: 'Columns',
                    },
                ],
            });
        };

        showPaymentItems = async function (id) {
            async function getInvoiceDetail(id) {
                return new Promise((resolve, reject) => {
                    $.ajax({
                        url: SERVER_PATH + 'payment/getInvoiceDetail',
                        type: 'POST',
                        data: {
                            invoice_id: id,
                        },
                        dataType: 'json',
                        // show loading
                        beforeSend: function () {
                            Swal.fire({
                                title: 'Loading...',
                                allowOutsideClick: false,
                                onBeforeOpen: () => {
                                    Swal.showLoading();
                                },
                            });
                        },
                        success: function (response) {
                            Swal.close();
                            if (response.status == 'OK') {
                                const { data } = response;
                                resolve(data);
                            } else {
                                Swal.fire({
                                    title: 'Error!',
                                    text: response.message,
                                    icon: 'error',
                                    confirmButtonText: 'OK',
                                });
                                reject();
                            }
                        },
                        error: function (xhr, status, error) {
                            Swal.close();
                            console.error(
                                'Error fetching invoice details:',
                                error
                            );
                            reject();
                        },
                    });
                });
            }

            const response = await getInvoiceDetail(id);

            const { invoice_info, invoice_details, invoice_detailS_refunded } =
                response;

            // status color mapping
            const statusColor = {
                Accepted: '#28C76F',
                Pending: '#FF9F43',
                Rejected: '#EA5455',
                Refunded: '#FF9F43',
                RequestPartialRefund: '#9c27b0',
                RequestRefund: '#ed1c24',
            };

            invoiveInfoHtml =
                `
            <div class="invoice-detail-left" style="padding: 16px 20px; display: flex; flex-direction: column; flex: 0 0 250px; background-color: #F3F3F3; border-radius: 8px;">
        <div class="invoice-date" style="display: flex; flex-direction: column; gap: 8px;">
          <label style="font-size: 13px; font-weight: 400; color: #667085; font-family: 'Inter', serif;">Payment
            Date</label>
          <span style="font-size: 15px; font-weight: 600; color: #333843; font-family: 'Inter', serif;">` +
                invoice_info.formatted_date +
                `</span>
        </div>
        <div class="invoice-status" style="display: flex; flex-direction: column; gap: 8px; margin-top: 16px;">
          <label style="font-size: 13px; font-weight: 400; color: #667085; font-family: 'Inter', serif;">Payment
            Status</label>
          <span class="payment-status" style="background-color: ` +
                statusColor[invoice_info.status] +
                `; color: white; border-radius: 4px; display: inline-block; width: fit-content; padding: 5px 10px;">` +
                invoice_info.status +
                `</span>
        </div>
        <div class="invoice-id" style="display: flex; flex-direction: column; gap: 8px; margin-top: 16px;">
          <label style="font-size: 13px; font-weight: 400; color: #667085; font-family: 'Inter', serif;">Invoice
            ID</label>
          <span style="font-size: 12px; font-weight: 600; background-color: #F4F5F6; border-radius: 8px;">` +
                invoice_info.invoice_number +
                `</span>
        </div>
      </div>
      <div class="invoice-detail-right" style="padding: 21px 8px;display: flex;flex-direction: column;justify-content: center;gap: 8px;flex: 1;align-items: flex-start;width: 100%;">
        <span class="parent-name" style="font-size: 16px; font-weight: 600;">` +
                invoice_info.parent_name +
                `</span>
        <div class="parent-email" style="font-size: 13px; font-weight: 600; color: #667085; display: flex; gap: 4px; align-items: flex-end;">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6" style="width: 18px; height: 18px; stroke: #667085;">
            <path stroke-linecap="round" stroke-linejoin="round" d="M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75"></path>
          </svg>
          <span>` +
                invoice_info.email +
                `</span>
        </div>
        <div class="parent-phone" style="font-size: 13px;font-weight: 600;color: #667085;display: flex;gap: 4px;align-items: flex-end;width: 100%;">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6" style="width: 18px;height: 18px;stroke: #667085;flex: 0 0 18px;">
            <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z"></path>
          </svg>
          <span style="
  line-height: 1;
">` +
                invoice_info.parent_phone +
                `</span>
        </div>
        <div class="total-amount" style="display: flex; justify-content: space-between; align-items: center; align-self: stretch">
            <span style="display: flex; width: 90px; flex-direction: column;justify-content: flex-end; align-self: stretch; color: #667085; font-family: Inter; font-size: 13px; font-style: normal; font-weight: 500; line-height: 132%; letter-spacing: 0.065px;">Total Payment</span>
            <span style="color:  #333843; text-align: right;  font-family: Inter; font-size: 20px; font-style: normal; font-weight: 700; line-height: normal; letter-spacing: 0.1px;">` +
                invoice_info.amount +
                `$</span>
        </div>
      </div>`;

            console.warn(invoice_details);

            let invoiceDetailHtml = ``;

            invoice_details.forEach((item) => {
                if (item.item_type == 'Course') {
                    const [course_name, player_name, itemType] =
                        item.item_name.split(' - ');
                    invoiceDetailHtml +=
                        `<div class="invoice-detail-item" style="font-size: 14px; position: relative;">
          <div class="course-name" style="padding: 12px 16px; color: #667085; background-color: #f4f4f4;"> ` +
                        course_name +
                        `
          </div>
          <div class="player-item" style="padding: 12px 16px; background-color: white; border-bottom: 0.5px solid #E0E2E7; position: relative;">
            <div class="player-name" style="font-size: 15px; font-weight: 500; color: #333843;">` +
                        player_name +
                        `</div>
            <div class="player-role" style="color: #667085; font-size: 14px; font-weight: 500; margin-top: 4px;">
              ` +
                        itemType +
                        `
            </div>
            <div class="player-price" style="position: absolute; right: 16px; top: 50%; transform: translateY(-50%); font-size: 15px; font-weight: 600; color: #333843;">
              ` +
                        item.price +
                        `$</div>
          </div>
        </div>`;
                }
            });
            invoice_details.forEach((item) => {
                if (item.item_type == 'Shipping') {
                    invoiceDetailHtml +=
                        `
        <div class="invoice-detail-item" style="font-size: 14px; position: relative;">
          <div class="shipping-fee" style="padding: 12px 16px; background-color: #f4f4f4; font-size: 15px; font-weight: 600; color: #667085; text-transform: uppercase;">
            Shipping fee</div>
          <div class="total-price" style="position: absolute; right: 16px; top: 50%; transform: translateY(-50%); font-size: 15px; font-weight: 600; color: #333843;">
            ` +
                        item.price +
                        `$</div>
        </div>`;
                }
            });

            var invoiveRefundedHtml = `
            <div class="refund-details" style="margin-top: 32px; font-family: 'Inter', serif; border-radius: 4px; padding: 12px; border: 1px solid #CFCFCF; font-size: 14px;">`;

            if (invoice_detailS_refunded.length == 0) {
                invoiveRefundedHtml = ` <div class="refund-details" style="margin-top: 32px; font-family: 'Inter', serif; border-radius: 4px; padding: 12px; text-align:center; font-size: 14px;">
                                        No refunds have been made yet
                                    </div>`;
            } else {
                const { metadata } = invoice_info;

                const refundOject = JSON.parse(metadata);

                const refundInfo = refundOject.refund_history[0];

                const refundReason_html =
                    `<div style="padding:6px; background-color: #f8f7f7;margin-bottom: 32px;border-radius: 8px;"><table class="refund-reason" style="width: 100%;">
                <tbody>
                    <tr>
                    <td style="font-weight: 600; color: #000; padding-right: 6px; text-wrap: nowrap;">Refund reason: </td>
                    <td style="padding: 4px 0;">` +
                    (refundInfo.reason ? refundInfo.reason : '') +
                    `</td>
                    </tr>
                    <tr>
                    <td style="font-weight: 600; color: #000; padding-right: 6px;">Refund by: </td>
                    <td style="padding: 4px 0;">Refund by: ` +
                    refundInfo.refund_by +
                    `</td>
                    </tr>
                    <tr>
                    <td style="font-weight: 600; color: #000; padding-right: 6px;">Refund date: </td>
                    <td style="padding: 4px 0;">` +
                    refundInfo.date +
                    `</td>
                    </tr>
                    <tr>
                    <td style="font-weight: 600; color: #000; padding-right: 6px;">Refund amount: </td>
                    <td style="padding: 4px 0;">` +
                    $scope.floatValue(refundInfo.amount) +
                    `$</td>
                    </tr>
                </tbody>
                </table></div>`;

                var refundItemsHtml = '';

                invoice_detailS_refunded.forEach((item) => {
                    if (item.item_type == 'Course') {
                        const [course_name, player_name, itemType] =
                            item.item_name.split(' - ');
                        refundItemsHtml +=
                            `<div class="invoice-detail-item" style="font-size: 14px; position: relative;">
              <div class="course-name" style="padding: 12px 16px; color: #667085; background-color: #f4f4f4;"> ` +
                            course_name +
                            `
              </div>
              <div class="player-item" style="padding: 12px 16px; background-color: white; border-bottom: 0.5px solid #E0E2E7; position: relative;">
                <div class="player-name" style="font-size: 15px; font-weight: 500; color: #333843;">` +
                            player_name +
                            `</div>
                <div class="player-role" style="color: #667085; font-size: 14px; font-weight: 500; margin-top: 4px;">
                  ` +
                            itemType +
                            `
                </div>
                <div class="player-price" style="position: absolute; right: 16px; top: 50%; transform: translateY(-50%); font-size: 15px; font-weight: 600; color: #333843;">
                  ` +
                            $scope.floatValue(item.price) +
                            `$</div>
              </div>
            </div>`;
                    }
                });
                invoice_detailS_refunded.forEach((item) => {
                    if (item.item_type == 'Shipping') {
                        refundItemsHtml +=
                            `
            <div class="invoice-detail-item" style="font-size: 14px; position: relative;">
              <div class="shipping-fee" style="padding: 12px 16px; background-color: #f4f4f4; font-size: 15px; font-weight: 600; color: #667085; text-transform: uppercase;">
                Shipping fee</div>
              <div class="total-price" style="position: absolute; right: 16px; top: 50%; transform: translateY(-50%); font-size: 15px; font-weight: 600; color: #333843;">
                ` +
                            $scope.floatValue(item.price) +
                            `$</div>
            </div>`;
                    }
                });

                invoiveRefundedHtml +=
                    refundReason_html +
                    refundItemsHtml +
                    `
                </div>
                </div>
            </div>`;
            }

            $('#invoiceDetail').html(invoiveInfoHtml);

            $('#invoiceDetailItems').html(invoiceDetailHtml);

            $('#refundDetails').html(invoiveRefundedHtml);

            $('#paymentDetail').modal('show');
        };

        $scope.closePaymentDetail = function () {
            $('#paymentDetail').modal('hide');
        };

        showPaymentDetail = async (id, user_id, payRef) => {
            async function getPaymentDetailForRefund(id) {
                return new Promise((resolve, reject) => {
                    $.ajax({
                        url: SERVER_PATH + 'payment/getPaymentDetailForRefund',
                        type: 'POST',
                        data: {
                            invoice_id: id,
                        },
                        dataType: 'json',
                        // show loading
                        beforeSend: function () {
                            Swal.fire({
                                title: 'Loading...',
                                allowOutsideClick: false,
                                onBeforeOpen: () => {
                                    Swal.showLoading();
                                },
                            });
                        },
                        success: function (response) {
                            Swal.close();
                            if (response.status == 'OK') {
                                const { data, invoice_type } = response;

                                switch (invoice_type) {
                                    case 'registration':
                                        $scope.can_cancel_registration = true;
                                        $scope.can_cancel_shipping = true;
                                        break;

                                    case 'change_shipping':
                                        $scope.can_cancel_registration = false;
                                        $scope.can_cancel_shipping = true;
                                        break;
                                    case 'add_parent':
                                        $scope.can_cancel_registration = true;
                                        $scope.can_cancel_shipping = false;
                                        break;
                                    default:
                                        console.warn(
                                            'Unknown invoice type:',
                                            invoice_type
                                        );
                                }

                                // find all input checkbox name='cancelRegistration'
                                const cancelRegistration =
                                    document.querySelectorAll(
                                        'input[name="cancelRegistration"]'
                                    );

                                // if invoice type is not registration, disable all cancelRegistration checkbox
                                if (!$scope.can_cancel_registration) {
                                    cancelRegistration.forEach((item) => {
                                        item.disabled = true;
                                    });
                                } else {
                                    cancelRegistration.forEach((item) => {
                                        item.disabled = false;
                                    });
                                }

                                // find all input checkbox name='cancelShipping'
                                const cancelShipping =
                                    document.querySelectorAll(
                                        'input[name="cancelShipping"]'
                                    );

                                // if invoice type is not change_shipping, disable all cancelShipping checkbox

                                if (!$scope.can_cancel_shipping) {
                                    cancelShipping.forEach((item) => {
                                        item.disabled = true;
                                    });
                                } else {
                                    cancelShipping.forEach((item) => {
                                        item.disabled = false;
                                    });
                                }

                                const rowGroup = [];

                                data.forEach((item) => {
                                    if (
                                        rowGroup.indexOf(item.rowGroup) === -1
                                    ) {
                                        rowGroup.push(item.rowGroup);
                                    }
                                });

                                resolve(renderRefundHtml(data, rowGroup, id));
                            } else {
                                Swal.fire({
                                    title: 'Error!',
                                    text: response.message,
                                    icon: 'error',
                                    confirmButtonText: 'OK',
                                });

                                reject();
                            }
                        },
                        error: function (xhr, status, error) {
                            Swal.close();
                            console.error(
                                'Error fetching payment details:',
                                error
                            );
                            reject();
                        },
                    });
                });
            }

            const response = await getPaymentDetailForRefund(id);

            // inner html to #userSelection
            $('#userSelection').html(response);

            $scope.formData = {
                selectedUsers: [],
                reason: '',
                shippingFee: null,
                cancelRegistration: 'No',
                cancelShipping: 'No',
            };

            $scope.validationMessages = {
                selectedUsers: '',
                reason: '',
                cancelRegistration: '',
                cancelShipping: '',
            };

            $('#refundForm').trigger('reset');

            $scope.$apply();

            $('#refundModal').modal('show');
        };

        function onlyUnique(value, index, self) {
            return self.indexOf(value) === index;
        }

        $scope.checkValidation = function (item) {
            if ($scope.validationMessages) {
                return $scope.validationMessages[item] != '';
            } else {
                return true;
            }
        };

        // ...existing code...

        $scope.closeModal = function () {
            $('#refundModal').modal('hide');
        };

        $scope.validateForm = function () {
            let isValid = true;
            $scope.validationMessages = {
                selectedUsers: '',
                reason: '',
                shippingFee: '',
                cancelRegistration: '',
                cancelShipping: '',
            };

            // Validate user selection
            const selectedUsers = angular.element(
                'input[name="refundUser"]:checked'
            );
            if (selectedUsers.length === 0) {
                isValid = false;
                $scope.validationMessages.selectedUsers =
                    'Please select at least one user.';
            }

            // Validate reason
            if (!$scope.formData.reason.trim()) {
                isValid = false;
                $scope.validationMessages.reason =
                    'Please enter a reason for the refund.';
            }

            // Validate shipping fee if checkbox is checked
            const shippingFeeCheckbox = angular
                .element('#shippingFeeCheckbox')
                .is(':checked');
            if (shippingFeeCheckbox) {
                const shippingFee = parseFloat(
                    angular.element('#shippingFee').val()
                );
                if (isNaN(shippingFee) || shippingFee < 0) {
                    isValid = false;
                    $scope.validationMessages.selectedUsers =
                        'Please enter a valid shipping fee.';
                }

                // get atrtribute max
                let temp_shippingFee = parseFloat(
                    angular.element('#shippingFee').attr('max')
                );

                if (shippingFee > temp_shippingFee) {
                    isValid = false;
                    $scope.validationMessages.selectedUsers =
                        'Shipping fee cannot be greater than the maximum shipping fee.';
                }
            }

            // Validate cancel registration
            if (!$scope.formData.cancelRegistration) {
                isValid = false;
                $scope.validationMessages.cancelRegistration =
                    'Please select whether to cancel registration.';
            }

            // Validate cancel shipping
            if (!$scope.formData.cancelShipping) {
                isValid = false;
                $scope.validationMessages.cancelShipping =
                    'Please select whether to cancel shipping.';
            }

            return isValid;
        };

        $scope.submitRefund = function () {
            // Perform validation
            if ($scope.validateForm()) {
                const data = [];

                $scope.formData.selectedUsers = [];
                angular
                    .element('input[name="refundUser"]:checked')
                    .each(function () {
                        // get input next to label
                        const nextElement = angular
                            .element(this)
                            .next()
                            .find('input');

                        const id = nextElement.attr('data-id');

                        const amount = parseFloat(nextElement.val());

                        data.push({
                            id,
                            amount,
                            type: nextElement.attr('data-type'),
                        });
                    });

                const isCheckCancelRegistration =
                    $scope.formData.cancelRegistration;
                const isCheckCancelShipping = $scope.formData.cancelShipping;
                const invoice_id = $('#refundForm').attr('data-invoice-id');

                const reason = $scope.formData.reason;

                refundDetailUpdate(
                    data,
                    invoice_id,
                    user_id,
                    reason,
                    isCheckCancelRegistration,
                    isCheckCancelShipping
                );
            }
        };

        $scope.floatValue = function (value) {
            return parseFloat(value).toFixed(2);
        };
    }
);

const refundDetailUpdate = function (
    detail_ids,
    invoice_id,
    user_id,
    reason,
    is_check_cancel_registration,
    is_check_cancel_shipping
) {
    $.ajax({
        url: SERVER_PATH + 'payment/partialRefundUpdate',
        type: 'POST',
        async: true,
        data: {
            detail_ids: JSON.stringify(detail_ids),
            invoice_id: invoice_id,
            user_id: user_id,
            reason: reason,
            is_check_cancel_registration: is_check_cancel_registration, // Fixed typo
            is_check_cancel_shipping: is_check_cancel_shipping,
        },
        beforeSend: function () {
            Swal.fire({
                title: 'Loading...',
                allowOutsideClick: false,
                onBeforeOpen: () => {
                    Swal.showLoading();
                },
            });
        },
        dataType: 'json',
        complete: function (response) {
            Swal.close();
            console.log('AJAX complete response:', response);
            try {
                var jsonData = JSON.parse(response.responseText);
                if (jsonData.status == 'OK') {
                    BootstrapDialog.closeAll();
                    let message = '';
                    if (typeof jsonData.message == 'object') {
                        jsonData.message.forEach((item) => {
                            message += `<p class="text-capitalize"><b>${item.type} status</b>: <span>${item.message}</span> </p>`;
                        });
                    } else {
                        message = jsonData.message;
                    }
                    BootstrapDialog.show({
                        title: 'SUCCESS',
                        type: BootstrapDialog.TYPE_SUCCESS,
                        message: message,
                        onhide: function (dialog) {
                            $('#refundModal').modal('hide');

                            // reset form
                            $('#refundForm').trigger('reset');

                            GLOBAL_SCOPE.formData = {
                                selectedUsers: [],
                                reason: '',
                                shippingFee: null,
                                cancelRegistration: null,
                                cancelShipping: null,
                            };
                        },
                        onhidden: function (dialogRef) {
                            $('#refundModal').modal('hide');
                            $('#refundForm').trigger('reset');

                            GLOBAL_SCOPE.formData = {
                                selectedUsers: [],
                                reason: '',
                                shippingFee: null,
                                cancelRegistration: null,
                                cancelShipping: null,
                            };
                        },
                    });
                    GLOBAL_SCOPE.table.ajax.reload();
                } else {
                    BootstrapDialog.show({
                        title: 'ERROR',
                        message: jsonData.message,
                        type: BootstrapDialog.TYPE_DANGER,
                        buttons: [
                            {
                                label: 'Close',
                                action: function (dialog) {
                                    dialog.close();
                                },
                            },
                        ],
                        onhide: function (dialog) {
                            // $('#refundModal').modal('hide');
                            // $('#refundForm').trigger('reset');
                            // GLOBAL_SCOPE.formData = {
                            //     selectedUsers: [],
                            //     reason: '',
                            //     shippingFee: null,
                            //     cancelRegistration: null,
                            //     cancelShipping: null,
                            // };
                        },
                        onhidden: function (dialogRef) {
                            // $('#refundModal').modal('hide');
                            // $('#refundForm').trigger('reset');
                            // GLOBAL_SCOPE.formData = {
                            //     selectedUsers: [],
                            //     reason: '',
                            //     shippingFee: null,
                            //     cancelRegistration: null,
                            //     cancelShipping: null,
                            // };
                        },
                    });
                }
            } catch (e) {
                console.error('Error parsing JSON response:', e);
            }
        },
        error: function (xhr, status, error) {
            console.error('AJAX error:', xhr, status, error);
        },
    });
};

syncPaymentStatus = function (payRef, invoice_id, table, user_id, user_name) {
    $.ajax({
        url: SERVER_PATH + 'payment/syncPaymentStatus',
        type: 'POST',
        async: true,
        headers: {
            'x-user-id': user_id,
            'x-user-email': user_name,
        },
        data: {
            payRef: payRef,
            invoice_id: invoice_id,
        },
        dataType: 'json',
        complete: function (response) {
            console.log(response);
            if (response.responseJSON.status == 'OK') {
                BootstrapDialog.show({
                    title: 'SUCCESS',
                    message: response.responseJSON.message,
                    type: BootstrapDialog.TYPE_SUCCESS,
                    buttons: [
                        {
                            label: 'Close',
                            action: function (dialog) {
                                dialog.close();
                            },
                        },
                    ],
                });
                table.ajax.reload();
            } else {
                BootstrapDialog.show({
                    title: 'ERROR',
                    message: response.responseJSON.message,
                    type: BootstrapDialog.TYPE_DANGER,
                    buttons: [
                        {
                            label: 'Close',
                            action: function (dialog) {
                                dialog.close();
                            },
                        },
                    ],
                });
            }
        },
        error: function (xhr, status, error) {},
    });
};

const renderRefundHtml = (data, rowGroup, invoice_id) => {
    // add setAttribute to data #refundForm
    $('#refundForm').attr('data-invoice-id', invoice_id);

    let refundHtml = '';
    rowGroup.forEach((row) => {
        refundHtml += `<label class="form-label fw-bold" style="color: #6F6F6F;font-weight: bold; font-size:13px">${row}</label>`;

        rowGroupData = data.filter((item) => item.rowGroup === row);
        rowGroupData.forEach((item) => {
            const type = item.type.replace('_', ' ');

            const { metadata } = item;

            let refundText = '';

            if (metadata) {
                switch (metadata.status) {
                    case 'RequestRefund':
                        refundText = 'Request Refund';
                        break;
                    case 'PartialRefund':
                        refundText = 'Partial Refund';
                        break;
                    case 'Refunded':
                        refundText = 'Refunded';
                        break;
                    case 'PartialRefunded':
                        refundText = 'Refunded';
                        break;
                    default:
                        refundText = '';
                        break;
                }

                refundText = `(${refundText} by ${metadata.refund_by})`;
            }

            if (item.type === 'shipping') {
                refundHtml +=
                    `<div class="form-check" style="display: flex; gap: 12px;">
                                    <input class="form-check-input" type="checkbox" id="shippingFeeCheckbox" name="refundUser">
                                    <div style="display: flex; justify-content: space-between; align-items: center; flex: 1;">
                                        <label for="shippingFee" class="form-label" style="font-weight: 600">Shipping Fee` +
                    refundText +
                    `:</label> <div class="price" style="display: flex; justify-content: space-between; align-items: center; gap: 4px; font-size: 14px; width: 40px;">
                                        <input data-type='${type}' type="number" class="form-control" id="shippingFee" min="0" max="` +
                    parseInt(item.amount) +
                    `" value="` +
                    parseInt(item.amount) +
                    `"
                                         style="flex: 40px 0 0;" ` +
                    (item.can_refund ? '' : 'disabled') +
                    ` data-id="${item.id}">$
                                    </div></div>
                                </div>`;
            } else {
                const [course_area, course_id, player_name, registration_type] =
                    item.fee.split('-');

                console.warn(
                    course_area,
                    course_id,
                    player_name,
                    registration_type
                );

                refundHtml +=
                    `
                                    <div class="form-check" style="display: flex; gap: 12px; margin-top:5px">
                                        <input  data-type='${type}' class="form-check-input" type="checkbox" id="${item.id}" value="${item.amount}" name="refundUser"` +
                    (item.can_refund ? '' : 'disabled') +
                    `>
                         <div style="display: flex; justify-content: space-between; align-items: flex-start; flex: 1;">
                                        <div style="display: flex; flex-direction: column;">
                                            <label  style="font-weight: 600" class="form-check-label" for="${item.id}">${player_name} ${refundText}</label>
                                            <span style="font-size: 12px; font-style: normal; font-weight: 400; line-height: 18px;color: #6F6F6F">${registration_type}</span>
                                        </div>
                                        <input type="hidden" name="type" value="${item.amount}" data-id="${item.id}" data-type="${type}">

                                        <span style="font-weight: 500">${item.amount}$</span>
                                    </div>
                                    </div>`;
            }
        });
    });
    return refundHtml;
};

refundDetails = (detail_ids, payRef, amount, remaining_shipping, user_id) => {
    $.ajax({
        url: SERVER_PATH + 'payment/partialRefund',
        type: 'POST',
        async: true,
        data: {
            detail_ids: detail_ids,
            payRef: payRef,
            amount: amount,
            remaining_shipping: remaining_shipping,
            user_id: user_id,
        },
        dataType: 'json',
        complete: function (response) {
            console.log(response);
            var jsonData = JSON.parse(response.responseText);
            if (jsonData.status == 'OK') {
                BootstrapDialog.closeAll();
                let message = '';
                if (typeof jsonData.message == 'object') {
                    for (let key in jsonData.message) {
                        console.log(key, jsonData.message[key]);
                        message += `<p class="text-capitalize"><b>${key} status</b>: <span>${jsonData.message[key]}</span> </p>`;
                    }
                } else {
                    message = jsonData.message;
                }
                BootstrapDialog.show({
                    title: 'SUCCESS',
                    type: BootstrapDialog.TYPE_SUCCESS,
                    message: message,
                });
                table.ajax.reload();
            } else {
                BootstrapDialog.show({
                    title: 'ERROR',
                    type: BootstrapDialog.TYPE_DANGER,
                    message: jsonData.message,
                });
            }
            tablePaymentDetail.ajax.reload();
        },
        error: function (xhr, status, error) {
            console.log(xhr);
            console.log(status);
            console.log(error);
        },
    });
};

refundDetailsNew = (...props) => {
    const [data, payRef, user_id, reason] = props;

    $.ajax({
        url: SERVER_PATH + 'payment/partialRefundNew',
        type: 'POST',
        data: {
            details: JSON.stringify(data),
            payRef: payRef,
            user_id: user_id,
            reason: reason,
        },
        dataType: 'json',
        beforeSend: function () {
            Swal.fire({
                title: 'Processing...',
                allowOutsideClick: false,
                onBeforeOpen: () => {
                    Swal.showLoading();
                },
            });
        },
        success: function (response) {
            Swal.close();

            const { invoice_details } = response;

            if (response.status == 'OK') {
                BootstrapDialog.show({
                    title: 'SUCCESS',
                    type: BootstrapDialog.TYPE_SUCCESS,
                    message: response.message,
                    onhide: function (dialog) {
                        let checkboxes = '';
                        Swal.fire({
                            title: 'Auto Cancel',
                            html:
                                `
                              <div style="text-align: left;">
                                ` +
                                (invoice_details.length >= 1 &&
                                    invoice_details.map((item, index) => {
                                        return (
                                            `<label style="display: flex; align-items: center;gap:5px"><input style="margin-left:10px;margin-top: 0px;" type="checkbox" id="option${
                                                index + 2
                                            }" value="` +
                                            item.id +
                                            `">Cancel ` +
                                            item.item_type +
                                            `</label><br>`
                                        );
                                    })) +
                                `
                              </div>
                            `,
                            confirmButtonText: 'Submit',
                            preConfirm: () => {
                                // get checked options
                                checkboxes = [];

                                for (
                                    let i = 0;
                                    i < invoice_details.length;
                                    i++
                                ) {
                                    const checkbox = document.getElementById(
                                        'option' + (i + 2)
                                    );
                                    if (checkbox.checked) {
                                        checkboxes.push(checkbox.value);
                                    }
                                }

                                return checkboxes;
                            },
                        }).then((result) => {
                            if (result.isConfirmed) {
                                // array to , separated string
                                const invoice_ids = checkboxes.join(',');

                                if (invoice_ids.length !== 0) {
                                    autoCancelRegistrationOrPayment(
                                        invoice_ids,
                                        user_id,
                                        reason
                                    );
                                }
                            }
                        });
                    },
                });

                GLOBAL_SCOPE.table.ajax.reload();
            } else {
                BootstrapDialog.show({
                    title: 'You cannot do this action.',
                    type: BootstrapDialog.TYPE_DANGER,
                    message: response.message,
                });
            }
        },
        error: function (xhr, status, error) {
            Swal.close();
            console.error('Error refunding:', error);
        },
    });
};

function autoCancelRegistrationOrPayment(invoice_details, user_id, reason) {
    const autoCancelApi = (invoice_details, user_id, reason) => {
        return new Promise((resolve, reject) => {
            $.ajax({
                url: SERVER_PATH + 'payment/autoCancelRegistrationOrPayment',
                type: 'POST',
                data: {
                    invoice_details: JSON.stringify(invoice_details),
                    user_id: user_id,
                    reason: reason,
                },
                dataType: 'json',
                beforeSend: function () {
                    Swal.fire({
                        title: 'Processing...',
                        allowOutsideClick: false,
                        onBeforeOpen: () => {
                            Swal.showLoading();
                        },
                    });
                },
                success: function (response) {
                    Swal.close();
                    resolve(response);
                },
                error: function (xhr, status, error) {
                    Swal.close();
                    console.error('Error auto cancelling:', error);
                    reject();
                },
            });
        });
    };

    autoCancelApi(invoice_details, user_id, reason).then((response) => {
        if (!response) {
            Swal.fire({
                title: 'Error',
                text: 'Something went wrong',
                icon: 'error',
                confirmButtonText: 'OK',
            });
        }
        if (response && response.status == 'ERROR') {
            Swal.fire({
                title: 'Error',
                text: response.message,
                icon: 'error',
                confirmButtonText: 'OK',
            });
        }

        if (response && response.status == 'OK') {
            const { data } = response;

            const successMessage = data.map((item) => {
                return `<p class="text-capitalize"><b>${
                    item.status == true ? 'OK' : 'Error'
                } status</b>: <span>${item.message}</span> </p>`;
            });

            Swal.fire({
                title: 'Success',
                html: successMessage.join('<br>'),
                icon: 'success',
                confirmButtonText: 'OK',
            });
        }
    });
}

getPaymentDetailHTML = function (id) {
    var str =
        '' +
        '<div class="table-responsive">' +
        '<table class="table table-striped table-bordered table-hover" id="paymentDetailTable_' +
        id +
        '">' +
        '<thead>' +
        '<tr>' +
        '<th></th>' +
        '<th>Fee</th>' +
        '<th>Amount</th>' +
        '</tr>' +
        '</thead>' +
        '</table>' +
        '</div>';

    return str;
};

function initModalRefund(amt, payRef, table, user_id) {
    var msg =
        '<div class="form-check">' +
        '<input class="form-check-input" name="refund" type="radio" id="refundFull" checked>' +
        '<label class="form-check-label" for="refundFull">&nbsp;' +
        'Refund Full' +
        '</label>' +
        ' </div>' +
        '<div class="form-check">' +
        '<input class="form-check-input" name="refund" type="radio" id="refundaPart" >' +
        '<label class="form-check-label" for="refundaPart">&nbsp;' +
        'Refund a part' +
        '</label>' +
        '</div>' +
        '<div class="row" id="amountContainer" style="display:none">' +
        '<label class="col-sm-3 control-label">Amount to Refund</label>' +
        '<div class="col-sm-9">' +
        '<input required id="amount" data-user-id="' +
        user_id +
        '"  type="number" value = "" class="form-control" min="1" max="' +
        amt +
        '" >' +
        '</div>' +
        ' </div>' +
        ' <div class="modal-footer" style="margin-top:10px">' +
        '<button type="button" class="btn btn-default" data-dismiss="modal">Close</button>' +
        '<button onClick=onSubmitRefund(' +
        amt +
        ',' +
        payRef +
        ') class="btn btn-success">Refund</button>' +
        ' </div>';
    // Show dialog
    BootstrapDialog.show({
        title: 'Refund Payment',
        message: msg,
        onshown: function (dialogRef) {
            //   onSubmitRefund(invoice_id, payRef);
            //if refundaPart radio button is checked, show the textbox
            $('#amountContainer').hide();
            $('#refundaPart').click(function () {
                if ($(this).is(':checked')) {
                    $('#amountContainer').show();
                } else {
                    $('#amountContainer').hide();
                }
            });
            //if refundFull radio button is checked, hide the textbox
            $('#refundFull').click(function () {
                if ($(this).is(':checked')) {
                    $('#amount').val('');
                    $('#amountContainer').hide();
                } else {
                    $('#amountContainer').show();
                }
            });
        },
        onhide: function (dialogRef) {
            table.ajax.reload();
        },
    });
}
