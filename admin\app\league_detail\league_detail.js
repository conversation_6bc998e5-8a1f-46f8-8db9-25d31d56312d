app.controller(
    'leagueDetailCtrl',
    function ($scope, $rootScope, $routeParams, $http) {
        $('#page-wrapper').removeClass('nav-small');
        var leagueSelected = [];
        $scope.event_id = $routeParams.id;
        var leagueId = $routeParams.leaguesid;
        var eventName = $routeParams.eventName;
        var groupName = $routeParams.groupName;
        var leagueName = $routeParams.leagueName;
        var groupIdSelected = $routeParams.groupIdSelected;
        var leagueType = $routeParams.leagueType;
        $rootScope.groupIdSelected = groupIdSelected;
        //--get start data--//
        $scope.eventName = eventName;
        $scope.leagueId = leagueId;
        $scope.leagueName = leagueName;
        $scope.groupName = groupName;
        $scope.groupIdSelected = groupIdSelected;
        jQuery.ajax({
            type: 'POST',
            url: SERVER_PATH + 'league/getleagueinfo',
            async: false,
            headers: {	
                'x-user-id': $rootScope.user_id,
                'x-user-email': $rootScope.user_name
            },
            data: {
                league_id: leagueId,
            },
            dataType: 'json',
            complete: function (response) {
                var jsonData = JSON.parse(response.responseText);
                console.log('league');
                console.log(jsonData);
                if (jsonData.status == 'OK') {
                    leagueSelected = jsonData.info;
                }
            },
        });

        jQuery.ajax({
            type: 'POST',
            url: SERVER_PATH + 'event/getEventInfo',
            async: false,
            headers: {	
                'x-user-id': $rootScope.user_id,
                'x-user-email': $rootScope.user_name
            },
            data: {
                event_id: $scope.event_id,
            },
            dataType: 'json',
            complete: function (response) {
                var jsonData = JSON.parse(response.responseText);
                var event = jsonData.info;
                $scope.event_name = event.name;
                $scope.event_type = event.type;
                $scope.normalizedType = normalizeEventType($scope.event_type);
            },
        });

        initLeagueTeamsTable(leagueId, leagueName, groupName, leagueType);

        function getLeagueMatchesDetailHtml(match_id) {
            var str =
                '' +
                '<div class="table-responsive">' +
                '<table id="tblLeagueMatchesEvents_' +
                match_id +
                '" class="table table-striped table-bordered table-hover" cellspacing="0" width="100%">' +
                '<thead>' +
                '<tr>' +
                '<th >Player</th>' +
                '<th >Team</th>' +
                '<th>Type</th>' +
                '<th >Time</th>' +
                '<th  class="center">User Create</th>' +
                '<th  class="center">Note</th>' +
                '</tr>' +
                '</thead>' +
                '</table>' +
                '</div>';
            return str;
        }
        function getUpdateMatchesResultHtml(
            home_name,
            away_name,
            home_score = 0,
            away_score = 0,
            home_respect_score = 0,
            away_respect_score = 0
        ) {
            var str =
                "<form method='get' id='updateScore' action=''><h3 style='color:red;font-family:Helvetica, Arial, sans-serif;position:relative'>The Score</h3><div id='the_score'><div class='form-group'><label class='control-label'>" +
                home_name +
                "</label><div><input type='number' class='form-control input-lg' id='home_team' name='home_team' value='"+home_score+"'></div></div><div class='form-group'><label class='control-label'>" +
                away_name +
                "</label><div><input type='number' class='form-control input-lg' id='away_team' name='away_team' value='"+away_score+"'></div></div></div><h3 style='color:red;font-family:Helvetica, Arial, sans-serif;position:relative'>The Respect Score</h3><div id='the_respect_score'><div class='form-group'><label class='control-label'>" +
                home_name +
                "</label><div><input type='number' class='form-control input-lg' id='home_respect_team' name='home_respect_team' value='"+home_respect_score+"'></div></div><div class='form-group'><label class='control-label'>" +
                away_name +
                "</label><div><input type='number' class='form-control input-lg' id='away_respect_team' name='away_respect_team' value='"+away_respect_score+"'></div></div></div></form>";
            console.warn(str);
            return str;
        }
        //--get start data--//
        $('body').unbind('click');
        $('body').on(
            'click',
            'li[class=active] a[data-toggle=tab]',
            function () {
                switch ($(this).data('target')) {
                    case '#tab-teams': {
                        initLeagueTeamsTable(
                            leagueId,
                            leagueName,
                            groupName,
                            leagueType
                        );
                        break;
                    }
                    case '#tab-matches': {
                        initLeagueMatchesTable(leagueId, leagueName, groupName);
                        break;
                    }
                    case '#tab-matchesfriendly': {
                        initLeagueFriendlyMatchesTable(
                            leagueId,
                            leagueName,
                            groupName
                        );
                        break;
                    }
                    case '#tab-table': {
                        var table = initLeagueTable(leagueId);
                        initUpdatedPoints(leagueId, table);
                        break;
                    }
                }
            }
        );

        function normalizeEventType(eventType) {
            switch (eventType) {
                case 'Summer Scheme':
                    return 'summer-scheme';
                case 'Regional':
                    return 'regional';
                case 'PL Junior':
                    return 'pl-junior';
                case 'Golden Age':
                    return 'golden-age';
                case 'Beginner':
                    return 'beginner';
                case 'District':
                    return 'district';
                default:
                    return '';
            }
        }

        function initLeagueTeamsTable(
            league_id,
            league_name,
            group_name,
            leagueType
        ) {
            if ($.fn.dataTable.isDataTable('#tblLeagueTeams')) {
                $('#tblLeagueTeams').DataTable().destroy();
            }
            var editorLeagueTeams = new $.fn.dataTable.Editor({
                ajax: {
                    type: 'POST',
                    url: SERVER_PATH + 'league/setLeagueTeams',
                    headers: {	
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name
                    },
                    data: {
                        league_id: league_id,
                    },
                    dataType: 'json',
                    complete: function (response) {
                        tableLeagueTeams.ajax.reload();
                    },
                    error: function (xhr, status, error) {},
                },
                table: '#tblLeagueTeams',
                formOptions: {
                    main: {
                        onBlur: 'none',
                    },
                },
                i18n: {
                    create: {
                        button: 'Add',
                        title: 'Add new team',
                        submit: 'Add',
                    },
                    edit: {
                        button: 'Edit',
                        title: 'Edit team',
                        submit: 'Save',
                    },
                    remove: {
                        button: 'Delete',
                        title: 'Delete team',
                        submit: 'Delete',
                    },
                    error: {
                        system: 'System error, please contact administrator.',
                    },
                },
                fields: [
                    {
                        label: 'League ID:',
                        name: 'league_teams.league_id',
                        type: 'hidden',
                        def: league_id,
                    },
                    {
                        label: 'Team ID:',
                        name: 'league_teams.team_id',
                        type: 'select2',
                        opts: {
                            placeholder: 'Select a team',
                            multiple: 'multiple',
                        },
                    },
                ],
            });
            editorLeagueTeams.on('preOpen', function () {
                $('.modal-backdrop').hide();
                $('.bootstrap-dialog').hide();
            });
            editorLeagueTeams.on('close', function () {
                // alert('close');
                $('.modal-backdrop').show();
                $('.bootstrap-dialog').show();
            });
            tableLeagueTeams = $('#tblLeagueTeams').DataTable({
                dom: '<"row"<"col-sm-6"B><"col-sm-6"f>>rt<"row"i>',
                stateSave: false,
                deferRender: true,
                ajax: {
                    url: SERVER_PATH + 'league/getLeagueTeams',
                    type: 'POST',
                    headers: {	
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name
                    },
                    data: {
                        league_id: league_id,
                    },
                    dataType: 'json',
                    complete: function (response) {
                        // response = JSON.parse(response.responseText);
                    },
                    error: function (xhr, status, error) {},
                },
                language: {
                    paginate: {
                        previous: '<i class="fa fa-chevron-left"></i>',
                        next: '<i class="fa fa-chevron-right"></i>',
                    },
                },
                columns: [
                    {
                        data: null,
                        render: function (data) {
                            var logo =
                                '<img class="club-logo" src="' +
                                SYSTEM_IMAGE_PATH +
                                'logo.png">';
                            if (
                                data.clubs.logo !== null &&
                                data.clubs.logo !== ''
                            ) {
                                logo =
                                    '<img class="club-logo" src="' +
                                    PRODUCT_IMAGE_PATH +
                                    data.clubs.logo +
                                    '">';
                            }
                            return logo + data.teams.name;
                        },
                    },
                ],
                select: {
                    style: SELECT_MODE,
                    selector: 'td:first-child',
                },
                buttons: [
                    {
                        extend: 'collection',
                        text: 'Actions',
                        autoClose: true,
                        buttons: [
                            {
                                extend: 'excel',
                                name: 'excel',
                                text: '<i class="fa fa-file-excel-o"></i>&emsp;Export to Excel',
                                titleAttr: 'Export data to an Excel file',
                                filename: group_name + ' - ' + league_name,
                                exportOptions: {
                                    columns: ':visible',
                                    modifier: {
                                        autoFilter: true,
                                        // selected: true
                                    },
                                },
                            },
                        ],
                    },
                    {
                        extend: 'create',
                        editor: editorLeagueTeams,
                        text: 'Add',
                    },
                    {
                        extend: 'remove',
                        editor: editorLeagueTeams,
                        text: 'Delete',
                    },

                    {
                        extend: 'selectedSingle',
                        text: 'Change Team',
                        action: function (e, dt, node, config) {
                            var team = [];
                            var team_id = tableLeagueTeams
                                .rows({
                                    selected: true,
                                })
                                .data()[0].league_teams.team_id;
                            var team_name = tableLeagueTeams
                                .rows({
                                    selected: true,
                                })
                                .data()[0].teams.name;
                            $.ajax({
                                type: 'POST',
                                async: false,
                                url:
                                    SERVER_PATH +
                                    'league/getTeamInSeasionByLeague',
                                headers: {	
                                    'x-user-id': $rootScope.user_id,
                                    'x-user-email': $rootScope.user_name
                                },
                                data: {
                                    league_id: league_id,
                                },
                                cache: false,
                                success: function (response) {
                                    data = JSON.parse(response);
                                    team = data.info;
                                },
                            });
                            var change_team_editor = new $.fn.dataTable.Editor({
                                ajax: {
                                    type: 'POST',
                                    url: SERVER_PATH + 'league/changeteam',
                                    headers: {	
                                        'x-user-id': $rootScope.user_id,
                                        'x-user-email': $rootScope.user_name
                                    },
                                    data: {
                                        league_id: league_id,
                                        team_id_old: team_id,
                                    },
                                    dataType: 'json',
                                    complete: function (response) {},
                                    error: function (xhr, status, error) {
                                        console.log(error);
                                    },
                                },
                                formOptions: {
                                    main: {
                                        onBlur: 'none',
                                    },
                                },
                                fields: [
                                    {
                                        label: 'Team Name',
                                        name: 'team_id_old',
                                        def: team_name,
                                    },
                                    {
                                        label: 'New Team',
                                        name: 'new_team_id',
                                        type: 'select',
                                        options: team,
                                    },
                                ],
                            });
                            change_team_editor.on('initEdit', function () {
                                change_team_editor.disable('team_id_old');
                            });
                            change_team_editor.on('initSubmit', function () {
                                change_team_editor.close();
                            });
                            change_team_editor.on('close', function () {
                                tableLeagueTeams.ajax.reload();
                            });
                            change_team_editor
                                .title('Change Team')
                                .buttons({
                                    label: 'Save',
                                    fn: function () {
                                        this.submit();
                                    },
                                })
                                .edit()
                                .open();
                        },
                    },
                    {
                        extend: 'colvis',
                        text: 'Columns',
                    },
                ],
                order: [[0, 'asc']],
                displayLength: -1,
            });
        }

        function initLeagueMatchesTable(league_id, league_name, group_name) {
            if ($.fn.dataTable.isDataTable('#tblLeagueMatches_' + league_id)) {
                $('#tblLeagueMatches_' + league_id)
                    .DataTable()
                    .destroy();
            }
            var editorLeagueMatches = new $.fn.dataTable.Editor({
                ajax: {
                    type: 'POST',
                    url: SERVER_PATH + 'league/setLeagueMatches',
                    headers: {	
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name
                    },
                    data: {
                        league_id: league_id,
                        type_of_match: 0,
                        user_id: $rootScope.user_id,
                    },
                    dataType: 'json',
                    beforeSend: function () {
                        Swal.fire({
                            title: 'Please Wait!',
                            allowOutsideClick: false,
                            didOpen: () => {
                                Swal.showLoading();
                            },
                        });
                    },
                    complete: function (response) {
                        Swal.close();
                        let data = JSON.parse(response.responseText);
                        if (
                            typeof data.fieldErrors == 'undefined' ||
                            data.fieldErrors.length == 0
                        ) {
                            tableLeagueMatches.ajax.reload();
                            tableLeagueMatches
                                .rows({ selected: true })
                                .deselect();
                        }
                    },
                    error: function (xhr, status, error) {
                        Swal.close();
                    },
                },
                table: '#tblLeagueMatches_' + league_id,
                formOptions: {
                    main: {
                        onBlur: 'none',
                    },
                },
                columnDefs: [{ type: 'date-dd-mmm-yyyy', targets: [0] }],
                i18n: {
                    create: {
                        button: 'Add',
                        title: 'Add new match',
                        submit: 'Add',
                    },
                    edit: {
                        button: 'Edit',
                        title: 'Edit match',
                        submit: 'Save',
                    },
                    error: {
                        system: 'System error, please contact administrator.',
                    },
                },
                fields: [
                    {
                        label: 'League ID:',
                        name: 'matches.league_id',
                        type: 'hidden',
                        def: league_id,
                    },
                    {
                        label: 'Date:',
                        name: 'matches.date',
                        type: 'datetime',
                        format: 'DD-MMM-YYYY',
                        default: moment().format('DD-MMM-YYYY'),
                    },
                    {
                        label: 'Start Time:',
                        name: 'matches.start_time',
                        type: 'datetime',
                        format: 'HH:mm',
                        def: '00:00',
                    },
                    {
                        label: 'End Time:',
                        name: 'matches.end_time',
                        type: 'datetime',
                        format: 'HH:mm',
                        def: '00:00',
                    },
                    {
                        label: 'Location:',
                        name: 'matches.location',
                        type: 'select2',
                        opts: {
                            placeholder: 'Select location',
                            tags: true,
                            closeOnSelect: true,
                        },
                    },
                    {
                        label: 'Home Team:',
                        name: 'matches.home_team_id',
                        type: 'select2',
                        opts: {
                            placeholder: 'Select a team',
                        },
                        def: 0,
                    },
                    {
                        label: 'Away Team:',
                        name: 'matches.away_team_id',
                        type: 'select2',
                        opts: {
                            placeholder: 'Select a team',
                        },
                        def: 0,
                    },
                ],
            });
            editorLeagueMatches.on('preOpen', function (e, data, action) {
                if (action == 'create') {
                    editorLeagueMatches.dependent(
                        'matches.start_time',
                        function (val) {
                            var start_time = new Date();
                            start_time_arr = val.split(':');
                            start_time.setHours(start_time_arr[0]);
                            start_time.setMinutes(start_time_arr[1]);
                            var end_time = new Date(
                                start_time.getTime() +
                                    parseInt(leagueSelected.match_duration) *
                                        60000
                            );
                            editorLeagueMatches.val(
                                'matches.end_time',
                                end_time
                            );
                        }
                    );
                }
                $('.modal-backdrop').hide();
                $('.bootstrap-dialog').hide();
            });
            editorLeagueMatches.on('initEdit', function () {
                editorLeagueMatches.field('matches.home_team_id').hide();
                editorLeagueMatches.field('matches.away_team_id').hide();
            });
            editorLeagueMatches.on('close', function () {
                editorLeagueMatches.undependent('matches.start_time');
                $('.modal-backdrop').show();
                $('.bootstrap-dialog').show();
            });
            tableLeagueMatches = $('#tblLeagueMatches_' + league_id).DataTable({
                dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
                stateSave: false,
                deferRender: true,
                ajax: {
                    url: SERVER_PATH + 'league/getLeagueMatches',
                    type: 'POST',
                    headers: {	
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name
                    },
                    data: {
                        league_id: league_id,
                        type_of_match: 0,
                    },
                    dataType: 'json',
                    complete: function (response) {},
                    error: function (xhr, status, error) {},
                },
                language: {
                    paginate: {
                        previous: '<i class="fa fa-chevron-left"></i>',
                        next: '<i class="fa fa-chevron-right"></i>',
                    },
                },
                columns: [
                    {
                        data: null,
                        render: function (data, type, full, meta) {
                            return full.matches.date == ''
                                ? 'TBD'
                                : full.matches.date;
                        },
                    },
                    {
                        data: null,
                        render: function (data, type, full, meta) {
                            return full.matches.start_time == ''
                                ? 'TBD'
                                : full.matches.start_time;
                        },
                    },
                    {
                        data: null,
                        render: function (data, type, full, meta) {
                            return full.matches.end_time == ''
                                ? 'TBD'
                                : full.matches.end_time;
                        },
                    },
                    {
                        data: null,
                        render: function (data, type, full, meta) {
                            return full.matches.location == 0
                                ? 'TBD'
                                : full.venues.name;
                        },
                    },
                    {
                        data: 'match_cancellations.cancel_type',
                        render: function (data, type, row) {
                            if (row.match_cancellations.cancel_type == null) {
                                return '<span class="label label-success">Can Play</span>';
                            } else {
                                switch (
                                    parseInt(
                                        row.match_cancellations.cancel_type
                                    )
                                ) {
                                    case MATCH_POSTPONED: {
                                        return (
                                            '<a href="javascript:void(0)" class="label" data-toggle="tooltip" data-placement="top" title="' +
                                            row.match_cancellations.reason +
                                            '">Postponed</a>'
                                        );
                                        break;
                                    }
                                    case MATCH_CANCELLED: {
                                        return (
                                            '<a href="javascript:void(0)" class="label" data-toggle="tooltip" data-placement="top" title="' +
                                            row.match_cancellations.reason +
                                            '">Cancelled</a>'
                                        );
                                        break;
                                    }
                                    case MATCH_ABANDONED: {
                                        return (
                                            '<a href="javascript:void(0)" class="label" data-toggle="tooltip" data-placement="top" title="' +
                                            row.match_cancellations.reason +
                                            '">Abandoned</a>'
                                        );
                                        break;
                                    }
                                }
                            }
                        },
                    },
                    {
                        data: 'matches.home_name',
                        className: 'center',
                        sortable: false,
                    },
                    {
                        data: null,
                        className: 'center',
                        sortable: false,
                        className: 'center',
                        render: function () {
                            return 'VS';
                        },
                    },
                    {
                        data: 'matches.away_name',
                        className: 'center',
                        sortable: false,
                    },
                    {
                        data: 'matches.home_team_score',
                        className: 'center',
                        sortable: false,
                    },
                    {
                        data: null,
                        className: 'center',
                        sortable: false,
                        className: 'center',
                        render: function () {
                            return '-';
                        },
                    },
                    {
                        data: 'matches.away_team_score',
                        sortable: false,
                    },
                    {
                        data: 'matches.home_respect_score',
                        className: 'center',
                        sortable: false,
                    },
                    {
                        data: null,
                        className: 'center',
                        sortable: false,
                        className: 'center',
                        render: function () {
                            return '-';
                        },
                    },
                    {
                        data: 'matches.away_respect_score',
                        sortable: false,
                    },
                ],
                createdRow: function (row, data, dataIndex) {
                    if (data.matches.highlight == 1) {
                        $(row).css('background-color', '#FFFACD');
                    }
                },
                select: {
                    style: SELECT_MODE,
                },
                buttons: [
                    {
                        extend: 'collection',
                        text: 'Actions',
                        autoClose: true,
                        buttons: [
                            {
                                name: 'generate_matches',
                                text: '<i class="fa fa-magic"></i>&emsp;Generate Matches',
                                titleAttr: 'Auto generate match in league',
                                action: function (e, dt, node, config) {
                                    jQuery.ajax({
                                        type: 'POST',
                                        url:
                                            SERVER_PATH +
                                            'league/generateMatch',
                                        async: false,
                                        headers: {	
                                            'x-user-id': $rootScope.user_id,
                                            'x-user-email': $rootScope.user_name
                                        },
                                        data: {
                                            leagues: league_id,
                                            type: leagueType,
                                        },
                                        dataType: 'json',
                                        complete: function (response) {
                                            var jsonData = JSON.parse(
                                                response.responseText
                                            );
                                            if (jsonData.status == 'OK') {
                                                BootstrapDialog.show({
                                                    size: BootstrapDialog.SIZE_WIDE,
                                                    type: BootstrapDialog.TYPE_DANGER,
                                                    title: league_name,
                                                    message: jsonData.message,
                                                });
                                                tableLeagueMatches.ajax.reload();
                                            } else {
                                                BootstrapDialog.show({
                                                    size: BootstrapDialog.SIZE_WIDE,
                                                    type: BootstrapDialog.TYPE_DANGER,
                                                    title: league_name,
                                                    message: jsonData.message,
                                                });
                                            }
                                        },
                                    });
                                },
                            },
                            {
                                extend: 'excel',
                                name: 'excel',
                                text: '<i class="fa fa-file-excel-o"></i>&emsp;Export to Excel',
                                titleAttr: 'Export data to an Excel file',
                                filename: group_name + ' - ' + league_name,
                                exportOptions: {
                                    columns: ':visible',
                                    modifier: {
                                        autoFilter: true,
                                        // selected: true
                                    },
                                },
                            },
                        ],
                    },
                    {
                        text: 'Add',
                        action: function () {
                            editorLeagueMatches
                                .field('matches.home_team_id')
                                .show();
                            editorLeagueMatches
                                .field('matches.away_team_id')
                                .show();
                            editorLeagueMatches
                                .title('Create Match')
                                .buttons({
                                    label: 'Create',
                                    fn: function () {
                                        this.submit();
                                    },
                                })
                                .create()
                                .open();
                        },
                    },
                    {
                        extend: 'selectedSingle',
                        text: 'Add Replace Match',
                        action: function () {
                            var dataSelectedRow = tableLeagueMatches
                                .rows({ selected: true })
                                .data()[0];
                            if (
                                dataSelectedRow.matches.home_team_id != null &&
                                dataSelectedRow.matches.away_team_id != null
                            ) {
                                editorLeagueMatches
                                    .field('matches.home_team_id')
                                    .hide();
                                editorLeagueMatches
                                    .field('matches.away_team_id')
                                    .hide();
                            } else {
                                editorLeagueMatches
                                    .field('matches.home_team_id')
                                    .show();
                                editorLeagueMatches
                                    .field('matches.away_team_id')
                                    .show();
                            }
                            editorLeagueMatches
                                .edit(
                                    tableLeagueMatches
                                        .rows({ selected: true })
                                        .indexes(),
                                    {
                                        title: 'Add Replace Match',
                                        buttons: 'Create',
                                    }
                                )
                                .mode('create');
                            editorLeagueMatches.on(
                                'preSubmit',
                                function (e, data, action) {
                                    data.old_match_id =
                                        dataSelectedRow.matches.id;
                                }
                            );
                        },
                    },
                    {
                        extend: 'edit',
                        editor: editorLeagueMatches,
                        text: 'Edit',
                    },
                    {
                        extend: 'selectedSingle',
                        text: 'Match detail',
                        action: function () {
                            var match = tableLeagueMatches
                                .rows({
                                    selected: true,
                                })
                                .data()[0].matches;
                            var msg = getLeagueMatchesDetailHtml(match.id);
                            // Show dialog
                            BootstrapDialog.show({
                                size: BootstrapDialog.SIZE_WIDE,
                                type: BootstrapDialog.TYPE_DANGER,
                                closable: true,
                                closeByBackdrop: false,
                                closeByKeyboard: true,
                                title:
                                    match.home_name + ' VS ' + match.away_name,
                                id: 'leaguematch',
                                message: msg,
                                onshown: function (dialog) {
                                    initLeagueMatchesEventTable(
                                        match.id,
                                        match.home_team_id,
                                        match.away_team_id
                                    );
                                },
                                onhide: function (dialog) {
                                    $('body').removeClass('modal-open-custom');
                                },
                                onhidden: function (dialogRef) {
                                    $('body').removeClass('modal-open-custom');
                                },
                            });
                        },
                    },
                    {
                        extend: 'selectedSingle',
                        text: 'Update Match Result',
                        action: function () {
                            var match = tableLeagueMatches
                                .rows({
                                    selected: true,
                                })
                                .data()[0].matches;
                            var msg = getUpdateMatchesResultHtml(
                                match.home_name,
                                match.away_name,
                                match.home_team_score,
                                match.away_team_score,
                                match.home_respect_score,
                                match.away_respect_score
                            );
                            BootstrapDialog.show({
                                size: BootstrapDialog.SIZE_WIDE,
                                type: BootstrapDialog.TYPE_DANGER,
                                closable: true,
                                closeByBackdrop: false,
                                closeByKeyboard: true,
                                title:
                                    match.home_name + ' VS ' + match.away_name,
                                message: msg,
                                buttons: [
                                    {
                                        label: 'Close',
                                        action: function (dialogRef) {
                                            dialogRef.close();
                                        },
                                    },
                                    {
                                        label: 'Save',
                                        action: function (dialogRef) {
                                            var $form = $('#updateScore');
                                            $form.validate({
                                                onfocusout: false,
                                                onkeyup: false,
                                                onclick: false,
                                                rules: {
                                                    home_team: {
                                                        required: true,
                                                        digits: true,
                                                    },
                                                    away_team: {
                                                        required: true,
                                                        digits: true,
                                                    },
                                                    home_respect_team: {
                                                        required: true,
                                                        digits: true,
                                                    },
                                                    away_respect_team: {
                                                        required: true,
                                                        digits: true,
                                                    },
                                                },
                                                messages: {
                                                    home_team: {
                                                        required:
                                                            'this field is required',
                                                        digits: 'please enter number',
                                                    },
                                                    away_team: {
                                                        required:
                                                            'this field is required',
                                                        minlength:
                                                            'please enter number',
                                                    },
                                                    home_respect_team: {
                                                        required:
                                                            'this field is required',
                                                        digits: 'please enter number',
                                                    },
                                                    away_respect_team: {
                                                        required:
                                                            'this field is required',
                                                        minlength:
                                                            'please enter number',
                                                    },
                                                },
                                            });
                                            if ($form.valid()) {
                                                var home_score = dialogRef
                                                    .getModalBody()
                                                    .find('#home_team')
                                                    .val();
                                                var away_score = dialogRef
                                                    .getModalBody()
                                                    .find('#away_team')
                                                    .val();
                                                var home_respect_score =
                                                    dialogRef
                                                        .getModalBody()
                                                        .find(
                                                            '#home_respect_team'
                                                        )
                                                        .val();
                                                var away_respect_score =
                                                    dialogRef
                                                        .getModalBody()
                                                        .find(
                                                            '#away_respect_team'
                                                        )
                                                        .val();
                                                var url =
                                                    SERVER_PATH +
                                                    'league/setLeagueMatchResult';
                                                var form = new FormData();
                                                form.append(
                                                    'home_score',
                                                    home_score
                                                );
                                                form.append(
                                                    'away_score',
                                                    away_score
                                                );
                                                form.append(
                                                    'home_respect_score',
                                                    home_respect_score
                                                );
                                                form.append(
                                                    'away_respect_score',
                                                    away_respect_score
                                                );
                                                form.append(
                                                    'match_id',
                                                    match.id
                                                );
                                                form.append(
                                                    'user_id',
                                                    $rootScope.user_id
                                                );
                                                $.ajax({
                                                    url: url,
                                                    type: 'POST',
                                                    cache: false,
                                                    headers: {	
                                                        'x-user-id': $rootScope.user_id,
                                                        'x-user-email': $rootScope.user_name
                                                    },
                                                    data: form,
                                                    async: false,
                                                    processData: false,
                                                    mimeType:
                                                        'multipart/form-data',
                                                    contentType: false,
                                                    timeout: 0,
                                                    error: function (err) {},
                                                    success: function (
                                                        response
                                                    ) {
                                                        const jsonData =
                                                            JSON.parse(
                                                                response
                                                            );
                                                        if (
                                                            jsonData.status ==
                                                            'OK'
                                                        ) {
                                                            tableLeagueMatches.ajax.reload();
                                                            dialogRef.close();
                                                        } else if (
                                                            jsonData.status ==
                                                            'WARNING'
                                                        ) {
                                                            BootstrapDialog.show(
                                                                {
                                                                    size: BootstrapDialog.SIZE_NORMAL,
                                                                    type: BootstrapDialog.TYPE_DANGER,
                                                                    message:
                                                                        jsonData.data,
                                                                }
                                                            );
                                                            dialogRef.close();
                                                        }
                                                    },
                                                });
                                            }
                                        },
                                    },
                                ],
                            });
                        },
                    },
                    {
                        extend: 'selectedSingle',
                        text: 'Cancel Match',
                        action: function () {
                            var matchID = tableLeagueMatches
                                .rows({ selected: true })
                                .data()[0].matches.id;
                            var editor = new $.fn.dataTable.Editor({
                                ajax: {
                                    type: 'POST',
                                    url:
                                        SERVER_PATH +
                                        'league/setMatchCancellations',
                                    headers: {	
                                        'x-user-id': $rootScope.user_id,
                                        'x-user-email': $rootScope.user_name
                                    },
                                    data: {
                                        user_id: $rootScope.user_id,
                                    },
                                    dataType: 'json',
                                    complete: function (response) {
                                        tableLeagueMatches
                                            .rows({ selected: true })
                                            .deselect();
                                    },
                                    error: function (xhr, status, error) {
                                        console.log(error);
                                    },
                                },
                                formOptions: {
                                    main: {
                                        onBlur: 'none',
                                    },
                                },
                                fields: [
                                    {
                                        label: 'Cancel type',
                                        name: 'match_cancellations.cancel_type',
                                        type: 'radio',
                                        options: [
                                            { label: 'Postponed', value: 1 },
                                            { label: 'Cancelled', value: 2 },
                                            { label: 'Abandoned', value: 3 },
                                        ],
                                        def: 1,
                                    },
                                    {
                                        label: 'Reason',
                                        name: 'match_cancellations.reason',
                                    },
                                    {
                                        label: 'Match Id',
                                        name: 'match_cancellations.match_id',
                                        def: matchID,
                                    },
                                ],
                            });
                            editor.on('initCreate', function () {
                                editor.hide('match_cancellations.match_id');
                            });
                            editor.on('initSubmit', function () {
                                editor.close();
                            });
                            editor.on('postSubmit', function () {
                                tableLeagueMatches.ajax.reload();
                            });
                            editor.on('preSubmit', function () {
                                return confirm('Ask again, are you sure?');
                            });
                            editor
                                .title('Cancel Match')
                                .buttons({
                                    label: 'Save',
                                    fn: function () {
                                        this.submit();
                                    },
                                })
                                .create()
                                .open();
                        },
                    },
                    {
                        extend: 'selectedSingle',
                        text: 'Reset match score',
                        action: function (e, dt, node, config) {
                            var selectedRows = tableLeagueMatches.rows({
                                selected: true,
                            });
                            var dataSelectedRow = selectedRows.data()[0];
                            // show confirm dialog
                            Swal.fire({
                                title: 'Are you sure?',
                                text: 'You will not be able to recover this match score!',
                                icon: 'warning',
                                showCancelButton: true,
                                confirmButtonColor: '#3085d6',
                                cancelButtonColor: '#d33',
                                confirmButtonText: 'Yes, reset it!',
                            }).then((result) => {
                                if (result.isConfirmed) {
                                    jQuery.ajax({
                                        type: 'POST',
                                        url:
                                            SERVER_PATH +
                                            'league/resetMatchScore',
                                        async: true,
                                        headers: {	
                                            'x-user-id': $rootScope.user_id,
                                            'x-user-email': $rootScope.user_name
                                        },
                                        data: {
                                            match_id:
                                                dataSelectedRow.matches.id,
                                            user_id: $rootScope.user_id,
                                        },
                                        dataType: 'json',
                                        beforeSend: function () {
                                            Swal.fire({
                                                title: 'Please Wait!',
                                                allowOutsideClick: false,
                                                didOpen: () => {
                                                    Swal.showLoading();
                                                },
                                            });
                                        },
                                        complete: function (response) {
                                            Swal.close();
                                            var jsonData = JSON.parse(
                                                response.responseText
                                            );
                                            if (jsonData.status == 'OK') {
                                                Swal.fire({
                                                    type: 'success',
                                                    icon: 'success',
                                                    title: jsonData.message,
                                                    confirmButtonClass:
                                                        'btn btn-primary',
                                                    buttonsStyling: false,
                                                });
                                                tableLeagueMatches.ajax.reload();
                                            } else {
                                                Swal.fire({
                                                    title: 'ERROR!',
                                                    text: jsonData.message,
                                                    icon: 'error',
                                                    type: 'error',
                                                });
                                            }
                                        },
                                    });
                                }
                            });
                        },
                    },
                    {
                        extend: 'selectedSingle',
                        text: 'Swap Home/Away',
                        action: function () {
                            console.log('on swatp');
                            var matchID = tableLeagueMatches
                                .rows({ selected: true })
                                .data()[0].matches.id;
                            var editor = new $.fn.dataTable.Editor({
                                ajax: {
                                    type: 'POST',
                                    url: SERVER_PATH + 'league/swapHomeAway',
                                    headers: {	
                                        'x-user-id': $rootScope.user_id,
                                        'x-user-email': $rootScope.user_name
                                    },
                                    data: {},
                                    dataType: 'json',
                                    complete: function (response) {
                                        var jsonData = JSON.parse(
                                            response.responseText
                                        );
                                        if (jsonData.status == 'OK') {
                                            BootstrapDialog.show({
                                                size: BootstrapDialog.SIZE_NORMAL,
                                                type: BootstrapDialog.TYPE_SUCCESS,
                                                message: jsonData.message,
                                            });
                                        } else {
                                            BootstrapDialog.show({
                                                size: BootstrapDialog.SIZE_NORMAL,
                                                type: BootstrapDialog.TYPE_DANGER,
                                                message: jsonData.message,
                                            });
                                        }
                                        tableLeagueMatches
                                            .rows({ selected: true })
                                            .deselect();
                                        tableLeagueMatches.ajax.reload();
                                    },
                                    error: function (xhr, status, error) {
                                        console.log(error);
                                    },
                                },
                                formOptions: {
                                    main: {
                                        onBlur: 'none',
                                    },
                                },
                                fields: [
                                    {
                                        label: 'Match Id',
                                        name: 'match_id',
                                        def: matchID,
                                    },
                                ],
                            });

                            editor.on('preSubmit', function () {
                                return confirm('Ask again, are you sure?');
                            });

                            editor.create(false).submit();
                        },
                    },
                    {
                        extend: 'colvis',
                        text: 'Columns',
                    },
                ],
                order: [[0, 'asc']],
                lengthMenu: [
                    [10, 25, 50, 100, -1],
                    [10, 25, 50, 100, 'All'],
                ],
            });
            tableLeagueMatches.on('select deselect', function () {
                var selectedRows = tableLeagueMatches.rows({ selected: true });
                var flat = true;
                if (selectedRows.count() == 1) {
                    var dataSelectedRow = selectedRows.data()[0];
                    if (
                        dataSelectedRow.match_cancellations.cancel_type != null
                    ) {
                        flat = false;
                    }
                } else {
                    flat = false;
                }
                tableLeagueMatches.button(6).enable(flat);
            });
            tableLeagueMatches.on('select deselect', function () {
                var selectedRows = tableLeagueMatches.rows({ selected: true });
                var flat = false;
                if (selectedRows.count() == 1) {
                    var dataSelectedRow = selectedRows.data()[0];
                    if (
                        dataSelectedRow.match_cancellations.cancel_type !=
                            null &&
                        dataSelectedRow.match_cancellations
                            .created_replace_match == 0
                    ) {
                        flat = true;
                    }
                } else {
                    flat = false;
                }
                tableLeagueMatches.button(2).enable(flat);
            });
        }
        //-- fuction to create event for match  --/
        function initLeagueMatchesEventTable(
            match_id,
            home_team_id,
            away_team_id
        ) {
            var editorLeagueMatchesEvent = new $.fn.dataTable.Editor({
                ajax: {
                    type: 'POST',
                    url: SERVER_PATH + 'league/setMatchEvents',
                    headers: {	
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name
                    },
                    data: {
                        match_id: match_id,
                    },
                    dataType: 'json',
                    complete: function (response) {
                        var jsonData = JSON.parse(response.responseText);
                        // --- may need to reload
                        if (DEVELOPMENT_ENVIRONMENT)
                            console.log('status = ' + jsonData.status);
                        if (jsonData.status == 'OK') {
                            table.ajax.reload();
                        }
                    },
                    error: function (xhr, status, error) {},
                },
                table: '#tblLeagueMatchesEvents_' + match_id,
                formOptions: {
                    main: {
                        onBlur: 'none',
                    },
                },
                i18n: {
                    create: {
                        button: 'New',
                        title: 'Add new event',
                        submit: 'Create',
                    },
                    edit: {
                        button: 'Edit',
                        title: 'Edit event',
                        submit: 'Save',
                    },
                    remove: {
                        button: 'Delete',
                        title: 'Delete event',
                        submit: 'Delete',
                    },
                    error: {
                        system: 'System error, please contact administrator.',
                    },
                },
                fields: [
                    {
                        label: 'Player Name',
                        name: 'matches_detail.player_id',
                        type: 'select2',
                    },
                    {
                        label: 'Team',
                        name: 'matches_detail.team_id',
                        type: 'select',
                    },
                    {
                        label: 'Type',
                        name: 'matches_detail.type',
                        type: 'select',
                    },
                    {
                        label: 'Time',
                        name: 'matches_detail.time',
                        attr: {
                            type: 'number',
                        },
                    },
                    {
                        label: 'Note',
                        name: 'matches_detail.note',
                    },
                    {
                        label: 'match_id',
                        name: 'matches_detail.match_id',
                        def: match_id,
                        type: 'hidden',
                    },
                    {
                        label: 'User ID',
                        name: 'matches_detail.user_create_id',
                        def: $rootScope.user_id,
                        type: 'hidden',
                    },
                ],
            });
            var tableLeagueMatchesEvent = $(
                '#tblLeagueMatchesEvents_' + match_id
            ).DataTable({
                dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
                stateSave: false,
                deferRender: true,
                ajax: {
                    url: SERVER_PATH + 'league/getmatchevents',
                    type: 'POST',
                    headers: {	
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name
                    },
                    data: {
                        match_id: match_id,
                        home_team_id: home_team_id,
                        away_team_id: away_team_id,
                    },
                    dataType: 'json',
                    complete: function (response) {
                        console.log(response);
                    },
                    error: function (xhr, status, error) {},
                },
                language: {
                    paginate: {
                        previous: '<i class="fa fa-chevron-left"></i>',
                        next: '<i class="fa fa-chevron-right"></i>',
                    },
                },
                columnDefs: [
                    {
                        visible: false,
                    },
                ],
                columns: [
                    {
                        data: null,
                        render: function (data, type, row) {
                            var other_name =
                                row.players.other_name == null
                                    ? '_'
                                    : row.players.other_name;
                            var surname =
                                row.players.surname == null
                                    ? '_'
                                    : row.players.surname;
                            return surname + ' ' + other_name;
                        },
                    },
                    {
                        data: 'teams.name',
                    },
                    {
                        data: 'matches_detail.type',
                    },
                    {
                        data: 'matches_detail.time',
                    },
                    {
                        data: null,
                        render: function (data, type, row) {
                            return (
                                row.parens.surname + ' ' + row.parens.other_name
                            );
                        },
                    },
                    {
                        data: 'matches_detail.note',
                    },
                ],
                select: {
                    style: 'single',
                },
                buttons: [
                    {
                        extend: 'create',
                        editor: editorLeagueMatchesEvent,
                    },
                    {
                        extend: 'edit',
                        editor: editorLeagueMatchesEvent,
                    },
                    {
                        extend: 'remove',
                        editor: editorLeagueMatchesEvent,
                        text: 'Delete',
                    },
                    {
                        extend: 'colvis',
                        text: 'Columns',
                    },
                ],
            });
            editorLeagueMatchesEvent.on('preOpen', function () {
                // alert('preOpen');
                $('.modal-backdrop').hide();
                $('.bootstrap-dialog').hide();
                $('body').removeClass('modal-open-custom');
            });
            editorLeagueMatchesEvent.on('close', function () {
                // alert('preOpen');
                tableLeagueMatchesEvent.ajax.reload();
                tableLeagueMatches.ajax.reload();
                $('.modal-backdrop').show();
                $('.bootstrap-dialog').show();
                $('body').addClass('modal-open-custom');
            });

            editorLeagueMatchesEvent.on('initCreate', function () {
                var value = editorLeagueMatchesEvent
                    .field('matches_detail.team_id')
                    .val();
                editorLeagueMatchesEvent.enable(
                    'matches_detail.user_create_id'
                );
                editorLeagueMatchesEvent.field('matches_detail.type').show();
                editorLeagueMatchesEvent.field('matches_detail.team_id').show();
                $.ajax({
                    url: SERVER_PATH + 'league/getallplayerinteam',
                    headers: {	
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name
                    },
                    data: {
                        team_id: value,
                    },
                    error: function () {},
                    dataType: 'jsonp',
                    complete: function (ajax) {
                        var data = JSON.parse(ajax.responseText);
                        editorLeagueMatchesEvent
                            .field('matches_detail.player_id')
                            .update(data.info);
                    },
                    error: function (data) {},
                    type: 'POST',
                });
            });
            editorLeagueMatchesEvent.on('initEdit', function () {
                editorLeagueMatchesEvent.disable(
                    'matches_detail.user_create_id'
                );
                editorLeagueMatchesEvent.field('matches_detail.type').hide();
                editorLeagueMatchesEvent.field('matches_detail.team_id').hide();
                var value = editorLeagueMatchesEvent
                    .field('matches_detail.team_id')
                    .val();
                $.ajax({
                    url: SERVER_PATH + 'league/getallplayerinteam',
                    headers: {	
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name
                    },
                    data: {
                        team_id: value,
                    },
                    error: function () {},
                    dataType: 'jsonp',
                    complete: function (ajax) {
                        var data = JSON.parse(ajax.responseText);
                        editorLeagueMatchesEvent
                            .field('matches_detail.player_id')
                            .update(data.info);
                    },
                    error: function (data) {},
                    type: 'POST',
                });
            });
            $(
                editorLeagueMatchesEvent.field('matches_detail.team_id').input()
            ).on('change', function (e, d) {
                if (!d || !d.editor) {
                    var value = editorLeagueMatchesEvent
                        .field('matches_detail.team_id')
                        .val();
                    $.ajax({
                        url: SERVER_PATH + 'league/getallplayerinteam',
                        headers: {	
                            'x-user-id': $rootScope.user_id,
                            'x-user-email': $rootScope.user_name
                        },
                        data: {
                            team_id: value,
                        },
                        error: function () {},
                        dataType: 'jsonp',
                        complete: function (ajax) {
                            var data = JSON.parse(ajax.responseText);
                            editorLeagueMatchesEvent
                                .field('matches_detail.player_id')
                                .update(data.info);
                        },
                        error: function (data) {},
                        type: 'POST',
                    });
                    editorLeagueMatchesEvent
                        .field('matches_detail.player_id')
                        .val('');
                }
            });
        }

        function initLeagueFriendlyMatchesTable(
            league_id,
            league_name,
            group_name
        ) {
            if (
                $.fn.dataTable.isDataTable(
                    '#friendly_matches_table_' + league_id
                )
            ) {
                $('#friendly_matches_table_' + league_id)
                    .DataTable()
                    .destroy();
            }
            var editorFriendlyMatches = new $.fn.dataTable.Editor({
                ajax: {
                    type: 'POST',
                    url: SERVER_PATH + 'league/setLeagueMatches',
                    headers: {	
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name
                    },
                    data: {
                        league_id: league_id,
                        type_of_match: 1,
                        user_id: $rootScope.user_id,
                    },
                    dataType: 'json',
                    beforeSend: function () {
                        Swal.fire({
                            title: 'Please Wait!',
                            allowOutsideClick: false,
                            didOpen: () => {
                                Swal.showLoading();
                            },
                        });
                    },
                    complete: function (response) {
                        Swal.close();
                    },
                    error: function (xhr, status, error) {
                        Swal.close();
                    },
                },
                table: '#friendly_matches_table_' + league_id,
                formOptions: {
                    main: {
                        onBlur: 'none',
                    },
                },
                i18n: {
                    create: {
                        button: 'Add',
                        title: 'Add new match',
                        submit: 'Add',
                    },
                    edit: {
                        button: 'Edit',
                        title: 'Edit match',
                        submit: 'Save',
                    },
                    remove: {
                        button: 'Delete',
                        title: 'Delete match',
                        submit: 'Delete',
                    },
                    error: {
                        system: 'System error, please contact administrator.',
                    },
                },
                fields: [
                    {
                        label: 'League ID:',
                        name: 'matches.league_id',
                        type: 'hidden',
                        def: league_id,
                    },
                    {
                        label: 'League ID:',
                        name: 'matches.type_of_match',
                        type: 'hidden',
                        def: 1,
                    },
                    {
                        label: 'Date:',
                        name: 'matches.date',
                        type: 'datetime',
                        format: 'DD-MMM-YYYY',
                        default: moment().format('DD-MMM-YYYY'),
                    },
                    {
                        label: 'Start Time:',
                        name: 'matches.start_time',
                        type: 'datetime',
                        format: 'HH:mm',
                        def: '00:00',
                    },
                    {
                        label: 'End Time:',
                        name: 'matches.end_time',
                        type: 'datetime',
                        format: 'HH:mm',
                        def: '00:00',
                    },
                    {
                        label: 'Location:',
                        name: 'matches.location',
                        type: 'select2',
                        opts: {
                            placeholder: 'Select location',
                            tags: true,
                            closeOnSelect: true,
                        },
                    },
                    {
                        label: 'Home Team:',
                        name: 'matches.home_team_id',
                        type: 'select2',
                        opts: {
                            placeholder: 'Select a team',
                        },
                        def: 0,
                    },
                    {
                        label: 'Away Team:',
                        name: 'matches.away_team_id',
                        type: 'select2',
                        opts: {
                            placeholder: 'Select a team',
                        },
                        def: 0,
                    },
                ],
            });
            editorFriendlyMatches.on('initEdit', function () {
                editorFriendlyMatches.field('matches.home_team_id').hide();
                editorFriendlyMatches.field('matches.away_team_id').hide();
            });
            editorFriendlyMatches.on('initCreate', function () {
                editorFriendlyMatches.field('matches.home_team_id').show();
                editorFriendlyMatches.field('matches.away_team_id').show();
            });
            tableLeagueMatches = $(
                '#friendly_matches_table_' + league_id
            ).DataTable({
                dom: '<"row"<"col-sm-6"B><"col-sm-6"f>>rt<"row"i>',
                stateSave: false,
                deferRender: true,
                ajax: {
                    url: SERVER_PATH + 'league/getLeagueMatches',
                    type: 'POST',
                    headers: {	
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name
                    },
                    data: {
                        league_id: league_id,
                        type_of_match: 1,
                    },
                    dataType: 'json',
                    complete: function (response) {},
                    error: function (xhr, status, error) {},
                },
                language: {
                    paginate: {
                        previous: '<i class="fa fa-chevron-left"></i>',
                        next: '<i class="fa fa-chevron-right"></i>',
                    },
                },
                createdRow: function (row, data, dataIndex) {
                    if (data.matches.highlight == 1) {
                        $(row).css('background-color', '#FFFACD');
                    }
                },
                columns: [
                    {
                        data: null,
                        render: function (data, type, full, meta) {
                            return full.matches.date == ''
                                ? 'TBD'
                                : full.matches.date;
                        },
                    },
                    {
                        data: null,
                        render: function (data, type, full, meta) {
                            return full.matches.start_time == ''
                                ? 'TBD'
                                : full.matches.start_time;
                        },
                    },
                    {
                        data: null,
                        render: function (data, type, full, meta) {
                            return full.matches.end_time == ''
                                ? 'TBD'
                                : full.matches.end_time;
                        },
                    },
                    {
                        data: null,
                        render: function (data, type, full, meta) {
                            return full.matches.location == 0
                                ? 'TBD'
                                : full.venues.name;
                        },
                    },
                    {
                        data: 'matches.home_name',
                        className: 'center',
                        sortable: false,
                    },
                    {
                        data: null,
                        className: 'center',
                        sortable: false,
                        className: 'center',
                        render: function () {
                            return 'VS';
                        },
                    },
                    {
                        data: 'matches.away_name',
                        sortable: false,
                    },
                    {
                        data: 'matches.home_team_score',
                        className: 'center',
                        sortable: false,
                    },
                    {
                        data: null,
                        className: 'center',
                        sortable: false,
                        className: 'center',
                        render: function () {
                            return '-';
                        },
                    },
                    {
                        data: 'matches.away_team_score',
                        sortable: false,
                    },
                ],
                select: {
                    style: SELECT_MODE,
                },
                buttons: [
                    {
                        extend: 'collection',
                        text: 'Actions',
                        autoClose: true,
                        buttons: [
                            {
                                extend: 'excel',
                                name: 'excel',
                                text: '<i class="fa fa-file-excel-o"></i>&emsp;Export to Excel',
                                titleAttr: 'Export data to an Excel file',
                                filename: group_name + ' - ' + league_id,
                                exportOptions: {
                                    columns: ':visible',
                                    modifier: {
                                        autoFilter: true,
                                        // selected: true
                                    },
                                },
                            },
                        ],
                    },
                    {
                        extend: 'create',
                        editor: editorFriendlyMatches,
                        text: 'Add',
                    },
                    {
                        extend: 'edit',
                        editor: editorFriendlyMatches,
                        text: 'Edit',
                    },
                    {
                        extend: 'selectedSingle',
                        text: 'Match Detail',
                        action: function () {
                            var match = tableLeagueMatches
                                .rows({
                                    selected: true,
                                })
                                .data()[0].matches;
                            var msg = getLeagueMatchesDetailHtml(match.id);
                            // Show dialog
                            BootstrapDialog.show({
                                size: BootstrapDialog.SIZE_WIDE,
                                type: BootstrapDialog.TYPE_DANGER,
                                closable: true,
                                closeByBackdrop: false,
                                closeByKeyboard: true,
                                title:
                                    match.home_name + ' VS ' + match.away_name,
                                message: msg,
                                id: 'leaguefriendlymatch',
                                onshown: function (dialog) {
                                    initLeagueMatchesEventTable(
                                        match.id,
                                        match.home_team_id,
                                        match.away_team_id
                                    );
                                },
                                onhide: function (dialog) {
                                    $('body').removeClass('modal-open-custom');
                                },
                                onhidden: function (dialogRef) {
                                    $('body').removeClass('modal-open-custom');
                                },
                            });
                        },
                    },
                    {
                        extend: 'selectedSingle',
                        text: 'Update Match Result',
                        action: function () {
                            var match = tableLeagueMatches
                                .rows({
                                    selected: true,
                                })
                                .data()[0].matches;
                            console.warn(match);
                            var msg = getUpdateMatchesResultHtml(
                                match.home_name,
                                match.away_name
                            );
                            BootstrapDialog.show({
                                size: BootstrapDialog.SIZE_WIDE,
                                type: BootstrapDialog.TYPE_DANGER,
                                closable: true,
                                closeByBackdrop: false,
                                closeByKeyboard: true,
                                title:
                                    match.home_name + ' VS ' + match.away_name,
                                message: msg,
                                buttons: [
                                    {
                                        label: 'Close',
                                        action: function (dialogRef) {
                                            dialogRef.close();
                                        },
                                    },
                                    {
                                        label: 'Save',
                                        action: function (dialogRef) {
                                            var $form = $('#updateScore');
                                            $form.validate({
                                                onfocusout: false,
                                                onkeyup: false,
                                                onclick: false,
                                                rules: {
                                                    home_team: {
                                                        required: true,
                                                        digits: true,
                                                    },
                                                    away_team: {
                                                        required: true,
                                                        digits: true,
                                                    },
                                                },
                                                messages: {
                                                    home_team: {
                                                        required:
                                                            'this field is required',
                                                        digits: 'please enter number',
                                                    },
                                                    away_team: {
                                                        required:
                                                            'this field is required',
                                                        minlength:
                                                            'please enter number',
                                                    },
                                                },
                                            });
                                            if ($form.valid()) {
                                                var home_score = dialogRef
                                                    .getModalBody()
                                                    .find('#home_team')
                                                    .val();
                                                var away_score = dialogRef
                                                    .getModalBody()
                                                    .find('#away_team')
                                                    .val();
                                                var url =
                                                    SERVER_PATH +
                                                    'league/setLeagueMatchResult';
                                                var form = new FormData();
                                                form.append(
                                                    'home_score',
                                                    home_score
                                                );
                                                form.append(
                                                    'away_score',
                                                    away_score
                                                );
                                                form.append(
                                                    'match_id',
                                                    match.id
                                                );
                                                form.append(
                                                    'user_id',
                                                    $rootScope.user_id
                                                );
                                                $.ajax({
                                                    url: url,
                                                    type: 'POST',
                                                    cache: false,
                                                    headers: {	
                                                        'x-user-id': $rootScope.user_id,
                                                        'x-user-email': $rootScope.user_name
                                                    },
                                                    data: form,
                                                    async: false,
                                                    processData: false,
                                                    mimeType:
                                                        'multipart/form-data',
                                                    contentType: false,
                                                    timeout: 0,
                                                    error: function (err) {},
                                                    success: function (
                                                        response
                                                    ) {
                                                        if (
                                                            response.status ==
                                                            'OK'
                                                        ) {
                                                            tableLeagueMatches.ajax.reload();
                                                            dialogRef.close();
                                                        }
                                                    },
                                                });
                                            }
                                        },
                                    },
                                ],
                            });
                        },
                    },
                    {
                        extend: 'remove',
                        editor: editorFriendlyMatches,
                        text: 'Delete',
                    },
                    {
                        extend: 'colvis',
                        text: 'Columns',
                    },
                ],
                order: [[0, 'asc']],
                displayLength: -1,
            });
        }
        function initUpdatedPoints(league_id, table) {
            if ($.fn.dataTable.isDataTable('#tblLeagueEventTable')) {
                $('#tblLeagueEventTable').DataTable().destroy();
            }
            var editorLeagueEventTable = new $.fn.dataTable.Editor({
                ajax: {
                    type: 'POST',
                    url: SERVER_PATH + 'league/setLeagueTableEventChangePoints',
                    headers: {	
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name
                    },
                    data: {
                        league_id: league_id,
                    },
                    dataType: 'json',
                    complete: function (response) {},
                    error: function (xhr, status, error) {},
                },
                table: '#tblLeagueEventTable',
                formOptions: {
                    main: {
                        onBlur: 'none',
                    },
                },
                i18n: {
                    create: {
                        button: 'Add',
                        title: 'Add event league table',
                        submit: 'Add',
                    },
                    edit: {
                        button: 'Edit',
                        title: 'Edit event league table',
                        submit: 'Save',
                    },
                    remove: {
                        button: 'Delete',
                        title: 'Delete event league table',
                        submit: 'Delete',
                    },
                    error: {
                        system: 'System error, please contact administrator.',
                    },
                },
                fields: [
                    {
                        label: 'Points',
                        name: 'updated_points.points',
                        attr: {
                            type: 'number',
                        },
                        default: 0,
                    },
                    {
                        label: 'Reason',
                        name: 'updated_points.reason',
                    },
                    {
                        label: 'League Team',
                        name: 'updated_points.league_team',
                        type: 'select',
                    },
                    {
                        label: 'User ID',
                        name: 'updated_points.user_id',
                        def: $rootScope.user_id,
                        type: 'hidden',
                    },
                    {
                        label: 'Created at',
                        name: 'updated_points.created_at',
                        def: Date.now(),
                        type: 'hidden',
                    },
                ],
            });
            tableLeagueEventTable = $('#tblLeagueEventTable').DataTable({
                dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
                stateSave: false,
                deferRender: true,
                ajax: {
                    url: SERVER_PATH + 'league/getLeagueTableEventChangePoints',
                    type: 'POST',
                    headers: {	
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name
                    },
                    data: {
                        league_id: league_id,
                    },
                    dataType: 'json',
                    complete: function (response) {
                        console.log(response);
                    },
                    error: function (xhr, status, error) {},
                },
                language: {
                    paginate: {
                        previous: '<i class="fa fa-chevron-left"></i>',
                        next: '<i class="fa fa-chevron-right"></i>',
                    },
                },
                columnDefs: [
                    {
                        visible: false,
                    },
                ],
                columns: [
                    {
                        data: null,
                        render: function (data, type, row) {
                            return (
                                row.parens.surname + ' ' + row.parens.other_name
                            );
                        },
                    },
                    {
                        data: 'teams.name',
                    },
                    {
                        data: 'updated_points.points',
                    },
                    {
                        data: 'updated_points.reason',
                    },
                ],
                select: {
                    style: 'single',
                },
                lengthMenu: [
                    [10, 25, 50, 100, -1],
                    [10, 25, 50, 100, 'All'],
                ],
                buttons: [
                    {
                        extend: 'create',
                        editor: editorLeagueEventTable,
                    },
                    {
                        extend: 'edit',
                        editor: editorLeagueEventTable,
                    },
                    {
                        extend: 'remove',
                        editor: editorLeagueEventTable,
                        text: 'Delete',
                    },
                    {
                        extend: 'colvis',
                        text: 'Columns',
                    },
                ],
            });
            editorLeagueEventTable.on('create', function (e, json, data) {
                table.ajax.reload();
            });
            editorLeagueEventTable.on('edit', function (e, json, data) {
                table.ajax.reload();
            });
            editorLeagueEventTable.on('remove', function (e, json, data) {
                table.ajax.reload();
            });
        }
        function initLeagueTable(league_id) {
            console.log('initLeagueTable');
            if ($.fn.dataTable.isDataTable('#leagueTable_' + league_id)) {
                $('#leagueTable_' + league_id)
                    .DataTable()
                    .destroy();
            }
            table = $('#leagueTable_' + league_id).DataTable({
                dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
                ajax: {
                    url: SERVER_PATH + 'league/getLeagueTable',
                    type: 'POST',
                    headers: {	
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name
                    },
                    dataType: 'json',
                    data: {
                        league_id: league_id,
                    },
                    complete: function (response) {
                        console.log(response);
                    },
                    error: function (xhr, status, error) {},
                },
                language: {
                    paginate: {
                        previous: '<i class="fa fa-chevron-left"></i>',
                        next: '<i class="fa fa-chevron-right"></i>',
                    },
                },
                order: [[8, 'desc']],
                columns: [
                    {
                        data: 'name',
                        sortable: false,
                    },
                    {
                        data: 'P',
                        className: 'center',
                        sortable: false,
                    },
                    {
                        data: 'win',
                        className: 'center',
                        sortable: false,
                    },
                    {
                        data: 'draw',
                        className: 'center',
                        sortable: false,
                    },
                    {
                        data: 'loss',
                        className: 'center',
                        sortable: false,
                    },
                    {
                        data: 'gf',
                        className: 'center',
                        sortable: false,
                    },
                    {
                        data: 'ga',
                        className: 'center',
                        sortable: false,
                    },
                    {
                        data: 'R',
                        className: 'center',
                        sortable: false,
                    },
                    {
                        data: 'point',
                        className: 'center',
                        bSortable: false,
                    },
                    {
                        data: null,
                        render: function (data, type, row) {
                            // create a button with icon up
                            if (row.can_change_position != null) {
                                return '<button class="btn btn-primary btn-xs"><i class="fa fa-arrow-up"></i></button>';
                            } else {
                                return '';
                            }
                        },
                    },
                ],
                buttons: [{ extend: 'colvis', text: 'Columns' }],
            });

            // unbind the click event of the button
            $('#leagueTable_' + league_id + ' tbody').unbind('click');

            $('#leagueTable_' + league_id + ' tbody').on(
                'click',
                'button',
                function () {
                    var data = table.row($(this).parents('tr')).data();
                    $.ajax({
                        url: SERVER_PATH + 'league/swapPosition',
                        type: 'POST',
                        headers: {	
                            'x-user-id': $rootScope.user_id,
                            'x-user-email': $rootScope.user_name
                        },
                        beforeSend: function () {
                            // setting a timeout
                            Swal.showLoading();
                        },
                        data: {
                            league_id: league_id,
                            team_id: data.id,
                            down_team_id: data.can_change_position,
                        },
                        success: function (response) {
                            swal.close();
                            // transform the response to JSON
                            response = JSON.parse(response);
                            if (response.status == 'OK') {
                                Swal.fire({
                                    toast: true,
                                    icon: 'success',
                                    title:
                                        'Position of team ' +
                                        data.name +
                                        ' changed successfully',
                                    animation: false,
                                    position: 'top-right',
                                    showConfirmButton: false,
                                    timer: 1000,
                                    timerProgressBar: true,
                                    didOpen: (toast) => {
                                        toast.addEventListener(
                                            'mouseenter',
                                            Swal.stopTimer
                                        );
                                        toast.addEventListener(
                                            'mouseleave',
                                            Swal.resumeTimer
                                        );
                                    },
                                });

                                table.ajax.reload();
                            } else {
                                Swal.fire({
                                    title: 'Error',
                                    html: response.message,
                                    icon: 'error',
                                    type: 'error',
                                    confirmButtonText: 'OK',
                                    confirmButtonColor: '#ed1c24',
                                });
                            }
                        },
                        error: function (xhr, status, error) {
                            swal.close();
                        },
                    });
                }
            );

            return table;
        }
    }
);
