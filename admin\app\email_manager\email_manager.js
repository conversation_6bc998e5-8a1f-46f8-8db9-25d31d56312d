app.controller('emailManagerCtrl', function ($scope, $rootScope, $http, $sce) {
    var $ctrl = this;
    $('#page-wrapper').removeClass('nav-small');
    console.log('emailManagerCtrl');
    admin_getAllTemplates();
    // let ClassicEditor = CKSource.Editor;
    $scope.SystemImage = SYSTEM_IMAGE_PATH;

    $scope.name = 'emailManagerCtrl';
    $scope.templateSelected;
    $scope.templateEmail = '';
    $scope.templateEmailreview = '';
    $scope.template = { listTemplate: [], listVariable: [] };
    $scope.ckeditor = null;
    $scope.ckeditorChinese = null;
    $scope.ckeditorSubject = null;
    $scope.ckeditorChineseSubject = null;
    let dragDropArea = document.querySelector('.upload-file-drag-drop');
    const fileInput = document.createElement('input');
    let fileList = document.querySelector('.upload-file-list');

    // Configure input element
    fileInput.type = 'file';
    fileInput.multiple = false;

    $scope.decodeHtml = function (encodedStr) {
        var parser = new DOMParser();
        var doc = parser.parseFromString(encodedStr, 'text/html');
        return doc.documentElement.textContent;
    };

    $scope.currentEditor = null;

    const customColorPalette = [
        {
            color: '#fff',
            label: 'White',
        },
        {
            color: '#000',
            label: 'Black',
        },
        {
            color: '#ff3333',
            label: 'Red',
        },
        {
            label: 'Orange',
            color: '#ff8000',
        },
        {
            label: 'Yellow',
            color: '#ffff00',
        },
        {
            label: 'Green',
            color: '#00ff00',
        },
        {
            color: '#0099ff',
            label: 'Blue',
        },
        {
            color: '#2977ff',
            label: 'Light Blue',
        },
        {
            color: '#ff80bf',
            label: 'Pink',
        },
        {
            color: '#a64dff',
            label: 'Purple',
        },

        // ...
    ];
    ClassicEditor.create(document.querySelector('#email-editor'), {
        toolbar: [
            'heading',
            '|',
            'bold',
            'italic',
            'underline',
            'link',
            'bulletedList',
            'numberedList',
            'todoList',
            '|',
            'outdent',
            'indent',
            '|',
            'undo',
            'redo',
            'fontSize',
            'fontFamily',
            'fontColor',
            'fontBackgroundColor',
            'highlight',
            'findAndReplace',
            'removeFormat',
            'imageUpload',
            'imageInsert',
            'blockQuote',
            'insertTable',
            'mediaEmbed',
            'htmlEmbed',
            'sourceEditing',
        ],
        removePlugins: ['Markdown', 'Title'],
        simpleUpload: {
            // The URL that the images are uploaded to.
            uploadUrl: SERVER_PATH + 'template/adminUploadImage',
        },
        table: {
            contentToolbar: [
                'tableColumn',
                'tableRow',
                'mergeTableCells',
                'tableCellProperties',
                'tableProperties',
            ],

            // Set the palettes for tables.
            tableProperties: {
                borderColors: customColorPalette,
                backgroundColors: customColorPalette,
            },

            // Set the palettes for table cells.
            tableCellProperties: {
                borderColors: customColorPalette,
                backgroundColors: customColorPalette,
            },
        },
        htmlSupport: {
            allow: [
                {
                    name: /.*/,
                    attributes: true,
                    classes: true,
                    styles: true,
                },
            ],
        },
        htmlEmbed: {
            showPreviews: true,
        },
        mention: {
            feeds: [
                {
                    marker: '{',
                    feed: getFeedItems,
                    minimumCharacters: 1,
                },
            ],
        },
    })
        .then((editor) => {
            $scope.ckeditor = editor;
            // console.log(editor);
            editor.editing.view.change((writer) => {
                writer.setAttribute(
                    'spellcheck',
                    'false',
                    editor.editing.view.document.getRoot()
                );
            });
        })
        .catch((error) => {
            console.error(error);
        });

    ClassicEditor.create(document.querySelector('#email-chinese-editor'), {
        toolbar: [
            'heading',
            '|',
            'bold',
            'italic',
            'underline',
            'link',
            'bulletedList',
            'numberedList',
            'todoList',
            '|',
            'outdent',
            'indent',
            '|',
            'undo',
            'redo',
            'fontSize',
            'fontFamily',
            'fontColor',
            'fontBackgroundColor',
            'highlight',
            'findAndReplace',
            'removeFormat',
            'imageUpload',
            'imageInsert',
            'blockQuote',
            'insertTable',
            'mediaEmbed',
            'htmlEmbed',
            'sourceEditing',
        ],
        removePlugins: ['Markdown', 'Title'],
        simpleUpload: {
            // The URL that the images are uploaded to.
            uploadUrl: SERVER_PATH + 'template/adminUploadImage',
        },
        table: {
            contentToolbar: [
                'tableColumn',
                'tableRow',
                'mergeTableCells',
                'tableCellProperties',
                'tableProperties',
            ],

            // Set the palettes for tables.
            tableProperties: {
                borderColors: customColorPalette,
                backgroundColors: customColorPalette,
            },

            // Set the palettes for table cells.
            tableCellProperties: {
                borderColors: customColorPalette,
                backgroundColors: customColorPalette,
            },
        },
        htmlSupport: {
            allow: [
                {
                    name: /.*/,
                    attributes: true,
                    classes: true,
                    styles: true,
                },
            ],
        },
        htmlEmbed: {
            showPreviews: true,
        },
        mention: {
            feeds: [
                {
                    marker: '{',
                    feed: getFeedItems,
                    minimumCharacters: 1,
                },
            ],
        },
    })
        .then((editor) => {
            $scope.ckeditorChinese = editor;
            // console.log(editor);
            editor.editing.view.change((writer) => {
                writer.setAttribute(
                    'spellcheck',
                    'false',
                    editor.editing.view.document.getRoot()
                );
            });
        })
        .catch((error) => {
            console.error(error);
        });

    ClassicEditor.create(document.querySelector('#chinese-subject-editor'), {
        toolbar: ['undo', 'redo'],
        removePlugins: ['Markdown', 'Title'],
        mention: {
            feeds: [
                {
                    marker: '{',
                    feed: getFeedItems,
                    minimumCharacters: 1,
                },
            ],
        },
        wordCount: {
            onUpdate: (stats) => {
                const charactersProgress =
                    (stats.characters / maxCharacters) * circleCircumference;

                const isLimitExceeded = stats.characters > maxCharacters;
                const isCloseToLimit =
                    !isLimitExceeded && stats.characters > maxCharacters * 0.8;

                const circleDashArray = Math.min(
                    charactersProgress,
                    circleCircumference
                );

                // Set the stroke of the circle to show how many characters were typed.
                progressCircleChinese.setAttribute(
                    'stroke-dasharray',
                    `${circleDashArray},${circleCircumference}`
                );

                // Display the number of characters in the progress chart. When the limit is exceeded,
                // display how many characters should be removed.
                if (isLimitExceeded) {
                    charactersChineseBox.textContent = `-${
                        stats.characters - maxCharacters
                    }`;
                } else {
                    charactersChineseBox.textContent = stats.characters;
                }

                wordsBox.textContent = `Words: ${stats.words}`;

                console.warn(containerChinese);

                // If the content length is close to the character limit, add a CSS class to warn the user.
                containerChinese.classList.toggle(
                    'limit-close',
                    isCloseToLimit
                );

                // If the character limit is exceeded, add a CSS class that makes the content's background red.
                containerChinese.classList.toggle(
                    'limit-exceeded',
                    isLimitExceeded
                );

                // If the character limit is exceeded, disable the send button.
                btnSave.toggleAttribute('disabled', isLimitExceeded);
            },
        },
    })
        .then((editor) => {
            $scope.ckeditorChineseSubject = editor;
            // console.log(editor);
            editor.editing.view.change((writer) => {
                writer.setAttribute(
                    'spellcheck',
                    'false',
                    editor.editing.view.document.getRoot()
                );
            });
        })
        .catch((error) => {
            console.error(error);
        });

    $scope.setCurrentEditor = function (editor) {
        switch (editor) {
            case 'email':
                $scope.currentEditor = $scope.ckeditor;
                break;
            case 'email-chinese':
                $scope.currentEditor = $scope.ckeditorChinese;
                break;
            case 'email-subject':
                $scope.currentEditor = $scope.ckeditorSubject;
                break;
            case 'email-chinese-subject':
                $scope.currentEditor = $scope.ckeditorChineseSubject;
                break;
            default:
                $scope.currentEditor = $scope.ckeditor;
                break;
        }
    };

    const maxCharacters = 120;
    const container = document.querySelector('.container-ckeditor');
    const containerChinese = document.querySelector(
        '.container-ckeditor-chinese'
    );
    const progressCircle = document.querySelector('.chart__circle');
    const charactersBox = document.querySelector('.chart__characters');
    const progressCircleChinese = document.querySelector(
        '.chart__circle_chinese'
    );
    const charactersChineseBox = document.querySelector(
        '.chart__characters_chinese'
    );
    const wordsBox = document.querySelector('.words');
    const circleCircumference = Math.floor(
        2 * Math.PI * progressCircle.getAttribute('r')
    );
    const btnSave = document.querySelector('.btn_save');

    ClassicEditor.create(document.querySelector('#subject-editor'), {
        toolbar: ['undo', 'redo'],
        removePlugins: ['Markdown', 'Title'],
        mention: {
            feeds: [
                {
                    marker: '{',
                    feed: getFeedItems,
                    minimumCharacters: 1,
                },
            ],
        },
        wordCount: {
            onUpdate: (stats) => {
                const charactersProgress =
                    (stats.characters / maxCharacters) * circleCircumference;
                const isLimitExceeded = stats.characters > maxCharacters;
                const isCloseToLimit =
                    !isLimitExceeded && stats.characters > maxCharacters * 0.8;
                const circleDashArray = Math.min(
                    charactersProgress,
                    circleCircumference
                );

                // Set the stroke of the circle to show how many characters were typed.
                progressCircle.setAttribute(
                    'stroke-dasharray',
                    `${circleDashArray},${circleCircumference}`
                );

                // Display the number of characters in the progress chart. When the limit is exceeded,
                // display how many characters should be removed.
                if (isLimitExceeded) {
                    charactersBox.textContent = `-${
                        stats.characters - maxCharacters
                    }`;
                } else {
                    charactersBox.textContent = stats.characters;
                }

                wordsBox.textContent = `Words: ${stats.words}`;

                // If the content length is close to the character limit, add a CSS class to warn the user.
                container.classList.toggle('limit-close', isCloseToLimit);

                // If the character limit is exceeded, add a CSS class that makes the content's background red.
                container.classList.toggle('limit-exceeded', isLimitExceeded);

                // If the character limit is exceeded, disable the send button.
                btnSave.toggleAttribute('disabled', isLimitExceeded);
            },
        },
    })
        .then((editor) => {
            $scope.ckeditorSubject = editor;
            // console.log(editor);
            editor.editing.view.change((writer) => {
                writer.setAttribute(
                    'spellcheck',
                    'false',
                    editor.editing.view.document.getRoot()
                );
            });
        })
        .catch((error) => {
            console.error(error);
        });

    function getFeedItems(queryText) {
        return new Promise((resolve) => {
            setTimeout(() => {
                const itemsToDisplay = $scope.template.listVariable
                    .filter(isItemMatching)
                    .slice(0, 10);

                resolve(itemsToDisplay);
            }, 100);
        });

        function isItemMatching(item) {
            const searchString = queryText.toLowerCase();
            return item.toLowerCase().includes(searchString);
            // ||        item.id.toLowerCase().includes(searchString)
        }
    }

    function admin_getAllTemplates() {
        $http({
            method: 'POST',
            url: SERVER_PATH + 'template/getTemplates',
            transformRequest: function (obj) {
                // show loading
                Swal.fire({
                    title: 'Loading...',
                    allowEscapeKey: false,
                    allowOutsideClick: false,
                    onOpen: () => {
                        Swal.showLoading();
                    },
                });
            },
        })
            .then(
                function successCallback(response) {
                    // console.log(response);
                    $scope.template.listTemplate = response.data.info;
                },
                function errorCallback(response) {
                    console.log(response);
                }
            )
            .finally(function () {
                // hide loading
                Swal.close();
            });
    }

    // Utility: Create file item
    function createFileItem(file, progress = 0) {

        let photoIcon='docs.png';

        switch (file.file_type) {
            case 'xlsx':
            case 'xls':
            case 'csv':
                photoIcon = 'excel.png';
                break;
            case 'pdf':
                photoIcon = 'pdf.png';
                break;
            case 'doc':
            case 'docx':
            case 'txt':
                photoIcon = 'docs.png';
                break;
            case 'jpg':
            case 'jpeg':
            case 'png':
                photoIcon = 'photo.png';
                break;
            default:
                photoIcon = 'docs.png';
                break;
        }

        photoIcon=$scope.SystemImage+photoIcon;

        const fileItem = document.createElement('div');
        fileItem.classList.add('upload-file-item');

        fileItem.innerHTML =
            `
          <div class="upload-file-icon">
              <img src="`+photoIcon+`" alt="File">
          </div>
          <div class="upload-file-info">
              <span class="upload-file-name">${file.name}</span>
              <span class="upload-file-size">${(
                  file.size /
                  1024 /
                  1024
              ).toFixed(2)} MB</span>
              <div class="upload-file-progress">
                  <div class="upload-file-progress-bar" style="--progress: ${progress}%;"></div>
                  <span class="upload-file-progress-percentage">${progress}%</span>
              </div>
          </div>
          <button class="upload-remove-button" data-id=` +
            file.id +
            `>
              <img src="` +
            $scope.SystemImage +
            `close-icon.png" alt="Remove">
          </button>
      `;

        const removeButton = fileItem.querySelector('.upload-remove-button');

        removeButton.addEventListener('click', () => {
            // get atrribute data-id
            const id = removeButton.getAttribute('data-id');

            Swal.fire({
                title: 'Are you sure?',
                text: 'You will not be able to recover this file!',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'Yes, delete it!',
                cancelButtonText: 'No, keep it',
            }).then((result) => {
                if (result.isConfirmed) {
                    $http({
                        method: 'POST',
                        url: SERVER_PATH + 'template/deleteAttachment',
                        data: {
                            attachment_id: id,
                        },
                        headers: { 'Content-Type': undefined },
                    }).then(
                        function successCallback(response) {
                            if (response.data.status === 'OK') {
                                fileItem.remove();
                                $scope.template.listTemplate = $scope.template.listTemplate.map(
                                    (item) => {
                                        if (item.id == $scope.templateSelected.id) {
                                            item.files = item.files.filter(
                                                (file) => file.id != id
                                            );
                                        }
                                        return item;
                                    }
                                );
                            } else {
                                Swal.fire({
                                    title: 'Error',
                                    text: response.data.message,
                                    icon: 'error',
                                    confirmButtonText: 'OK',
                                });
                            }
                        },
                        function errorCallback(response) {
                            Swal.fire({
                                title: 'Error',
                                text: 'An error occurred while deleting the file',
                                icon: 'error',
                                confirmButtonText: 'OK',
                            });
                        }
                    );
                }
            });
        });
        return fileItem;
    }

    function simulateUpload(file, fileItem) {
        let progress = 0;
        const progressBar = fileItem.querySelector('.upload-file-progress-bar');
        const progressPercentage = fileItem.querySelector(
            '.upload-file-progress-percentage'
        );

        const interval = setInterval(() => {
            progress += Math.floor(Math.random() * 10) + 5;
            if (progress >= 100) progress = 100;

            progressBar.style.setProperty('--progress', `${progress}%`);
            progressPercentage.textContent = `${progress}%`;

            if (progress === 100) clearInterval(interval);
        }, 300);
    }

    function handleUploadAttachment(file) {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('template_id', $scope.templateSelected.id);

        Swal.fire({
            title: 'Loading...',
            allowEscapeKey: false,
            allowOutsideClick: false,
            onOpen: () => {
                Swal.showLoading();
            },
        });

        $http({
            method: 'POST',
            url: SERVER_PATH + 'template/adminUploadAttachment',
            data: formData,
            mimeType: 'multipart/form-data',
            headers: {
                'Content-Type': undefined,
            },
        })
            .then(
                function successCallback(response) {
                    Swal.close();
                    const responseData = response.data;

                    if (responseData.status == 'OK') {
                        const temp_file = {
                            name: file.name,
                            size: file.size,
                            id: responseData.info.id,
                            file_type: file.extension
                        };
                        const fileItem = createFileItem(temp_file, 100);
                        fileList.appendChild(fileItem);
                    } else {
                        Swal.fire({
                            title: 'Error',
                            text: responseData.message,
                            icon: 'error',
                            confirmButtonText: 'OK',
                        });
                    }
                },
                function errorCallback(response) {
                    Swal.close();
                    Swal.fire({
                        title: 'Error',
                        text: 'An error occurred while uploading the file',
                        icon: 'error',
                        confirmButtonText: 'OK',
                    });
                }
            )
    }

    function handleFiles(files) {
        if (files.length == 0) return;

        if (files.length > 1) {
            Swal.fire({
                title: 'Warning',
                text: 'Only one file can be uploaded at a time',
                icon: 'warning',
                confirmButtonText: 'OK',
            });

            return;
        }

        Array.from(files).forEach((file) => {
            handleUploadAttachment(file);
        });
    }

    function handleDragover(event) {
        event.preventDefault();
        dragDropArea.classList.add('drag-over');
    }

    function handleLeave(event) {
        dragDropArea.classList.remove('drag-over');
    }

    function handleDrop(event) {
        event.preventDefault();
        dragDropArea.classList.remove('drag-over');
        handleFiles(event.dataTransfer.files);
    }

    function handleClicked() {
        fileInput.click();
    }

    function handleChanged(event) {
        handleFiles(fileInput.files);
        event.target.value = '';
    }

    $scope.changeTemplate = function (template) {
        template = $scope.template.listTemplate.find(
            (x) => x.id == template.id
        );
        // console.log($scope.ckeditor);
        $scope.templateSelected = template;
        $scope.templateEmail = template.content;
        $scope.templateEmailreview = $sce.trustAsHtml(template.content);
        //string to array template.vars
        if (template.vars) {
            $scope.template.listVariable = addSpecialChar(
                '{{',
                template.vars.split('|'),
                '}}'
            );
        }
        initCkeditor(template);

        setTimeout(() => {
            dragDropArea = document.querySelector('.upload-file-drag-drop');

            fileList = document.querySelector('.upload-file-list');

            // reset all event listeners
            dragDropArea.removeEventListener('dragover', handleDragover);

            dragDropArea.removeEventListener('dragleave', handleLeave);

            dragDropArea.removeEventListener('drop', handleDrop);

            dragDropArea.removeEventListener('click', handleClicked);

            fileInput.removeEventListener('change', handleChanged);

            // Add event listeners

            dragDropArea.addEventListener('dragover', handleDragover);

            dragDropArea.addEventListener('dragleave', handleLeave);

            dragDropArea.addEventListener('drop', handleDrop);

            dragDropArea.addEventListener('click', handleClicked);

            fileInput.addEventListener('change', handleChanged);

            // Reset file list
            fileList.innerHTML = '';

            const files = template.files;

            if (files.length > 0) {
                files
                    .map((item) => ({
                        name: item.filename,
                        size: item.filesize,
                        id: item.id,
                        file_type: item.extension,
                    }))
                    .forEach((file) => {
                        const fileItem = createFileItem(file, 100);
                        fileList.appendChild(fileItem);
                    });
            }
        }, 300);
    };
    function addSpecialChar(first_char, vars, last_chart) {
        for (var i = 0; i < vars.length; i++) {
            vars[i] = first_char + vars[i].trim() + last_chart;
        }
        return vars;
    }

    $scope.insertContent = function (html) {
        if (!$scope.currentEditor) {
            return false;
        }

        $scope.currentEditor.model.change((writer) => {
            const insertPosition =
                $scope.currentEditor.model.document.selection.getFirstPosition();
            writer.insertText(html, { 'data-mention': html }, insertPosition);
            // $scope.ckeditor.model.insertContent(html, insertPosition);
        });
        return true;
    };

    function initCkeditor(template) {
        // set html to ckeditor
        // $scope.ckeditor.setData(template.content);
        // html decode
        const temp_content = $scope.decodeHtml(template.content);
        const temp_chinese_content = $scope.decodeHtml(
            template.chinese_content
        );
        $scope.ckeditor.setData(temp_content);
        $scope.ckeditorChinese.setData(temp_chinese_content);
        $scope.ckeditorSubject.setData(template.subject);
        $scope.ckeditorChineseSubject.setData(template.chinese_subject);
    }

    $scope.getData = function () {
        $scope.ckeditor.setData($scope.ckeditor.getData());
    };

    function showNotification(message) {
        var notification = new NotificationFx({
            message: message,
            layout: 'attached',
            effect: 'genie',
            type: 'success', // notice, success, warning or error
            ttl: 1000,
            onClose: function () {},
        });

        notification.show();
    }

    $scope.saveTemplate = function () {
        if ($scope.templateSelected) {
            $http({
                method: 'POST',
                url: SERVER_PATH + 'template/saveTemplate',
                data: {
                    id: $scope.templateSelected.id,
                    subject: $scope.ckeditorSubject.getData(),
                    chinese_subject: $scope.ckeditorChineseSubject.getData(),
                    content: $scope.ckeditor.getData(),
                    chinese_content: $scope.ckeditorChinese.getData(),
                    updated_by: $rootScope.user_id,
                    action: 'update',
                },
                headers: { 'Content-Type': undefined },
            }).then(
                function successCallback(response) {
                    console.log(response);
                    if (response.data.status == 'OK') {
                        showNotification(response.data.message);
                    } else {
                        BootstrapDialog.show({
                            title: 'Error',
                            type: BootstrapDialog.TYPE_DANGER,
                            message: response.data.message,
                            buttons: [
                                {
                                    label: 'OK',
                                    action: function (dialog) {
                                        dialog.close();
                                    },
                                },
                            ],
                        });
                    }

                    admin_getAllTemplates();
                },
                function errorCallback(response) {
                    console.log(response);
                }
            );
        }
    };
});
