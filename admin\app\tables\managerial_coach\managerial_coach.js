app.controller(
    'ManagerialCoachCtrl',
    function ($scope, $rootScope, $routeParams, $http) {
        var editorManagerialCoach = new $.fn.dataTable.Editor({
            ajax: {
                type: 'POST',
                url: SERVER_PATH + 'paren/setManagerialCoach',
                headers: {	
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name
                },
                data: {
                    // "pgroup_id": pgroup_id
                },
                dataType: 'json',
                complete: function (response) {
                    var jsonData = JSON.parse(response.responseText);
                    if (typeof jsonData.fieldErrors == 'undefined') {
                        tableManagerialCoach.ajax.reload();
                    }
                },
                error: function (xhr, status, error) {},
            },
            table: '#tableManagerialCoach',
            formOptions: {
                main: {
                    onBlur: 'none',
                },
            },
            i18n: {
                create: {
                    button: 'New',
                    title: 'Create new Coach',
                    submit: 'Create',
                },
                edit: {
                    button: 'Edit',
                    title: 'Edit Coach',
                    submit: 'Save',
                },
                error: {
                    system: 'System error, please contact Coach.',
                },
            },
            fields: [
                {
                    label: 'Surname:',
                    name: 'parens.surname',
                },
                {
                    label: 'Other name',
                    name: 'parens.other_name',
                },
                {
                    label: 'Chinese name',
                    name: 'parens.chinese_name',
                },
                {
                    label: 'Email',
                    name: 'parens.email',
                },
                {
                    name: 'parens.type',
                    type: 'hidden',
                    def: TYPE_MANAGERIAL_COACH,
                },
                {
                    name: 'parens.country_code',
                    type: 'hidden',
                    def: '+852',
                },
                {
                    name: 'parens.iso_code',
                    type: 'hidden',
                    def: 'hk',
                },
                {
                    label: 'Phone number',
                    name: 'parens.phone',
                    type: 'telephone',
                    opts: {
                        preferredCountries: ['hk', 'cn'],
                        initialCountry: 'hk',
                    },
                },
                {
                    label: 'CoachID',
                    name: 'parens.coach_id_no',
                },
                {
                    label: 'Create user:',
                    name: 'create_user',
                    type: 'checkbox',
                    separator: '|',
                    options: [{ label: '', value: 1 }],
                },
            ],
        });

        // add parent.id when editing
        editorManagerialCoach.on('preSubmit', function (e, o, action) {
            if (action == 'edit') {
                var data = tableManagerialCoach.rows({ selected: true }).data();
                var parent_id = data[0]['parens']['id'];
                o.parent_id = parent_id;
            }
        });

        editorManagerialCoach.on('initEdit', function () {
            editorManagerialCoach.field('create_user').hide();
        });

        editorManagerialCoach.on('initCreate', function () {
            editorManagerialCoach.field('create_user').show();
        });

        tableManagerialCoach = $('#tableManagerialCoach').DataTable({
            dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
            stateSave: true,
            deferRender: true,
            ajax: {
                url: SERVER_PATH + 'paren/getManagerialCoach',
                type: 'POST',
                headers: {	
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name
                },
                data: {},
                dataType: 'json',
                complete: function (response) {},
                error: function (xhr, status, error) {},
            },
            language: {
                info: 'Showing _START_ to _END_ of _TOTAL_ Coach',
                infoEmpty: 'Showing 0 to 0 of 0 Coach',
                lengthMenu: 'Show _MENU_ Coach',
                select: {
                    rows: {
                        _: 'You have selected %d Coach ',
                        0: 'Click an Coach to select',
                        1: '1 Coach selected',
                    },
                },
                paginate: {
                    previous: '<i class="fa fa-chevron-left"></i>',
                    next: '<i class="fa fa-chevron-right"></i>',
                },
            },
            columns: [
                { data: 'parens.coach_id_no' },
                { data: 'parens.surname' },
                { data: 'parens.other_name' },
                { data: 'parens.chinese_name' },
                { data: 'parens.email' },
                { data: 'parens.phone' },
                {
                    data: null,
                    render: function (data, type, row) {
                        return '<button type="button" data-toggle="modal" data-target="#modal-coaches" class="btn btn-primary modal-coaches" >Manage Certificates</button>';
                    },
                },
            ],
            select: {
                style: 'single',
                selector: 'td:first-child',
            },
            order: [[1, 'asc']],
            lengthMenu: [
                [10, 25, 50, 100, -1],
                [10, 25, 50, 100, 'All'],
            ],
            buttons: [
                { extend: 'create', editor: editorManagerialCoach },
                { extend: 'edit', editor: editorManagerialCoach },
                {
                    extend: 'selectedSingle',
                    text: 'Change managerial coach to coach',
                    action: function (e, dt, node, config) {
                        let data = tableManagerialCoach
                            .rows({ selected: true })
                            .data();

                        let coach_id = data[0]['parens']['id'];

                        jQuery.ajax({
                            type: 'POST',
                            url:
                                SERVER_PATH +
                                'paren/changeManagerialCoachToCoach',
                            async: false,
                            headers: {	
                                'x-user-id': $rootScope.user_id,
                                'x-user-email': $rootScope.user_name
                            },
                            data: {
                                coach_id: coach_id,
                            },
                            dataType: 'json',
                            complete: function (response) {
                                var jsonData = JSON.parse(
                                    response.responseText
                                );

                                if (jsonData.status == 'OK') {
                                    BootstrapDialog.show({
                                        title: 'SUCCESS',
                                        type: BootstrapDialog.TYPE_SUCCESS,
                                        message: jsonData.message,
                                    });

                                    tableManagerialCoach.ajax.reload();
                                } else {
                                    BootstrapDialog.show({
                                        title: 'ERROR !',
                                        type: BootstrapDialog.TYPE_DANGER,
                                        message: jsonData.message,
                                    });
                                }
                            },
                        });
                    },
                },
                {
                    extend: 'selectedSingle',
                    text: 'Change managerial coach to club manager',
                    action: function (e, dt, node, config) {
                        let data = tableManagerialCoach
                            .rows({ selected: true })
                            .data();

                        let coach_id = data[0]['parens']['id'];

                        jQuery.ajax({
                            type: 'POST',
                            url:
                                SERVER_PATH +
                                'paren/changeManagerialCoachToClubManager',
                            async: false,
                            headers: {	
                                'x-user-id': $rootScope.user_id,
                                'x-user-email': $rootScope.user_name
                            },
                            data: {
                                coach_id: coach_id,
                            },
                            dataType: 'json',
                            complete: function (response) {
                                var jsonData = JSON.parse(
                                    response.responseText
                                );

                                if (jsonData.status == 'OK') {
                                    BootstrapDialog.show({
                                        title: 'SUCCESS',
                                        type: BootstrapDialog.TYPE_SUCCESS,
                                        message: jsonData.message,
                                    });

                                    tableManagerialCoach.ajax.reload();
                                } else {
                                    BootstrapDialog.show({
                                        title: 'ERROR !',
                                        type: BootstrapDialog.TYPE_DANGER,
                                        message: jsonData.message,
                                    });
                                }
                            },
                        });
                    },
                },
                { extend: 'colvis', text: 'Columns' },
            ],
        });

        function getModalLevelTableHtml(id) {
            var str =
                '' +
                '<div class="main-box-body clearfix">' +
                '<div class="table-responsive">' +
                // '<a data-match-route="/tables/coach" href="#/tables/coach" class="active">Add new Coach</a>'+
                '<table id="levelHistory_' +
                id +
                '" class="table table-striped table-bordered table-hover" cellspacing="0" width="100%">' +
                '<thead>' +
                '<tr>' +
                '<th>Level</th>' +
                '<th>From date</th>' +
                '</tr>' +
                '</thead>' +
                '</table>' +
                '</div>' +
                '</div>' +
                '';
            return str;
        }

        $('#tableManagerialCoach').on(
            'click',
            'tbody .modal-coaches',
            function () {
                let row = $(this).closest('tr');
                let data = tableManagerialCoach.row(row).data();
                var msg = getModalLevelTableHtml(data.parens.id);
                // Show dialog
                BootstrapDialog.show({
                    title:
                        "Coach's Certification History - " +
                        data.parens.other_name +
                        ' ' +
                        data.parens.surname +
                        ' (' +
                        data.parens.email +
                        ')',
                    message: msg,
                    size: BootstrapDialog.SIZE_WIDE,
                    onshown: function (dialogRef) {
                        initLevelHistoryTbl(data);
                    },
                });
            }
        );

        function initLevelHistoryTbl(d) {
            // console.log(d);
            editorLevels = new $.fn.dataTable.Editor({
                ajax: {
                    type: 'POST',
                    url: SERVER_PATH + 'paren/getCoachLevels',
                    headers: {	
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name
                    },
                    data: {
                        coach_id: d.parens.id,
                    },
                    async: false,
                    dataType: 'json',
                    complete: function (response) {
                        // reload table
                        tableLevels.ajax.reload();
                    },
                    error: function (xhr, status, error) {},
                },
                table: '#levelHistory_' + d.parens.id,
                formOptions: {
                    main: {
                        onBlur: 'none',
                    },
                },
                i18n: {
                    create: {
                        title: 'Add new level',
                        submit: 'Add',
                    },
                    edit: {
                        title: 'Edit level',
                        submit: 'Update',
                    },
                    error: {
                        system: 'System error, please contact administrator.',
                    },
                },
                fields: [
                    {
                        name: 'coach_levels.coach_id',
                        type: 'hidden',
                        def: d.parens.id,
                    },
                    {
                        name: 'coach_levels.level',
                        label: 'Level',
                        type: 'select',
                    },
                    {
                        name: 'coach_levels.from_date',
                        label: 'From date',
                        type: 'datetime',
                        format: 'DD-MMM-YYYY',
                    },
                ],
            });

            tableLevels = $('#levelHistory_' + d.parens.id).DataTable({
                dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
                stateSave: false,
                deferRender: true,
                ajax: {
                    url: SERVER_PATH + 'paren/getCoachLevels',
                    type: 'POST',
                    headers: {	
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name
                    },
                    data: {
                        coach_id: d.parens.id,
                    },
                    dataType: 'json',
                    complete: function (response) {},
                    error: function (xhr, status, error) {},
                },
                language: {
                    paginate: {
                        previous: '<i class="fa fa-chevron-left"></i>',
                        next: '<i class="fa fa-chevron-right"></i>',
                    },
                },

                columns: [
                    {
                        data: 'coach_levels.level',
                        render: function (data, type, row) {
                            return COACH_CERTIFICATE[data]['name'];
                        },
                    },
                    { data: 'coach_levels.from_date' },
                ],
                select: {
                    style: 'single',
                    selector: 'td:not(:last-child)',
                },
                order: [[0, 'asc']],
                buttons: [
                    {
                        extend: 'create',
                        editor: editorLevels,
                    },
                    {
                        extend: 'remove',
                        editor: editorLevels,
                    },
                    {
                        extend: 'colvis',
                        text: 'Columns',
                    },
                ],
            });
        }
    }
);
