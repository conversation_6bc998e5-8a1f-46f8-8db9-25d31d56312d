<div class="row">
    <div class="col-lg-12">
        <div class="clearfix">
            <h1 class="pull-left">Coaches</h1>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-12">
        <div class="main-box clearfix">
            <div class="main-box-body clearfix">
                <div class="table-responsive">
                    <table id="tableCoach" class="table table-striped table-bordered table-hover" cellspacing="0"
                        width="100%">
                        <thead>
                            <tr>
                                <th>Coach ID</th>
                                <th>Surname</th>
                                <th>Other name</th>
                                <th>Chinese name</th>
                                <th>Email</th>
                                <th>Phone</th>
                                <th>Coach level</th>
                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript" language="javascript" class="init">

    $(document).ready(function () {

        var table;
        var tableCoach;

        user_id = localStorage.getItem('hkjflApp.user_id');
		user_name = localStorage.getItem('hkjflApp.user_name');

        var uploadEditor = new $.fn.dataTable.Editor({
            fields: [{
                label: 'CSV file:',
                name: 'csv',
                type: 'upload',
                fieldInfo: 'You can download the sample file <a href="' + SYSTEM_IMAGE_PATH + 'coaches.xlsx" target="_blank">here</a>',
                ajax: function (files, done) {
                    var form = new FormData();
                    form.append("file", files[0]);
                    var settings = {
                        "url": SERVER_PATH + "paren/updoadCoachFile",
                        "method": "POST",
                        "timeout": 0,
                        "processData": false,
                        "mimeType": "multipart/form-data",
                        "contentType": false,
                        "data": form,
                        "headers": {
                            'x-user-id': user_id,
                            'x-user-email': user_name
                        },
                        beforeSend: function (xhr) {
                            Swal.fire({
                                title: 'Please Wait!',
                                allowOutsideClick: false,
                                didOpen: () => {
                                    Swal.showLoading();
                                },
                            });
                        }
                    };

                    $.ajax(settings).done(function (response) {
                        Swal.close();

                        var results = JSON.parse(response);

                        if (results.status == 'ERROR') {
                            uploadEditor.field('csv').error(' Import was failed: ' + results.message);
                        }
                        else {
                            uploadEditor.close();
                            Swal.fire({
                                type: 'success',
                                icon: 'success',
                                title: 'Success',
                                text: results.message,
                                confirmButtonClass: 'btn btn-primary',
                                buttonsStyling: false,
                            });
                            tableCoach.ajax.reload();
                        }
                    });
                }
            }]
        });

        var editorCoach = new $.fn.dataTable.Editor({
            ajax: {
                type: 'POST',
                url: SERVER_PATH + "paren/setCoachs",
                headers: {	
                    'x-user-id': user_id,
                    'x-user-email': user_name
                },
                data: {
                    // "pgroup_id": pgroup_id
                },
                dataType: 'json',
                complete: function (response) {
                    var jsonData = JSON.parse(response.responseText);
                    if (typeof jsonData.fieldErrors == 'undefined') {
                        tableCoach.ajax.reload();
                    }
                },
                error: function (xhr, status, error) {
                },
            },
            table: "#tableCoach",
            formOptions: {
                main: {
                    onBlur: 'none'
                }
            },
            i18n: {
                create: {
                    button: "New",
                    title: "Create new Coach",
                    submit: "Create"
                },
                edit: {
                    button: "Edit",
                    title: "Edit Coach",
                    submit: "Save"
                },
                error: {
                    system: "System error, please contact Coach."
                },
            },
            fields: [
                {
                    label: "Surname:",
                    name: "parens.surname"
                }, {
                    label: "Other name",
                    name: "parens.other_name"
                },
                {
                    label: "Chinese name",
                    name: "parens.chinese_name"
                },
                {
                    label: "Email",
                    name: "parens.email"
                },
                {
                    name: "parens.type",
                    type: "hidden",
                    def: TYPE_COACH
                },
                {
                    name: "parens.country_code",
                    type: "hidden",
                    def: '+852'
                },
                {
                    name: "parens.iso_code",
                    type: "hidden",
                    def: 'hk'
                },
                {
                    label: "Phone number",
                    name: "parens.phone",
                    type: "telephone",
                    opts: {
                        preferredCountries: ['hk', 'cn'],
                        initialCountry: 'hk'
                    }
                },
                {
                    label: "CoachID",
                    name: "parens.coach_id_no"
                }
                , {
                    label: "Create user:",
                    name: "create_user",
                    type: "checkbox",
                    separator: "|",
                    options: [
                        { label: '', value: 1 }
                    ]
                }
            ]
        });
        // add parent.id when editing
        editorCoach.on('preSubmit', function (e, o, action) {
            if (action == 'edit') {
                var data = tableCoach.rows({ selected: true }).data();
                var parent_id = data[0]['parens']['id'];
                o.parent_id = parent_id;
            }
        });

        tableCoach = $('#tableCoach').DataTable({
            dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
            stateSave: true,
            deferRender: true,
            ajax: {
                url: SERVER_PATH + "paren/getCoachs",
                type: 'POST',
                headers: {	
                    'x-user-id': user_id,
                    'x-user-email': user_name
                },
                data: {
                    // "pgroup_id": pgroup_id
                },
                dataType: 'json',
                complete: function (response) {
                },
                error: function (xhr, status, error) {
                },
            },
            language: {
                info: "Showing _START_ to _END_ of _TOTAL_ Coach",
                infoEmpty: "Showing 0 to 0 of 0 Coach",
                lengthMenu: "Show _MENU_ Coach",
                select: {
                    rows: {
                        "_": "You have selected %d Coach ",
                        "0": "Click an Coach to select",
                        "1": "1 Coach selected"
                    },
                },
                paginate: {
                    previous: '<i class="fa fa-chevron-left"></i>',
                    next: '<i class="fa fa-chevron-right"></i>'
                }
            },
            columns: [
                { data: "parens.coach_id_no" },
                { data: "parens.surname" },
                { data: "parens.other_name" },
                { data: "parens.chinese_name" },
                { data: "parens.email" },
                { data: "parens.phone" },
                {
                    data: null, render: function (data, type, row) {
                        return '<button type="button" data-toggle="modal" data-target="#modal-coaches" class="btn btn-primary modal-coaches" >Manage Certificates</button>';
                    }
                },
            ],
            select: {
                style: 'single',
                selector: 'td:first-child',
            },
            order: [[1, 'asc']],
            lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "All"]],
            buttons: [
                { extend: "create", editor: editorCoach },
                { extend: "edit", editor: editorCoach },
                {
                    extend: "selectedSingle",
                    text: "Change coach to parent",
                    action: function (e, dt, button, config) {
                        let data = tableCoach.rows({ selected: true }).data();

                        let coach_id = data[0]['parens']['id']

                        jQuery.ajax({
                            type: 'POST',
                            url: SERVER_PATH + "paren/changeCoachToParent",
                            async: false,
                            data: {
                                'coach_id': coach_id
                            },
                            headers: {	
                                'x-user-id': user_id,
                                'x-user-email': user_name
                            },
                            dataType: 'json',
                            complete: function (response) {
                                var jsonData = JSON.parse(response.responseText);

                                console.log(jsonData);

                                if (jsonData.status == "OK") {
                                    BootstrapDialog.show({
                                        title: 'SUCCESS',
                                        type: BootstrapDialog.TYPE_SUCCESS,
                                        message: jsonData.message
                                    });

                                    tableCoach.ajax.reload();
                                } else {
                                    BootstrapDialog.show({
                                        title: 'ERROR !',
                                        type: BootstrapDialog.TYPE_DANGER,
                                        message: jsonData.message
                                    });
                                }
                            }
                        });
                    }
                },
                {
                    extend: "selectedSingle",
                    text: "Change Coach to Managerial Coach",
                    action: function (e, dt, node, config) {
                        let data = tableCoach.rows({ selected: true }).data();

                        let coach_id = data[0]['parens']['id'];

                        jQuery.ajax({
                            type: 'POST',
                            url: SERVER_PATH + "paren/changeCoachToManagerialCoach",
                            async: false,
                            data: {
                                'coach_id': coach_id
                            },
                            headers: {	
                                'x-user-id': user_id,
                                'x-user-email': user_name
                            },
                            dataType: 'json',
                            complete: function (response) {
                                var jsonData = JSON.parse(response.responseText);

                                console.log(jsonData);

                                if (jsonData.status == "OK") {
                                    BootstrapDialog.show({
                                        title: 'SUCCESS',
                                        type: BootstrapDialog.TYPE_SUCCESS,
                                        message: jsonData.message
                                    });

                                    tableCoach.ajax.reload();
                                } else {
                                    BootstrapDialog.show({
                                        title: 'ERROR !',
                                        type: BootstrapDialog.TYPE_DANGER,
                                        message: jsonData.message
                                    });
                                }
                            }
                        });
                    }
                },
                {
                    text: 'Import file coaches',
                    action: function (e, dt, node, config) {
                        uploadEditor.create({
                            title: 'Import file coaches',
                        });
                    },
                },
                { extend: 'colvis', text: 'Columns' }
            ]
        });

        editorCoach.on('initEdit', function () {
            editorCoach.field('create_user').hide();
        });

        editorCoach.on('initCreate', function () {
            editorCoach.field('create_user').show();
        });

        function getModalLevelTableHtml(id) {
            var str = '' +
                '<div class="main-box-body clearfix">' +
                '<div class="table-responsive">' +
                // '<a data-match-route="/tables/coach" href="#/tables/coach" class="active">Add new Coach</a>'+
                '<table id="levelHistory_' + id + '" class="table table-striped table-bordered table-hover" cellspacing="0" width="100%">' +
                '<thead>' +
                '<tr>' +
                '<th>Level</th>' +
                '<th>From date</th>' +
                '</tr>' +
                '</thead>' +
                '</table>' +
                '</div>' +
                '</div>' +
                '';
            return str;
        }

        $('#tableCoach').on(
            'click',
            'tbody .modal-coaches',
            function () {
                let row = $(this).closest('tr');
                let data = tableCoach.row(row).data();
                console.log(data);
                var msg = getModalLevelTableHtml(data.parens.id);
                // Show dialog
                BootstrapDialog.show({
                    title: "Coach's Certification History - " + data.parens.other_name + ' ' + data.parens.surname + ' (' + data.parens.email + ')',
                    message: msg,
                    size: BootstrapDialog.SIZE_WIDE,
                    onshown: function (dialogRef) {
                        initLevelHistoryTbl(data);
                    },
                });
            }
        );

        function initLevelHistoryTbl(d) {
            // console.log(d);
            editorLevels = new $.fn.dataTable.Editor({
                ajax: {
                    type: 'POST',
                    url: SERVER_PATH + "paren/getCoachLevels",
                    data: {
                        coach_id: d.parens.id
                    },
                    headers: {	
                        'x-user-id': user_id,
                        'x-user-email': user_name
                    },
                    async: false,
                    dataType: 'json',
                    complete: function (response) {
                        // reload table
                        tableLevels.ajax.reload();
                    },
                    error: function (xhr, status, error) { },
                },
                table: '#levelHistory_' + d.parens.id,
                formOptions: {
                    main: {
                        onBlur: 'none',
                    },
                },
                i18n: {
                    create: {
                        title: 'Add new level',
                        submit: 'Add',
                    },
                    edit: {
                        title: 'Edit level',
                        submit: 'Update',
                    },
                    error: {
                        system: 'System error, please contact administrator.',
                    },
                },
                fields: [
                    {
                        name: 'coach_levels.coach_id',
                        type: 'hidden',
                        def: d.parens.id,
                    },
                    {
                        name: 'coach_levels.level',
                        label: 'Level',
                        type: 'select',
                    },
                    {
                        name: 'coach_levels.from_date',
                        label: 'From date',
                        type: 'datetime',
                        format: 'DD-MMM-YYYY'
                    }
                ],
            });

            tableLevels = $('#levelHistory_' + d.parens.id).DataTable({
                dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
                stateSave: false,
                deferRender: true,
                ajax: {
                    url: SERVER_PATH + "paren/getCoachLevels",
                    type: 'POST',
                    headers: {	
                        'x-user-id': user_id,
                        'x-user-email': user_name
                    },
                    data: {
                        coach_id: d.parens.id
                    },
                    dataType: 'json',
                    complete: function (response) { },
                    error: function (xhr, status, error) { },
                },
                language: {
                    paginate: {
                        previous: '<i class="fa fa-chevron-left"></i>',
                        next: '<i class="fa fa-chevron-right"></i>',
                    },
                },

                columns: [
                    {
                        data: 'coach_levels.level', render: function (data, type, row) {
                            return typeof COACH_CERTIFICATE[data] != 'undefined' ?  COACH_CERTIFICATE[data]['name'] : data;
                        }
                    },
                    { data: 'coach_levels.from_date' }
                ],
                select: {
                    style: 'single',
                    selector: 'td:not(:last-child)',
                },
                order: [[0, 'asc']],
                buttons: [
                    {
                        extend: 'create',
                        editor: editorLevels,
                    },
                    {
                        extend: 'remove',
                        editor: editorLevels,
                    },
                    {
                        extend: 'colvis',
                        text: 'Columns',
                    },
                ],
            });
        }
    });

</script>