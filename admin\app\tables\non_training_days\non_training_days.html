<div class="row" id="tab-page">
    <div class="row">
        <div class="col-lg-12">
            <ol class="breadcrumb">
                <li>Setup</li>
                <li class="active"><span>No Training Days</span></li>
            </ol>
        </div>
    </div>
</div>
<div class="row">
    <div class="col-lg-12">
        <div class="clearfix">
            <h1 class="pull-left">No Training Days</h1>
        </div>
    </div>
</div>
<div class="row">
    <div class="col-lg-12">
        <div class="main-box clearfix">
            <div class="main-box-body clearfix">
                <div class="table-responsive">
                    <table id="noTrainingDayTable" class="table table-striped table-bordered table-hover"
                        cellspacing="0" width="100%">
                        <thead>
                            <tr>
                                <th>Id</th>
                                <th>Date</th>
                                <th>Reason</th>
                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript" language="javascript" class="init">
    $(document).ready(function () {
        var table;

        user_id = localStorage.getItem('hkjflApp.user_id');
		user_name = localStorage.getItem('hkjflApp.user_name');

        var tableEditor = new $.fn.dataTable.Editor({
            ajax: {
                type: 'POST',
                url: SERVER_PATH + "non-training-days/CRUDNonTrainingDays",
                headers: {	
                    'x-user-id': user_id,
                    'x-user-email': user_name
                },
                data: {},
                dataType: 'json',
                complete: function (response) {
                    var jsonData = JSON.parse(response.responseText);
                    // --- may need to reload
                    if (DEVELOPMENT_ENVIRONMENT) console.log('status = ' + jsonData.status);
                    if (jsonData.status == 'OK') {
                        table.ajax.reload();
                    }
                },
                error: function (xhr, status, error) { },
            },
            table: "#noTrainingDayTable",
            formOptions: {
                main: {
                    onBlur: 'none',
                }
            },
            i18n: {
                create: {
                    button: "New",
                    title: "Create new No Training Day",
                    submit: "Create"
                },
                edit: {
                    button: "Edit",
                    title: "Edit No Training Day",
                    submit: "Update"
                },
                remove: {
                    button: "Delete",
                    title: "Delete No Training Day",
                    submit: "Delete",
                    confirm: {
                        _: "Are you sure you want to delete these No Training Day?",
                        1: "Are you sure you want to delete this No Training Day?"
                    }
                },
                error: {
                    system: "System error, please contact administrator."
                },
            },
            fields: [
                {
                    label: "Date",
                    name: "non_training_days.date",
                    type: "datetime",
                    format: 'DD-MMM-YYYY'

                },
                {
                    label: "Reason",
                    name: "non_training_days.reason",
                }
            ]
        });

        var table = $('#noTrainingDayTable').DataTable({
            dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
            stateSave: false,
            deferRender: true,
            ajax: {
                url: SERVER_PATH + "non-training-days/CRUDNonTrainingDays",
                type: 'POST',
                data: {},
                headers: {	
                    'x-user-id': user_id,
                    'x-user-email': user_name
                },
                dataType: 'json',
                complete: function (response) { },
                error: function (xhr, status, error) { },
            },
            language: {
                paginate: {
                    previous: '<i class="fa fa-chevron-left"></i>',
                    next: '<i class="fa fa-chevron-right"></i>'
                }
            },
            "columnDefs" : [{"targets":1, "type":"date-eu"}],
            "aoColumns": [
                { "mData": null },
                { "mData": "non_training_days.date" },
                { "mData": "non_training_days.reason" }
            ],
            select: {
                style: 'single',
            },
            order: [
                [0, 'asc']
            ],
            lengthMenu: [
                [10, 25, 50, 100, -1],
                [10, 25, 50, 100, "All"]
            ],
            buttons: [
                { extend: "create", editor: tableEditor },
                { extend: "edit", editor: tableEditor },
                { extend: "remove", editor: tableEditor }
            ]
        });

        table.on('order.dt search.dt', function () {
            table.column(0, { search: 'applied', order: 'applied' }).nodes().each(function (cell, i) {
                cell.innerHTML = i + 1;
            });
        }).draw();
    });
</script>