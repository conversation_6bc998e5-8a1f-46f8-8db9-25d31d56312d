<style>
	#saveNotice {
		position: absolute;
		display: none;
		margin-top: 5em;
		margin-left: 10em;
		width: 50%;
		z-index: 2;
		cursor: pointer;
	}
</style>
<div class="row" id="tab-page">
    <div class="row">
        <div class="col-lg-12">
            <ol class="breadcrumb">
                <li>Events</li>
                <li><a href="" ng-click="goBack()">Golden Age</a></li>
                <li>{{event_name}}</li>
                <li class="active"><span>Leagues</span></li>
            </ol>
        </div>
    </div>
</div>

<div class="row">
	<div class="col-lg-12">
		<div class="clearfix">
			<h1 class="pull-left">Leagues</h1>
			<hr>
		</div>
	</div>

	<div class="col-lg-12 col-md-12 col-sm-12">
		<div class="main-box clearfix">
			<div class="tabs-wrapper profile-tabs">
				<ul class="nav nav-tabs">
					<li class="active"><a showtab="" data-target="#tab-groups" data-toggle="tab">List groups</a>
					</li>
					<li><a showtab="" data-target="#tab-matches" data-toggle="tab">Matches Report</a></li>
					<li><a showtab="" data-target="#tab-formb" data-toggle="tab">Form B</a></li>
				</ul>
				<div class="tab-content">
					<div class="tab-pane fade in active" id="tab-groups">
						<div class="col-lg-12 ">
							<div class="container">
								<div class="row">
									<h1>Boys</h1>
									<div class="col-md-2" ng-repeat="group in mixedGroups" id="groupId">
										<a data-match-route="/leaguesList/{{group.id}}/{{event_id}}/{{event_name}}?groupName={{group.name}}"
											href="#/leaguesList/{{group.id}}/{{event_id}}/{{event_name}}?groupName={{group.name}}"
											type="button" class="btn btn-outline-danger text-center">
											<h4>{{group.name}}</h4>
										</a>
									</div>
								</div>
								<div class="row">
									<h1>Girls</h1>
									<div class="col-md-2" ng-repeat="group in girlGroups" id="groupId">
										<a data-match-route="/leaguesList/{{group.id}}/{{event_id}}/{{event_name}}?groupName={{group.name}}"
											href="#/leaguesList/{{group.id}}/{{event_id}}/{{event_name}}?groupName={{group.name}}"
											type="button" class="btn btn-outline-danger text-center">
											<h4>{{group.name}}</h4>
										</a>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div class="tab-pane fade" id="tab-matches">
						<div class="col-lg-12 ">
							<div class="container">
								<form role="form">
									<div class="row">
										<div class="form-group col-md-4">
											<label for="datepickerDate">Date</label>
											<div class="input-group">
												<span class="input-group-addon"><i class="fa fa-calendar"></i></span>
												<input type="text" class="form-control" id="datepickerDate2"
													ng-model="selectedDate" ng-change="initMatchesTable()">
											</div>
										</div>
										<div class="form-group col-md-4">
											<label>Select club</label>
											<select class="form-control" id="selLocation" ng-model="selectedClub"
												ng-options="location.id as location.name for location in clubs"
												ng-change="initMatchesTable()">
											</select>
										</div>
									</div>
								</form>
							</div>
							<div class="container">
								<div class="table-responsive">
									<table id="tbReportMatches" class="table table-striped table-bordered table-hover"
										cellspacing="0" width="100%">
										<thead>
											<tr>
												<th rowspan="2">League</th>
												<th rowspan="2">Date</th>
												<th rowspan="2">Start time</th>
												<th rowspan="2">End time</th>
												<th rowspan="2">Location</th>
												<th colspan="3" class="center">Match</th>
												<th colspan="3" class="center">Result</th>
											</tr>
											<tr>
												<th></th>
												<th></th>
												<th></th>
												<th></th>
												<th></th>
												<th></th>
											</tr>
										</thead>
									</table>
								</div>
							</div>
						</div>
					</div>
					<div class="tab-pane fade" id="tab-formb">
						<div class="table-responsive">
							<table id="tbFormB" class="table table-striped table-bordered table-hover" cellspacing="0"
								width="100%">
								<thead>
									<tr>
										<th>Match Date</th>
										<th>Status</th>
										<th>Generate</th>
										<th>Percent</th>
										<th>Action</th>
									</tr>
								</thead>
							</table>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<!-- Button trigger modal -->
<!-- this page specific scripts -->
<script type="text/javascript">
	$('#datepickerDate2').datepicker({
		format: 'yyyy-mm-dd',
		todayHighlight: 'TRUE',
		autoclose: true,
	});
</script>
<style>
	#groupId a {
		width: -webkit-fill-available;
		height: 70px;
		margin-bottom: 10px;
		text-align: center;
		border: solid;
		background-color: white;
	}

	#groupId a:hover {
		background-color: red;
		color: white;
	}

	#groupId h4 {
		margin-top: 16px;
		text-decoration: none;
	}
</style>