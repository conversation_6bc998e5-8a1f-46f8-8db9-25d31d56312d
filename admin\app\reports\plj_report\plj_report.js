app.controller(
    'PLJReportsCtrl',
    function (user, $scope, $rootScope, $routeParams, $http) {
        $('#page-wrapper').removeClass('nav-small');
        console.log('PLJReportsCtrl');
        $scope.user = user;
        var table = null;
        var event_id = $routeParams.eventId;

        $.fn.dataTable.moment('DD/MM/YYYY');

        $scope.events = [];
        $scope.selectedEvent;

        jQuery.ajax({
            type: 'POST',
            url: SERVER_PATH + 'event/getEventInfo',
            async: false,
            headers: {	
                'x-user-id': $rootScope.user_id,
                'x-user-email': $rootScope.user_name
            },
            data: {
                event_id: event_id,
            },
            dataType: 'json',
            complete: function (response) {
                var jsonData = JSON.parse(response.responseText);
                var event = jsonData.info;
                event_name = event.name;
                event_type = event.type;
            },
        });
        $scope.event_name = event_name;
        $scope.event_type = event_type;
        // getEventPLJs();

        $('#select_event_plj').select2();

        $('#select_event_plj').change(function () {
            // fix bug delay change value
            setTimeout(function () {
                $scope.initReport();
            }, 100);
        });

        // config date range picker
        $('input[name="dateFilter"]').daterangepicker({
            startDate: moment().startOf('month'),
            endDate: moment().endOf('month'),
            locale: {
                format: 'DD/MM/YYYY',
            },
            maxSpan: {
                days: 90,
            },
            ranges: {
                Yesterday: [
                    moment().subtract(1, 'days'),
                    moment().subtract(1, 'days'),
                ],
                Today: [moment(), moment()],
                Tomorrow: [moment().add(1, 'days'), moment().add(1, 'days')],
                'Last week': [
                    moment().subtract(1, 'week').startOf('week').add(1, 'days'),
                    moment().subtract(1, 'week').endOf('week').add(1, 'days'),
                ],
                'This week': [
                    // start week on monday
                    moment().startOf('week').add(1, 'days'),
                    moment().endOf('week').add(1, 'days'),
                ],
                'Next week': [
                    moment().add(1, 'week').startOf('week').add(1, 'days'),
                    moment().add(1, 'week').endOf('week').add(1, 'days'),
                ],
                'Last Month': [
                    moment().subtract(1, 'month').startOf('month'),
                    moment().subtract(1, 'month').endOf('month'),
                ],
                'This Month': [
                    moment().startOf('month'),
                    moment().endOf('month'),
                ],
                'Next Month': [
                    moment().subtract(-1, 'month').startOf('month'),
                    moment().subtract(-1, 'month').endOf('month'),
                ],
            },
        });
        // Date range picker
        $scope.start_date = $('input[name="dateFilter"]')
            .data('daterangepicker')
            .startDate.format('YYYY-MM-DD');
        $scope.end_date = $('input[name="dateFilter"]')
            .data('daterangepicker')
            .endDate.format('YYYY-MM-DD');

        $('input[name="dateFilter"]').on(
            'apply.daterangepicker',
            function (ev, picker) {
                console.log('date change');
                $scope.start_date = picker.startDate.format('YYYY-MM-DD');
                $scope.end_date = picker.endDate.format('YYYY-MM-DD');
                $scope.initReport();
            }
        );

        $scope.initReport = function () {
            getPLJuniorSummary(event_id);
            initReportTable(event_id);
        };

        function getEventPLJs() {
            $http({
                method: 'POST',
                url: SERVER_PATH + 'event/getEventPLJ',
                headers: {	
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name
                }
            }).success(function (response) {
                console.log(response);
                if (response.status == 'OK') {
                    $scope.events = response.data;
                    console.log($scope.events[0]);
                    $scope.selectedEvent = $scope.events[0];
                    console.warn($scope.selectedEvent);
                    $scope.initReport();
                    // initTeamCoachesReport($scope.selectedEvent.id);
                }
            });
        }

        function getPLJuniorSummary(event_id) {
            $http({
                method: 'POST',
                url: SERVER_PATH + 'dashboard/getPLJuniorSummary',
                data: 'event_id=' + event_id,
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name
                },
            }).success(function (response) {
                console.log(response);
                if (response.status == 'OK') {
                    $scope.registration_courses =
                        response.data.registration_courses;
                    $scope.registration_invoices =
                        response.data.registration_invoices;

                    // total (number_course * a.total) in array $scope.registration_courses
                    $scope.total_registrations = 0;
                    $scope.registration_courses.forEach(function (value) {
                        $scope.total_registrations += parseInt(value.number_course) * parseInt(value.total);
                    });
                }
            });
        }

        $scope.initReportHtml = function (event_id) {
            var html =
                '<div class="table-responsive">' +
                '<table id="reports_plj_' +
                event_id +
                '" class="table table-striped table-bordered table-hover" cellspacing="0"width="100%">' +
                '<thead>' +
                '<tr>' +
                '<th colspan="9" class="center">Coach information</th>' +
                '<th colspan="4" class="center">Event information</th>' +
                '<th colspan="4" class="center">Session information</th>' +
                '<th colspan="5" class="center">Approval information</th>' +
                '<th rowspan="2" class="center">Action</th>' +
                '</tr>' +
                '<tr>' +
                '<th>Name - Phone</th>' +
                '<th>Coach name</th>' +
                '<th>Coach ID</th>' +
                '<th>Chinese name</th>' +
                '<th>Email</th>' +
                '<th>Phone</th>' +
                '<th>Role in Session</th>' +
                '<th>Level</th>' +
                '<th>Attendance</th>' +
                '<th>Date</th>' +
                '<th>Time</th>' +
                '<th>Status</th>' +
                '<th>Reason</th>' +
                '<th>Course</th>' +
                '<th>Region</th>' +
                '<th>Group</th>' +
                '<th>Session type</th>' +
                '<th>Duration</th>' +
                '<th>Amount</th>' +
                '<th>Date</th>' +
                '<th>Status</th>' +
                '<th>Reason</th>' +
                '</tr>' +
                '</table>' +
                '</div>';
            return html;
        };

        $scope.initReport();

        $scope.goBack = function () {
            window.history.back();
        };

        function initReportTable(event_id) {
            $('#attendanceContent').html($scope.initReportHtml(event_id));

            table = $('#reports_plj_' + event_id).DataTable({
                dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
                stateSave: false,
                deferRender: true,
                bDestroy: true,
                ajax: {
                    url: SERVER_PATH + 'course/getReportSummerSchemeSession',
                    type: 'POST',
                    headers: {	
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name
                    },
                    data: {
                        event_id: event_id,
                        start_date: $scope.start_date,
                        end_date: $scope.end_date,
                    },
                    dataType: 'json',
                    complete: function (response) {},
                    error: function (xhr, status, error) {},
                },
                language: {
                    paginate: {
                        previous: '<i class="fa fa-chevron-left"></i>',
                        next: '<i class="fa fa-chevron-right"></i>',
                    },
                },
                columns: [
                    {
                        data: 'parens.for_filter',
                        visible: false,
                    },
                    {
                        data: null,
                        render: function (data, type, row) {
                            const name =
                                data.parens.surname +
                                ' ' +
                                data.parens.other_name;

                            if (data.is_substitute) {
                                return name + ' (Substituted)';
                            } else {
                                return name;
                            }
                        },
                    },
                    {
                        data: 'parens.coach_id_no',
                    },
                    {
                        data: 'parens.chinese_name',
                        visible: false,
                    },
                    {
                        data: 'parens.email',
                        visible: false,
                    },
                    {
                        data: 'parens.phone',
                        visible: false,
                    },
                    {
                        data: 'ccoaches.role',
                    },
                    {
                        data: 'ccoaches.level',
                        render: function (data, type, row) {
                            if (data == null) {
                                return '';
                            }
                            return COACH_CERTIFICATE[data]['name'];
                        },
                    },
                    {
                        data: 'ccoaches.check_attendance',
                        className: 'center',
                        render: function (data, type, row) {
                            data = +data;
                            switch (data) {
                                case COACH_ATTENDANCE_STATUS_PENDING:
                                    return (
                                        '<span class="label label-sm label-primary">' +
                                        ATTENDANCE_STATUS.get(
                                            COACH_ATTENDANCE_STATUS_PENDING
                                        ) +
                                        '</span>'
                                    );
                                case COACH_ATTENDANCE_STATUS_ATTENDED:
                                    return (
                                        '<span class="label label-sm label-success">' +
                                        ATTENDANCE_STATUS.get(
                                            COACH_ATTENDANCE_STATUS_ATTENDED
                                        ) +
                                        '</span>'
                                    );
                                case COACH_ATTENDANCE_STATUS_CANCELLED:
                                    return (
                                        '<span class="label label-sm label-warning">' +
                                        ATTENDANCE_STATUS.get(
                                            COACH_ATTENDANCE_STATUS_CANCELLED
                                        ) +
                                        '</span>'
                                    );
                                case COACH_ATTENDANCE_STATUS_DID_NOT_ATTEND:
                                    return (
                                        '<span class="label label-sm label-danger">' +
                                        ATTENDANCE_STATUS.get(
                                            COACH_ATTENDANCE_STATUS_DID_NOT_ATTEND
                                        ) +
                                        '</span>'
                                    );
                                case COACH_ATTENDANCE_STATUS_SUBSTITUTED:
                                    return (
                                        '<span class="label label-sm label-info">' +
                                        ATTENDANCE_STATUS.get(
                                            COACH_ATTENDANCE_STATUS_SUBSTITUTED
                                        ) +
                                        '</span>'
                                    );
                                default:
                                    return '<span class="label label-sm label-default"></span>';
                            }
                        },
                    },
                    {
                        data: 'cdates.date',
                    },
                    {
                        data: null,
                        render: function (data, type, row) {
                            return (
                                row.cdates.start_time +
                                ' - ' +
                                row.cdates.end_time
                            );
                        },
                    },
                    {
                        data: 'cdates.status',
                        render: function (data, type, row) {
                            if (data == 'Active') {
                                // badge success
                                return '<span class="badge badge-success">Active</span>';
                            } else if (data == 'Cancelled') {
                                return '<span class="badge badge-danger">Cancelled</span>';
                            } else if (data == 'Completed') {
                                // badge warning
                                return '';
                            }
                        },
                    },
                    {
                        data: 'cdates.cancelled_reason',
                    },
                    {
                        data: 'courses.class_code',
                    },
                    {
                        data: 'venues.region',
                        visible: false,
                    },
                    {
                        data: 'groups.name',
                        visible: false,
                    },
                    {
                        data: 'cdates.type',
                    },
                    {
                        data: 'ccoaches.duration',
                        className: 'center',
                    },
                    {
                        data: 'ccoaches.amount',
                        render: $.fn.dataTable.render.number(',', '.', 2, '$'),
                    },
                    {
                        data: 'course_coache_log.update_time',
                    },
                    {
                        data: 'ccoaches.approval_status',
                        className: 'center',
                        render: function (data, type, row) {
                            switch (data) {
                                case 'ACCEPT':
                                    return '<span class="label label-sm label-success">Accept</span>';
                                case 'DECLINE':
                                    return '<span class="label label-sm label-danger">Decline</span>';
                                case 'PENDING FOR PAYMENT':
                                    return '<span class="label label-sm label-warning">Pending for payment</span>';
                                case 'APPROVE':
                                    return '<span class="label label-sm label-success">Approve</span>';
                                case 'REJECT':
                                    return '<span class="label label-sm label-danger">Reject</span>';
                                case 'PAID':
                                    return '<span class="label label-sm label-primary">Paid</span>';
                                case 'REFUNDED':
                                    return '<span class="label label-sm label-danger">Refunded</span>';
                                default:
                                    return '<span class="label label-sm label-default"></span>';
                            }
                        },
                    },
                    {
                        data: 'course_coache_log.reason',
                    },
                    {
                        data: null,
                        className: 'center',
                        render: function (data, type, row) {
                            var action_menu =
                                '<div class="btn-group" style="margin-top:0px">';
                            action_menu +=
                                '<button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">';
                            action_menu += 'Action <span class="caret"></span>';
                            action_menu += '</button>';
                            action_menu += '<ul class="dropdown-menu">';
                            if (user.role == USER_SUPER_ADMIN || user.role == USER_LEAGUE_ADMIN || user.role == USER_GRASSROOTS_FINANCE || user.role == USER_FINANCE) {
                                action_menu +=
                                    '<li><a href="javascript:void(0)" onclick="editCCoach(' +
                                    row.ccoaches.id +
                                    ')"><i class="fa fa-pencil" aria-hidden="true"></i>Edit</a></li>';
                            }
                            action_menu +=
                                '<li><a href="javascript:void(0)" onclick="approveCCoach(' +
                                row.ccoaches.id +
                                ')"><i class="fa fa-check" aria-hidden="true"></i>Approve</a></li>';
                            action_menu +=
                                '<li><a href="javascript:void(0)" onclick="historyCCoach(' +
                                row.ccoaches.id +
                                ')"><i class="fa fa-history" aria-hidden="true"></i>History</a></li>';
                            action_menu += '</ul>';
                            action_menu += '</div>';
                            return action_menu;
                        },
                    },
                ],
                order: [[9, 'asc']],
                displayLength: 100,
                lengthMenu: [
                    [10, 25, 50, 100, -1],
                    [10, 25, 50, 100, 'All'],
                ],
                select: {
                    style: SELECT_MODE,
                },
                buttons: [
                    {
                        name: 'approve',
                        text: '<i class="fa fa-check" aria-hidden="true"></i>&emsp;Approve',
                        action: function (e, dt, node, config) {
                            // Get selected rows
                            var selectedRows = dt.rows({
                                selected: true,
                            });

                            // Get selected rows data
                            var selectedRowsData = selectedRows.data();

                            // Get selected rows id
                            var selectedRowsId = selectedRowsData
                                .toArray()
                                .map((item) => item['ccoaches']['id']);

                            // Check if there is any selected row
                            if (selectedRowsId.length == 0) {
                                // alert('Please select at least one row');
                                Swal.fire({
                                    icon: 'warning',
                                    title: 'Oops...',
                                    text: 'Please select at least one row',
                                });
                                return;
                            } else {
                                let editorApproveMultiple =
                                    new $.fn.dataTable.Editor({
                                        ajax: {
                                            type: 'POST',
                                            url:
                                                SERVER_PATH +
                                                'course/approveAttendanceCoaches',
                                            headers: {	
                                                'x-user-id': $rootScope.user_id,
                                                'x-user-email': $rootScope.user_name
                                            },
                                            data: {
                                                user_id: $rootScope.user_id,
                                                ccoach_ids: selectedRowsId,
                                            },
                                            dataType: 'json',
                                            complete: function (response) {
                                                let data =
                                                    response.responseJSON;
                                                if (data.status == 'OK') {
                                                    Swal.fire({
                                                        title: 'Completed!',
                                                        html: data.message,
                                                        className: 'align-left',
                                                        icon: 'success',
                                                        confirmButtonText: 'OK',
                                                    });
                                                    table.ajax.reload();
                                                } else if (
                                                    data.status == 'ERROR'
                                                ) {
                                                    Swal.fire({
                                                        title: 'Error!',
                                                        text: data.message,
                                                        icon: 'error',
                                                        confirmButtonText: 'OK',
                                                    });
                                                } else {
                                                    // skip
                                                }
                                            },
                                            error: function (
                                                xhr,
                                                status,
                                                error
                                            ) {
                                                console.log(error);
                                            },
                                        },
                                        formOptions: {
                                            main: {
                                                onBlur: 'none',
                                            },
                                        },
                                        fields: [
                                            {
                                                label: 'Status:',
                                                name: 'status',
                                                type: 'radio',
                                                options: [
                                                    ...SESSION_APPROVAL_STATUS.filter(
                                                        (item) =>
                                                            item.role ==
                                                            user.role
                                                    ).map((item) => {
                                                        return {
                                                            label: item.name,
                                                            value: item.name,
                                                        };
                                                    }),
                                                ],
                                            },
                                            // add field to enter reason
                                            {
                                                label: 'Reason:',
                                                name: 'reason',
                                                type: 'textarea',
                                            },
                                        ],
                                    });

                                editorApproveMultiple
                                    .title('Approve Session Attendance')
                                    .buttons({
                                        label: 'Save',
                                        fn: function () {
                                            this.submit();
                                        },
                                    })
                                    .edit()
                                    .open();
                            }

                            // Deselect all rows
                            // dt.rows().deselect();

                            // Reload datatable
                            // dt.ajax.reload();
                        },
                    },
                    {
                        extend: 'excel',
                        name: 'excel',
                        text: '<i class="fa fa-file-excel-o"></i>&emsp;Export to Excel',
                        titleAttr: 'Export data to an Excel file',
                        filename: `HKFA Grassroots - ${event_type} ${event_name} - Payment Report - ${moment().format(
                            'YYYY-MM-DD HH:mm:ss'
                        )}`,
                        exportOptions: {
                            columns: ':visible',
                            modifier: {
                                autoFilter: true,
                                // selected: true
                            },
                        },
                    },
                    {
                        extend: 'colvis',
                        text: 'Columns',
                        className:
                            'dt-button buttons-collection buttons-colvis',
                    },
                ],
                createdRow: function (row, data, dataIndex) {
                    if (data.is_non_training_day) {
                        $(row).css('background-color', '#FFB6C1');
                        return;
                    }
                    if (data.overlap) {
                        $(row).css('background-color', '#FFFACD');
                        return;
                    }
                },
                initComplete: function () {
                    // hide first column
                    this.api().columns([0]).visible(false);

                    // Add filter by district, group, coach
                    var districtFilterElement = {
                        orderColumn: 14,
                        elementId: 'district_filter',
                        name: 'district',
                        label: 'Filter by region:',
                        selectId: 'selDistrict',
                    };
                    var groupFilterElement = {
                        orderColumn: 15,
                        elementId: 'group_filter',
                        name: 'group',
                        label: 'Filter by group:',
                        selectId: 'selGroup',
                    };
                    var coachFilterElement = {
                        orderColumn: 0,
                        elementId: 'coach_filter',
                        selectId: 'selType',
                        name: 'coach',
                        label: 'Filter by coach:',
                    };
                    var approvalStatusFilterElement = {
                        orderColumn: 19,
                        column: 'district_session_coaches.approval_status',
                        elementId: 'approval_status_filter',
                        selectId: 'selApprovalStatus',
                        name: 'approval_status',
                        label: 'Filter by approval status:',
                    };

                    var filterColumns = [
                        districtFilterElement,
                        groupFilterElement,
                        coachFilterElement,
                        approvalStatusFilterElement,
                    ];

                    filterColumns.forEach((item) => {
                        this.api()
                            .columns(item.orderColumn)
                            .every(function () {
                                var column = this;

                                // Create label element for filter
                                let labelElement =
                                    document.createElement('label');
                                labelElement.htmlFor = item.name;
                                labelElement.innerHTML = item.label;
                                $(`#${item.elementId}`).html(labelElement);

                                // Create select element for filter
                                let selectElement =
                                    document.createElement('select');
                                selectElement.id = item.selectId;
                                selectElement.className = 'form-control';
                                selectElement.name = item.name;
                                $(`#${item.elementId}`).append(selectElement);

                                // Apply listener for user change in value
                                selectElement.addEventListener(
                                    'change',
                                    function () {
                                        var val =
                                            $.fn.dataTable.util.escapeRegex(
                                                $(this).val()
                                            );

                                        if (val.indexOf('_') !== -1) {
                                            column
                                                .search('', true, false)
                                                .draw();
                                        } else {
                                            column
                                                .search(
                                                    '^' + val + '$',
                                                    true,
                                                    false
                                                )
                                                .draw();
                                        }
                                    }
                                );

                                // Add default option to select element
                                selectElement.innerHTML =
                                    '<option value="_">All</option>';

                                // define sort logic
                                let sortLogic = undefined;
                                if (item.name == 'group') {
                                    sortLogic = function (a, b) {
                                        var age_group_a = a.replace('U', '');
                                        var age_group_b = b.replace('U', '');
                                        return age_group_a - age_group_b;
                                    };
                                }

                                // Add list of options
                                column
                                    .data()
                                    .unique()
                                    .sort(sortLogic)
                                    .each(function (d, j) {
                                        selectElement.add(new Option(d));
                                    });
                            });
                    });
                },
            });
        }

        editCCoach = function (id) {
            if (
                user.role != USER_SUPER_ADMIN && 
                user.role != USER_LEAGUE_ADMIN  &&
                user.role != USER_GRASSROOTS_FINANCE &&
                user.role != USER_FINANCE
            ) {
                Swal.fire({
                    title: 'Error!',
                    text: 'You do not have permission to edit this event.',
                    icon: 'error',
                    confirmButtonText: 'OK',
                });

                return;
            }

            var data = table
                .data()
                .toArray()
                .find(function (value) {
                    return value.ccoaches.id == id;
                });

            // find in array
            var status_role = APPROVAL_STATUS_CAN_CHANGE.get(
                parseInt(user.role)
            );

            let index = status_role.findIndex(
                (x) => x == data.ccoaches.approval_status
            );

            if (index == -1) {
                Swal.fire({
                    title: 'Error!',
                    text: 'You do not have permission to change this event.',
                    icon: 'error',
                    confirmButtonText: 'OK',
                });

                return;
            }

            let modifyEditor = new $.fn.dataTable.Editor({
                ajax: {
                    type: 'POST',
                    url: SERVER_PATH + 'course/modifyDurationAndRate',
                    headers: {	
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name
                    },
                    data: {
                        user_id: $rootScope.user_id,
                        ccoach_id: data.ccoaches.id,
                    },
                    dataType: 'json',
                    complete: function (response) {
                        let data = response.responseJSON;
                        if (data.status == 'OK') {
                            table.ajax.reload();
                        } else {
                            if (typeof data.fieldErrors == 'undefined') {
                                Swal.fire({
                                    title: 'Error!',
                                    text: data.message,
                                    icon: 'error',
                                    confirmButtonText: 'OK',
                                });
                            }
                        }
                    },
                    error: function (xhr, status, error) {
                        console.log(error);
                    },
                },
                formOptions: {
                    main: {
                        onBlur: 'none',
                    },
                },
                fields: [
                    {
                        label: 'Duration:',
                        name: 'duration',
                        attr: {
                            type: 'number',
                        },
                        default: data.ccoaches.duration,
                    },
                    {
                        label: 'Rate:',
                        name: 'rate',
                        type: 'select',
                        options:  COACH_CERTIFICATE.filter(function (o) {
                            return typeof o.name != 'undefined';
                        }).map((item, index) => {
                            return {
                                label: item.name,
                                value: item.value,
                            };
                        }),
                        default: data.ccoaches.level,
                    },
                ],
            });

            modifyEditor
                .title('Modify Coach Attendance')
                .buttons({
                    label: 'Save',
                    fn: function () {
                        this.submit();
                    },
                })
                .edit()
                .open();
        };

        var getModalHistoryTableHtml = function (id) {
            var html =
                '<table id="tbl_history_summer_scheme_' +
                id +
                '" class="table table-striped table-bordered" style="width:100%"><thead><tr> <td>Action</td> <td>Reason</td> <td>Description</td> <td>Update time</td> <td>Update by</td> </tr></thead></table>';
            return html;
        };

        historyCCoach = function (id) {
            // get data from the row
            var data = table
                .data()
                .toArray()
                .find(function (value) {
                    return value.ccoaches.id == id;
                });

            var msg = getModalHistoryTableHtml(data.ccoaches.id);

            console.log(data);

            BootstrapDialog.show({
                title:
                    'History - ' +
                    data.parens.surname +
                    ' ' +
                    data.parens.other_name +
                    ' - ' +
                    data.courses.class_code +
                    '_' +
                    data.cdates.date,
                message: msg,
                size: BootstrapDialog.SIZE_WIDE,
                onshown: function (dialogRef) {
                    initHistoryTbl(data);
                },
            });
        };

        initHistoryTbl = function (data) {
            var tableHistory = $(
                '#tbl_history_summer_scheme_' + data.ccoaches.id
            ).DataTable({
                dom: '<"row"B>rt<"row"i>',
                stateSave: true,
                deferRender: true,
                ajax: {
                    url: SERVER_PATH + 'course/adminCCourseHistory',
                    type: 'POST',
                    headers: {	
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name
                    },
                    data: {
                        ccoach_id: data.ccoaches.id,
                    },
                    dataType: 'json',
                    complete: function (response) {},
                    error: function (xhr, status, error) {},
                },
                language: {
                    paginate: {
                        previous: '<i class="fa fa-chevron-left"></i>',
                        next: '<i class="fa fa-chevron-right"></i>',
                    },
                },
                columns: [
                    {
                        data: 'course_coache_log.status',
                    },
                    {
                        data: 'course_coache_log.reason',
                    },
                    {
                        data: 'course_coache_log.description',
                    },
                    {
                        data: 'course_coache_log.update_time',
                        render: function (data, type, row) {
                            return moment(data).format('DD/MM/YYYY HH:mm');
                        },
                    },
                    {
                        data: 'name',
                    },
                ],
                select: {
                    style: 'single',
                    selector: 'td:not(:last-child)',
                },
                columnDefs: [{ type: 'date-euro', targets: 3 }],
                order: [[3, 'desc']],
            });
        };

        approveCCoach = function (id) {
            if (
                user.role != USER_SUPER_ADMIN &&
                user.role != USER_LEAGUE_ADMIN &&
                user.role != USER_GRASSROOTS_FINANCE &&
                user.role != USER_FINANCE
            ) {
                Swal.fire({
                    title: 'Error!',
                    text: 'You do not have permission to edit this session.',
                    icon: 'error',
                    confirmButtonText: 'OK',
                });

                return;
            }

            var data = table
                .data()
                .toArray()
                .find(function (value) {
                    return value.ccoaches.id == id;
                });

            // find in array
            var status_role = APPROVAL_STATUS_CAN_CHANGE.get(
                parseInt(user.role)
            );

            let index = status_role.findIndex(
                (x) => x == data.ccoaches.approval_status
            );

            if (index == -1) {
                Swal.fire({
                    title: 'Error!',
                    text: 'You do not have permission to approve this session.',
                    icon: 'error',
                    confirmButtonText: 'OK',
                });

                return;
            }

            let approveEditor = new $.fn.dataTable.Editor({
                ajax: {
                    type: 'POST',
                    url: SERVER_PATH + 'course/adminApproveSession',
                    headers: {	
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name
                    },
                    data: {
                        user_id: $rootScope.user_id,
                        ccoach_id: data.ccoaches.id,
                    },
                    dataType: 'json',
                    complete: function (response) {
                        let data = response.responseJSON;
                        if (data.status == 'OK') {
                            table.ajax.reload();
                        } else {
                            Swal.fire({
                                title: 'Error!',
                                text: data.message,
                                icon: 'error',
                                confirmButtonText: 'OK',
                            });
                        }
                    },
                    error: function (xhr, status, error) {
                        console.log(error);
                    },
                },
                formOptions: {
                    main: {
                        onBlur: 'none',
                    },
                },
                fields: [
                    {
                        label: 'Status:',
                        name: 'status',
                        type: 'select',
                        options: [
                            ...SESSION_APPROVAL_STATUS.filter(
                                (item) => item.role == user.role
                            ).map((item) => {
                                return {
                                    label: item.name,
                                    value: item.name,
                                };
                            }),
                        ],
                    },
                    // add field to enter reason
                    {
                        label: 'Reason:',
                        name: 'reason',
                        type: 'textarea',
                    },
                ],
            });

            approveEditor
                .title('Approve Session Attendance')
                .buttons({
                    label: 'Save',
                    fn: function () {
                        this.submit();
                    },
                })
                .edit()
                .open();
        };

        $scope.$on('$destroy', function () {
            $('#select_event_summer_scheme').select2('destroy');
            // $('#btnSelectSummerScheme').unbind();
        });
    }
);
