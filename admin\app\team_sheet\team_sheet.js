app.controller(
    'teamSheetCtrl',
    function ($scope, $rootScope, $routeParams, $http) {
        $('#page-wrapper').removeClass('nav-small');

        // get info event
        var event_id = $routeParams.id;
        jQuery.ajax({
            type: 'POST',
            url: SERVER_PATH + 'event/getEventInfo',
            async: false,
            headers: {
                'x-user-id': $rootScope.user_id,
                'x-user-email': $rootScope.user_name,
            },
            data: {
                event_id: event_id,
            },
            dataType: 'json',
            complete: function (response) {
                var jsonData = JSON.parse(response.responseText);
                var event = jsonData.info;
                event_name = event.name;
                event_type = event.type;
            },
        });
        console.log(
            'paymentsCtrl - event_id, name, type  = ' +
                event_id +
                ', ' +
                event_name +
                ', ' +
                event_type
        );
        $scope.event_name = event_name;

        jQuery.ajax({
            type: 'POST',
            url: SERVER_PATH + 'event/getGroupsByUserEvent',
            async: false,
            headers: {
                'x-user-id': $rootScope.user_id,
                'x-user-email': $rootScope.user_name,
            },
            data: {
                event_id: event_id,
                user_id: $rootScope.user_id,
            },
            dataType: 'json',
            complete: function (response) {
                var jsonData = JSON.parse(response.responseText);
                if (jsonData.status == 'OK') {
                    $scope.groups = jsonData.info;
                    $scope.role = $scope.groups.role;
                    $scope.selectedGroup = $scope.groups[0];
                    // getGroup();
                }
            },
        });

        $('button').click(function (e) {
            getGroup();
        });

        $scope.goBack = function () {
            window.history.back();
        };

        function getGroup() {
            var role = $scope.role;
            var user_id = $rootScope.user_id;
            var group_id = $scope.selectedGroup.id;

            // initialize html
            var html = '';
            html += getGroupTeamsTableHtml(group_id);
            $('#teamSheetPageContent').html(html);

            // initialize data
            initGroupTeamsTable(group_id, role, user_id);
        }

        function getGroupTeamsTableHtml(group_id) {
            var str =
                '' +
                '<div class="row">' +
                '<div class="col-lg-12">' +
                '<div class="main-box clearfix">' +
                '<div class="main-box-body clearfix">' +
                '<div class="table-responsive">' +
                '<table id="tblTeamSheet_' +
                group_id +
                '" class="table table-striped table-bordered table-hover" cellspacing="0" width="100%">' +
                '<thead>' +
                '<tr>' +
                '<th>Club</th>' +
                '<th>Age Group</th>' +
                '<th>Team</th>' +
                '<th>PDF Link</th>' +
                '<th>Submitted</th>' +
                '<th>Status</th>' +
                '<th>Action</th>' +
                '</tr>' +
                '</thead>' +
                '</table>' +
                '</div>' +
                '</div>' +
                '</div>' +
                '</div>' +
                '</div>';
            return str;
        }

        function getTeamPlayersTableHtml(team_id) {
            var str =
                '' +
                '<div class="table-responsive">' +
                '<table id="tblTeamSheetchild_' +
                team_id +
                '" class="table table-striped table-bordered table-hover" cellspacing="0" width="100%">' +
                '<thead>' +
                '<tr>' +
                '<th>Club</th>' +
                '<th>Age Group</th>' +
                '<th>Team</th>' +
                '<th>PDF Link</th>' +
                '<th>Submitted</th>' +
                '</tr>' +
                '</thead>' +
                '</table>' +
                '</div>';
            return str;
        }

        function initGroupTeamsTable(group_id, role, user_id) {
            tableTeamSheet = $('#tblTeamSheet_' + group_id).DataTable({
                dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
                stateSave: true,
                deferRender: true,
                ajax: {
                    url: SERVER_PATH + 'team/getTeamsSheets',
                    type: 'POST',
                    headers: {
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name,
                    },
                    data: {
                        group_id: group_id,
                        event_id: event_id,
                        user_id: user_id,
                    },
                    dataType: 'json',
                    complete: function (response) {
                        response = JSON.parse(response.responseText);
                    },
                    error: function (xhr, status, error) {},
                },
                language: {
                    paginate: {
                        previous: '<i class="fa fa-chevron-left"></i>',
                        next: '<i class="fa fa-chevron-right"></i>',
                    },
                },
                columns: [
                    {
                        data: 'club_name',
                        // className: "center"
                    },
                    {
                        data: 'group_name',
                        // className: "center"
                    },
                    {
                        data: 'team_name',
                    },
                    {
                        data: 'document',
                        render: function (data, type, full, meta) {
                            if (data != '') {
                                return '<a class="openPDF">Open</a>';
                            }
                            return '';
                        },
                    },
                    {
                        data: 'date',
                    },
                    {
                        data: null,
                        render: function (data, type, full, meta) {
                            var checked = data.locked == 0 ? '' : 'checked';
							var temp_label = data.locked == 0 ? 'Unlock' : 'Lock';
                            return `
							<div class="onoffswitch onoffswitch-danger">
								<input type="checkbox" name="onoffswitch2" class="onoffswitch-checkbox" id="switch${data.DT_RowId}" ${checked}>
								<label class="onoffswitch-label" for="switch${data.DT_RowId}">
									<div class="onoffswitch-inner"></div>
									<div class="onoffswitch-switch"></div>
								</label>
							</div>

							<span class="hidden">${temp_label}</span>
						`;
                        },
                    },
                    {
                        data: '',
                        render: function (data, type, full, meta) {
                            if (data != '') {
                                return '<a class="openArchive">View archived</a>';
                            }
                            return '';
                        },
                    },
                ],
                select: {
                    style: SELECT_MODE,
                    // selector: 'td:first-child'
                },
                order: [[1, 'asc']],
                displayLength: -1,
                butoons: [],
            });

            // Add event listener for opening and closing details (if role = 9)
            if (role == USER_SUPER_ADMIN || role == USER_LEAGUE_ADMIN) {
                $('#tblTeamSheet_' + group_id).on(
                    'click',
                    'tbody td a.openArchive',
                    function () {
                        var tr = $(this).closest('tr');
                        var row = tableTeamSheet.row(tr);

                        if (row.child.isShown()) {
                            // This row is already open - close it
                            row.child.hide();
                            tr.removeClass('shown');
                        } else {
                            // Open this row
                            row.child(
                                getTeamPlayersTableHtml(row.data().id)
                            ).show();
                            tr.addClass('shown');

                            // add class for next tr (child row)
                            $(this)
                                .closest('tr')
                                .next()
                                .addClass('child-row-detail');

                            initTeamPlayersTable(row.data().id);
                        }
                    }
                );
            } else {
                $('#tblTeamSheet_' + group_id).on(
                    'click',
                    'tbody td a.openArchive',
                    function () {
                        BootstrapDialog.show({
                            title: 'ERROR',
                            type: BootstrapDialog.TYPE_DANGER,
                            message: 'Only for admin.',
                        });
                    }
                );
            }

            // Open file PDF when click on name drill
            $('#tblTeamSheet_' + group_id).on(
                'click',
                'tbody td a.openPDF',
                function (e, row) {
                    var $row = $(this).closest('tr');
                    var data = tableTeamSheet.row($row).data();
                    // Open file
                    if (data.document != null) {
                        window.open(PRODUCT_IMAGE_PATH + data.document);
                    }
                }
            );

            $('#tblTeamSheet_' + group_id).off(
                'click',
                'tbody td div.onoffswitch-checkbox'
            );

            // Lock/Unlock
            $('#tblTeamSheet_' + group_id).on(
                'click',
                'tbody td .onoffswitch-checkbox',
                function (e, row) {
                    var $row = $(this).closest('tr');
                    var data = tableTeamSheet.row($row).data();
                    var status = data.locked == 1 ? '0' : '1';
                    // call to server
                    lockTeamSheet(data.team_sheet_id, status);
                }
            );
        }

        function initTeamPlayersTable(team_id) {
            tableTeamSheetChild = $('#tblTeamSheetchild_' + team_id).DataTable({
                dom: 'rt',
                stateSave: true,
                deferRender: true,
                ajax: {
                    url: SERVER_PATH + 'team/getTeamsSheetChild',
                    type: 'POST',
                    headers: {
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name,
                    },
                    data: {
                        team_id: team_id,
                    },
                    dataType: 'json',
                    complete: function (response) {
                        // response = JSON.parse(response.responseText);
                    },
                    error: function (xhr, status, error) {},
                },
                language: {
                    paginate: {
                        previous: '<i class="fa fa-chevron-left"></i>',
                        next: '<i class="fa fa-chevron-right"></i>',
                    },
                },
                columns: [
                    {
                        data: 'club_name',
                        // className: "center"
                    },
                    {
                        data: 'group_name',
                        // className: "center"
                    },
                    {
                        data: 'team_name',
                    },
                    {
                        data: 'document',
                        render: function (data, type, full, meta) {
                            if (data != '') {
                                return '<a class="openPDF">Open</a>';
                            }
                            return '';
                        },
                    },
                    {
                        data: 'date',
                    },
                ],
                select: {
                    style: SELECT_MODE,
                    selector: 'td:first-child',
                },
                order: [[1, 'asc']],
                displayLength: -1,
            });

            // Open file PDF when click on name drill
            $('#tblTeamSheetchild_' + team_id).on(
                'click',
                'tbody td a.openPDF',
                function (e, row) {
                    var $row = $(this).closest('tr');
                    var data = tableTeamSheetChild.row($row).data();
                    // Open file
                    if (data.document != null) {
                        window.open(PRODUCT_IMAGE_PATH + data.document);
                    }
                }
            );
        }

        function lockTeamSheet(team_sheet_id, status) {
            console.log(team_sheet_id, status);
            var action = status == 1 ? 'lock' : 'unlock';
            var title = 'Team sheet';
            var message =
                'Are you sure you want to ' + action + ' this team sheet?';
            var dialog = BootstrapDialog.confirm(
                title,
                message,
                function (result) {
                    if (result) {
                        dialog.close();
                        jQuery.ajax({
                            type: 'POST',
                            url: SERVER_PATH + 'team/lockTeamSheet',
                            async: false,
                            data:
                                'team_sheet_id=' +
                                team_sheet_id +
                                '&user_id=' +
                                $rootScope.user_id +
                                '&status=' +
                                status,
                            headers: {
                                'x-user-id': $rootScope.user_id,
                                'x-user-email': $rootScope.user_name,
                            },
                            dataType: 'json',
                            complete: function (response) {
                                var jsonData = JSON.parse(
                                    response.responseText
                                );
                                console.log(jsonData);
                                BootstrapDialog.show({
                                    title: jsonData.status,
                                    type:
                                        jsonData.status == 'OK'
                                            ? BootstrapDialog.TYPE_SUCCESS
                                            : BootstrapDialog.TYPE_WARNING,
                                    message: jsonData.message,
                                    onhide: function (dialogRef) {
                                        tableTeamSheet.ajax.reload();
                                    },
                                });
                            },
                            error: function (xhr, status, error) {
                                alert(
                                    'ajaxAction.Error - status, error = ' +
                                        status +
                                        ',' +
                                        error +
                                        ',' +
                                        xhr
                                );
                            },
                        });
                    } else {
                        tableTeamSheet.ajax.reload();
                    }
                }
            );
        }
    }
);
