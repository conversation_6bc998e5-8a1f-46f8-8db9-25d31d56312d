app.controller('cdateCtrl', function ($scope, $rootScope, $routeParams, $http) {
    $('#page-wrapper').removeClass('nav-small');
    var event_id = $routeParams.id;
    $scope.course_id = $routeParams.courseId;
    $scope.course_type = $routeParams.course_type;
    // cut link from end to class_code=
    var link=window.location.href;
    var index=link.indexOf('class_code=');
    // set class_code
    $scope.class_code = link.substring(index+11,link.length);
    // check class_code is urlEncoded
    if($scope.class_code.indexOf('%')!=-1){
        $scope.class_code = decodeURIComponent($scope.class_code);
    }
    
    // get info event
    jQuery.ajax({
        type: 'POST',
        url: SERVER_PATH + 'event/getEventInfo',
        async: false, 
        headers: {	
            'x-user-id': $rootScope.user_id,
            'x-user-email': $rootScope.user_name
		},
        data: {
            event_id: event_id,
        },
        dataType: 'json',
        complete: function (response) {
            var jsonData = JSON.parse(response.responseText);
            var event = jsonData.info;
            $scope.selectedEvent = jsonData.info;
            $scope.selectedEvent.id = event_id;
            event_name = event.name;
            event_type = event.type;
            normalizedType = normalizeEventType(event_type);
        },
    });

    $scope.event_name = event_name;
    $scope.event_type = event_type;
    $scope.normalizedType = normalizedType;

    function normalizeEventType(eventType) {
        switch (eventType) {
            case 'Summer Scheme':
                return 'summer-scheme';
            case 'Regional':
                return 'regional';
            case 'PL Junior':
                return 'pl-junior';
            case 'Golden Age':
                return 'golden-age';
            case 'Beginner':
                return 'beginner';
            case 'District':
                return 'district';
            default:
                return '';
        }
    }

    function formatCdates(d) {
        // `d` is the original data object for the row
        $content =
            '<div class="table-responsive">' +
            '<table id="tableCdates_' +
            d.courses.id +
            '" class="table table-striped table-bordered table-hover" cellspacing="0" width="100%">' +
            '<thead><tr>' +
            '<th rowspan="2">Type</th>' +
            '<th rowspan="2">Date</th>' +
            '<th rowspan="2">Time</th>' +
            '<th rowspan="2">Status</th>' +
            '<th rowspan="2">Cancelled Reason</th>' +
            '<th colspan="2">Action</th>' +
            '</tr><tr><th></th><th></th></tr></thead>' +
            '</table>';
        ('</div>');

        return $content;
    }

    function tablePlayers(d) {
        // `d` is the original data object for the row
        $content = `<div class="table-responsive">
            <table id="tablePlayers_${d.courses.id}" class="table table-striped table-bordered table-hover" cellspacing="0" width="100%">
            </table></div>`;
        return $content;
    }

    function tableParents(d) {
        // `d` is the original data object for the row
        $content = `<div class="table-responsive">
            <table id="tableParents_${d.courses.id}" class="table table-striped table-bordered table-hover" cellspacing="0" width="100%">
                <thead>
                    <tr>
                        <td>Parent name</td>
                        <td>Player name</td>
                        <td>Parent type</td>
                    </tr>
                </thead>
                <tbody>
                </tbody>
            </table></div>`;
        return $content;
    }

    $scope.initCdates = function (d) {
        var date_type = [];

        if ($scope.event_type == EVENT_PL_JUNIOR) {
            date_type = [{ label: 'Training', value: 'Training' }];
        } else {
            date_type = [
                { label: 'Training', value: 'Training' },
                { label: 'Game Days', value: 'Game Days' },
            ];
        }

        editorCdates = new $.fn.dataTable.Editor({
            ajax: {
                type: 'POST',
                url: SERVER_PATH + 'course/setCdates',
                headers: {	
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name
                },
                data: {
                    course_id: d.courses.id,
                    user_id: $rootScope.user_id,
                },
                async: false,
                dataType: 'json',
                complete: function (response) {
                    var jsonData = JSON.parse(response.responseText);
                    if (typeof jsonData.fieldErrors == 'undefined') {
                        tableCdates.ajax.reload();
                    }
                },
                error: function (xhr, status, error) {},
            },
            table: '#tableCdates_' + d.courses.id,
            formOptions: {
                main: {
                    onBlur: 'none',
                },
            },
            i18n: {
                create: {
                    button: 'New',
                    title: 'Create new event',
                    submit: 'Create',
                },
                edit: {
                    button: 'Edit',
                    title: 'Edit event',
                    submit: 'Update',
                },
                remove: {
                    button: 'Delete',
                    title: 'Delete event',
                    submit: 'Delete',
                    confirm: {
                        _: 'Are you sure you want to delete these event?',
                        1: 'Are you sure you want to delete this event?',
                    },
                },

                error: {
                    system: 'System error, please contact administrator.',
                },
            },
            fields: [
                {
                    type: 'hidden',
                    name: 'cdates.course_id',
                    default: d.courses.id,
                },
                {
                    label: 'Type',
                    name: 'cdates.type',
                    type: 'radio',
                    options: date_type,
                    default: 'Training',
                },
                {
                    label: 'Date',
                    name: 'cdates.date',
                    type: 'datetime',
                    format: 'DD/MM/YYYY',
                    default: moment().format('DD/MM/YYYY'),
                },
                {
                    label: 'Start Time',
                    name: 'cdates.start_time',
                    type: 'datetime',
                    format: 'HH:mm',
                    def: '00:00',
                },
                {
                    label: 'End Time',
                    name: 'cdates.end_time',
                    type: 'datetime',
                    format: 'HH:mm',
                    def: '00:00',
                },
            ],
        });

        editorCancel = new $.fn.dataTable.Editor({
            ajax: {
                type: 'POST',
                url: SERVER_PATH + 'course/getCdates',
                headers: {	
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name
                },
                data: {
                    course_id: d.courses.id,
                    user_id: $rootScope.user_id,
                },
                async: false,
                dataType: 'json',
                complete: function (response) {
                    var jsonData = JSON.parse(response.responseText);
                    if (typeof jsonData.fieldErrors == 'undefined') {
                        tableCdates.ajax.reload();
                    }
                },
                error: function (xhr, status, error) {},
            },
            table: '#tableCdates_' + d.courses.id,
            formOptions: {
                main: {
                    onBlur: 'none',
                },
            },
            i18n: {
                error: {
                    system: 'System error, please contact administrator.',
                },
            },
            fields: [
                {
                    type: 'hidden',
                    name: 'cdates.course_id',
                    default: d.courses.id,
                },
                {
                    label: 'Type',
                    name: 'cdates.type',
                    type: 'hidden',
                    options: [
                        { label: 'Training', value: 'Training' },
                        { label: 'Game Days', value: 'Game Days' },
                    ],
                    default: 'Training',
                },
                {
                    label: 'Date',
                    name: 'cdates.date',
                    type: 'hidden',
                    format: 'DD/MM/YYYY',
                    default: moment().format('DD/MM/YYYY'),
                },
                {
                    label: 'Start Time',
                    name: 'cdates.start_time',
                    type: 'hidden',
                    format: 'HH:mm',
                    def: '00:00',
                },
                {
                    label: 'End Time',
                    name: 'cdates.end_time',
                    type: 'hidden',
                    format: 'HH:mm',
                    def: '00:00',
                },
                {
                    label: 'Status',
                    name: 'cdates.status',
                    def: SESSION_STATUS_CANCELED,
                    type: 'hidden',
                },
                {
                    label: 'Reason*',
                    name: 'cdates.cancelled_reason',
                    type: 'textarea',
                },
                {
                    label: 'Cancel by',
                    name: 'cdates.cancelled_by',
                    type: 'hidden',
                    def: $rootScope.user_id,
                },
            ],
        });

        editorCancel.on('initEdit', function (e, o, action) {
            console.log($rootScope.user_id);
            // set cancel by
            editorCancel
                .field('cdates.cancelled_by')
                .val($rootScope.user.person_id);
            editorCancel.field('cdates.status').val(SESSION_STATUS_CANCELED);
        });

        tableCdates = $('#tableCdates_' + d.courses.id).DataTable({
            dom: '<"row"<"col-sm-6"B><"col-sm-6"f>>rt<"row"i>',
            stateSave: false,
            deferRender: true,
            bDestroy: true,
            ajax: {
                url: SERVER_PATH + 'course/getCdates',
                type: 'POST',
                headers: {	
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name
                },
                data: {
                    course_id: d.courses.id,
                    user_id: $rootScope.user_id,
                },
                dataType: 'json',
                complete: function (response) {},
                error: function (xhr, status, error) {},
            },
            language: {
                paginate: {
                    previous: '<i class="fa fa-chevron-left"></i>',
                    next: '<i class="fa fa-chevron-right"></i>',
                },
            },

            columns: [
                {
                    data: 'cdates.type',
                },
                {
                    data: 'cdates.date',
                },
                {
                    data: null,
                    render: function (data, type, row, meta) {
                        return (
                            row.cdates.start_time + ' - ' + row.cdates.end_time
                        );
                    },
                },
                {
                    data: 'cdates.status',
                    className: 'center',
                    render: function (data, type, row, meta) {
                        return data == 'Active'
                            ? '<span class="badge badge-success">Active</span>'
                            : '<span class="badge badge-danger">Cancelled</span>';
                    },
                },
                {
                    data: 'cdates.cancelled_reason',
                    className: 'center',
                },
                {
                    data: 'role',
                    className: 'center',
                    sortable: false,
                    render: function (data) {
                        if (
                            parseInt(data) == ROLE_ASSITANT_COACH ||
                            parseInt(data) == ROLE_GOALKEEPER_COACH ||
                            parseInt(data) == ROLE_HEAD_COACH ||
                            parseInt(data) == USER_SUPER_ADMIN ||
                            parseInt(data) == USER_LEAGUE_ADMIN
                        ) {
                            var actions =
                                '<button type="button" data-toggle="modal" data-target="#modal-coaches" class="btn btn-primary modal-coaches" >Coach Attendance</button> &nbsp';
                            return actions;
                        }
                        return '';
                    },
                },
                {
                    data: null,
                    className: 'center',
                    sortable: false,
                    render: function (data) {
                        var actions =
                            '<button type="button" data-toggle="modal" data-target="#modal-player-attendance" class="btn btn-success modal-player-attendance" >Player Attendance</button>';
                        return actions;
                    },
                },
            ],
            select: {
                style: 'single',
                selector: 'td:not(:last-child)',
            },
            order: [
                [0, 'desc'],
                [1, 'asc'],
                [2, 'asc'],
            ],
            buttons: [
                {
                    extend: 'create',
                    editor: editorCdates,
                },
                {
                    extend: 'edit',
                    editor: editorCdates,
                },
                {
                    extend: 'remove',
                    editor: editorCdates,
                },
                {
                    extend: 'selectedSingle',
                    text: 'Cancel',
                    action: function (e, dt, node, config) {
                        var row = tableCdates.row({ selected: true }).data();

                        if (
                            row.cdates.status.toLowerCase() ==
                            SESSION_STATUS_CANCELED.toLowerCase()
                        ) {
                            BootstrapDialog.show({
                                title: 'Warning',
                                message: 'This session has been canceled',
                                type: BootstrapDialog.TYPE_WARNING,
                                buttons: [
                                    {
                                        label: 'OK',
                                        action: function (dialog) {
                                            dialog.close();
                                        },
                                    },
                                ],
                            });
                            return;
                        }

                        BootstrapDialog.show({
                            title: 'Confirm',
                            message:
                                'Are you sure you want to cancel this session?',
                            type: BootstrapDialog.TYPE_WARNING,
                            buttons: [
                                {
                                    label: 'Cancel',
                                    action: function (dialog) {
                                        dialog.close();
                                    },
                                },
                                {
                                    label: 'OK',
                                    action: function (dialog) {
                                        var rows = dt
                                            .rows({ selected: true })
                                            .data();
                                        editorCancel
                                            .edit(
                                                tableCdates
                                                    .rows({ selected: true })
                                                    .indexes()
                                            )
                                            .title('Cancel Session')
                                            .buttons('Submit')

                                            .open();
                                        dialog.close();
                                    },
                                },
                            ],
                        });
                    },
                },
                {
                    extend: 'colvis',
                    text: 'Columns',
                },
            ],
            displayLength: -1,
            columnDefs: [
                {
                    type: 'justNum',
                    targets: 2,
                },
                { targets: 1, type: 'date-eu' },
            ],
        });

        // Add event listener for opening and closing details
        $('#tableCdates_' + d.courses.id).on(
            'click',
            'tbody .modal-coaches',
            function () {
                let row = $(this).closest('tr');
                let data = tableCdates.row(row).data();

                var msg = formatCcoaches(data);
                // Show dialog
                BootstrapDialog.show({
                    title:
                        'Manage Coaches - ' +
                        data.cdates.type +
                        ' ' +
                        data.cdates.date,
                    message: msg,
                    size: BootstrapDialog.SIZE_WIDE,
                    onshown: function (dialogRef) {
                        $scope.initCcoaches(data);
                    },
                });
            }
        );

        $('#tableCdates_' + d.courses.id).on(
            'click',
            'tbody .modal-player-attendance',
            function () {
                let row = $(this).closest('tr');
                let data = tableCdates.row(row).data();

                var msg = getModalAttendancePlayersTableHtml(data.cdates.id);

                // show dialog
                BootstrapDialog.show({
                    title: ' Player attendance - ' + data.cdates.date,
                    message: msg,
                    size: BootstrapDialog.SIZE_WIDE,
                    onshown: function (dialogRef) {
                        $scope.initPlayerAttendanceReportTbl(data);
                    },
                });
            }
        );
    };

    function formatCcoaches(d) {
        console.log(d);
        // `d` is the original data object for the row
        $content =
            '<div class="table-responsive">' +
            '<table id="tableCcoaches_' +
            d.cdates.id +
            '" class="table table-striped table-bordered table-hover" cellspacing="0" width="100%">' +
            '<thead><tr>' +
            '<th>Coach Name</th>' +
            '<th>Email</th>' +
            '<th>Phone</th>' +
            '<th>Role</th>' +
            '<th>Level</th>' +
            '<th>Attendance</th>' +
            '<th>Overlapped</th>' +
            '</tr></thead>' +
            '</table>';
        ('</div>');

        return $content;
    }

    function getModalAttendancePlayersTableHtml(cdate_id) {
        var str =
            '' +
            '<div class="main-box-body clearfix">' +
            '<div class="table-responsive">' +
            '<table id="tableAttendancePlayers_' +
            cdate_id +
            '" class="table table-striped table-bordered table-hover" cellspacing="0" width="100%">' +
            '<thead>' +
            '<tr>' +
            '<th>Surname</th>' +
            '<th>Other Name</th>' +
            '<th>Parent Name</th>' +
            '<th>Parent Email</th>' +
            '<th>Parent Phone</th>' +
            '<th>Attendance</th>' +
            '<th>Update By</th>' +
            '<th>Update At</th>' +
            '</tr>' +
            '</thead>' +
            '</table>' +
            '</div>' +
            '</div>' +
            '';
        return str;
    }

    $scope.initCcoaches = function (d) {
        console.log(d);
        editorCcoaches = new $.fn.dataTable.Editor({
            ajax: {
                type: 'POST',
                url: SERVER_PATH + 'course/setCcoaches',
                headers: {	
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name
                },
                data: {
                    cdate_id: d.cdates.id,
                },
                async: false,
                dataType: 'json',
                complete: function (response) {
                    var jsonData = JSON.parse(response.responseText);
                    if (typeof jsonData.fieldErrors == 'undefined') {
                        tableCcoaches.ajax.reload();
                    }
                },
                error: function (xhr, status, error) {},
            },
            table: '#tableCcoaches_' + d.cdates.id,
            formOptions: {
                main: {
                    onBlur: 'none',
                },
            },
            i18n: {
                create: {
                    button: 'Add',
                    title: 'Create new coach',
                    submit: 'Create',
                },
                edit: {
                    button: 'Change Role',
                    title: 'Change Role',
                    submit: 'Update',
                },
                remove: {
                    button: 'Delete',
                    title: 'Delete coach',
                    submit: 'Delete',
                    confirm: {
                        _: 'Are you sure you want to delete these coach?',
                        1: 'Are you sure you want to delete this coach?',
                    },
                },

                error: {
                    system: 'System error, please contact administrator.',
                },
            },
            fields: [
                {
                    type: 'hidden',
                    name: 'ccoaches.cdate_id',
                    default: d.cdates.id,
                },
                {
                    label: 'Select Coach',
                    name: 'ccoaches.coach_id',
                    type: 'select2',
                    opts: {
                        multiple: false,
                        placeholder: 'Search and select coach...',
                    },
                },
                {
                    label: 'Role',
                    name: 'ccoaches.role',
                    type: 'radio',
                    options: [
                        { label: ROLE_HEAD_COACH, value: ROLE_HEAD_COACH },
                        {
                            label: ROLE_ASSITANT_COACH,
                            value: ROLE_ASSITANT_COACH,
                        },
                        {
                            label: ROLE_GOALKEEPER_COACH,
                            value: ROLE_GOALKEEPER_COACH,
                        },
                    ],
                    default: ROLE_HEAD_COACH,
                },
            ],
        });

        editorCcoaches.on('initEdit', function (e, json, data, action) {
            editorCcoaches.disable('ccoaches.coach_id');
        });

        editorCcoaches.on('initCreate', function (e, json, data, action) {
            editorCcoaches.enable('ccoaches.coach_id');
        });

        editorSubstituteCoach = new $.fn.dataTable.Editor({
            ajax: {
                type: 'POST',
                url: SERVER_PATH + 'course/changeSubstitutedCoach',
                headers: {	
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name
                },
                data: {
                    cdate_id: d.cdates.id,
                    user_id: $rootScope.user_id,
                },
                async: false,
                dataType: 'json',
                complete: function (response) {
                    var jsonData = JSON.parse(response.responseText);
                    if (typeof jsonData.fieldErrors == 'undefined') {
                        tableCcoaches.ajax.reload();
                    }
                },
                error: function (xhr, status, error) {},
            },
            table: '#tableCcoaches_' + d.cdates.id,
            formOptions: {
                main: {
                    onBlur: 'none',
                },
            },
            i18n: {
                edit: {
                    button: 'Substitute Coach',
                    title: 'Substitute Coach',
                    submit: 'Update',
                },
                error: {
                    system: 'System error, please contact administrator.',
                },
            },
            fields: [
                {
                    type: 'hidden',
                    name: 'edit_type',
                    default: 'substitute',
                },
                {
                    type: 'hidden',
                    name: 'ccoaches.cdate_id',
                    default: d.cdates.id,
                },
                {
                    label: 'Select substitute Coach',
                    name: 'substitute_coach_id',
                    type: 'select2',
                    opts: {
                        multiple: false,
                        placeholder: 'Search and select coach...',
                    },
                },
                {
                    label: 'Role',
                    name: 'ccoaches.role',
                    type: 'hidden',
                },
            ],
        });

        tableCcoaches = $('#tableCcoaches_' + d.cdates.id).DataTable({
            dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
            stateSave: false,
            deferRender: true,
            bDestroy: true,
            ajax: {
                url: SERVER_PATH + 'course/getCcoaches',
                headers: {	
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name
                },
                type: 'POST',
                data: {
                    cdate_id: d.cdates.id,
                },
                dataType: 'json',
                complete: function (response) {},
                error: function (xhr, status, error) {},
            },
            language: {
                paginate: {
                    previous: '<i class="fa fa-chevron-left"></i>',
                    next: '<i class="fa fa-chevron-right"></i>',
                },
            },

            columns: [
                {
                    data: null,
                    render: function (data) {
                        return (
                            data.parens.surname + ' ' + data.parens.other_name
                        );
                    },
                },
                {
                    data: 'parens.email',
                },
                {
                    data: 'parens.phone',
                },
                {
                    data: 'ccoaches.role',
                },
                {
                    data: 'coach_levels.level',
                },
                {
                    data: 'ccoaches.check_attendance',
                    className: 'center',
                    render: function (data, type, row) {
                        ATTENDANCE_STATUS.forEach(function (item, key) {
                            if (key == data) {
                                data = item;
                            }
                        });

                        return data;
                    },
                },
                {
                    data: 'is_overlap',
                    className: 'center',
                    render: function (data, type, row) {
                        if (data) {
                            return '<a href="javascript:void(0)"><i class="fa fa-flag fa-lg"></i></a>';
                        } else {
                            return '';
                        }
                    },
                },
            ],
            select: {
                style: 'single',
                selector: 'td:not(:last-child)',
            },
            order: [[2, 'asc']],
            buttons: [
                {
                    extend: 'create',
                    className: 'coach-cdate',
                    editor: editorCcoaches,
                },
                {
                    extend: 'edit',
                    className: 'coach-cdate',
                    editor: editorCcoaches,
                },
                {
                    extend: 'edit',
                    className: 'coach-cdate',
                    text: 'Substitute Coach',
                    editor: editorSubstituteCoach,
                },
                {
                    extend: 'selectedSingle',
                    text: 'Change Attendance',
                    action: function (e, dt, node, config) {
                        let data = tableCcoaches
                            .rows({ selected: true })
                            .data()[0];

                        Swal.fire({
                            title: 'Change Attendance',
                            html: 'Please select the new attendance status:',
                            showCloseButton: true,
                            focusConfirm: false,
                            confirmButtonText: ATTENDANCE_STATUS.get(
                                COACH_ATTENDANCE_STATUS_ATTENDED
                            ),
                            showDenyButton: true,
                            denyButtonText: ATTENDANCE_STATUS.get(
                                COACH_ATTENDANCE_STATUS_DID_NOT_ATTEND
                            ),
                            denyButtonColor: 'red',
                            confirmButtonColor: 'green',
                        }).then((result) => {
                            if (result.isConfirmed) {
                                jQuery.ajax({
                                    type: 'POST',
                                    url:
                                        SERVER_PATH +
                                        'course/changeAttendanceStatus',
                                    headers: {	
                                            'x-user-id': $rootScope.user_id,
                                            'x-user-email': $rootScope.user_name
                                    },
                                    data: {
                                        ccoaches_id: data.ccoaches.id,
                                        attendance_status:
                                            COACH_ATTENDANCE_STATUS_ATTENDED,
                                        user_id: $rootScope.user_id,
                                        event_id: event_id,
                                    },
                                    async: false,
                                    dataType: 'json',
                                    complete: function (response) {
                                        response = response.responseJSON;
                                        if (response.status == 'OK') {
                                            Swal.close();
                                            tableCcoaches.ajax.reload();
                                            // close modal
                                        } else {
                                            Swal.fire({
                                                title: 'Error',
                                                text: response.message,
                                                icon: 'error',
                                                confirmButtonText: 'OK',
                                            });
                                        }
                                    },
                                });
                            } else if (result.isDenied) {
                                jQuery.ajax({
                                    type: 'POST',
                                    url:
                                        SERVER_PATH +
                                        'course/changeAttendanceStatus',
                                    headers: {	
                                        'x-user-id': $rootScope.user_id,
                                        'x-user-email': $rootScope.user_name
                                    },
                                    data: {
                                        ccoaches_id: data.ccoaches.id,
                                        attendance_status:
                                            COACH_ATTENDANCE_STATUS_DID_NOT_ATTEND,
                                        user_id: $rootScope.user_id,
                                        event_id: event_id,
                                    },
                                    async: false,
                                    dataType: 'json',
                                    complete: function (response) {
                                        response = response.responseJSON;
                                        if (response.status == 'OK') {
                                            Swal.close();
                                            tableCcoaches.ajax.reload();
                                        } else {
                                            Swal.fire({
                                                title: 'Error',
                                                text: response.message,
                                                icon: 'error',
                                                confirmButtonText: 'OK',
                                            });
                                        }
                                    },
                                });
                            }
                        });
                    },
                },
                {
                    extend: 'remove',
                    className: 'coach-cdate',
                    editor: editorCcoaches,
                },
                {
                    extend: 'colvis',
                    text: 'Columns',
                },
            ],
            columnDefs: [
                {
                    type: 'justNum',
                    targets: 2,
                },
            ],
        });

        tableCcoaches.on('init.dt', function () {
            // hide buttons
            if (
                $rootScope.user_role != USER_SUPER_ADMIN &&
                $rootScope.user_role != USER_LEAGUE_ADMIN
            ) {
                tableCcoaches.buttons('.coach-cdate').remove();
            }
        });
    };

    $scope.initPlayerAttendanceReportTbl = function (data) {
        tablePlayerAttendanceReport = $(
            '#tableAttendancePlayers_' + data.cdates.id
        ).DataTable({
            dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
            stateSave: false,
            deferRender: true,
            ajax: {
                url: SERVER_PATH + 'course/getPlayersOfCdate',
                type: 'POST',
                headers: {	
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name
                },
                data: {
                    cdate_id: data.cdates.id,
                },
                dataType: 'json',
                complete: function (response) {},
                error: function (xhr, status, error) {},
            },
            language: {
                paginate: {
                    previous: '<i class="fa fa-chevron-left"></i>',
                    next: '<i class="fa fa-chevron-right"></i>',
                },
            },
            columns: [
                { data: 'players.surname' },
                { data: 'players.other_name' },
                {
                    data: null,
                    render: function (data) {
                        return (
                            data.parens.surname + ' ' + data.parens.other_name
                        );
                    },
                },
                { data: 'parens.email' },
                { data: 'parens.phone' },
                {
                    data: null,
                    render: function (data) {
                        if (data.cdate_cplayers.status == 1) {
                            return 'Present';
                        } else {
                            return '';
                        }
                    },
                },
                {
                    data: null,
                    render: function (data) {
                        if (data.updated_by.surname) {
                            return (
                                data.updated_by.surname +
                                ' ' +
                                data.updated_by.other_name
                            );
                        } else {
                            return '';
                        }
                    },
                },
                { data: 'cdate_cplayers.updated_at' },
            ],
            select: {
                style: 'single',
                selector: 'td:not(:last-child)',
            },
            order: [[0, 'asc']],
        });
    };

    function modalSendMsg() {
        var modal = `<div id="modal-send-msg">
            <div class="form-group">
                <label>Type</label>
                <div class="form-check">
                    <input class="form-check-input" type="radio" name="type" id="type1" value="App" checked>
                    <label class="form-check-label" for="type1">
                        App
                    </label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="radio" name="type" id="type2" value="App and Email">
                    <label class="form-check-label" for="type2">
                        App & Email
                    </label>
                </div>
            </div>
            <div class="form-group">
                <label>From</label>
                <input type="text" class="form-control" id="from" value="${$rootScope.user.email}" disabled>
            </div>
            <div class="form-group">
                <label>Cc</label>
                <input type="text" class="form-control" id="cc" placeholder="Enter email address separated by ;">
            </div>
            <div class="form-group">
                <label>Bcc</label>
                <input type="text" class="form-control" id="bcc" placeholder="Enter email address separated by ;">
            </div>
            <div class="form-group">
                <label>Subject</label>
                <input type="text" class="form-control" id="subject">
                <p class="form-text text-danger" id="subject-error"></p>
            </div>
            <div class="form-group">
                <label>Message</label>
                <textarea class="form-control" rows="5" id="msg"></textarea>
                <p class="form-text text-danger" id="msg-error"></p>
            </div>
        </div>`;
        return modal;
    }

    function sendMsg(player_ids) {
        let divParent = $('#modal-send-msg');
        let type = divParent.find('input[name="type"]:checked').val();
        let cc = divParent.find('#cc').val();
        let bcc = divParent.find('#bcc').val();
        let subject = divParent.find('#subject').val();
        let msg = divParent.find('#msg').val();
        let subject_error = divParent.find('#subject-error');
        let msg_error = divParent.find('#msg-error');
        subject_error.text('');
        msg_error.text('');

        if (!subject) {
            subject_error.text('Please enter subject');
            return;
        }
        if (!msg) {
            msg_error.text('Please enter message');
            return;
        }
        let sending = BootstrapDialog.show({
            title: 'Sending Message',
            message: 'Please wait...',
            closable: false,
            closeByBackdrop: false,
            type: BootstrapDialog.TYPE_SUCCESS,
        });
        console.log(type, from, cc, bcc, subject, msg);
        setTimeout(function () {
            // post to sendMessageByCoach
            jQuery.ajax({
                type: 'POST',
                url: SERVER_PATH + 'course/sendMessageByCoach',
                headers: {	
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name
                },
                data: {
                    user_id: $rootScope.user_id,
                    type: type,
                    cc: cc,
                    bcc: bcc,
                    subject: subject,
                    content: msg,
                    player_ids: player_ids,
                    event_id: event_id,
                },
                async: false,
                dataType: 'json',
                complete: function (response) {
                    sending.close();
                    response = response.responseJSON;
                    if (response.status == 'OK') {
                        //    close modal
                        BootstrapDialog.closeAll();
                        BootstrapDialog.show({
                            title: 'SUCCESS',
                            message: response.message,
                            type: BootstrapDialog.TYPE_SUCCESS,
                            buttons: [
                                {
                                    label: 'OK',
                                    action: function (dialog) {
                                        dialog.close();
                                    },
                                },
                            ],
                        });
                    } else {
                        console.log(response);

                        BootstrapDialog.show({
                            title: 'Error',
                            message: response.message,
                            type: BootstrapDialog.TYPE_DANGER,
                            buttons: [
                                {
                                    label: 'OK',
                                    action: function (dialog) {
                                        dialog.close();
                                    },
                                },
                            ],
                        });
                    }
                },
            });
        }, 300);
    }
    $scope.initPlayers = function (d) {
        tablePlayers = $('#tablePlayers_' + d.courses.id).DataTable({
            dom: '<"row"<"col-sm-6"B><"col-sm-6"f>>rt<"row"i>',
            stateSave: false,
            deferRender: true,
            bDestroy: true,
            ajax: {
                url: SERVER_PATH + 'course/getPlayerInCourse',
                type: 'POST',
                headers: {	
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name
                },
                data: {
                    course_id: d.courses.id,
                    event_id: event_id,
                },
                dataType: 'json',
                complete: function (response) {},
                error: function (xhr, status, error) {},
            },
            language: {
                paginate: {
                    previous: '<i class="fa fa-chevron-left"></i>',
                    next: '<i class="fa fa-chevron-right"></i>',
                },
            },

            columns: [
                {
                    data: 'players.id',
                    title: '',
                },
                {
                    data: 'players.surname',
                    title: 'Surname',
                },
                {
                    data: 'players.other_name',
                    title: 'Other Name',
                },
                {
                    data: 'players.chinese_name',
                    title: 'Chinese Name',
                },
                {
                    data: 'players.dob',
                    title: 'Date of Birth',
                    render: function (data, type, row) {
                        return moment(data).format('YYYY-MM-DD');
                    },
                },
                {
                    data: 'parens.surname',
                    title: 'Parent name',
                    render: function (data, type, row) {
                        return `${row.parens.other_name} ${data}`;
                    },
                    visible: false,
                },
                {
                    data: 'parens.email',
                    title: 'Parent Email',
                    visible: false,
                },
                {
                    data: 'parens.phone',
                    title: 'Parent Phone',
                    render: function (data, type, row) {
                        return '<a href="https://wa.me/' + row.parens.country_code+data + '" target="_blank">' + `${row.parens.country_code} ${data}` + '</a>';
                    },
                    visible: false,
                },
            ],

            columnDefs: [
                {
                    targets: 0,
                    checkboxes: {
                        selectRow: true,
                    },
                },
            ],

            select: {
                style: 'multi',
                selector: 'td:first-child',
            },

            order: [[1, 'asc']],

            buttons: [
                // {
                //     extend: 'selected',
                //     text: 'Send Message',
                //     action: function (e, dt, node, config) {
                //         var rows = dt.rows({ selected: true }).data();
                //         if (rows.length > 0) {
                //             // get player ids
                //             var player_ids = [];
                //             rows.each(function (value, index) {
                //                 player_ids.push(value.players.id);
                //             }
                //             );
                //             console.log('player_ids', player_ids);
                //             player_ids = player_ids.join(',');
                //             // show modal
                //             BootstrapDialog.show({
                //                 title: 'Send Message',
                //                 message: $(modalSendMsg()),
                //                 size: BootstrapDialog.SIZE_WIDE,
                //                 buttons: [
                //                     {
                //                         label: 'Cancel',
                //                         action: function (dialog) {
                //                             dialog.close();
                //                         }
                //                     },
                //                     {
                //                         label: 'Send',
                //                         cssClass: 'btn-primary',
                //                         action: function (dialog) {
                //                             sendMsg(player_ids);
                //                         }
                //                     }
                //                 ],
                //             });
                //         }
                //     }
                // },
                {
                    extend: 'colvis',
                    text: 'Columns',
                },
            ],
            displayLength: -1,
        });
    };

    $scope.initParents = function (d) {
        tableParents = $('#tableParents_' + d.courses.id).DataTable({
            dom: '<"row"<"col-sm-6"B><"col-sm-6"f>>rt<"row"i>',
            stateSave: false,
            deferRender: true,
            bDestroy: true,
            ajax: {
                url: SERVER_PATH + 'course/getParentsInCourse',
                type: 'POST',
                headers: {	
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name
                },
                data: {
                    course_id: d.courses.id,
                },
                dataType: 'json',
                complete: function (response) {},
                error: function (xhr, status, error) {},
            },
            language: {
                paginate: {
                    previous: '<i class="fa fa-chevron-left"></i>',
                    next: '<i class="fa fa-chevron-right"></i>',
                },
            },
            columns: [
                {
                    data:'parent_name'
                },
                {
                    data: 'player_names',
                },
                {
                    data: 'parent_type'
                }
            ],
            order: [[0, 'asc']],
            buttons: [
                {
                    extend: 'colvis',
                    text: 'Columns',
                },
            ],
            displayLength: -1,
        });
    }

    // init cdates
    let data = {
        courses: {
            id: $scope.course_id,
            class_code: $scope.class_code,
        },
    };

    $('#tableCdateContent').html(formatCdates(data));
    $('#tablePlayers').html(tablePlayers(data));
    $('#tableParents').html(tableParents(data));
    $scope.initPlayers(data);
    $scope.initCdates(data);
    $scope.initParents(data);

    $scope.generateReport = function () {
        console.log('generate report');
        createReport($scope.course_id);
    };

    function getReportTableHtml(ipg, data) {
        var str =
            '<div class="table-responsive">' +
            '<button id="export" style="margin: 20px 0 20px 0;">' +
            '<span>Export to Excel</span>' +
            '</button>' +
            '<table id="tblReport_' +
            ipg +
            '" class="table table-striped table-bordered table-hover" cellspacing="0" width="100%">';

        for (var r = 0; r < data.length; r++) {
            const row = data[r];
            str += '<tr>';
            for (var c = 0; c < row.length; c++) {
                class_name = '';
                if (r > 0 && c > 0) {
                    class_name =
                        row[c] == 'Y'
                            ? 'text-center bg-success'
                            : 'text-center';
                }
                if (r == 0) {
                    class_name = 'text-center';
                }
                if (c == 0) {
                    str +=
                        '<td class="text-left ' +
                        class_name +
                        '" style="text-wrap: nowrap">';
                } else {
                    str += '<td class="text-left ' + class_name + '">';
                }
                str += row[c];
                str += '</td>';
            }
            str += '</tr>';
        }
        str += '</table>' + '</div>';
        return str;
    }

    function createReport(course_id) {
        jQuery.ajax({
            type: 'POST',
            url: SERVER_PATH + 'course/getPlayerAttendanceReport',
            async: false,
            headers: {	
                'x-user-id': $rootScope.user_id,
                'x-user-email': $rootScope.user_name
            },
            data: {
                course_id: course_id,
            },
            dataType: 'json',
            complete: function (response) {
                var json = JSON.parse(response.responseText);
                var data = json.data;
                var htmlReport = getReportTableHtml(
                    $scope.selectedEvent.id,
                    data
                );
                $('#tab-report').html(htmlReport);
            },
        });

        $('#export').click(function () {
            ExportFile(event_id);
        });

        function ExportFile(event_id) {
            var tab_text = "<table border='2px'><tr >";
            var j = 0;
            tab = document.getElementById('tblReport_' + event_id); // id of table

            for (j = 0; j < tab.rows.length; j++) {
                tab_text = tab_text + tab.rows[j].innerHTML + '</tr>';
                //tab_text=tab_text+"</tr>";
            }

            tab_text = tab_text + '</table>';
            tab_text = tab_text.replace(/<A[^>]*>|<\/A>/g, ''); //remove if u want links in your table
            tab_text = tab_text.replace(/<img[^>]*>/gi, ''); // remove if u want images in your table
            tab_text = tab_text.replace(/<input[^>]*>|<\/input>/gi, ''); // reomves input params

            var ua = window.navigator.userAgent;
            var msie = ua.indexOf('MSIE ');

            if (msie > 0 || !!navigator.userAgent.match(/Trident.*rv\:11\./)) {
                // If Internet Explorer
                txtArea1.document.open('txt/html', 'replace');
                txtArea1.document.write(tab_text);
                txtArea1.document.close();
                txtArea1.focus();
                sa = txtArea1.document.execCommand(
                    'SaveAs',
                    true,
                    'Say Thanks to Sumit.xls'
                );
            } //other browser not tested on IE 11
            else
                sa = window.open(
                    'data:application/vnd.ms-excel,' +
                        encodeURIComponent(tab_text)
                );

            return sa;
        }
    }
});
