var app = angular.module('hkjflApp', ['ngRoute']);

app.controller('openCertificatePlayer', function ($scope, $rootScope, $http, $routeParams, $location) {
	var url = $location.absUrl().split('?')[1];
	var arrayParameter = url.split("/");

	player_id = arrayParameter[0];
	cer_id = arrayParameter[1];
	width = arrayParameter[2];

	$scope.parameterData = {
		'certificate_id': cer_id,
		'player_id': player_id,
		'width': width
	};

	$http({
		method: 'POST',
		url: SERVER_PATH + "certificate/openCenificate",
		headers: {	
			'x-user-id': $rootScope.user_id,
			'x-user-email': $rootScope.user_name
		},
		data: $.param($scope.parameterData),
		headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
	}).success(function (response) {
		if (response.status === 'OK') {
			$("#open_Certificate_Player_Content").html(response.info);
		} else {
			BootstrapDialog.show({
				title: 'ERROR',
				type: BootstrapDialog.TYPE_WARNING,
				message: response.message
			});
		}
	});
})