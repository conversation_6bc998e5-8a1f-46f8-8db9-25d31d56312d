app.controller('profileCtrl', function (user, $scope, $rootScope, $http) {
    $('#page-wrapper').removeClass('nav-small');

    console.log('profileCtrl');
    console.log(JSON.stringify(user));

    const user_name = user.username;
    const user_id = user.id;

    $scope.role_name = '';
    console.log('Role: ' + user.role);
    switch (parseInt(user.role)) {
        case USER_PARENT:
            $scope.role_name = 'Parent';
            break;
        case USER_TEAM_COACH:
            $scope.role_name = 'Coach';
            break;
        case USER_CLUB_MANAGER:
            $scope.role_name = 'Club Manager';
            break;
        case USER_FINANCE:
            $scope.role_name = 'Finance';
            break;
        case USER_SHIPPING:
            $scope.role_name = 'Shipping User';
            break;
        case USER_SUPERVISOR:
            $scope.role_name = 'Supervisor';
            break;
        case USER_MANAGERIAL_COACH:
            $scope.role_name = 'Managerial Coach';
            break;
        case USER_LEAGUE_ADMIN:
            $scope.role_name = 'League Admin';
            break;
        case USER_SUPER_ADMIN:
            $scope.role_name = 'Super Admin';
            break;
        case USER_GRASSROOTS_FINANCE:
            $scope.role_name = 'Grassroots Finance';
            break;
        case USER_SUPERVISOR_COACH:
            $scope.role_name = 'Supervisor Coach';
            break;
        default:
            $scope.role_name = 'Unknown';
            break;
    }

    setTimeout(
        function () {
            // set status 2FA
            $scope.isEnable2FA =
                user.two_factor_auth === '0' ? false : true;
            $('#switch2FA').prop('checked', $scope.isEnable2FA);

            // on/off 2FA
            $('#switch2FA').on('click', function () {
                $scope.isEnable2FA = $(this).prop('checked');
                if ($(this).prop('checked') == true) {
                    enable2FA();
                } else if ($(this).prop('checked') == false) {
                    disable2FA();
                }
            });

            function enable2FA() {
                $http({
                    method: 'POST',
                    url: SERVER_PATH + 'user/enable2FA',
                    data: 'user_id=' + user_id,
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'x-user-id': user_id,
                        'x-user-email': user_name,
                    },
                }).success(function (response) {
                    if (response.status === 'OK') {
                        verify2FA(
                            response.info.qrcode_url,
                            response.info.secret_key
                        );
                    } else {
                        BootstrapDialog.show({
                            title: 'ERROR',
                            onhide: function (dialogRef) {
                                $('#switch2FA').prop(
                                    'checked',
                                    !$scope.isEnable2FA
                                );
                            },
                            type: BootstrapDialog.TYPE_WARNING,
                            message: response.message,
                        });
                    }
                });
            }

            function verify2FA(qrcode_url, secret_key) {
                var $textAndPic = $('<div class="container"></div>');

                var $textQRCode = $('<div class="row"></div>');
                var $textRow1Column1 = $('<div class="col-md-4"></div>');
                $textRow1Column1.append('QR Code: ');
                $textQRCode.append($textRow1Column1);
                var $textRow1Column2 = $('<div class="col-md-8"></div>');
                $textRow1Column2.append('<img src="' + qrcode_url + '" />');
                $textQRCode.append($textRow1Column2);

                var $textSecretKey = $('<div class="row"></div>');
                var $textRow2Column1 = $('<div class="col-md-4"></div>');
                $textRow2Column1.append('Secret Key: ');
                $textSecretKey.append($textRow2Column1);
                var $textRow2Column2 = $('<div class="col-md-8"></div>');
                $textRow2Column2.append(secret_key);
                $textSecretKey.append($textRow2Column2);

                $textAndPic.append($textQRCode);
                $textAndPic.append('<br />');
                $textAndPic.append($textSecretKey);
                $textAndPic.append('<hr />');
                $textAndPic.append(
                    'Please install <b>Google Authenticatior</b> in your phone, open it and then scan the bar code or type the above secret key to add this application. After you have added this application enter the code you see in the Google Authenticator App into the below input box to complete login process. <br />'
                );
                $textAndPic.append('<br />');
                $textAndPic.append(
                    '<input type="text" id="txtCode" placeholder="Enter Code" class="form-control">'
                );

                BootstrapDialog.show({
                    title: 'Two Factor Authentication',
                    message: $textAndPic,
                    onshown: function (dialog) {
                        // Get the input field
                        var input = document.getElementById('txtCode');

                        // Execute a function when the user releases a key on the keyboard
                        input.addEventListener('keyup', function (event) {
                            // Number 13 is the "Enter" key on the keyboard
                            if (event.keyCode === 13) {
                                // Cancel the default action, if needed
                                event.preventDefault();
                                // Trigger the button element with a click
                                document.getElementById('btnSubmit').click();
                            }
                        });
                    },
                    onhide: function (dialogRef) {
                        if (user.two_factor_auth === '0') {
                            $('#switch2FA').prop('checked', false);
                        }
                    },
                    closable: true,
                    closeByBackdrop: false,
                    closeByKeyboard: false,
                    buttons: [
                        {
                            label: 'OK',
                            id: 'btnSubmit',
                            action: function (dialogRef) {
                                $scope.one_code = dialogRef
                                    .getModalBody()
                                    .find('input')
                                    .val();
                                $http({
                                    method: 'POST',
                                    url: SERVER_PATH + 'user/verify2FA',
                                    data:
                                        'user_id=' +
                                        user_id +
                                        '&one_code=' +
                                        $scope.one_code,
                                    headers: {
                                        'Content-Type':
                                            'application/x-www-form-urlencoded',
                                        'x-user-id': user_id,
                                        'x-user-email': user_name,
                                    },
                                }).success(function (response) {
                                    console.log(response);
                                    if (response.status === 'OK') {
                                        BootstrapDialog.show({
                                            title: 'OK',
                                            type: BootstrapDialog.TYPE_SUCCESS,
                                            message: response.message,
                                        });
                                        user.two_factor_auth = '1';
                                        dialogRef.close();
                                    } else {
                                        BootstrapDialog.show({
                                            title: 'ERROR',
                                            type: BootstrapDialog.TYPE_WARNING,
                                            message: response.message,
                                        });
                                    }
                                });
                            },
                        },
                    ],
                });
            }

            function disable2FA() {
                $http({
                    method: 'POST',
                    url: SERVER_PATH + 'user/disable2FA',
                    data: 'user_id=' + user_id,
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'x-user-id': user_id,
                        'x-user-email': user_name,
                    },
                }).success(function (response) {
                    console.log(response);
                    if (response.status === 'OK') {
                        BootstrapDialog.show({
                            title: 'OK',
                            type: BootstrapDialog.TYPE_SUCCESS,
                            message: response.message,
                        });
                    } else {
                        BootstrapDialog.show({
                            title: 'ERROR',
                            onhide: function (dialogRef) {
                                $('#switch2FA').prop(
                                    'checked',
                                    !$scope.isEnable2FA
                                );
                            },
                            type: BootstrapDialog.TYPE_WARNING,
                            message: response.message,
                        });
                    }
                });
            }

            $scope.passwordData = {};
            $scope.password = false;
            $scope.password = function (form) {
                if (
                    typeof $scope.passwordData.old_password === 'undefined' ||
                    $scope.passwordData.old_password === ''
                ) {
                    BootstrapDialog.show({
                        title: 'ERROR',
                        type: BootstrapDialog.TYPE_WARNING,
                        message: 'Old password can not be empty!',
                    });
                } else if (
                    typeof $scope.passwordData.new_password === 'undefined' ||
                    $scope.passwordData.new_password === ''
                ) {
                    BootstrapDialog.show({
                        title: 'ERROR',
                        type: BootstrapDialog.TYPE_WARNING,
                        message: 'New password can not be empty!',
                    });
                } else if (
                    typeof $scope.passwordData.rnew_password === 'undefined' ||
                    $scope.passwordData.rnew_password === ''
                ) {
                    BootstrapDialog.show({
                        title: 'ERROR',
                        type: BootstrapDialog.TYPE_WARNING,
                        message: 'New password must be retyped!',
                    });
                } else {
                    $http({
                        method: 'POST',
                        url: SERVER_PATH + 'user/changePassword',
                        data:
                            $.param($scope.passwordData) +
                            '&id=' +
                            user_id,
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                            'x-user-id': user_id,
                            'x-user-email': user_name,
                        },
                    }).success(function (response) {
                        console.log('status: ' + response.status);
                        if (response.status === 'OK') {
                            BootstrapDialog.show({
                                title: 'Information',
                                type: BootstrapDialog.TYPE_SUCCESS,
                                message: response.message,
                                onshow: function (dialogRef) {
                                    $('#btnClose').trigger('click');
                                },
                            });
                        } else {
                            BootstrapDialog.show({
                                title: 'ERROR',
                                type: BootstrapDialog.TYPE_WARNING,
                                message: response.message,
                            });
                        }
                    });
                }
            };
        },
        user == null ? 1000 : 0
    );
});
