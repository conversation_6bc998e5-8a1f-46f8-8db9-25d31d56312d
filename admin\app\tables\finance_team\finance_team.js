app.controller('financeTeamCtrl', function ($scope, $rootScope, $http, localStorageService,$routeParams,$location ) {
    var table;
    $scope.init = function () {
        var editor = new $.fn.dataTable.Editor({
            ajax: {
                type: 'POST',
                url: SERVER_PATH + "finance/setFinance",
                headers: {	
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name
                },
                data: {},
                dataType: 'json',
                complete: function (response) {
                    var jsonData = JSON.parse(response.responseText);
                    // --- may need to reload
                    if (DEVELOPMENT_ENVIRONMENT) console.log('status = ' + jsonData.status);
                    if (jsonData.status == 'OK') {
                        if (DEVELOPMENT_ENVIRONMENT) console.log('Before reload');
                        table.ajax.reload();
                    }
                },
                error: function (xhr, status, error) {
                },
            },
            table: "#finance_table",
            formOptions: {
                main: {
                    onBlur: 'none'
                }
            },
            i18n: {
                create: {
                    button: "New",
                    title: "Create new finance employee",
                    submit: "Create"
                },
                edit: {
                    button: "Edit",
                    title: "Edit finance employee",
                    submit: "Save"
                },
                remove: {
                    button: "Delete",
                    title: "Delete finance employee",
                    submit: "Delete",
                    confirm: {
                        _: "Are you sure you want to delete these finance employees?",
                        1: "Are you sure you want to delete this finance employee?"
                    }
                },
                error: {
                    system: "System error, please contact finance employee."
                },
            },
            fields: [
                {
                    label: "Surname:",
                    name: "parens.surname"
                }, {
                    label: "Other name",
                    name: "parens.other_name"
                }, {
                    label: "Email",
                    name: "parens.email"
                }, {
                    name: "parens.type",
                    type: "hidden",
                    def: TYPE_FINANCE
                },
                {
                    label: "Phone number",
                    name: "parens.phone",
                    type: "telephone",
                    opts: {
                        preferredCountries: ['hk', 'cn'],
                        initialCountry: 'hk'
                    }
                },
                {
                    label: "Create user:",
                    name: "create_user",
                    type: "checkbox",
                    separator: "|",
                    options: [
                        { label: '', value: 1 }
                    ]
                }
            ]
        });

        // hide the create user checkbox for edit
        editor.on('open', function (e, mode, action) {
            if (action == 'edit') {
                editor.field('create_user').hide();
            } else {
                editor.field('create_user').show();
            }
        });

        table= $('#finance_table').DataTable({
            dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
            stateSave: true,
            deferRender: true,
            ajax: {
                url : SERVER_PATH + 'finance/getFinance',
                type : 'POST',
                headers: {	
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name
                },
                data:{},
                dataType: 'json',
                complete: function (response) {
                },
                error: function (xhr, status, error) {
                }
            },
            language:{
                info: "Showing _START_ to _END_ of _TOTAL_ finances",
                infoEmpty: "Showing 0 to 0 of 0 finances",
                lengthMenu: "Show _MENU_ finances",
                select: {
                    rows: {
                        "_": "You have selected %d finances",
                        "0": "Click an finance to select",
                        "1": "1 finance selected"
                    },
                },
                paginate: {
                    previous: '<i class="fa fa-chevron-left"></i>',
                    next: '<i class="fa fa-chevron-right"></i>'
                }
            },
            columns: [
                { data: "parens.surname" },
                { data: "parens.other_name" },
                { data: "parens.email" },
                { data: "parens.phone", className: "center" }
            ],
            select: {
                style: 'single',
                selector: 'td:first-child',
            },
            order: [[1, 'asc']],
            lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "All"]],
            buttons: [
                { extend: "create", editor: editor },
                { extend: "edit", editor: editor },
                { extend: "remove", editor: editor },
            ]
        });
    }
});