apply plugin: 'com.android.application'

android {
    namespace "com.ezactive.hkfa"
    compileSdk rootProject.ext.compileSdkVersion
    defaultConfig {
        applicationId "com.ezactive.hkfa"
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
        versionCode 120011
        versionName "1.20.1"
        testInstrumentationRunner "androidxss.test.runner.AndroidJUnitRunner"
    }
    signingConfigs {
        release {
            storeFile file("../../build/android/hkfa.jks")
            storePassword "hkfa@2021"
            keyAlias "hkfa"
            keyPassword "hkfa@2021"
        }
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.release
        }
    }
}

repositories {
    flatDir{
        dirs '../capacitor-cordova-android-plugins/src/main/libs', 'libs'
    }
}

dependencies {
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    implementation 'com.github.chrisbanes:PhotoView:v1.2.4'
    implementation "androidx.appcompat:appcompat:$androidxAppCompatVersion"
    implementation project(':capacitor-android')
    testImplementation "junit:junit:$junitVersion"
    androidTestImplementation "androidx.test.ext:junit:$androidxJunitVersion"
    androidTestImplementation "androidx.test.espresso:espresso-core:$androidxEspressoCoreVersion"
    implementation project(':capacitor-cordova-android-plugins')
    implementation 'androidx.work:work-runtime:2.7.0-alpha05'
}

apply from: 'capacitor.build.gradle'

try {
    def servicesJSON = file('google-services.json')
    if (servicesJSON.text) {
        apply plugin: 'com.google.gms.google-services'
    }
} catch(Exception e) {
    logger.warn("google-services.json not found, google-services plugin not applied. Push Notifications won't work")
}