<div class="row" id="tab-page">
    <div class="row">
        <div class="col-lg-12">
            <ol class="breadcrumb">
                <li>Events</li>
                <li><a href="" ng-click="goBack()">Golden Age</a></li>
                <li>{{event_name}}</li>
                <li class="active"><span>Team Sheet</span></li>
            </ol>
        </div>
    </div>
</div>
<div class="row">
	<div class="col-lg-12">
		<div class="clearfix">
			<h1 class="pull-left">Team Sheet</h1>
			<hr>
		</div>
		<div>
			<h3 ng-if="showSelectGroups" style="margin-top: 0px !important;"><span>Select a league group</span></h3>
			<select style="width:300px" id="sel2" ng-model="selectedGroup" ng-options="group.name for group in groups">
			</select>
			<button type="button" class="btn btn-primary">Select</button>
			<hr>
		</div>
	</div>
</div>

<div id="teamSheetPageContent"></div>

<!-- this page specific scripts -->
<script type="text/javascript">
	$('select').select2();
</script>

<style>
	.onoffswitch {
		position: relative; width: 90px;
		-webkit-user-select:none; -moz-user-select:none; -ms-user-select: none;
	}
	.onoffswitch-checkbox {
		position: absolute;
		opacity: 0;
		pointer-events: none;
	}
	.onoffswitch-label {
		display: block; overflow: hidden; cursor: pointer;
		border: 2px solid #999999; border-radius: 20px;
	}
	.onoffswitch-inner {
		display: block; width: 200%; margin-left: -100%;
		transition: margin 0.3s ease-in 0s;
	}
	.onoffswitch-inner:before, .onoffswitch-inner:after {
		display: block; float: left; width: 50%; height: 30px; padding: 0; line-height: 30px;
		font-size: 14px; color: white; font-family: Trebuchet, Arial, sans-serif; font-weight: bold;
		box-sizing: border-box;
	}
	.onoffswitch-inner:before {
		content: "Lock";
		padding-left: 10px;
		background-color: #ed1c24; color: #FFFFFF;
	}
	.onoffswitch-inner:after {
		content: "Unlock";
		padding-right: 10px;
		background-color: #EEEEEE; color: #999999;
		text-align: right;
	}
	.onoffswitch-switch {
		display: block; width: 18px; margin: 6px;
		background: #FFFFFF;
		position: absolute; top: 0; bottom: 0;
		right: 56px;
		border: 2px solid #999999; border-radius: 20px;
		transition: all 0.3s ease-in 0s; 
	}
	.onoffswitch-checkbox:checked + .onoffswitch-label .onoffswitch-inner {
		margin-left: 0;
	}
	.onoffswitch-checkbox:checked + .onoffswitch-label .onoffswitch-switch {
		right: 0px; 
	}
</style>