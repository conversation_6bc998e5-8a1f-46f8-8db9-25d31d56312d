<div class="container">
  <div class="row">
    <div class="col-lg-12">
      <ol class="breadcrumb">
        <li>Events</li>
        <li class="active"><span>PL Junior</span></li>
      </ol>

      <h1>PL Junior</h1>
    </div>
  </div>

  <div style="display: flex; justify-content: space-between; width: 100%; margin-bottom: 20px;">
    <div style="width: 25%; display: flex; align-items: center; gap: 8px;">
      <label style="white-space: nowrap; margin-bottom: 0;">Select Season:</label>
      <select class="form-control" ng-model="selectedSeasonId" ng-change="filterEventsBySeason()" style="flex: 1;">
        <option value="">-- All Seasons --</option>
        <option ng-repeat="season in seasons" value="{{season.id}}">
          {{season.name}}
        </option>
      </select>
    </div>

    <div style="width: 25%; display: flex; align-items: center; gap: 8px;">
      <label style="white-space: nowrap; margin-bottom: 0;">Search Event:</label>
      <input type="text" class="form-control" placeholder="Enter event name..." ng-model="searchText" style="flex: 1;">
    </div>
  </div>

  <div class="row" ng-if="events.length === 0" style="margin-left: 2px; margin-right: 2px;">
    <div class="col-12">
      <div class="main-box clearfix">
        <div class="main-box-body clearfix d-flex justify-content-center align-items-center" style="padding: 20px 30px; min-height: 72px;">
          <p style="margin: 0; font-size: 14px; text-align: center; color: #8B8B8B;">
            There are no events in the selected season.
          </p>
        </div>
      </div>
    </div>
  </div>

  <div class="row" ng-if="events.length > 0" style="margin-left: 2px; margin-right: 2px;">
    <div class="col-12" ng-repeat="event in events | filter :{name: searchText}">
      <div class="main-box clearfix" style="padding-top: 12px;">
        <header class="main-box-header clearfix" style="padding: 10px 30px;">
          <div class="row">
            <div class="col-xs-11">
              <h2 style="margin: 0;">{{event.name}}</h2>
            </div>
            <div class="col-xs-1 text-right">
              <span class="label"
                    ng-class="{
                      'label-warning': event.status === 'Upcoming',
                      'label-success': event.status === 'Active',
                      'label-primary': event.status === 'Completed'
                    }"
                    ng-if="event.status"
                    style="padding: 6px 12px; font-size: 13px;">
                {{event.status}}
              </span>
            </div>
          </div>
        </header>
        <h4 style="margin: 0; padding: 10px 30px;">Quick Access</h4>
        <div class="main-box-body clearfix">
          <div class="row text-center"></div>
          <div class="col-xs-12 col-sm-6 col-md-3" ng-repeat="item in items track by $index">
            <a ng-href="{{item.href.replace('{event.id}', event.id)}}" style="text-decoration:none; color:inherit;">
              <div class="panel panel-default item-panel-hover" style="border-radius:4px; padding:4px 0; transition: box-shadow 0.2s, border-color 0.2s;background-color: #F5F5F5;">
                <div class="panel-body d-flex align-items-center gap-2" style="padding:18px; ">
                  <!-- <i class="fa" ng-class="item.icon" style="font-size:1em; color: #f83a42;"></i> -->
                  <img src="{{item.image}}" class="sidemenu-icon" style="width: 30px; height: 30px;">
                  <span style="font-size:1em; margin-left: 4px;">{{item.name}}</span>
                </div>
              </div>
            </a>
          </div>
          <style>
            .item-panel-hover:hover {
              box-shadow: 0 4px 16px rgba(0,0,0,0.12);
              border-color: #ed1c24;
              cursor: pointer;
            }
          </style>
        </div>
      </div>
    </div>
  </div>
</div>