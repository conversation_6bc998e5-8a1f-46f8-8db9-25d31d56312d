/* mobile screen */

    body {
        min-height: 100vh;
        max-height: 100vh;
        min-width: 100vw;
        padding: 2em 0 5em 0;
        background: url(img/In\ queue.png);
        background-position: center;
        background-size: cover;
        background-repeat: no-repeat;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: center;

    }

    .main {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        gap: 1em
    }

    .main .please-wait {
        color: #000;
        font-family: Poppins;
        font-size: calc(1em + 1vw);
        font-style: normal;
        font-weight: 500;
    }

    .main .number-ahead {
        color: var(--Secondary-Color, #F83A42);
        text-align: center;
        font-family: Poppins;
        font-size: calc(1em + 1vw);
        font-style: normal;
        font-weight: 500;
        line-height: normal;
    }

    .main .timer {
        color: var(--Secondary-Color, #F83A42);
        font-family: Poppins;
        font-size: calc(3em + 1vw);
        font-style: normal;
        font-weight: 600;
        line-height: normal;
        
    }


    .main img {
        width: calc(80% - 2em);
        height: calc(80% - 2em);
    }
