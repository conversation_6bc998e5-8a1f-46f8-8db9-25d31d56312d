# build and deploy client to firebase hosting when PR is opened from release branch to main branch

name: Deploy to Firebase Hosting on PR
on:
  pull_request:
    branches:
      - main
    types: [opened]
jobs:
  build_and_preview:
    if: '${{ github.event.pull_request.head.repo.full_name == github.repository }}'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup node 14
        uses: actions/setup-node@v1
        with:
          node-version: 14.x
      - run: npm i -g @ionic/cli
      - run: cd client && npm ci && npm run build-staging
      - uses: FirebaseExtended/action-hosting-deploy@v0
        with:
          repoToken: '${{ secrets.GITHUB_TOKEN }}'
          firebaseServiceAccount: '${{ secrets.FIREBASE_SERVICE_ACCOUNT_HKFA_GRASSROOTS }}'
          projectId: hkfa-grassroots
