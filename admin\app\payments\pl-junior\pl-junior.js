const renderRefundModal = (data, rowGroup, invoice_id) => {
    $('#refundForm').attr('data-invoice-id', invoice_id);

    let refundHtml = '';

    rowGroup.forEach((row) => {
        refundHtml += `<label class="form-label fw-bold" style="color: #6F6F6F;font-weight: bold; font-size:13px">${row}</label>`;
        rowGroupData = data.filter((item) => item.rowGroup === row);

        rowGroupData.forEach((item) => {
            const type = item.type.replace('_', ' ');

            const { metadata } = item;

            let refundText = '';

            if (metadata) {
                switch (metadata.status) {
                    case 'RequestRefund':
                        refundText = 'Request Refund';
                        break;
                    case 'PartialRefund':
                        refundText = 'Partial Refund';
                        break;
                    case 'Refunded':
                        refundText = 'Refunded';
                        break;
                    case 'PartialRefunded':
                        refundText = 'Refunded';
                        break;
                    default:
                        refundText = '';
                        break;
                }

                refundText = `(${refundText} by ${metadata.refund_by})`;
            }

            refundHtml +=
                `
                                    <div class="form-check" style="display: flex; gap: 12px; margin-top:5px; align-items: flex-start;">
                                        <input  data-type='${type}' class="form-check-input" type="checkbox" id="${item.id}" value="${item.amount}" name="refundUser"` +
                (item.can_refund ? '' : 'disabled') +
                `>
                         <div style="display: flex; justify-content: space-between; align-items: center; flex: 1;">
                                        <div style="display: flex; flex-direction: column;">
                                            <label  style="font-weight: 600" class="form-check-label" for="${item.id}">${item.player_name} ${refundText}</label>
                                            <span style="font-size: 12px; font-style: normal; font-weight: 400; line-height: 18px;color: #6F6F6F">Player</span>
                                        </div>
                                        <input type="hidden" name="type" value="${item.amount}" data-id="${item.id}" data-type="${type}">

                                        <span style="font-weight: 500">${item.amount}$</span>
                                    </div>
                                    </div>`;
        });
    });

    return refundHtml;
};

const getPaymentDetailForRefund = async (id) => {
    return new Promise((resolve, reject) => {
        $.ajax({
            url: SERVER_PATH + 'payment/getDataForRefundPLJ',
            type: 'POST',
            data: {
                invoice_id: id,
            },
            dataType: 'json',
            beforeSend: function () {
                Swal.fire({
                    title: 'Loading...',
                    allowOutsideClick: false,
                    onBeforeOpen: () => {
                        Swal.showLoading();
                    },
                });
            },
            success: function (response) {
                Swal.close();
                const { status, message, data } = response;
                if (status === 'OK') {
                    resolve(data);
                } else {
                    Swal.fire({
                        title: 'Error',
                        text: message,
                        icon: 'error',
                        showCancelButton: false,
                        confirmButtonClass: 'btn-danger',
                        confirmButtonText: 'OK',
                        closeOnConfirm: false,
                    });

                    reject(message);
                }
            },
            error: function (xhr, status, error) {
                Swal.close();
                reject(error);
            },
        });
    });
};

const refundDetail = (...props) => {
    return new Promise((resolve, reject) => {
        const [data, payRef, user_id, reason] = props;

        $.ajax({
            url: SERVER_PATH + 'payment/partialRefundPLJ',
            type: 'POST',
            data: {
                details: JSON.stringify(data),
                payRef: payRef,
                user_id: user_id,
                reason: reason,
            },
            dataType: 'json',
            beforeSend: function () {
                Swal.fire({
                    title: 'Processing...',
                    allowOutsideClick: false,
                    onBeforeOpen: () => {
                        Swal.showLoading();
                    },
                });
            },
            success: function (response) {
                Swal.close();

                const { status, message, invoice_details } = response;

                if (status == 'OK') {
                    Swal.fire({
                        title: 'Success',
                        text: message,
                        icon: 'success',
                        showCancelButton: false,
                        confirmButtonClass: 'btn-success',
                        confirmButtonText: 'OK',
                        closeOnConfirm: false,
                    }).then((result) => {
                        resolve(invoice_details);
                    });
                } else {
                    BootstrapDialog.show({
                        title: 'ERROR',
                        type: BootstrapDialog.TYPE_DANGER,
                        message: message,
                    });
                    reject(message);
                }
            },
            error: function (xhr, status, error) {
                Swal.close();
                console.error('Error refunding:', error);
                reject(error);
            },
        });
    });
};

app.controller(
    'paymentsPLJuniorCtrl',
    function ($scope, $rootScope, $routeParams, $http) {
        var event_id = $routeParams.id;
        $scope.event_id = event_id;

        GLOBAL_SCOPE = $scope;

        jQuery.ajax({
            type: 'POST',
            url: SERVER_PATH + 'event/getEventInfo',
            async: false,
            headers: {
                'x-user-id': $rootScope.user_id,
                'x-user-email': $rootScope.user_name,
            },
            data: {
                event_id: event_id,
            },
            dataType: 'json',
            complete: function (response) {
                var jsonData = JSON.parse(response.responseText);
                var event = jsonData.info;
                $scope.event_name = event.name;
                $scope.event_type = event.type;
                $scope.normalizedType = normalizeEventType(event.type);
            },
        });

        if ($.fn.daterangepicker) {
            $('input[name="dateFilter"]').daterangepicker('destroy');
        }

        $('input[name="dateFilter"]').daterangepicker({
            startDate: moment().startOf('month'),
            endDate: moment().endOf('month'),
            locale: {
                format: 'DD/MM/YYYY',
            },
            maxSpan: {
                days: 90,
            },
            ranges: {
                Yesterday: [
                    moment().subtract(1, 'days'),
                    moment().subtract(1, 'days'),
                ],
                Today: [moment(), moment()],
                'Last week': [
                    moment().subtract(1, 'week').startOf('week').add(1, 'days'),
                    moment().subtract(1, 'week').endOf('week').add(1, 'days'),
                ],
                'This week': [
                    // start week on monday
                    moment().startOf('week').add(1, 'days'),
                    moment().endOf('week').add(1, 'days'),
                ],
                'Last Month': [
                    moment().subtract(1, 'month').startOf('month'),
                    moment().subtract(1, 'month').endOf('month'),
                ],
                'This Month': [
                    moment().startOf('month'),
                    moment().endOf('month'),
                ],
            },
        });

        $('input[name="dateFilter"]').on(
            'apply.daterangepicker',
            function (ev, picker) {
                console.log('date change');
                $scope.start_date = picker.startDate.format('YYYY-MM-DD');
                $scope.end_date = picker.endDate.format('YYYY-MM-DD');

                initPaymentTable();
            }
        );

        // Date range picker
        $scope.start_date = $('input[name="dateFilter"]')
            .data('daterangepicker')
            .startDate.format('YYYY-MM-DD');
        $scope.end_date = $('input[name="dateFilter"]')
            .data('daterangepicker')
            .endDate.format('YYYY-MM-DD');

        setTimeout(() => {
            initPaymentTable();
        }, 400);

        function normalizeEventType(eventType) {
            switch (eventType) {
                case 'Summer Scheme':
                    return 'summer-scheme';
                case 'Regional':
                    return 'regional';
                case 'PL Junior':
                    return 'pl-junior';
                case 'Golden Age':
                    return 'golden-age';
                case 'Beginner':
                    return 'beginner';
                case 'District':
                    return 'district';
                default:
                    return '';
            }
        }

        initPaymentTable = function () {
            $('#paymentTable_' + event_id)
                .DataTable()
                .destroy();

            var table = $('#paymentTable_' + event_id).DataTable({
                dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
                // stateSave: true,
                deferRender: true,
                ajax: {
                    url: SERVER_PATH + 'payment/getPaymentsPLJunior',
                    type: 'POST',
                    headers: {
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name,
                    },
                    data: {
                        event_id: event_id,
                        start_date: $scope.start_date,
                        end_date: $scope.end_date,
                    },
                    dataType: 'json',
                    complete: function (response) {},
                    error: function (xhr, status, error) {},
                },
                language: {
                    info: 'Showing _START_ to _END_ of _TOTAL_ payment',
                    infoEmpty: 'Showing 0 to 0 of 0 payments',
                    lengthMenu: 'Show _MENU_ payments',
                    select: {
                        rows: {
                            _: 'You have selected %d payments',
                            0: 'Click a payment to select',
                            1: '1 payment selected',
                        },
                    },
                    paginate: {
                        previous: '<i class="fa fa-chevron-left"></i>',
                        next: '<i class="fa fa-chevron-right"></i>',
                    },
                },
                columns: [
                    {
                        data: null,
                        render: function (data, type, row) {
                            return (
                                data.players.surname +
                                ' ' +
                                data.players.other_name
                            );
                        },
                    },
                    {
                        data: 'players.chinese_name',
                    },
                    {
                        data: 'players.dob',
                    },
                    {
                        data: 'players.gender',
                    },
                    {
                        data: 'parens.phone',
                    },
                    {
                        data: 'parens.email',
                    },
                    {
                        data: 'courses.class_code',
                    },
                    {
                        data: 'invoices.invoice_number',
                        visible: false,
                    },
                    {
                        data: 'invoices.invoice_identification',
                    },
                    {
                        data: 'invoices.invoice_date',
                    },
                    {
                        data: 'invoices.amount',
                    },
                    {
                        data: 'invoices.status',
                        className: 'center',
                        render: function (data, type, full, meta) {
                            switch (data) {
                                case STATUS_SUCCEEDED:
                                    return (
                                        '<span class="label label-success">' +
                                        // get element
                                        DISTRICT_PAYMENT_STATUS[data] +
                                        '</span>'
                                    );
                                case STATUS_PROCESSING:
                                    return (
                                        '<span class="label label-danger">' +
                                        DISTRICT_PAYMENT_STATUS[data] +
                                        '</span>'
                                    );
                                case STATUS_WAITING_PAYMENT:
                                    return (
                                        '<span class="label label-warning">' +
                                        DISTRICT_PAYMENT_STATUS[data] +
                                        '</span>'
                                    );
                                case STATUS_OFFLINE_PAYMENT:
                                    return (
                                        '<span class="label label-info" data-toggle="tooltip" data-placement="top" title="' +
                                        full.invoices.accepted_detail +
                                        '">' +
                                        DISTRICT_PAYMENT_STATUS[data] +
                                        '</span>'
                                    );
                                case STATUS_REQUEST_PARTIAL_REFUND:
                                    return (
                                        '<span class="label label-warning">' +
                                        DISTRICT_PAYMENT_STATUS[data] +
                                        '</span>'
                                    );
                                case STATUS_REQUEST_REFUND:
                                    return (
                                        '<span class="label label-danger">' +
                                        DISTRICT_PAYMENT_STATUS[data] +
                                        '</span>'
                                    );
                                default:
                                    return (
                                        '<span class="label label-default">' +
                                        data +
                                        '</span>'
                                    );
                            }
                        },
                    },
                    {
                        data: 'invoices.payment_method',
                    },
                    {
                        data: null,
                        render: function (data, type, row) {
                            if (data.invoices.accepted_user)
                                return data.invoices.accepted_user;
                            else return '';
                        },
                    },
                    {
                        data: 'invoices.note',
                    },
                    {
                        data: 'invoices.id',
                        render: function (data, type, row) {
                            let payRef = row.invoices.invoice_identification;
                            const invoice_id = row.invoices.id;
                            // button show payment detail
                            let user_id = $rootScope.user_id;
                            var refundButton = `<button class="btn btn-info btn-sm" data-toggle="modal" data-target="#paymentDetailModal" onclick="showPaymentDetail(${invoice_id})">Refund</button>`;
                            var detailButton = `<button class="btn btn-primary btn-sm" data-toggle="modal" data-target="#paymentDetailModal" onclick="showPaymentItems(${invoice_id})">Detail</button>`;
                            return (
                                '<div style=" display: flex; align-content: center; align-items: center; gap: 10px; ">' +
                                detailButton +
                                refundButton +
                                '</div>'
                            );
                        },
                    },
                ],
                initComplete: function () {
                    var payment_status_column = {
                        orderColumn: 11,
                        elementId: 'payment_status-content',
                        selectId: 'selType',
                    };

                    var payment_method_column = {
                        orderColumn: 12,
                        elementId: 'payment_method-content',
                        selectId: 'selType',
                    };

                    filterColumns = [
                        payment_status_column,
                        payment_method_column,
                    ];

                    filterColumns.forEach((item, index) => {
                        this.api()
                            .columns(item.orderColumn)
                            .every(function () {
                                var column = this;

                                column.search('', true, false, false);

                                $(`#${item.elementId}`)
                                    .children()
                                    .not(':first')
                                    .remove();

                                var select = $(
                                    `<select id="${item.orderColumn}" class="form-control"><option value="">All</option></select>`
                                )
                                    .appendTo($(`#${item.elementId}`))
                                    .on('change', function () {
                                        var val =
                                            $.fn.dataTable.util.escapeRegex(
                                                // change '_' to ' '
                                                $(this).val()
                                            );

                                        column
                                            .search(
                                                val ? '^' + val + '$' : '',
                                                true,
                                                false,
                                                false
                                            )
                                            .draw(false);
                                    })
                                    .select2();
                                var column_data = column.data();
                                var select_data = [];
                                column_data.map((item) => {
                                    if (item != null) {
                                        item.indexOf(', ') > 0
                                            ? (item = item.split(', '))
                                            : (item = [item]);
                                        item.forEach((item) => {
                                            select_data.push(
                                                item.replace('_', ' ')
                                            );
                                        });
                                    }
                                });
                                select_data
                                    .filter(onlyUnique)
                                    .sort()
                                    .map(function (d, j) {
                                        if (index == 0) {
                                            select.append(
                                                `<option value="${DISTRICT_PAYMENT_STATUS[d]}">${DISTRICT_PAYMENT_STATUS[d]}</option>`
                                            );
                                        } else {
                                            select.append(
                                                // lower case and upper case first character
                                                `<option value="${d}">${d}</option>`
                                            );
                                        }
                                    });
                            });
                    });
                },
                select: {
                    style: SELECT_MODE,
                },
                order: [[0, 'asc']],
                lengthMenu: [
                    [10, 25, 50, 100, -1],
                    [10, 25, 50, 100, 'All'],
                ],
                buttons: [
                    {
                        extend: 'excel',
                        name: 'excel',
                        text: '<i class="fa fa-file-excel-o"></i>&emsp;Export to Excel',
                        titleAttr: 'Export data to an Excel file',
                        filename: `HKFA Grassroots - ${$scope.event_type} ${$scope.event_name} - Payments`,
                        exportOptions: {
                            columns: ':visible',
                            modifier: {
                                autoFilter: true,
                                // selected: true
                            },
                        },
                    },
                    // {
                    //     extend: 'selectedSingle',
                    //     text: 'Refund Payment',
                    //     action: async function () {
                    //         const row = table
                    //             .rows({
                    //                 selected: true,
                    //             })
                    //             .data()[0];

                    //         const { invoices } = row;

                    //         const { id } = invoices;

                    //         const data = await getPaymentDetailForRefund(id);

                    //         const rowGroup = [];

                    //         data.forEach((item) => {
                    //             if (rowGroup.indexOf(item.rowGroup) === -1) {
                    //                 rowGroup.push(item.rowGroup);
                    //             }
                    //         });

                    //         const tableHtml = renderRefundModal(
                    //             data,
                    //             rowGroup,
                    //             id
                    //         );

                    //         $('#userSelection').html(tableHtml);

                    //         $('#refundModal').modal('show');
                    //         //     title: 'Refund Details',
                    //         //     message: tableHtml,
                    //         //     size: BootstrapDialog.SIZE_WIDE,
                    //         //     // on shown
                    //         //     onshown: function (dialog) {
                    //         //         // add event listener on change all checkboxes .refund-checkbox
                    //         //     },
                    //         //     onhide: function (dialog) {},
                    //         //     buttons: [
                    //         //         {
                    //         //             label: 'Cancel',
                    //         //             action: function (dialog) {
                    //         //                 dialog.close();
                    //         //             },
                    //         //         },
                    //         //         {
                    //         //             label: 'Submit',
                    //         //             cssClass: 'btn-primary',
                    //         //             action: function (dialog) {
                    //         //                 let isValid = true;
                    //         //                 // Get all checkboxes and inputs
                    //         //                 const checkboxes =
                    //         //                     document.querySelectorAll(
                    //         //                         '.refund-checkbox'
                    //         //                     );
                    //         //                 const inputs =
                    //         //                     document.querySelectorAll(
                    //         //                         '.refund-input'
                    //         //                     );

                    //         //                 const invalidMsgs =
                    //         //                     document.querySelectorAll(
                    //         //                         '.invalid-message'
                    //         //                     );

                    //         //                 const reasonInput =
                    //         //                     document.querySelector(
                    //         //                         '.reason-input'
                    //         //                     );

                    //         //                 const reasonInputValue =
                    //         //                     reasonInput.value;

                    //         //                 checkboxes.forEach(
                    //         //                     (checkbox, index) => {
                    //         //                         const input = inputs[index];

                    //         //                         const invalidMessage =
                    //         //                             invalidMsgs[index];

                    //         //                         // Check if checkbox is checked
                    //         //                         if (checkbox.checked) {
                    //         //                             // Ensure the input is filled and within the valid range
                    //         //                             const value =
                    //         //                                 parseFloat(
                    //         //                                     input.value
                    //         //                                 );
                    //         //                             const max = parseFloat(
                    //         //                                 input.dataset.max
                    //         //                             );
                    //         //                             if (
                    //         //                                 isNaN(value) ||
                    //         //                                 value <= 0 ||
                    //         //                                 value > max
                    //         //                             ) {
                    //         //                                 isValid = false;
                    //         //                                 input.style.borderColor =
                    //         //                                     'red'; // Highlight invalid input
                    //         //                                 invalidMessage.style.display =
                    //         //                                     'block'; // Show invalid message
                    //         //                             } else {
                    //         //                                 input.style.borderColor =
                    //         //                                     ''; // Reset if valid
                    //         //                                 invalidMessage.style.display =
                    //         //                                     'none'; // Hide invalid message
                    //         //                             }
                    //         //                         }
                    //         //                     }
                    //         //                 );

                    //         //                 if (!isValid) {
                    //         //                     alert(
                    //         //                         'Please fill in valid refund amounts for checked items.'
                    //         //                     );
                    //         //                 } else {
                    //         //                     // check if any checkbox is checked
                    //         //                     if (
                    //         //                         !Array.from(
                    //         //                             checkboxes
                    //         //                         ).some(
                    //         //                             (checkbox) =>
                    //         //                                 checkbox.checked
                    //         //                         )
                    //         //                     ) {
                    //         //                         alert(
                    //         //                             'Please select at least one item to refund.'
                    //         //                         );
                    //         //                         return;
                    //         //                     }

                    //         //                     const data = [];

                    //         //                     checkboxes.forEach(
                    //         //                         (checkbox, index) => {
                    //         //                             if (checkbox.checked) {
                    //         //                                 const input =
                    //         //                                     inputs[index];

                    //         //                                 const amount =
                    //         //                                     parseFloat(
                    //         //                                         input.value
                    //         //                                     );

                    //         //                                 const id =
                    //         //                                     input.dataset
                    //         //                                         .id;

                    //         //                                 data.push({
                    //         //                                     id,
                    //         //                                     amount,
                    //         //                                 });
                    //         //                             }
                    //         //                         }
                    //         //                     );

                    //         //                     const payRef =
                    //         //                         invoices.invoice_identification;

                    //         //                     const user_id =
                    //         //                         $rootScope.user_id;

                    //         //                     refundDetail(
                    //         //                         data,
                    //         //                         payRef,
                    //         //                         user_id,
                    //         //                         reasonInputValue
                    //         //                     )
                    //         //                         .then((invoice_details) => {
                    //         //                             table.ajax.reload();

                    //         //                             const refund_detail_ids =
                    //         //                                 invoice_details.map(
                    //         //                                     (item) =>
                    //         //                                         item.id
                    //         //                                 );

                    //         //                             const refund_detail_ids_str =
                    //         //                                 refund_detail_ids.join(
                    //         //                                     ','
                    //         //                                 );

                    //         //                             Swal.fire({
                    //         //                                 title: 'Cancel Registration',
                    //         //                                 input: 'radio',
                    //         //                                 inputOptions: {
                    //         //                                     yes: 'Yes',
                    //         //                                     no: 'No',
                    //         //                                 },
                    //         //                                 inputValidator: (
                    //         //                                     value
                    //         //                                 ) => {
                    //         //                                     if (!value) {
                    //         //                                         return 'You need to choose something!';
                    //         //                                     }
                    //         //                                 },
                    //         //                             }).then((result) => {
                    //         //                                 if (
                    //         //                                     result.isConfirmed
                    //         //                                 ) {
                    //         //                                     jQuery.ajax({
                    //         //                                         type: 'POST',
                    //         //                                         url:
                    //         //                                             SERVER_PATH +
                    //         //                                             'payment/autoRegistrationForPlj',
                    //         //                                         async: false,
                    //         //                                         data: {
                    //         //                                             user_id:
                    //         //                                                 $rootScope.user_id,
                    //         //                                             reason: reasonInputValue,
                    //         //                                             invoice_details_ids:
                    //         //                                                 refund_detail_ids_str,
                    //         //                                         },
                    //         //                                         headers: {
                    //         //                                             'x-user-id':
                    //         //                                                 $rootScope.user_id,
                    //         //                                             'x-user-email':
                    //         //                                                 $rootScope.user_name,
                    //         //                                         },
                    //         //                                         dataType: 'json',
                    //         //                                         complete: function (response) {
                    //         //                                             console.warn(response);
                    //         //                                         }
                    //         //                                     });
                    //         //                                 }
                    //         //                             });
                    //         //                         })
                    //         //                         .catch((error) => {
                    //         //                             console.error(
                    //         //                                 'Error refunding:',
                    //         //                                 error
                    //         //                             );
                    //         //                         });

                    //         //                     dialog.close();
                    //         //                 }
                    //         //             },
                    //         //         },
                    //         //     ],
                    //         // });
                    //     },
                    // },
                    {
                        extend: 'selected',
                        text: 'Approve Payment',
                        attr: {
                            id: 'btnApprovePayment',
                        },
                        action: function () {
                            // get selected row data
                            var data = table
                                .rows({
                                    selected: true,
                                })
                                .data();

                            array_data_id = [];

                            for (var i = 0; i < data.length; i++) {
                                array_data_id.push(data[i].invoices.id);
                            }

                            // get selected row id
                            var invoice_ids = array_data_id.join(',');

                            //show editor for enter note
                            Swal.fire({
                                title: 'Enter note',
                                input: 'textarea',
                                inputPlaceholder: 'Enter note',
                                showCancelButton: true,
                                inputValidator: (value) => {
                                    if (!value) {
                                        return 'You need to write something!';
                                    }
                                },
                            }).then((result) => {
                                if (result.value) {
                                    //call ajax to update invoice status
                                    jQuery.ajax({
                                        type: 'POST',
                                        url:
                                            SERVER_PATH +
                                            'payment/approveOfflinePayment',
                                        async: false,
                                        headers: {
                                            'x-user-id': $rootScope.user_id,
                                            'x-user-email':
                                                $rootScope.user_name,
                                        },
                                        data: {
                                            user_id: user_id,
                                            invoice_ids: invoice_ids,
                                            note: result.value,
                                        },
                                        dataType: 'json',
                                        complete: function (response) {
                                            var jsonData = JSON.parse(
                                                response.responseText
                                            );
                                            if (jsonData.status == 'OK') {
                                                Swal.fire({
                                                    title: 'Success',
                                                    text: 'Payment approved successfully',
                                                    icon: 'success',
                                                    showCancelButton: false,
                                                    confirmButtonClass:
                                                        'btn-success',
                                                    confirmButtonText: 'OK',
                                                    closeOnConfirm: false,
                                                });
                                                table.ajax.reload();
                                            } else {
                                                Swal.fire({
                                                    title: 'Error',
                                                    text: jsonData.message,
                                                    icon: 'error',
                                                    showCancelButton: false,
                                                    confirmButtonClass:
                                                        'btn-danger',
                                                    confirmButtonText: 'OK',
                                                    closeOnConfirm: false,
                                                });
                                            }
                                        },
                                    });
                                }
                            });
                        },
                    },
                    {
                        extend: 'selected',
                        extend: 'selectedSingle',
                        text: 'Update Payment Method',
                        attr: {
                            id: 'btnUpdatePaymentMethod',
                        },
                        action: function () {
                            // get selected row data
                            var data = table
                                .rows({
                                    selected: true,
                                })
                                .data()[0];

                            if (
                                data['invoices']['status'] ==
                                    STATUS_WAITING_PAYMENT ||
                                data['invoices']['status'] ==
                                    STATUS_PROCESSING ||
                                data['invoices']['status'] ==
                                    STATUS_OFFLINE_REJECTED
                            ) {
                                var editor = new $.fn.dataTable.Editor({
                                    ajax: {
                                        type: 'POST',
                                        url:
                                            SERVER_PATH +
                                            'payment/updatePaymentMethod',
                                        dataType: 'json',
                                        headers: {
                                            'x-user-id': $rootScope.user_id,
                                            'x-user-email':
                                                $rootScope.user_name,
                                        },
                                        data: {
                                            invoice_id: data['invoices']['id'],
                                        },
                                        beforeSend: function () {},
                                        complete: function (response) {
                                            if (
                                                response.responseJSON.status ==
                                                'OK'
                                            ) {
                                                Swal.fire({
                                                    title: 'SUCCESS!',
                                                    text: response.responseJSON
                                                        .message,
                                                    icon: 'success',
                                                });
                                            } else {
                                                Swal.fire({
                                                    title: 'ERROR!',
                                                    text: response.responseJSON
                                                        .message,
                                                    icon: 'error',
                                                });
                                            }

                                            table.ajax.reload();
                                        },
                                        error: function (xhr, status, error) {},
                                    },
                                    formOptions: {
                                        main: {
                                            onBlur: 'none',
                                        },
                                    },
                                    fields: [
                                        {
                                            label: 'Payment Method:',
                                            type: 'select2',
                                            name: 'payment_method',
                                            options: [
                                                {
                                                    label: 'AsiaPay',
                                                    value: 'AsiaPay',
                                                },
                                                {
                                                    label: 'Offline Payment',
                                                    value: 'Offline Payment',
                                                },
                                            ],
                                            default:
                                                data['invoices'][
                                                    'payment_method'
                                                ],
                                        },
                                    ],
                                });

                                editor
                                    .title('Update Payment Method')
                                    .buttons({
                                        label: 'Submit',
                                        fn: function () {
                                            this.submit();
                                        },
                                    })
                                    .edit()
                                    .open();
                            } else {
                                Swal.fire({
                                    title: 'Error',
                                    text: 'Only Processing payment can be updated',
                                    icon: 'error',
                                    type: 'error',
                                    showCancelButton: false,
                                    confirmButtonClass: 'btn-danger',
                                    confirmButtonText: 'OK',
                                    closeOnConfirm: false,
                                });
                            }
                        },
                    },
                    {
                        extend: 'colvis',
                        text: 'Columns',
                    },
                ],
            });
        };

        function onlyUnique(value, index, self) {
            return self.indexOf(value) === index;
        }

        $scope.formData = {
            selectedUsers: [],
            reason: '',
            shippingFee: null,
            cancelRegistration: 'No',
        };

        $scope.validateForm = function () {
            let isValid = true;
            $scope.validationMessages = {
                selectedUsers: '',
                reason: '',
                shippingFee: '',
                cancelRegistration: '',
            };

            // Validate user selection
            const selectedUsers = angular.element(
                'input[name="refundUser"]:checked'
            );

            if (selectedUsers.length === 0) {
                isValid = false;
                $scope.validationMessages.selectedUsers =
                    'Please select at least one user.';
            }

            // Validate reason
            if (!$scope.formData.reason || !$scope.formData.reason.trim()) {
                isValid = false;
                $scope.validationMessages.reason =
                    'Please enter a reason for the refund.';
            }

            // Validate cancel registration
            if (!$scope.formData.cancelRegistration) {
                isValid = false;
                $scope.validationMessages.cancelRegistration =
                    'Please select whether to cancel registration.';
            }

            return isValid;
        };

        $scope.checkValidation = function (item) {
            if ($scope.validationMessages) {
                return $scope.validationMessages[item] != '';
            } else {
                return true;
            }
        };

        $scope.closeModal = function () {
            $('#refundModal').modal('hide');
        };

        $scope.closePaymentDetail = function () {
            $('#paymentDetail').modal('hide');
        };

        $scope.submitRefund = function () {
            if ($scope.validateForm()) {
                const data = [];

                $scope.formData.selectedUsers = [];
                $scope.formData.selectedUsers = [];
                angular
                    .element('input[name="refundUser"]:checked')
                    .each(function () {
                        // get input next to label
                        const nextElement = angular
                            .element(this)
                            .next()
                            .find('input');

                        const id = nextElement.attr('data-id');

                        const amount = parseFloat(nextElement.val());

                        data.push({
                            id,
                            amount,
                            type: nextElement.attr('data-type'),
                        });
                    });

                const isCheckCancelRegistration =
                    $scope.formData.cancelRegistration;

                const invoice_id = $('#refundForm').attr('data-invoice-id');

                const reason = $scope.formData.reason;

                const user_id = $rootScope.user_id;

                refundPaymentDetail(
                    data,
                    invoice_id,
                    user_id,
                    reason,
                    isCheckCancelRegistration,
                    event_id
                );
            }
        };

        showPaymentDetail = async function (invoice_id) {
            const data = await getPaymentDetailForRefund(invoice_id);

            console.warn(data);

            const rowGroup = [];

            data.forEach((item) => {
                if (rowGroup.indexOf(item.rowGroup) === -1) {
                    rowGroup.push(item.rowGroup);
                }
            });

            const tableHtml = renderRefundModal(data, rowGroup, invoice_id);

            $('#userSelection').html(tableHtml);

            // reset form
            $('#refundForm').trigger('reset');

            $scope.formData = {
                selectedUsers: [],
                reason: '',
                shippingFee: null,
                cancelRegistration: 'No',
            };

            $scope.validationMessages = {
                selectedUsers: '',
                reason: '',
                cancelRegistration: '',
            };

            $scope.$apply();

            $('#refundModal').modal('show');
        };

        showPaymentItems = async function (id) {
            async function getInvoiceDetail(id) {
                return new Promise((resolve, reject) => {
                    $.ajax({
                        url: SERVER_PATH + 'payment/getInvoiceDetailPLJ',
                        type: 'POST',
                        data: {
                            invoice_id: id,
                        },
                        dataType: 'json',
                        // show loading
                        beforeSend: function () {
                            Swal.fire({
                                title: 'Loading...',
                                allowOutsideClick: false,
                                onBeforeOpen: () => {
                                    Swal.showLoading();
                                },
                            });
                        },
                        success: function (response) {
                            Swal.close();
                            if (response.status == 'OK') {
                                const { data } = response;
                                resolve(data);
                            } else {
                                Swal.fire({
                                    title: 'Error!',
                                    text: response.message,
                                    icon: 'error',
                                    confirmButtonText: 'OK',
                                });
                                reject();
                            }
                        },
                        error: function (xhr, status, error) {
                            Swal.close();
                            console.error(
                                'Error fetching invoice details:',
                                error
                            );
                            reject();
                        },
                    });
                });
            }

            const response = await getInvoiceDetail(id);

            const { invoice_info, invoice_details, invoice_details_refunded } =
                response;

            // status color mapping
            const statusColor = {
                Accepted: '#28C76F',
                Pending: '#FF9F43',
                Rejected: '#EA5455',
                Refunded: '#FF9F43',
                RequestPartialRefund: '#9c27b0',
                RequestRefund: '#ed1c24',
            };

            invoiveInfoHtml =
                `
            <div class="invoice-detail-left" style="padding: 16px 20px; display: flex; flex-direction: column; flex: 0 0 250px; background-color: #F3F3F3; border-radius: 8px;">
        <div class="invoice-date" style="display: flex; flex-direction: column; gap: 8px;">
          <label style="font-size: 13px; font-weight: 400; color: #667085; font-family: 'Inter', serif;">Payment
            Date</label>
          <span style="font-size: 15px; font-weight: 600; color: #333843; font-family: 'Inter', serif;">` +
                invoice_info.formatted_date +
                `</span>
        </div>
        <div class="invoice-status" style="display: flex; flex-direction: column; gap: 8px; margin-top: 16px;">
          <label style="font-size: 13px; font-weight: 400; color: #667085; font-family: 'Inter', serif;">Payment
            Status</label>
          <span class="payment-status" style="background-color: ` +
                statusColor[invoice_info.status] +
                `; color: white; border-radius: 4px; display: inline-block; width: fit-content; padding: 5px 10px;">` +
                invoice_info.status +
                `</span>
        </div>
        <div class="invoice-id" style="display: flex; flex-direction: column; gap: 8px; margin-top: 16px;">
          <label style="font-size: 13px; font-weight: 400; color: #667085; font-family: 'Inter', serif;">Invoice
            ID</label>
          <span style="font-size: 12px; font-weight: 600; background-color: #F4F5F6; border-radius: 8px;">` +
                invoice_info.invoice_number +
                `</span>
        </div>
      </div>
      <div class="invoice-detail-right" style="padding: 21px 8px;display: flex;flex-direction: column;justify-content: center;gap: 8px;flex: 1;align-items: flex-start;width: 100%;">
        <span class="parent-name" style="font-size: 16px; font-weight: 600;">` +
                invoice_info.parent_name +
                `</span>
        <div class="parent-email" style="font-size: 13px; font-weight: 600; color: #667085; display: flex; gap: 4px; align-items: flex-end;">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6" style="width: 18px; height: 18px; stroke: #667085;">
            <path stroke-linecap="round" stroke-linejoin="round" d="M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75"></path>
          </svg>
          <span>` +
                invoice_info.email +
                `</span>
        </div>
        <div class="parent-phone" style="font-size: 13px;font-weight: 600;color: #667085;display: flex;gap: 4px;align-items: flex-end;width: 100%;">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6" style="width: 18px;height: 18px;stroke: #667085;flex: 0 0 18px;">
            <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z"></path>
          </svg>
          <span style="
  line-height: 1;
">` +
                invoice_info.parent_phone +
                `</span>
        </div>
      </div>`;

            let invoiceDetailHtml = ``;

            invoice_details.forEach((item) => {
                if (item.item_type == 'Course') {
                    let [course_name, player_name, itemType] = [];
                    if (item.item_name && item.item_name.includes(' - ')) {
                        [course_name, player_name, itemType] =
                            item.item_name.split(' - ');
                    } else {
                        course_name = item.item_name;
                        player_name = '';
                        itemType = item.item_type;
                    }
                    invoiceDetailHtml +=
                        `<div class="invoice-detail-item" style="font-size: 14px; position: relative;">
          <div class="course-name" style="padding: 12px 16px; color: #667085; background-color: #f4f4f4;"> ` +
                        course_name +
                        `
          </div>
          <div class="player-item" style="padding: 12px 16px; background-color: white; border-bottom: 0.5px solid #E0E2E7; position: relative;">
            <div class="player-name" style="font-size: 15px; font-weight: 500; color: #333843;">` +
                        player_name +
                        `</div>
            <div class="player-role" style="color: #667085; font-size: 14px; font-weight: 500; margin-top: 4px;">
              ` +
                        itemType +
                        `
            </div>
            <div class="player-price" style="position: absolute; right: 16px; top: 50%; transform: translateY(-50%); font-size: 15px; font-weight: 600; color: #333843;">
              ` +
                        item.price +
                        `$</div>
          </div>
        </div>`;
                }
            });
            invoice_details.forEach((item) => {
                if (item.item_type == 'Shipping' || item.item_type == 'Kit') {
                    invoiceDetailHtml +=
                        `
        <div class="invoice-detail-item" style="font-size: 14px; position: relative;">
          <div class="shipping-fee" style="padding: 12px 16px; background-color: #f4f4f4; font-size: 15px; font-weight: 600; color: #667085; text-transform: uppercase;">
            Kit</div>
          <div class="total-price" style="position: absolute; right: 16px; top: 50%; transform: translateY(-50%); font-size: 15px; font-weight: 600; color: #333843;">
            ` +
                        item.price +
                        `$</div>
        </div>`;
                }
            });

            var invoiveRefundedHtml = `
            <div class="refund-details" style="margin-top: 32px; font-family: 'Inter', serif; border-radius: 4px; padding: 12px; border: 1px solid #CFCFCF; font-size: 14px;">`;

            if (invoice_details_refunded.length == 0) {
                invoiveRefundedHtml = ` <div class="refund-details" style="margin-top: 32px; font-family: 'Inter', serif; border-radius: 4px; padding: 12px; text-align:center; font-size: 14px; max-height: 50vh; overflow-y: scroll; -ms-overflow-style: none; scrollbar-width: none;">
                                        No refunds have been made yet
                                    </div>`;
            } else {
                const { metadata } = invoice_info;

                const refundOject = JSON.parse(metadata);

                const refundInfo = refundOject.refund_history[0];

                const refundReason_html =
                    `<div style="padding:6px; background-color: #f8f7f7;margin-bottom: 32px;border-radius: 8px;"><table class="refund-reason" style="width: 100%;">
                <tbody>
                    <tr>
                    <td style="font-weight: 600; color: #000; padding-right: 6px; text-wrap: nowrap;">Refund reason: </td>
                    <td style="padding: 4px 0;">` +
                    (refundInfo.reason ? refundInfo.reason : '') +
                    `</td>
                    </tr>
                    <tr>
                    <td style="font-weight: 600; color: #000; padding-right: 6px;">Refund by: </td>
                    <td style="padding: 4px 0;">Refund by: ` +
                    refundInfo.refund_by +
                    `</td>
                    </tr>
                    <tr>
                    <td style="font-weight: 600; color: #000; padding-right: 6px;">Refund date: </td>
                    <td style="padding: 4px 0;">` +
                    refundInfo.date +
                    `</td>
                    </tr>
                    <tr>
                    <td style="font-weight: 600; color: #000; padding-right: 6px;">Refund amount: </td>
                    <td style="padding: 4px 0;">` +
                    $scope.floatValue(refundInfo.amount) +
                    `$</td>
                    </tr>
                </tbody>
                </table></div>`;

                var refundItemsHtml = '';

                invoice_details_refunded.forEach((item) => {
                    if (item.item_type == 'Course') {
                        const [course_name, player_name, itemType] =
                            item.item_name.split(' - ');
                        refundItemsHtml +=
                            `<div class="invoice-detail-item" style="font-size: 14px; position: relative;">
              <div class="course-name" style="padding: 12px 16px; color: #667085; background-color: #f4f4f4;"> ` +
                            course_name +
                            `
              </div>
              <div class="player-item" style="padding: 12px 16px; background-color: white; border-bottom: 0.5px solid #E0E2E7; position: relative;">
                <div class="player-name" style="font-size: 15px; font-weight: 500; color: #333843;">` +
                            player_name +
                            `</div>
                <div class="player-role" style="color: #667085; font-size: 14px; font-weight: 500; margin-top: 4px;">
                  ` +
                            itemType +
                            `
                </div>
                <div class="player-price" style="position: absolute; right: 16px; top: 50%; transform: translateY(-50%); font-size: 15px; font-weight: 600; color: #333843;">
                  ` +
                            $scope.floatValue(item.price) +
                            `$</div>
              </div>
            </div>`;
                    }
                });
                invoice_details_refunded.forEach((item) => {
                    if (
                        item.item_type == 'Shipping' ||
                        item.item_type == 'Kit'
                    ) {
                        refundItemsHtml +=
                            `
            <div class="invoice-detail-item" style="font-size: 14px; position: relative;">
              <div class="shipping-fee" style="padding: 12px 16px; background-color: #f4f4f4; font-size: 15px; font-weight: 600; color: #667085; text-transform: uppercase;">
                Kit</div>
              <div class="total-price" style="position: absolute; right: 16px; top: 50%; transform: translateY(-50%); font-size: 15px; font-weight: 600; color: #333843;">
                ` +
                            $scope.floatValue(item.price) +
                            `$</div>
            </div>`;
                    }
                });

                invoiveRefundedHtml +=
                    refundReason_html +
                    refundItemsHtml +
                    `
                </div>
                </div>
            </div>`;
            }

            $('#invoiceDetail').html(invoiveInfoHtml);

            $('#invoiceDetailItems').html(invoiceDetailHtml);

            $('#refundDetails').html(invoiveRefundedHtml);

            $('#paymentDetail').modal('show');
        };

        $scope.switchTab = function (tabId) {
            const tabs = document.querySelectorAll('.modal-tab');
            const tabContents = document.querySelectorAll('.tab-content');
            tabs.forEach((tab) => {
                tab.classList.remove('active');
            });

            tabContents.forEach((tabContent) => {
                tabContent.classList.remove('active');
            });

            const tab = document.querySelector(`[data-tabId="${tabId}"]`);
            const tabContent = document.querySelector(`[data-tab="${tabId}"]`);

            tab.classList.add('active');
            tabContent.classList.add('active');
        };

        //to float ,->2 decimal value

        $scope.floatValue = function (value) {
            return parseFloat(value).toFixed(2);
        };
    }
);

const refundPaymentDetail = async (
    detail_ids,
    invoice_id,
    user_id,
    reason,
    is_check_cancel_registration,
    event_id
) => {
    $.ajax({
        url: SERVER_PATH + 'payment/partialRefundPLJNew',
        type: 'POST',
        async: true,
        data: {
            details: JSON.stringify(detail_ids),
            invoice_id: invoice_id,
            user_id: user_id,
            reason: reason,
            is_check_cancel_registration: is_check_cancel_registration,
        },
        dataType: 'json',
        beforeSend: function () {
            Swal.fire({
                title: 'Processing...',
                allowOutsideClick: false,
                onBeforeOpen: () => {
                    Swal.showLoading();
                },
            });
        },
        success: function (response) {
            Swal.close();
            try {
                BootstrapDialog.closeAll();
                if (response.status == 'OK') {
                    let message = '';
                    if (typeof response.message == 'object') {
                        response.message.forEach((item) => {
                            message += `<p class="text-capitalize"><b>${item.type} status</b>: <span>${item.message}</span> </p>`;
                        });
                    } else {
                        message = response.message;
                    }

                    BootstrapDialog.show({
                        title: 'SUCCESS',
                        type: BootstrapDialog.TYPE_SUCCESS,
                        message: message,
                        onhide: function (dialog) {
                            $('#refundModal').modal('hide');

                            // reset form
                            $('#refundForm').trigger('reset');

                            GLOBAL_SCOPE.formData = {
                                selectedUsers: [],
                                reason: '',
                                shippingFee: null,
                                cancelRegistration: 'No',
                            };
                        },
                    });

                    // reload table
                    $($('#paymentTable_' + event_id))
                        .DataTable()
                        .ajax.reload();
                } else {
                    BootstrapDialog.show({
                        title: 'ERROR',
                        message: response.message,
                        type: BootstrapDialog.TYPE_DANGER,
                        buttons: [
                            {
                                label: 'Close',
                                action: function (dialog) {
                                    dialog.close();
                                },
                            },
                        ],
                        onhide: function (dialog) {
                            $('#refundModal').modal('hide');
                            $('#refundForm').trigger('reset');
                            GLOBAL_SCOPE.formData = {
                                selectedUsers: [],
                                reason: '',
                                shippingFee: null,
                                cancelRegistration: 'No',
                            };
                        },
                        onhidden: function (dialogRef) {
                            $('#refundModal').modal('hide');
                            $('#refundForm').trigger('reset');
                            GLOBAL_SCOPE.formData = {
                                selectedUsers: [],
                                reason: '',
                                shippingFee: null,
                                cancelRegistration: 'No',
                            };
                        },
                    });
                }
            } catch (error) {
                console.error('Error parsing JSON response:', error);
            }
        },
        error: function (xhr, status, error) {
            Swal.close();
            console.error('Error refunding:', error);
        },
    });
};
