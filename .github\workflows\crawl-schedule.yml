name: Run Scraper Daily

on:
  schedule:
    - cron: "0 0 * * *"  # Runs daily at midnight UTC
  workflow_dispatch:  # Allows manual execution from GitHub Actions UI

jobs:
  scrape:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: "3.x"  # Use latest Python 3 version

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install selenium beautifulsoup4 google-api-python-client google-auth google-auth-httplib2 google-auth-oauthlib mysql-connector-python

      - name: Authenticate to Google Cloud and manage IP
        env:
          GCP_SA_KEY: ${{ secrets.GCP_SA_KEY }}
          ACTION: add
        run: |
          echo "${GCP_SA_KEY}" > /tmp/gcloud-key.json
          export GOOGLE_APPLICATION_CREDENTIALS="/tmp/gcloud-key.json"
          python scripts/network_authen.py  # Run your script to add IP

      - name: Download Chrome & ChromeDriver
        run: |
          sudo apt-get update
          sudo apt-get install -y wget unzip
          wget https://dl.google.com/linux/direct/google-chrome-stable_current_amd64.deb
          sudo apt install ./google-chrome-stable_current_amd64.deb -y
          CHROME_VERSION=$(google-chrome --version | awk '{print $3}')
          wget https://storage.googleapis.com/chrome-for-testing-public/$CHROME_VERSION/linux64/chromedriver-linux64.zip
          unzip chromedriver-linux64.zip
          sudo mv chromedriver-linux64/chromedriver /usr/local/bin/
          chmod +x /usr/local/bin/chromedriver

      - name: Run Python script
        run: python crawl/crawl-philosophy.py
        env:
          MYSQL_HOST: ${{ secrets.MYSQL_HOST }}
          MYSQL_USER: ${{ secrets.MYSQL_USER }}
          MYSQL_PASSWORD: ${{ secrets.MYSQL_PASSWORD }}
          MYSQL_DB: ${{ secrets.MYSQL_DB }}

      - name: Remove temporary IP
        env:
          GCP_SA_KEY: ${{ secrets.GCP_SA_KEY }}
          ACTION: delete
        run: |
          echo "${GCP_SA_KEY}" > /tmp/gcloud-key.json
          export GOOGLE_APPLICATION_CREDENTIALS="/tmp/gcloud-key.json"
          python scripts/network_authen.py  # Remove IP from allowlist
