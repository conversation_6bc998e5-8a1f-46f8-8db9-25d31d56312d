// DO NOT EDIT THIS FILE! IT IS GENERATED EACH TIME "capacitor update" IS RUN

android {
  compileOptions {
      sourceCompatibility JavaVersion.VERSION_21
      targetCompatibility JavaVersion.VERSION_21
  }
}

apply from: "../capacitor-cordova-android-plugins/cordova.variables.gradle"
dependencies {
    implementation project(':capacitor-community-barcode-scanner')
    implementation project(':capacitor-app')
    implementation project(':capacitor-app-launcher')
    implementation project(':capacitor-browser')
    implementation project(':capacitor-clipboard')
    implementation project(':capacitor-inappbrowser')
    implementation project(':capacitor-push-notifications')
    implementation project(':capacitor-splash-screen')
    implementation project(':capacitor-status-bar')
    implementation project(':capgo-capacitor-updater')
    implementation "androidx.core:core:1.6.+"
    implementation "com.android.support:support-v4:27.+"
}
apply from: "../../node_modules/com-sarriaroman-photoviewer/src/android/photoviewer.gradle"
apply from: "../../node_modules/cordova-androidx-build/src/android/cordova-androidx-build.gradle"

if (hasProperty('postBuildExtras')) {
  postBuildExtras()
}
