app.controller('messagesDetailCtrl', [
    '$scope',
    '$rootScope',
    '$routeParams',
    '$http',
    '$q',
    function ($scope, $rootScope, $routeParams, $http, $q) {
        $('#page-wrapper').removeClass('nav-small');

        $scope.title = $routeParams.title;
        $scope.id = $routeParams.id;
        $scope.eventId = $routeParams.eventId;

        var active_tab = "App";
        var message_id = $scope.id;

        jQuery.ajax({
            type: 'POST',
            url: SERVER_PATH + 'event/getEventInfo',
            async: false,
            headers: {	
                'x-user-id': $rootScope.user_id,
                'x-user-email': $rootScope.user_name
            },
            data: {
                event_id: $routeParams.eventId,
            },
            dataType: 'json',
            complete: function (response) {
                var jsonData = JSON.parse(response.responseText);
                var event = jsonData.info;
                event_name = event.name;
                event_type = event.type;
                normalizedType = normalizeEventType(event_type);
            },
        });

        $scope.event_name = event_name;
        $scope.event_type = event_type;
        $scope.normalizedType = normalizedType;

        function normalizeEventType(eventType) {
            switch (eventType) {
                case 'Summer Scheme':
                    return 'summer-scheme';
                case 'Regional':
                    return 'regional';
                case 'PL Junior':
                    return 'pl-junior';
                case 'Golden Age':
                    return 'golden-age';
                case 'Beginner':
                    return 'beginner';
                case 'District':
                    return 'district';
                default:
                    return '';
            }
        }

        $scope.initEmail = function (id) {
            console.log('initEmail');
            var table_email = '#table_send_message_email';
        
            // destroy and reinitialize DataTables
            if ($.fn.dataTable.isDataTable(table_email)) {
              $(table_email).DataTable().destroy();
            }
        
            $(table_email).DataTable({
              dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
              stateSave: false,
              deferRender: true,
              ajax: {
                url: SERVER_PATH + 'message/editorSendMessageEmail',
                type: 'POST',
                headers: {	
                  'x-user-id': $rootScope.user_id,
                  'x-user-email': $rootScope.user_name
                },
                data: { send_id: id },
                dataType: 'json',
                complete: function (response) { },
                error: function (xhr, status, error) { },
              },
              language: {
                paginate: {
                  previous: '<i class="fa fa-chevron-left"></i>',
                  next: '<i class="fa fa-chevron-right"></i>',
                },
              },
              columnDefs: [
                {
                  targets: [0],
                  visible: false,
                },
              ],
              columns: [
                {
                  data: 'send_message_email.id',
                },
                {
                  data: null,
                  render: function (data, type, row) {
                    return data.parens.surname + ' ' + data.parens.other_name;
                  },
                },
                {
                  data: 'send_message_email.email',
                },
                {
                  data: 'send_message_email.created_at',
                },
                {
                  data: 'send_message_email.status',
                  render: function (data, type, row) {
                    // uppdercase first letter
                    return data.charAt(0).toUpperCase() + data.slice(1);
                  }
                },
              ],
              select: {
                style: 'single',
              },
              order: [[0, 'asc']],
              lengthMenu: [
                [10, 25, 50, 100, -1],
                [10, 25, 50, 100, 'All'],
              ],
            });
          };
        
          $scope.initNotification = function (id) {
            console.log('initNotification');
            var table_notification = '#table_send_message_notification';
        
            // destroy and reinitialize DataTables
            if ($.fn.dataTable.isDataTable(table_notification)) {
              $(table_notification).DataTable().destroy();
            }
        
            $(table_notification).DataTable({
              dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
              stateSave: false,
              deferRender: true,
              ajax: {
                url: SERVER_PATH + 'message/editorSendMessageNotification',
                type: 'POST',
                headers: {	
                  'x-user-id': $rootScope.user_id,
                  'x-user-email': $rootScope.user_name
                },
                data: { send_id: id },
                dataType: 'json',
                complete: function (response) { },
                error: function (xhr, status, error) { },
              },
              language: {
                paginate: {
                  previous: '<i class="fa fa-chevron-left"></i>',
                  next: '<i class="fa fa-chevron-right"></i>',
                },
              },
              columnDefs: [
                {
                  targets: [0],
                  visible: false,
                },
              ],
              columns: [
                {
                  data: 'send_message_notification.id',
                },
                {
                  data: null,
                  render: function (data, type, row) {
                    return data.parens.surname + ' ' + data.parens.other_name;
                  },
                },
                {
                  data: null,
                  width: 50,
                  className: 'center',
                  render: function (data, type, row) {
                    if (data.send_message_notification.read == 0) return 'No';
                    if (data.send_message_notification.read == 1) return 'Yes';
                    return 'No';
                  },
                },
                {
                  data: 'send_message_notification.created_at',
                },
                {
                  data: 'send_message_notification.status',
                },
              ],
              select: {
                style: 'single',
              },
              order: [[0, 'asc']],
              lengthMenu: [
                [10, 25, 50, 100, -1],
                [10, 25, 50, 100, 'All'],
              ],
            });
          };

        $scope.initTab = function () {
            if (active_tab.trim() == "Email") $scope.initEmail(message_id);
            if (active_tab.trim() == "App") $scope.initNotification(message_id);
        }
        
        setTimeout(function () {
            $scope.initTab();
        },500)
    
        $('.nav-tabs a').on('shown.bs.tab', function (event) {
            console.log("nav-tab")
            var tab = $(event.target).text();         // active tab
            active_tab = tab;
            $scope.initTab();
        });
    }])