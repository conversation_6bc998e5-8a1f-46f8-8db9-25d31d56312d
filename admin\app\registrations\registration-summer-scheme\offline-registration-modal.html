<link
  rel="stylesheet"
  href="app/registrations/registration-summer-scheme/offline-registration-modal.css"
/>

<!-- Modal -->
<div class="modal" id="myModal" role="dialog">
  <div
    class="modal-dialog"
    style="
      width: 640px;
      margin: auto;
      background-color: #ffffff;
      border-radius: 15px;
    "
  >
    <!-- <div class="row justify-content-center mt-0"> -->
    <div class="col-11 p-0 mt-3 mb-2">
      <div class="card px-0 pt-4 pb-0 mt-3 mb-3">
        <div class="row text-right" style="margin-right: 0px">
          <button
            type="button"
            class="btn btn-close"
            style="border: 0px"
            data-dismiss="modal"
          >
            x
          </button>
        </div>
        <div class="row text-center">
          <h2><strong>Offline Registration</strong></h2>
          <p>Fill all form field to go to next step</p>
        </div>
        <div class="row">
          <div class="col-md-12 mx-0">
            <form id="msform" name="regisForm">
              <!-- progressbar -->
              <ul id="progressbar">
                <li class="active" id="account"><strong>Parent</strong></li>
                <li id="personal"><strong>Player</strong></li>
                <li id="course"><strong>Course</strong></li>
                <li id="shipping"><strong>Shipping</strong></li>
                <li id="confirm"><strong>Finish</strong></li>
              </ul>
              <!-- fieldsets -->
              <!-- Parent Infor -->
              <fieldset ng-form="parentInfor">
                <div class="form-card">
                  <h2 class="fs-title">Parent Information</h2>

                  <!-- Parent email -->
                  <!-- <div class="input-group"> -->
                  <input
                    class="form-control"
                    type="email"
                    name="email"
                    placeholder="Parent Email"
                    ng-model="email"
                    ng-change="checkEmail(parentInfor.email.$viewValue,parentInfor)"
                    ng-pattern="regexEmail"
                    required
                  />
                  <!-- <span class="b-addon input-group-addon">
                      <button
                        ng-disabled="parentInfor.email.$invalid"
                        type="button"
                        class="btn-addon"
                        ng-click="checkEmail(parentInfor.email.$viewValue,parentInfor)"
                      >
                        Check <i class="fa fa-refresh" aria-hidden="true"></i>
                      </button>
                    </span> -->
                  <!-- </div> -->

                  <div class="errorContainer">
                    <div ng-repeat="validation in enMsg.email">
                      <div
                        ng-show="parentInfor.email.$dirty && parentInfor.email.$invalid && parentInfor.email.$error.hasOwnProperty(validation.type)"
                        class="text-danger"
                      >
                        <i class="fa fa-times"></i>
                        <span> {{ validation.message }} </span>
                      </div>
                    </div>
                  </div>
                  <!-- <input
                    id="edit_infor"
                    type="checkbox"
                    ng-model="edit_parent.value"
                  />
                  <label for="edit_infor">{{edit_parent.title}}</label> -->
                  <!-- <pre>Errors: {{parentInfor.email.$error | json}}</pre> -->
                  <!-- Parent surname -->
                  <input
                    ng-trim="true"
                    ng-disabled="(!edit_parent.value && edit_parent.surname.disabled)?true:false"
                    class="form-control"
                    type="text"
                    name="surname"
                    placeholder="English Surname"
                    ng-model="surname"
                    ng-pattern="regexEnglishName"
                    maxlength="50"
                    required
                  />
                  <div class="errorContainer">
                    <div ng-repeat="validation in enMsg.surname">
                      <div
                        ng-show="parentInfor.surname.$dirty && parentInfor.surname.$invalid && parentInfor.surname.$error.hasOwnProperty(validation.type)"
                        class="text-danger"
                      >
                        <i class="fa fa-times"></i>
                        <span> {{ validation.message }} </span>
                      </div>
                    </div>
                  </div>

                  <!-- Parent Other Name -->
                  <input
                    ng-trim="true"
                    class="form-control"
                    ng-disabled="(!edit_parent.value && edit_parent.other_name.disabled)?true:false"
                    type="text"
                    name="other_name"
                    placeholder="English Other Name"
                    ng-model="other_name"
                    ng-pattern="regexEnglishName"
                    maxlength="50"
                    required
                  />
                  <div class="errorContainer">
                    <div ng-repeat="validation in enMsg.other_name">
                      <div
                        ng-show="parentInfor.other_name.$dirty && parentInfor.other_name.$invalid && parentInfor.other_name.$error.hasOwnProperty(validation.type)"
                        class="text-danger"
                      >
                        <i class="fa fa-times"></i>
                        <span> {{ validation.message }} </span>
                      </div>
                    </div>
                  </div>

                  <!-- Parent Mobile -->
                  <div class="input-group">
                    <div class="input-group-addon">
                      <span class="input-group-text">(+852)</span>
                    </div>

                    <input
                      class="form-control"
                      ng-disabled="(!edit_parent.value && edit_parent.phone.disabled)?true:false"
                      type="text"
                      name="phone"
                      placeholder="Parent Mobile Number"
                      ng-model="phone"
                      ng-pattern="regexMobile"
                      maxlength="8"
                      required
                    />
                  </div>
                  <div class="errorContainer">
                    <div ng-repeat="validation in enMsg.phone">
                      <div
                        ng-show="parentInfor.phone.$dirty && parentInfor.phone.$invalid && parentInfor.phone.$error.hasOwnProperty(validation.type)"
                        class="text-danger"
                      >
                        <i class="fa fa-times"></i>
                        <span> {{ validation.message }} </span>
                      </div>
                    </div>
                  </div>
                  <input
                    id="create_user"
                    type="checkbox"
                    ng-model="create_user.value"
                  />
                  <label for="create_user">{{create_user.title}} </label>
                </div>

                <button
                  type="button"
                  name="next"
                  class="next action-button"
                  value="Next Step"
                  ng-click="submitParent($event, parentInfor)"
                >
                  Next Step
                </button>
              </fieldset>

              <!-- Player Infor -->
              <fieldset ng-form="playerInfor">
                <div class="form-card">
                  <h2 class="fs-title">Player Information</h2>
                  <!-- Player SurName -->
                  <input
                    ng-trim="true"
                    class="form-control"
                    type="text"
                    name="p_surname"
                    placeholder="English Surname"
                    ng-model="p_surname"
                    ng-pattern="regexEnglishName"
                    maxlength="50"
                    required
                  />
                  <div class="errorContainer">
                    <div ng-repeat="validation in enMsg.surname">
                      <div
                        ng-show="playerInfor.p_surname.$dirty && playerInfor.p_surname.$invalid && playerInfor.p_surname.$error.hasOwnProperty(validation.type)"
                        class="text-danger"
                      >
                        <i class="fa fa-times"></i>
                        <span> {{ validation.message }} </span>
                      </div>
                    </div>
                  </div>

                  <!-- Player Other Name -->
                  <input
                    ng-trim="true"
                    class="form-control"
                    type="text"
                    name="p_other_name"
                    placeholder="English Other Name"
                    ng-model="p_other_name"
                    ng-pattern="regexEnglishName"
                    maxlength="50"
                    required
                  />
                  <div class="errorContainer">
                    <div ng-repeat="validation in enMsg.other_name">
                      <div
                        ng-show="playerInfor.p_other_name.$dirty && playerInfor.p_other_name.$invalid && playerInfor.p_other_name.$error.hasOwnProperty(validation.type)"
                        class="text-danger"
                      >
                        <i class="fa fa-times"></i>
                        <span> {{ validation.message }} </span>
                      </div>
                    </div>
                  </div>

                  <!-- Player Chinese Name -->
                  <input
                    ng-trim="true"
                    class="form-control"
                    type="text"
                    name="p_chinese_name"
                    placeholder="Chinese Name"
                    ng-model="p_chinese_name"
                    ng-pattern="regexChineseName"
                    maxlength="100"
                  />
                  <div class="errorContainer">
                    <div ng-repeat="validation in enMsg.chinese_name">
                      <div
                        ng-show="playerInfor.p_chinese_name.$dirty && playerInfor.p_chinese_name.$invalid && playerInfor.p_chinese_name.$error.hasOwnProperty(validation.type)"
                        class="text-danger"
                      >
                        <i class="fa fa-times"></i>
                        <span> {{ validation.message }} </span>
                      </div>
                    </div>
                  </div>

                  <!-- radio button gender -->
                  <div class="row" style="margin-left: 8px">
                    <div class="col">
                      <h5>Gender</h5>
                    </div>
                    <div class="col">
                      <div class="row">
                        <input
                          id="radMale"
                          type="radio"
                          name="p_gender"
                          value="Male"
                          ng-model="p_gender"
                          ng-required="!p_gender"
                        />
                        <label for="radMale">Male</label>
                        <input
                          id="radFemale"
                          type="radio"
                          name="gender"
                          value="Female"
                          ng-model="p_gender"
                          ng-required="!p_gender"
                        />
                        <label for="radFemale">Female</label>
                      </div>
                    </div>
                  </div>
                  <div class="errorContainer">
                    <div ng-repeat="validation in enMsg.gender">
                      <div
                        ng-show="playerInfor.p_gender.$dirty && playerInfor.p_gender.$invalid && playerInfor.p_gender.$error.hasOwnProperty(validation.type)"
                        class="text-danger"
                      >
                        <i class="fa fa-times"></i>
                        <span> {{ validation.message }} </span>
                      </div>
                    </div>
                  </div>

                  <!-- Player DOB -->
                  <h5>Date of Birth</h5>
                  <input
                    class="form-control"
                    type="date"
                    max="{{today}}"
                    id="p_dob"
                    name="p_dob"
                    placeholder="Date of Birth"
                    ng-model="p_dob"
                    required
                  />
                  <div class="errorContainer">
                    <div ng-repeat="validation in enMsg.dob">
                      <div
                        ng-show="playerInfor.p_dob.$dirty && playerInfor.p_dob.$invalid && playerInfor.p_dob.$error.hasOwnProperty(validation.type)"
                        class="text-danger"
                      >
                        <i class="fa fa-times"></i>
                        <span> {{ validation.message }} </span>
                      </div>
                    </div>
                  </div>

                  <!-- Player HKID -->
                  <input
                    class="form-control"
                    type="text"
                    name="p_hkid_no"
                    placeholder="HKID No."
                    ng-model="p_hkid_no"
                    ng-pattern="regexHKID"
                    maxlength="9"
                    required
                  />
                  <div class="errorContainer">
                    <div ng-repeat="validation in enMsg.hkid_no">
                      <div
                        ng-show="playerInfor.p_hkid_no.$dirty && playerInfor.p_hkid_no.$invalid && playerInfor.p_hkid_no.$error.hasOwnProperty(validation.type)"
                        class="text-danger"
                      >
                        <i class="fa fa-times"></i>
                        <span> {{ validation.message }} </span>
                      </div>
                    </div>
                  </div>
                </div>
                <button
                  type="button"
                  name="previous"
                  class="previous action-button-previous"
                >
                  Previous
                </button>
                <button
                  type="button"
                  name="next"
                  class="next action-button"
                  value="Next Step"
                  ng-click="submitPlayer($event, playerInfor,courseInfor)"
                >
                  Next Step
                </button>
              </fieldset>

              <!-- Course Infor -->
              <fieldset ng-form="courseInfor">
                <div class="form-card">
                  <h2 class="fs-title">Course Information</h2>

                  <!-- Training class -->
                  <div ng-repeat="course in checkboxCourse">
                    <div class="panel form-card-sm">
                      <div class="panel-heading">
                        <input
                          type="checkbox"
                          id="{{course.type}}"
                          ng-disabled="course.disabled"
                          ng-model="select_course"
                          ng-click="toggleCourse(course.type,courseInfor)"
                          ng-checked="selectedCourses.indexOf(course.type) > -1"
                          ng-required="selectedCourses.length == 0"
                        />
                        <label for="{{course.type}}" class="h5"
                          ><strong class="title"
                            >{{course.title}}</strong
                          ></label
                        >
                      </div>
                      <div class="panel-body">
                        <div class="row">
                          <div class="col-sm-8">
                            <p ng-repeat="title in course.sub_title">
                              {{title}}
                            </p>
                          </div>
                          <div class="col-sm-4">
                            <p>
                              Class selected:
                              <strong>{{course.class_selected.code}} </strong>
                            </p>
                          </div>
                        </div>
                        <div
                          class="row"
                          ng-show="course.hasOwnProperty('input') && selectedCourses.indexOf(course.type) > -1"
                        >
                          <div ng-repeat="input in course.input">
                            <div class="col-sm-3">
                              <p>{{input.title}}:</p>
                            </div>
                            <div class="col-sm-9">
                              <input
                                class="form-control text-capitalize"
                                type="{{input.type}}"
                                name="{{input.name}}"
                                max="{{input.type=='date'?input.max :''}}"
                                min="{{input.type=='date'?input.min :''}}"
                                placeholder="{{input.placeholder}}"
                                ng-model="input.value"
                                ng-required="selectedCourses.indexOf(course.type) > -1 && input.required"
                                ng-pattern="selectedCourses.indexOf(course.type) > -1? input.pattern : ''"
                              />
                              <div class="errorContainer">
                                <!-- {{courseInfor[input.name].$error}}
                              {{courseInfor[input.name].$dirty}} -->
                                <div
                                  ng-repeat="validation in enMsg[input.validate]"
                                >
                                  <div
                                    ng-show="courseInfor[input.name].$dirty && courseInfor[input.name].$invalid && courseInfor[input.name].$error.hasOwnProperty(validation.type)"
                                    class="text-danger"
                                  >
                                    <i class="fa fa-times"></i>
                                    <span> {{ validation.message }} </span>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <hr />
                        <div
                          ng-show="selectedCourses.indexOf(course.type) > -1"
                        >
                          <div class="errorContainer">
                            <div
                              ng-show="course.course.length == 0"
                              class="text-danger"
                            >
                              <span>
                                There are no classes matching this player
                              </span>
                            </div>
                          </div>
                          <div ng-show="show">
                            <div class="row">
                              <div class="col-sm-6">
                                <!-- <label
                                >Region: <input ng-model="search.region"
                              /></label> -->
                              </div>
                              <div class="col-sm-6">
                                <label
                                  >Search: <input ng-model="search.$"
                                /></label>
                              </div>
                            </div>
                            <div class="container-class">
                              <ul class="list-group">
                                <li
                                  ng-repeat="class in course.course | filter:search"
                                  class="list-group-item"
                                >
                                  <div class="row">
                                    <div class="col-sm-9">
                                      <p>
                                        Class code:
                                        <strong> {{class.class_code}}</strong>
                                      </p>
                                      <p>
                                        Venue:
                                        <strong>{{class.venue_name}}</strong>
                                      </p>
                                      <p>
                                        Day of the Week:
                                        <strong>{{class.dotw}}</strong>
                                      </p>
                                      <p>
                                        Birth year:
                                        <strong>{{class.group_year}}</strong>
                                      </p>
                                      <p>
                                        Region:
                                        <strong>{{class.region}}</strong>
                                      </p>
                                    </div>
                                    <div class="col-sm-3">
                                      <button
                                        type="button"
                                        class="btn btn-danger"
                                        ng-click="selectCourse(class, course.type)"
                                      >
                                        select
                                      </button>
                                    </div>
                                  </div>
                                </li>
                              </ul>
                              <!-- {{checkboxCourse.normal}} -->
                            </div>
                          </div>
                          <button
                            type="button"
                            class="btn btn-outline-primary"
                            ng-init="show=false; "
                            ng-click="show=!show; "
                            style="width: 100%; margin-bottom: 10px"
                          >
                            <i
                              ng-class="show?'fa fa-angle-up':'fa fa-angle-down'"
                              aria-hidden="true"
                            ></i>
                          </button>
                        </div>
                      </div>
                    </div>
                    <div class="errorContainer">
                      <div
                        ng-show="selectedCourses.indexOf(course.type) > -1 && course.class_selected.id == '' "
                        class="text-danger"
                      >
                        <i class="fa fa-times"></i>
                        <span> Please select the class </span>
                      </div>
                    </div>
                  </div>
                  <div class="errorContainer">
                    <div
                      ng-show="courseInfor.$dirty && selectedCourses.length == 0"
                      class="text-danger"
                    >
                      <i class="fa fa-times"></i>
                      <span> Please select the course </span>
                    </div>
                  </div>

                  <!-- Parent size -->
                  <div
                    class="row"
                    ng-show="selectedCourses.indexOf('parent') > -1"
                  >
                    <div class="col-sm-4">Parent bib size:</div>
                    <div class="col-sm-8">
                      <select class="form-control" ng-model="size.parent">
                        <option
                          ng-repeat="size in shirt_sizes"
                          ng-value="size.id"
                        >
                          {{size.size_name}}
                        </option>
                      </select>
                    </div>
                  </div>
                  <div class="errorContainer">
                    <div
                      ng-show="selectedCourses.indexOf('parent') > -1 && size.parent==0"
                      class="text-danger"
                    >
                      <i class="fa fa-times"></i>
                      <span> Please select the bib size </span>
                    </div>
                  </div>

                  <!-- Player size -->
                  <div class="row">
                    <div class="col-sm-4">Player bib size:</div>
                    <div class="col-sm-8">
                      <select class="form-control" ng-model="size.player">
                        <option
                          ng-repeat="size in shirt_sizes"
                          ng-value="size.id"
                        >
                          {{size.size_name}}
                        </option>
                      </select>
                    </div>
                  </div>
                  <div class="errorContainer">
                    <div
                      ng-show="selectedCourses.length >0 && size.player==0"
                      class="text-danger"
                    >
                      <i class="fa fa-times"></i>
                      <span> Please select the bib size </span>
                    </div>
                  </div>
                </div>

                <button
                  type="button"
                  name="previous"
                  class="previous action-button-previous"
                >
                  Previous
                </button>
                <button
                  type="button"
                  name="next"
                  class="next action-button"
                  value="Next Step"
                  ng-click="submitCourse($event, courseInfor,shippingInfor)"
                >
                  Next Step
                </button>

                <!-- <button
                  type="button"
                  name="make_payment"
                  class="next action-button"
                >
                  Confirm
                </button> -->
              </fieldset>

              <!-- Shipping Infor -->
              <fieldset ng-form="shippingInfor">
                <div class="form-card">
                  <h2 class="fs-title">Shipping Information</h2>
                  <div class="row" style="margin-left: 8px">
                    <div class="col">
                      <h5>Shipping type</h5>
                    </div>
                    <div class="col">
                      <div class="row">
                        <input
                          id="home"
                          type="radio"
                          value="home"
                          ng-model="shipping.type"
                          ng-required="!shipping"
                        />
                        <label for="home">Door</label>
                        <!-- <input
                          id="locker"
                          type="radio"
                          value="locker"
                          ng-model="shipping.type"
                          ng-required="!shipping"
                        />
                        <label for="locker">Self Pick-up Location</label> -->

                        <input
                          id="self_pickup"
                          type="radio"
                          value="self_pickup"
                          ng-show="shipping.show_self_pickup"
                          ng-model="shipping.type"
                          ng-required="!shipping"
                        />
                        <label
                          for="self_pickup"
                          ng-show="shipping.show_self_pickup"
                          >Self Pick-up HKFA</label
                        >
                      </div>
                    </div>
                  </div>
                  <div ng-show="shipping.type == 'home'">
                    <input
                      ng-trim="true"
                      class="form-control"
                      type="text"
                      placeholder="Delivery address"
                      ng-model="shipping.address"
                      name="address"
                      ng-pattern="shipping.type=='home'?regexAddress :''"
                      maxlength="150"
                      ng-required="shipping.type=='home'"
                    />
                    <div class="errorContainer">
                      <!-- <pre>{{shippingInfor|json}} </pre> -->
                      <div
                        ng-show="shippingInfor.$dirty && shipping.address == ''"
                        class="text-danger"
                      >
                        <i class="fa fa-times"></i>
                        <span> Please enter the address </span>
                      </div>
                      <div
                        ng-show="shippingInfor.$dirty && shipping.address != '' && shippingInfor.$error.pattern"
                        class="text-danger"
                      >
                        <i class="fa fa-times"></i>
                        <span> Please enter the valid address </span>
                      </div>
                    </div>
                  </div>
                  <div ng-show="shipping.type == 'locker'">
                    <div class="row">
                      <div class="col-sm-4">Region:</div>
                      <div class="col-sm-8">
                        <select
                          class="form-control"
                          ng-model="locker_selected.area_chi"
                          ng-change="getAllSubDistrict()"
                        >
                          <option
                            ng-repeat="area in area_chi"
                            ng-value="area.area_chi"
                          >
                            {{area.area_chi}}
                          </option>
                        </select>
                      </div>
                    </div>
                    <div class="row">
                      <div class="col-sm-4">Districts:</div>
                      <div class="col-sm-8">
                        <select
                          class="form-control"
                          ng-model="locker_selected.subdistricts_chi"
                          ng-change="getAllLockerBrand()"
                        >
                          <option
                            ng-repeat="district in sub_district"
                            ng-value="district.subdistricts_chi"
                          >
                            {{district.subdistricts_chi}}
                          </option>
                        </select>
                      </div>
                    </div>
                    <div class="row">
                      <div class="col-sm-4">Self Pick up type:</div>
                      <div class="col-sm-8">
                        <select
                          class="form-control"
                          ng-model="locker_selected.locker_brand"
                          ng-change="getAllLocker()"
                        >
                          <option
                            ng-repeat="brand in locker_brand"
                            ng-value="brand.locker_brand"
                          >
                            {{brand.locker_brand}}
                          </option>
                        </select>
                      </div>
                    </div>
                    <div class="row" style="margin-top: 10px">
                      <div class="col-sm-8">
                        <h5 style="margin: 0px; font-weight: 500">
                          Locker selected:
                          <strong> {{shipping.locker_address}} </strong>
                        </h5>
                      </div>
                      <div class="col-sm-4">
                        <label
                          >Search: <input ng-model="search_locker.$"
                        /></label>
                      </div>
                    </div>
                    <div class="container-class">
                      <div class="list-group">
                        <a
                          ng-repeat="locker in all_locker | filter:search_locker"
                          class="list-group-item item {{locker.id == shipping.locker_id?'active':''}}"
                          ng-click="selectLocker(locker)"
                        >
                          <strong>{{locker.locker_id}}</strong>
                          <p>{{locker.address_chi}}</p>
                        </a>
                      </div>
                    </div>
                  </div>
                </div>
                <button
                  type="button"
                  name="previous"
                  class="previous action-button-previous"
                >
                  Previous
                </button>
                <button
                  type="button"
                  name="make_payment"
                  class="next action-button"
                  ng-click="submitShipping($event, shippingInfor)"
                >
                  Confirm
                </button>
              </fieldset>
              <!-- Success -->
              <fieldset>
                <div>
                  <h2 class="text-center">Success !</h2>
                  <br /><br />
                  <div class="row justify-content-center">
                    <div style="width: 100px; margin: auto">
                      <img
                        src="https://img.icons8.com/color/96/000000/ok--v2.png"
                        class="fit-image"
                      />
                    </div>
                  </div>
                  <br /><br />
                  <!-- <div class="row justify-content-center">
                    <div class="col-7 text-center">
                      <h5>You Have Successfully Registration</h5>
                    </div>
                  </div> -->
                  <div class="d-flex justify-content-end">
                    <div>
                      <button
                        type="button"
                        class="btn btn-success"
                        ng-click="reloadForm()"
                      >
                        New Offline Registration
                        <i class="fa fa-refresh" aria-hidden="true"></i>
                      </button>
                    </div>
                  </div>
                </div>
              </fieldset>
            </form>
          </div>
        </div>
      </div>
      <!-- </div> -->
    </div>
  </div>
</div>
<!-- End Modal -->
