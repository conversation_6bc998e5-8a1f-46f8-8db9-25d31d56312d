app.controller('eventEditorCtrl', function ($scope, $rootScope, $http) {
    jQuery.extend(jQuery.fn.dataTableExt.oSort, {
        'mixedGroup-pre': (a) => {
            // loop through characters in the string and get sum of chart code points
            let sum = 0;
            for (let i = 0; i < a.length; i++) {
                sum += a.charCodeAt(i);
            }
            return sum;
        },
        'mixedGroup-asc': (a, b) => a - b,
        'mixedGroup-desc': (a, b) => b - a,
    });

    var active_tab = 'Event';

    var eventLevels = [];

    jQuery.extend(jQuery.fn.dataTableExt.oSort, {
        'event-level-asc': function (a, b) {
            // compare the index of the item in the eventLevels array
            return (
                eventLevels.findIndex((item) => item.value === a) -
                eventLevels.findIndex((item) => item.value === b)
            );
        },
        'event-level-desc': function (a, b) {
            // compare the index of the item in the eventLevels array
            return (
                eventLevels.findIndex((item) => item.value === b) -
                eventLevels.findIndex((item) => item.value === a)
            );
        },
    });

    jQuery.ajax({
        type: 'POST',
        url: SERVER_PATH + 'event/getEventLevels',
        async: false,
        headers: {	
            'x-user-id': $rootScope.user_id,
            'x-user-email': $rootScope.user_name
        },
        data: {},
        dataType: 'json',
        complete: function (response) {
            var jsonData = JSON.parse(response.responseText);
            eventLevels = jsonData.data;

            eventLevels = eventLevels.map(function (item) {
                return {
                    label: item.name,
                    value: item.name,
                };
            });
        },
    });

    $scope.initTableEvent = function () {
        function format(d) {
            // `d` is the original data object for the row
            $content = '<h2 class="pull-left">Child Events</h2><br/>';
            $content +=
                '<div class="table-responsive">' +
                '<table id="child_event_table_' +
                d.id +
                '" class="table table-striped table-bordered table-hover" cellspacing="0" width="100%">' +
                '<thead><tr>' +
                '<th>Photo</th>' +
                '<th>Name</th>' +
                '<th>Start date</th>' +
                '<th>End date</th>' +
                '<th>Start register date</th>' +
                '<th>End register date</th>' +
                '<th>Start checkout date</th>' +
                '<th>Type</th>' +
                '<th>Team limit</th>' +
                '<th>Status</th>' +
                '<th>Color</th>' +
                '<th>Action</th>' +
                '<th>Price</th>' +
                '<th>Coach Fee</th>' +
                '<th>Certificate</th>' +
                '</tr></thead>' +
                '</table>';
            ('</div>');

            return $content;
        }
        // get user_id in local storage hkjflApp.user_id
        setTimeout(() => {
            const userId = parseInt(localStorage.getItem('hkjflApp.user_id'));
        }, 500);

        var table;

        function orderEventLevel() {
            $content =
                '<div class="table-responsive">' +
                '<table id="order_event_level_table" class="table table-striped table-bordered table-hover" cellspacing="0" width="100%">' +
                '<thead>' +
                '<tr>' +
                '<th>Order</th>' +
                '<th>Name</th>' +
                '<th>Chinese name</th>' +
                '</tr>' +
                '</thead>' +
                '</table>' +
                '</div>';

            return $content;
        }

        function initEventLevelTable() {
            var table;
            var editor = new $.fn.dataTable.Editor({
                ajax: {
                    type: 'POST',
                    url: SERVER_PATH + 'event/setEventLevels',
                    headers: {	
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name
                    },
                    data: {},
                    async: false,
                    dataType: 'json',
                    complete: function (response) {
                        var jsonData = JSON.parse(response.responseText);
                        // --- may need to reload
                        table.ajax.reload();

                        if (jsonData.status == 'OK') {
                            Swal.fire({
                                icon: 'question',
                                text: 'Do you want to reload page?',
                                showCancelButton: true,
                                cancelButtonText: 'No',
                                confirmButtonText: 'Yes',
                                allowOutsideClick: false,
                            }).then((result) => {
                                if (result.isConfirmed) {
                                    location.reload();
                                }
                            });
                        }else
                        {
                            // check fieldErrors is not empty
                            if (typeof jsonData.fieldErrors == 'undefined') {
                                Swal.fire({
                                    icon: 'error',
                                    title: 'Error',
                                    text: jsonData.message,
                                });
                            }
                        }
                    },
                    error: function (xhr, status, error) {},
                },
                table: '#order_event_level_table',
                formOptions: {
                    main: {
                        onBlur: 'none',
                    },
                },
                i18n: {
                    create: {
                        button: 'New',
                        title: 'Create new event level',
                        submit: 'Create',
                    },
                    edit: {
                        button: 'Edit',
                        title: 'Edit event level',
                        submit: 'Save',
                    },
                    remove: {
                        button: 'Delete',
                        title: 'Delete level',
                        submit: 'Delete',
                    },
                    error: {
                        system: 'System error, please contact administrator.',
                    },
                },
                fields: [
                    {
                        label: 'Order',
                        name: 'order',
                        type: 'hidden',
                    },
                    {
                        label: 'Name',
                        name: 'name',
                    },
                    {
                        label: 'Chinese name',
                        name: 'chinese_name',
                    },
                    {
                        label: 'is_open_form',
                        name: 'is_open_form',
                        type: 'hidden',
                        def: BOOLEAN_FALSE
                    }
                ],
            });

           
            table = $('#order_event_level_table').DataTable({
                dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
                stateSave: true,
                deferRender: true,
                ajax: {
                    url: SERVER_PATH + 'event/getEventLevels',
                    type: 'POST',
                    headers: {	
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name
                    },
                    data: {},
                    dataType: 'json',
                    complete: function (response) {
                        var jsonData = JSON.parse(response.responseText);

                        if (jsonData.status == 'OK') {
                            table.ajax.reload();
                        }
                    },
                    error: function (xhr, status, error) {},
                },
                language: {
                    paginate: {
                        previous: '<i class="fa fa-chevron-left"></i>',
                        next: '<i class="fa fa-chevron-right"></i>',
                    },
                },
                columns: [
                    {
                        data: 'order',
                        visible: false,
                    },
                    {
                        data: 'name',
                        sortable: false,
                    },
                    {
                        data: 'chinese_name',
                        sortable: false,
                    },
                ],
                select: {
                    style: 'single',
                    selector: 'td:not(:first-child)',
                },
                order: [[0, 'asc']],
                columnDefs: [],
                lengthMenu: [
                    [10, 25, 50, 100, -1],
                    [10, 25, 50, 100, 'All'],
                ],
                buttons: [
                    { extend: 'colvis', text: 'Columns' },
                    { extend: 'create', editor: editor },
                    { extend: 'edit', editor: editor }, 
                    { extend: 'remove', editor: editor },
                ],
                rowReorder: {
                    dataSrc: 'order',
                    editor: editor,
                },
            });
        }

        if ($.fn.dataTable.isDataTable('#event_table')) {
            $('#event_table').DataTable().destroy();
        }

        var editor = new $.fn.dataTable.Editor({
            ajax: {
                type: 'POST',
                url: SERVER_PATH + 'event/setEvents',
                headers: {	
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name
                },
                data: {
                    // "pgroup_id": pgroup_id
                    user_id: userId,
                },
                dataType: 'json',
                complete: function (response) {
                    var jsonData = JSON.parse(response.responseText);
                    // --- may need to reload
                    if (DEVELOPMENT_ENVIRONMENT)
                        console.log('status = ' + jsonData.status);
                    if (jsonData.status == 'OK') {
                        if (DEVELOPMENT_ENVIRONMENT)
                            console.log('Before reload');
                        table.ajax.reload();
                    }
                },
                error: function (xhr, status, error) {},
            },
            table: '#event_table',
            formOptions: {
                main: {
                    onBlur: 'none',
                },
            },
            i18n: {
                create: {
                    button: 'New',
                    title: 'Create new event',
                    submit: 'Create',
                },
                edit: {
                    button: 'Edit',
                    title: 'Edit event',
                    submit: 'Save',
                },
                remove: {
                    button: 'Delete',
                    title: 'Delete event',
                    submit: 'Delete',
                    confirm: {
                        _: 'Are you sure you want to delete these events?',
                        1: 'Are you sure you want to delete this event?',
                    },
                },
                error: {
                    system: 'System error, please contact administrator.',
                },
            },
            fields: [
                {
                    label: 'Name:',
                    name: 'name',
                },
                {
                    label: 'Chinese Name:',
                    name: 'chinese_name',
                },
                {
                    label: 'Type:',
                    name: 'type',
                    type: 'radio',
                    def: EVENT_SEASON,
                    options: [{ label: EVENT_SEASON, value: EVENT_SEASON }],
                },

                {
                    label: 'Start date:',
                    name: 'start_date',
                    type: 'datetime',
                    format: 'DD-MMM-YYYY',
                },
                {
                    label: 'End date:',
                    name: 'end_date',
                    type: 'datetime',
                    format: 'DD-MMM-YYYY',
                },
            ],
        });

        // type is golden age
        $()

        var table = $('#event_table').DataTable({
            dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
            stateSave: true,
            deferRender: true,
            ajax: {
                url: SERVER_PATH + 'event/getEvents',
                type: 'POST',
                headers: {	
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name
                },
                data: {
                    // "pgroup_id": pgroup_id
                },
                dataType: 'json',
                complete: function (response) {},
                error: function (xhr, status, error) {},
            },
            language: {
                info: 'Showing _START_ to _END_ of _TOTAL_ event',
                infoEmpty: 'Showing 0 to 0 of 0 events',
                lengthMenu: 'Show _MENU_ events',
                select: {
                    rows: {
                        _: 'You have selected %d events',
                        0: 'Click an event to select',
                        1: '1 event selected',
                    },
                },
                paginate: {
                    previous: '<i class="fa fa-chevron-left"></i>',
                    next: '<i class="fa fa-chevron-right"></i>',
                },
            },
            columns: [
                {
                    className: 'details-control',
                    orderable: false,
                    data: null,
                    defaultContent: '',
                },
                { data: 'name', className: 'center' },
                // { data: "type", className: "center" },
                { data: 'start_date', className: 'center' },
                { data: 'end_date', className: 'center' },
                { data: 'type', className: 'center' },
            ],
            select: {
                style: 'single',
                // selector: 'td:first-child',
            },
            order: [[1, 'asc']],
            lengthMenu: [
                [10, 25, 50, 100, -1],
                [10, 25, 50, 100, 'All'],
            ],
            buttons: [
                { extend: 'create', editor: editor },
                { extend: 'edit', editor: editor },
                {
                    text: 'Event level',
                    action: function () {
                        var msg = orderEventLevel();

                        BootstrapDialog.show({
                            size: BootstrapDialog.SIZE_WIDE,
                            type: BootstrapDialog.TYPE_DANGER,
                            closable: true,
                            closeByBackdrop: false,
                            closeByKeyboard: true,
                            title: 'Change Order Event Level',
                            message: msg,
                            onshown: function (dialog) {
                                initEventLevelTable();
                            },
                        });
                    },
                },
                { extend: 'colvis', text: 'Columns' },
            ],
        });

        function getModalGroupTableHtml(event_id) {
            var str =
                '' +
                // '<div class="modal fade" id="modal-group" tabindex="-1" role="dialog" aria-labelledby="passwordLabel" aria-hidden="true">' +
                // 	'<div class="modal-dialog">' +
                // 		'<div class="modal-content">' +
                // 			'<div class="modal-header">' +
                // 				'<button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>' +
                // 				'<h4 class="modal-title">Edit player group</h4>' +
                // 			'</div>' +
                // 			'<div class="modal-body">' +
                '<div id="group_table" class="main-box-body clearfix">' +
                '<div class="table-responsive">' +
                '<table id="tblGroups_' +
                event_id +
                '" class="table table-striped table-bordered table-hover" cellspacing="0" width="100%">' +
                '<thead>' +
                '<tr>' +
                '<th>Name</th>' +
                '<th>Type</th>' +
                '<th>Year</th>' +
                '<th>Girl offer</th>' +
                '</tr>' +
                '</thead>' +
                '</table>' +
                '</div>' +
                '</div>' +
                // 			'</div>' +
                // 		'</div>' +
                // 	'</div>' +
                // '</div>';
                '';
            return str;
        }

        function getModalDistrictTableHtml(event_id) {
            var str =
                '' +
                '<div id="group_table" class="main-box-body clearfix">' +
                '<div class="table-responsive">' +
                '<table id="tblDictricts_' +
                event_id +
                '" class="table table-striped table-bordered table-hover" cellspacing="0" width="100%">' +
                '<thead>' +
                '<tr>' +
                '<th>Name</th>' +
                '<th>Chinese name</th>' +
                '</tr>' +
                '</thead>' +
                '</table>' +
                '</div>' +
                '</div>' +
                '';
            return str;
        }

        function getModalEventPrice(event_id) {
            var str =
                '' +
                '<div id="event_table" class="main-box-body clearfix">' +
                '<div class="table-responsive">' +
                '<table id="tblEventPrice_' +
                event_id +
                '" class="table table-striped table-bordered table-hover" cellspacing="0" width="100%">' +
                '<thead>' +
                '<tr>' +
                '<th>Type</th>' +
                '<th>Description</th>' +
                '<th>Fee</th>' +
                '</tr>' +
                '</thead>' +
                '</table>' +
                '</div>' +
                '</div>';
            return str;
        }

        function getModalCoachFees(event_id) {
            var str =
                '' +
                '<div id="event_table" class="main-box-body clearfix">' +
                '<div class="table-responsive">' +
                '<div class="form-group">' +
                '<label for="trainTypeSelect">Select Train Type:</label>' +
                '<div class="align-items-center gap-2" style="display: flex">' +
                '<select id="trainTypeSelect" class="form-control w-auto" style="width: 300px;">' +
                '<option value="">Loading...</option>' +
                '</select>' +
                '<button type="button" class="btn btn-primary" style="margin-left: 1rem" id="selectTrainTypeBtn">Select</button>' +
                '</div>' +
                '</div>' +
                '<p>You can edit coach fee by click on the cell, type the changes</p>' +
                '<table id="tableCoachFees_' +
                event_id +
                '" class="table table-striped table-bordered table-hover" cellspacing="0" width="100%">' +
                '<thead><tr>' +
                '<th>Level \\ Role</th>' +
                '<th>Head Coach</th>' +
                '<th>Assistant Coach</th>' +
                '<th>Goalkeeper Coach</th>' +
                '</tr></thead>' +
                '</table>' +
                '</div>' +
                '</div>';
            return str;
        }
        $('button').click(function (e) {
            console.log('click');
        });
    
        function loadTrainTypes(event_id) {
            $.ajax({
                type: 'POST',
                url: SERVER_PATH + "course/getTrainType",
                headers: {	
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name
                },
                data: {
                    event_id: event_id,
                },
                dataType: 'json',
                success: function (response) {
                    var select = $('#trainTypeSelect');
                    select.empty();
                    if (response.status === "OK" && response.info.length > 0) {
                        response.info.forEach(function (item) {
                            console.log(item);
                            select.append('<option value="' + item.value + '">' + item.label + '</option>');
                        });
                    } else {
                        select.append('<option value="">No data available</option>');
                    }
                },
                error: function () {
                    $('#trainTypeSelect').html('<option value="">Error loading data</option>');
                }
            });
        }             

        function initEventPrice(event_id) {
            var price_editor;
            var price_table;

            function initializeEditorAndTable() {
                // Ensure the modal body has the correct overflow properties
                $('.modal-body').css({
                    'overflow-y': 'auto',
                    'max-height': 'calc(100vh - 100px)', // Adjust based on your modal height needs
                });

                price_editor = new $.fn.dataTable.Editor({
                    ajax: {
                        url: SERVER_PATH + 'event/setPriceEvent',
                        type: 'POST',
                        headers: {	
                            'x-user-id': $rootScope.user_id,
                            'x-user-email': $rootScope.user_name
                        },
                        data: function (d) {
                            d.event_id = event_id;
                            d.user_id = this.userId;
                        },
                        dataType: 'json',
                        complete: function (response) {
                            // Close the modal after save
                        },
                        error: function (xhr, status, error) {
                            // Handle error
                        },
                    },
                    idSrc: 'id',
                    table: '#tblEventPrice_' + event_id,
                    formOptions: {
                        main: {
                            onBlur: 'none',
                        },
                    },
                    fields: [
                        {
                            label: 'Type:',
                            type: 'hidden',
                            name: 'type',
                        },
                        {
                            label: 'Fee:',
                            name: 'fee',
                            attr: {
                                type: 'number',
                            },
                        },
                    ],
                    i18n: {
                        edit: {
                            button: 'Edit',
                            title: 'Edit price',
                            submit: 'Save',
                        },
                        error: {
                            system: 'System error, please contact administrator.',
                        },
                    },
                });

                price_table = $('#tblEventPrice_' + event_id).DataTable({
                    dom: '<"row"B>rt<"row"i>',
                    stateSave: true,
                    deferRender: true,
                    ajax: {
                        url: SERVER_PATH + 'event/getPriceEvent',
                        type: 'POST',
                        headers: {	
                            'x-user-id': $rootScope.user_id,
                            'x-user-email': $rootScope.user_name
                        },
                        data: {
                            event_id: event_id,
                        },
                        dataType: 'json',
                        complete: function (response) {
                            // No changes needed here
                        },
                        error: function (xhr, status, error) {
                            // Handle error
                        },
                    },
                    language: {
                        info: 'Showing _TOTAL_ total events',
                        infoEmpty: 'Showing 0 events',
                        lengthMenu: 'Show _MENU_ events',

                        paginate: {
                            previous: '<i class="fa fa-chevron-left"></i>',
                            next: '<i class="fa fa-chevron-right"></i>',
                        },
                    },
                    columns: [
                        { data: 'type',
                            render: function (data, type, row) {
                                // first word to uppercase hoa binh -> HOA Binh
                                return data.replace('_',' ');
                            }
                         },
                        { data: 'description' },
                        { data: 'fee', className: 'center' },
                    ],
                    select: {
                        style: 'single',
                    },
                    displayLength: -1,
                    buttons: [{ extend: 'edit', editor: price_editor }],
                });
            }

            initializeEditorAndTable();
        }

        function initGroupTable(data) {
            let event_id = data.id;
            let opts = [
                {
                    label: TEAM_BOYS,
                    value: TEAM_BOYS,
                },
                {
                    label: TEAM_GIRLS,
                    value: TEAM_GIRLS,
                },
                {
                    label: TEAM_MIXED,
                    value: TEAM_MIXED,
                },
            ];
            var group_editor = new $.fn.dataTable.Editor({
                ajax: {
                    url: SERVER_PATH + 'event/setGroups',
                    type: 'POST',
                    headers: {	
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name
                    },
                    data: {
                        event_id: event_id,
                    },
                    dataType: 'json',
                    complete: function (response) {},
                    error: function (xhr, status, error) {},
                },
                table: '#tblGroups_' + event_id,
                formOptions: {
                    main: {
                        onBlur: 'none',
                    },
                },
                fields: [
                    {
                        type: 'hidden',
                        name: 'groups.event_id',
                        default: event_id,
                    },
                    {
                        label: 'Name:',
                        name: 'groups.name',
                    },
                    {
                        label: 'Type:',
                        name: 'groups.type',
                        type: 'radio',
                        options: opts,
                        def: TEAM_BOYS,
                    },
                    {
                        label: 'Year:',
                        name: 'groups.year',
                        fieldInfo: 'Example: 2009 or 2009-2010',
                    },
                    {
                        label: 'Girl offer:',
                        name: 'groups.girl_offer',
                        type: 'radio',
                        options: [
                            { label: 'Yes', value: '1' },
                            { label: 'No', value: '0' },
                        ],
                        def: '0',
                    },
                ],
                i18n: {
                    create: {
                        button: 'New',
                        title: 'Add new group',
                        submit: 'Create',
                    },
                    edit: {
                        button: 'Edit',
                        title: 'Edit group',
                        submit: 'Save',
                    },
                    remove: {
                        button: 'Delete',
                        title: 'Delete group',
                        submit: 'Delete',
                        confirm: {
                            _: 'Are you sure you want to delete these group?',
                            1: 'Are you sure you want to delete this group?',
                        },
                    },
                    error: {
                        system: 'System error, please contact administrator.',
                    },
                },
            });

            group_editor.on('preOpen', function () {
                // alert('preOpen');
                $('.modal-backdrop').hide();
                $('.modal').hide();

                // only show girl_offer for PL Junior event
                if (data.type == EVENT_PL_JUNIOR) {
                    group_editor.field('groups.girl_offer').show();
                } else {
                    group_editor.field('groups.girl_offer').hide();
                }
            });
            group_editor.on('close', function () {
                // alert('close');
                $('.modal-backdrop').show();
                $('.modal').show();
            });

            group_editor.on('initEdit', function () {
                if (
                    data.type == EVENT_DISTRICT ||
                    data.type == EVENT_REGIONAL ||
                    data.type == EVENT_BEGINNER
                ) {
                    group_editor.field('groups.type').disable();
                }
            });

            group_editor.on('initCreate', function () {
                if (
                    data.type == EVENT_DISTRICT ||
                    data.type == EVENT_REGIONAL ||
                    data.type == EVENT_BEGINNER
                ) {
                    group_editor.field('groups.type').enable();
                    group_editor.message(
                        '<span style="color:red">*Note: Type of the group cannot be changed after creation</span>'
                    );
                }
            });

            // group_editor.on('preSubmit', () => {
            // 	var year_group = group_editor.field('year');
            // 	// if ( ! year_group.isMultiValue() ) {
            // 		if ( year_group.val().length === 0){
            // 			year_group.error( 'This field is required' );
            // 		} else {
            // 			year_group.val().forEach((e) => {
            // 				if ((! /^20\d{1,2}$/.test(e)) && (! /^U\d{1,2}\-U\d{1,2}$/.test(e))) {
            // 					year_group.error( '\'' + e + '\' is not according to the format. Please enter according to the below format' );
            // 					return false;
            // 				}
            // 			})
            // 		}
            // 	// }

            // 	if ( editor.inError() ) {
            // 		return false;
            // 	}
            // })

            group_table = $('#tblGroups_' + event_id).DataTable({
                dom: '<"row"B>rt<"row"i>',
                stateSave: true,
                deferRender: true,
                ajax: {
                    url: SERVER_PATH + 'event/getGroups',
                    type: 'POST',
                    headers: {	
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name
                    },
                    data: {
                        event_id: event_id,
                    },
                    dataType: 'json',
                    complete: function (response) {},
                    error: function (xhr, status, error) {},
                },
                language: {
                    info: 'Showing _TOTAL_ total groups',
                    infoEmpty: 'Showing 0 groups',
                    lengthMenu: 'Show _MENU_ groups',

                    paginate: {
                        previous: '<i class="fa fa-chevron-left"></i>',
                        next: '<i class="fa fa-chevron-right"></i>',
                    },
                },
                columns: [
                    { data: 'groups.name' },
                    { data: 'groups.type', className: 'center' },
                    { data: 'groups.year', className: 'center' },
                    {
                        data: 'groups.girl_offer',
                        className: 'center',
                        render: function (data, type, row) {
                            if (data == 1) {
                                return 'Yes';
                            } else {
                                return 'No';
                            }
                        },
                    },
                ],
                columnDefs: [
                    {
                        type: 'mixedGroup',
                        targets: 0,
                    },
                ],
                initComplete: function () {
                    if (data.type == EVENT_PL_JUNIOR) {
                        group_table.column(3).visible(true);
                    } else {
                        group_table.column(3).visible(false);
                    }
                },
                select: {
                    style: 'single',
                    // selector: 'td:first-child',
                },
                order: [[0, 'asc']],
                displayLength: -1,
                buttons: [
                    { extend: 'create', editor: group_editor },
                    { extend: 'edit', editor: group_editor },
                    { extend: 'remove', editor: group_editor },
                ],
            });
        }

        function initDistrictTable(event_id) {
            var district_editor = new $.fn.dataTable.Editor({
                ajax: {
                    url: SERVER_PATH + 'district/getDistrictOfEvent',
                    type: 'POST',
                    headers: {	
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name
                    },
                    data: {
                        event_id: event_id,
                    },
                    dataType: 'json',
                    complete: function (response) {},
                    error: function (xhr, status, error) {},
                },
                table: '#tblDictricts_' + event_id,
                formOptions: {
                    main: {
                        onBlur: 'none',
                    },
                },
                fields: [
                    {
                        type: 'hidden',
                        name: 'districts.event_id',
                        default: event_id,
                    },
                    {
                        label: 'Name:',
                        name: 'districts.name',
                    },
                    {
                        label: 'Chinese name:',
                        name: 'districts.chinese_name',
                    },
                ],
                i18n: {
                    create: {
                        button: 'New',
                        title: 'Add new district',
                        submit: 'Create',
                    },
                    edit: {
                        button: 'Edit',
                        title: 'Edit new district',
                        submit: 'Save',
                    },
                    remove: {
                        button: 'Delete',
                        submit: 'Delete',
                        confirm: {
                            _: 'Are you sure you want to delete?',
                            1: 'Are you sure you want to delete?',
                        },
                    },
                    error: {
                        system: 'System error, please contact administrator.',
                    },
                },
            });

            district_editor.on('preOpen', function () {
                // alert('preOpen');
                $('.modal-backdrop').hide();
                $('.modal').hide();
            });

            district_editor.on('close', function () {
                // alert('close');
                $('.modal-backdrop').show();
                $('.modal').show();
                setTimeout(() => {
                    // show loading sweetalert2
                    if (!$('body').hasClass('modal-open')) {
                        // add class modal-open
                        $('body').addClass('modal-open');
                    }
                }, 500);
            });

            district_editor.on('initEdit', function (e, json, data) {});

            // district_editor.on('initCreate', function (e, json, data) {
            //     district_editor.message('<span style="color:red">*Note: Gender cannot be changed after creation</span>');
            // });

            district_table = $('#tblDictricts_' + event_id).DataTable({
                dom: '<"row"B>lfrt<"row"i>p',
                ajax: {
                    url: SERVER_PATH + 'district/getDistrictOfEvent',
                    type: 'POST',
                    headers: {	
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name
                    },
                    data: {
                        event_id: event_id,
                    },
                    dataType: 'json',
                    complete: function (response) {},
                    error: function (xhr, status, error) {},
                },
                language: {
                    info: 'Showing _TOTAL_ total districts',
                    infoEmpty: 'Showing 0 districts',
                    lengthMenu: 'Show _MENU_ districts',

                    paginate: {
                        previous: '<i class="fa fa-chevron-left"></i>',
                        next: '<i class="fa fa-chevron-right"></i>',
                    },
                },
                columns: [
                    { data: 'districts.name' },
                    { data: 'districts.chinese_name', className: 'center' },
                ],
                select: {
                    style: 'single',
                    // selector: 'td:first-child',
                },
                order: [[0, 'asc']],
                displayLength: 10,
                buttons: [
                    { extend: 'create', editor: district_editor },
                    { extend: 'edit', editor: district_editor },
                    { extend: 'remove', editor: district_editor },
                ],
                lengthMenu: [
                    [10, 25, 50, 100, -1],
                    [10, 25, 50, 100, 'All'],
                ],
            });
        }

        function initCoachFeesTable(event_id, train_type) {
            // get event type by event_id from ajax
            var event_type = 'Individual';
            $.ajax({
                url: SERVER_PATH + 'event/getEventInfo',
                type: 'POST',
                headers: {	
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name
                },
                data: {
                    event_id: event_id,
                },
                dataType: 'json',
                complete: function (response) {
                    var jsonData = JSON.parse(response.responseText);
                    event_type = jsonData.info.type;
                },
                error: function (xhr, status, error) {},
            });
            $('button').click(function (e) {
                tableCoachFees.ajax.reload(null, false);
            });
            //set Groups
            var editorCoachFees = new $.fn.dataTable.Editor({
                ajax: {
                    url: SERVER_PATH + 'event/setCoachFees',
                    type: 'POST',
                    headers: {	
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name
                    },
                    data: {
                        event_id: event_id,
                        user_id: userId,
                    },
                    dataType: 'json',
                    complete: function (response) {
                        var jsonData = JSON.parse(response.responseText);
                        // --- may need to reload
                        if (DEVELOPMENT_ENVIRONMENT)
                            console.log('status = ' + jsonData.status);
                        if (jsonData.status == 'OK') {
                            if (DEVELOPMENT_ENVIRONMENT)
                                console.log('Before reload');
                            child_table.ajax.reload();
                        } else if (jsonData.status == 'ERROR') {
                            BootstrapDialog.show({
                                title: jsonData.status,
                                message: jsonData.message,
                                type: BootstrapDialog.TYPE_DANGER,
                            });
                        }
                    },
                    error: function (xhr, status, error) {},
                },
                table: '#tableCoachFees_' + event_id,
                formOptions: {
                    main: {
                        onBlur: 'none',
                    },
                },
                fields: [
                    {
                        type: 'hidden',
                        name: 'coach_fees.event_id',
                        default: event_id,
                    },
                    {
                        label: 'Level:',
                        name: 'coach_fees.level',
                        type: 'select',
                    },
                    {
                        label: 'Training Type:',
                        name: 'coach_fees.train_type',
                        type: 'select2',
                    },
                    {
                        label: 'Role:',
                        name: 'coach_fees.role',
                        type: 'select2',
                        default: 'Head Coach',
                    },
                    {
                        label: 'Fee:',
                        name: 'coach_fees.fee',
                        attr: {
                            type: 'number',
                        },
                    },
                ],

                i18n: {
                    create: {
                        button: 'New',
                        title: 'Add new fee',
                        submit: 'Create',
                    },
                    edit: {
                        button: 'Edit',
                        title: 'Edit fee',
                        submit: 'Save',
                    },
                    error: {
                        system: 'System error, please contact administrator.',
                    },
                },
            });

            editorCoachFees.on('close', function () {
                // check tag body have class modal-opens
                Swal.fire({
                    title: 'Loading...',
                    allowOutsideClick: false,
                    onBeforeOpen: () => {
                        Swal.showLoading();
                    },
                });
                setTimeout(function () {
                    // show loading sweetalert2
                    if (!$('body').hasClass('modal-open')) {
                        // add class modal-open
                        $('body').addClass('modal-open');
                    }
                    Swal.close();
                }, 500);
            });

            var tableCoachFees = $('#tableCoachFees_' + event_id).DataTable({
                dom: '<"row">rt<"row"i>',
                stateSave: true,
                deferRender: true,
                ajax: {
                    url: SERVER_PATH + 'event/getCoachFees',
                    type: 'POST',
                    headers: {	
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name
                    },
                    data: function(d) {
                        let defaultTrainType = (train_type === "PL Junior") ? "regular" : "normal";
                        let selectedTrainType = $('#trainTypeSelect').val();

                        d.event_id = event_id;
                        d.train_type = selectedTrainType ? selectedTrainType : defaultTrainType;

                    },
                    dataType: 'json',
                    complete: function (response) {},
                    error: function (xhr, status, error) {},
                },
                language: {
                    info: 'Showing _TOTAL_ total fees',
                    infoEmpty: 'Showing 0 fees',
                    lengthMenu: 'Show _MENU_ fees',
                    select: {
                        rows: {
                            _: 'You have selected %d fees',
                            0: 'Click an fee to select',
                            1: '1 fee selected',
                        },
                    },
                    paginate: {
                        previous: '<i class="fa fa-chevron-left"></i>',
                        next: '<i class="fa fa-chevron-right"></i>',
                    },
                },
                columns: [
                    {
                        data: null,
                        render: function (data, type, row) {
                            if (
                                typeof COACH_CERTIFICATE[
                                    data.coach_fees.level
                                ] == 'undefined'
                            ) {
                                console.warn(
                                    'COACH_CERTIFICATE[' +
                                        data.coach_fees.level +
                                        '] is undefined'
                                );
                                return '';
                            }
                            return COACH_CERTIFICATE[data.coach_fees.level][
                                'name'
                            ];
                        },
                    },
                    {
                        data: null,
                        title: 'Head Coach',
                        render: function (data, type, row) {
                            let fee = row.coach_fees.value.find(f => f.role === "Head Coach");
                            return fee ? fee.fee : '-';
                        },
                    },
                    {
                        data: null,
                        title: 'Assistant Coach',
                        render: function (data, type, row) {
                            let fee = row.coach_fees.value.find(f => f.role === "Assistant Coach");
                            return fee ? fee.fee : '-';
                        },
                    },
                    {
                        data: null,
                        title: 'Goalkeeper Coach',
                        render: function (data, type, row) {
                            let fee = row.coach_fees.value.find(f => f.role === "Goalkeeper Coach");
                            return fee ? fee.fee : '-';
                        },
                    },
                ],
                select: {
                    style: 'single',
                    selector: 'td',
                },
                order: [[0, 'asc']],
                displayLength: -1,
                // buttons: [
                //     { extend: 'create', editor: editorCoachFees },
                //     { extend: 'edit', editor: editorCoachFees },
                //     // { extend: "remove", editor: editorCoachFees },
                //     { extend: 'colvis', text: 'Columns' },
                // ],
            });

            editorCoachFees.on('initEdit', function (e, json, data) {
                editorCoachFees.disable('coach_fees.level');
            });

            editorCoachFees.on('initCreate', function (e, json, data) {
                editorCoachFees.enable('coach_fees.level');
            });

            $('#tableCoachFees_' + event_id).on('click', 'tbody td:not(:first-child)', function (e) {
                var cell = tableCoachFees.cell(this);
                if (!cell) return;
                
                var data = cell.data();
                if (!data.coach_fees || !Array.isArray(data.coach_fees.value)) return;
                
                var select = cell[0][0].column - 1;
                if (!data.coach_fees.value[select]) return;
                
                var currentValue = data.coach_fees.value[select].fee;
                $('.edit-input').each(function () {
                    var oldInput = $(this);
                    var oldValue = oldInput.val();
                    oldInput.parent().text(oldValue);
                });
                $(this).html('<input type="text" value="' + currentValue + '" class="form-control edit-input">');
                var input = $(this).find('.edit-input');
                setTimeout(function () {
                    input.focus();
                    input[0].setSelectionRange(currentValue.length, currentValue.length);
                }, 0);
            
                input.on('keypress', function (event) {
                    if (event.which === 13) {
                        var newValue = input.val().trim();
                        if (newValue !== currentValue) {
                            $.ajax({
                                url: SERVER_PATH + 'event/updateCoachFee',
                                type: 'POST',
                                data: {
                                    id: data.coach_fees.value[select].id,
                                    newValue: newValue
                                },
                                success: function (response) {
                                    $('#tableCoachFees_' + event_id).DataTable().ajax.reload(null, false);
                                },
                                error: function (xhr, status, error) {
                                    console.error(error);
                                }
                            });
                        }
                        input.blur();
                    }
                });
            });                        
        }

        // unbind event
        $('#event_table tbody').off('click', 'td.details-control');

        // Add event listener for opening and closing details
        $('#event_table tbody').on('click', 'td.details-control', function () {
            var tr = $(this).closest('tr');
            // console.log(tr);
            var row = table.row(tr);
            console.log(row.data());

            let event_id = table.row(tr).data().id;

            if (row.child.isShown()) {
                // This row is already open - close it
                row.child.hide();
                tr.removeClass('shown');
            } else {
                // Open this row
                row.child(format(row.data())).show();
                tr.addClass('shown');

                // add class for next tr (child row)
                $(this).closest('tr').next().addClass('child-row-detail');

                //set Groups
                var child_editor = new $.fn.dataTable.Editor({
                    ajax: {
                        url: SERVER_PATH + 'event/setChildEvents',
                        type: 'POST',
                        headers: {	
                            'x-user-id': $rootScope.user_id,
                            'x-user-email': $rootScope.user_name
                        },
                        data: {
                            event_id: event_id,
                            user_id: userId,
                        },
                        dataType: 'json',
                        complete: function (response) {
                            var jsonData = JSON.parse(response.responseText);
                            // --- may need to reload
                            if (DEVELOPMENT_ENVIRONMENT)
                                console.log('status = ' + jsonData.status);
                            if (jsonData.status == 'OK') {
                                if (DEVELOPMENT_ENVIRONMENT)
                                    console.log('Before reload');
                                child_table.ajax.reload();

                                // show prompt alert with sweetalert2
                                Swal.fire({
                                    icon: 'question',
                                    text: 'Do you want to reload page?',
                                    showCancelButton: true,
                                    cancelButtonText: 'No',
                                    confirmButtonText: 'Yes',
                                    allowOutsideClick: false,
                                }).then((result) => {
                                    if (result.isConfirmed) {
                                        location.reload();
                                    }
                                });
                            } else if (jsonData.status == 'ERROR') {
                                BootstrapDialog.show({
                                    title: jsonData.status,
                                    message: jsonData.message,
                                    type: BootstrapDialog.TYPE_DANGER,
                                });
                            }
                        },
                        error: function (xhr, status, error) {},
                    },
                    table: '#child_event_table_' + event_id,
                    formOptions: {
                        main: {
                            onBlur: 'none',
                        },
                    },
                    fields: [
                        {
                            label: 'Name:',
                            name: 'name',
                        },
                        {
                            label: 'Chinese Name:',
                            name: 'chinese_name',
                        },
                        {
                            label: 'Photo:',
                            name: 'photo',
                            type: 'upload',
                            dragDrop: false,
                            display: function (data) {
                                return (
                                    '<a class="openImage"><img src="' +
                                    UPLOAD_FILE_PATH +
                                    data +
                                    '" width="100%"></a>'
                                );
                            },
                            clearText: 'Clear',
                            noImageText: 'No image',
                        },
                        {
                            label: 'Description:',
                            name: 'description',
                            type: 'textarea',
                            attr: {
                                rows: 5,
                                maxlength: 255,
                            },
                        },
                        {
                            label: 'Chinese Description:',
                            name: 'chinese_description',
                            type: 'textarea',
                            attr: {
                                rows: 5,
                                maxlength: 255,
                            },
                        },
                        {
                            label: 'Type:',
                            name: 'type',
                            type: 'radio',
                            def: EVENT_GOLDENAGE,
                            options: [
                                {
                                    label: EVENT_GOLDENAGE,
                                    value: EVENT_GOLDENAGE,
                                },
                                {
                                    label: EVENT_TRAINING_SCHEME,
                                    value: EVENT_TRAINING_SCHEME,
                                },
                                {
                                    label: EVENT_SUMMER_SCHEME,
                                    value: EVENT_SUMMER_SCHEME,
                                },
                                {
                                    label: EVENT_DISTRICT,
                                    value: EVENT_DISTRICT,
                                },
                                {
                                    label: EVENT_PL_JUNIOR,
                                    value: EVENT_PL_JUNIOR,
                                },
                                {
                                    label: EVENT_REGIONAL,
                                    value: EVENT_REGIONAL,
                                },
                                {
                                    label: EVENT_BEGINNER,
                                    value: EVENT_BEGINNER,
                                },
                                { label: EVENT_ELDERLY, value: EVENT_ELDERLY },
                            ],
                        },
                        {
                            label: "Team Limit",
                            name: 'team_limit'                   			
                        },
                        {
                            label: 'Start date:',
                            name: 'start_date',
                            type: 'datetime',
                            format: 'DD-MMM-YYYY',
                        },
                        {
                            label: 'End date:',
                            name: 'end_date',
                            type: 'datetime',
                            format: 'DD-MMM-YYYY',
                        },
                        {
                            label: 'Start register date:',
                            name: 'start_register_date',
                            type: 'datetime',
                            format: 'DD-MMM-YYYY HH:mm:ss',
                        },
                        {
                            label: 'End register date:',
                            name: 'end_register_date',
                            type: 'datetime',
                            format: 'DD-MMM-YYYY HH:mm:ss',
                        },
                        {
                            label: 'Start checkout date:',
                            name: 'start_checkout_date',
                            type: 'datetime',
                            format: 'DD-MMM-YYYY HH:mm:ss',
                        },
                        {
                            label: 'Status:',
                            name: 'status',
                            titleAttr: 'Active or archived',
                            type: 'radio',
                            options: [
                                { label: EVENT_ACTIVE, value: EVENT_ACTIVE },
                                { label: EVENT_UPCOMING, value: EVENT_UPCOMING },
                                { label: EVENT_COMPLETED, value: EVENT_COMPLETED },
                                { label: EVENT_ARCHIVED, value: EVENT_ARCHIVED },
                            ],
                            def: EVENT_ACTIVE,
                        },
                        {
                            label: 'Color',
                            name: 'color',
                            type: 'colorpicker',
                        },
                        {
                            label: 'Level',
                            name: 'level',
                            type: 'select',
                            default: 'beginner',
                            options: eventLevels,
                        },
                    ],

                    i18n: {
                        create: {
                            button: 'New',
                            title: 'Add new event',
                            submit: 'Create',
                        },
                        edit: {
                            button: 'Edit',
                            title: 'Edit Event',
                            submit: 'Save',
                        },

                        error: {
                            system: 'System error, please contact administrator.',
                        },
                    },
                });

                var child_table = $('#child_event_table_' + event_id).DataTable(
                    {
                        dom: '<"row"B>rt<"row"i>',
                        stateSave: true,
                        deferRender: true,
                        ajax: {
                            url: SERVER_PATH + 'event/getChildEvents',
                            type: 'POST',
                            headers: {	
                                'x-user-id': $rootScope.user_id,
                                'x-user-email': $rootScope.user_name
                            },
                            data: {
                                event_id: event_id,
                            },
                            dataType: 'json',
                            complete: function (response) {},
                            error: function (xhr, status, error) {},
                        },
                        language: {
                            info: 'Showing _TOTAL_ total events',
                            infoEmpty: 'Showing 0 events',
                            lengthMenu: 'Show _MENU_ events',
                            select: {
                                rows: {
                                    _: 'You have selected %d events',
                                    0: 'Click an event to select',
                                    1: '1 event selected',
                                },
                            },
                            paginate: {
                                previous: '<i class="fa fa-chevron-left"></i>',
                                next: '<i class="fa fa-chevron-right"></i>',
                            },
                        },
                        columns: [
                            {
                                data: 'photo',
                                className: 'avatar',
                                orderable: false,
                                render: function (data) {
                                    if (data !== null && data !== '') {
                                        return (
                                            '<img src="' +
                                            UPLOAD_FILE_PATH +
                                            data +
                                            '">'
                                        );
                                    } else {
                                        return (
                                            '<img src="' +
                                            SYSTEM_IMAGE_PATH +
                                            'favicon.png">'
                                        );
                                    }
                                },
                            },
                            { data: 'name', className: 'center' },
                            { data: 'start_date', className: 'center' },
                            { data: 'end_date', className: 'center' },
                            {
                                data: 'start_register_date',
                                className: 'center',
                            },
                            { data: 'end_register_date', className: 'center' },
                            {
                                data: 'start_checkout_date',
                                className: 'center',
                            },
                            { data: 'type', className: 'center' },
                            {
                                data: "team_limit",
                                className: 'center',
                                render: function (data, type, row) {
                                    let result = data;
                                    if(data){
                                        result = (data == 0 )? 'Unlimited' : data;
                                        
                                    }

                                    return (row.type == EVENT_GOLDENAGE) ? result : '';
                                }
                            },
                            { data: 'status', className: 'center' },
                            {
                                data: 'color',
                                render: function (data, type, row) {
                                    if (type === 'display') {
                                        return (
                                            '<div class="colorSquare" style="background:' +
                                            data +
                                            ';"></div>'
                                        );
                                    }
                                    return data;
                                },
                                className: 'dt-body-center',
                            },
                            {
                                data: null,
                                render: function (data) {
                                    let actions =
                                        '<button type="button" data-toggle="modal" data-target="#modal-group" class="btn btn-primary modal-group" style="width:100px">Edit Group</button>';
                                    if (
                                        data.type.toLowerCase() ===
                                            'district' ||
                                        data.type.toLowerCase() ===
                                            'regional' ||
                                        data.type.toLowerCase() === 'beginner'
                                    ) {
                                        actions =
                                            '<button type="button" data-toggle="modal" data-target="#modal-group" class="btn btn-primary modal-group" style="width:100px">Edit Group</button> <button type="button" data-toggle="modal" data-target="#modal-district" class="btn btn-success modal-district" style="margin-top:5px;width:100px">Edit District</button>';
                                    }
                                    return actions;
                                },
                            },
                            {
                                data: null,
                                render: function (data) {
                                    return '<button type="button" data-toggle="modal" data-target="#modal-price" class="btn btn-primary modal-price">Edit Price</button>';
                                },
                            },
                            {
                                data: null,
                                render: function (data) {
                                    return '<button type="button" data-toggle="modal" data-target="#modal-coach-fees" class="btn btn-primary modal-coach-fees">Coach Fees</button>';
                                },
                            },
                            {
                                data: null,
                                render: function (data) {
                                    return (
                                        '<a  data-match-route="/certificates/' +
                                        data.id +
                                        '" href="#/certificates/' +
                                        data.id +
                                        '" class="btn btn-primary">Certificates</a>'
                                    );
                                },
                            }
                           
                        ],
                        select: {
                            style: 'single',
                            // selector: 'td:first-child',
                        },
                        order: [
                            [2, 'asc'],
                            [1, 'desc'],
                            [0, 'desc'],
                        ],
                        displayLength: -1,
                        buttons: [
                            { extend: 'create', editor: child_editor },
                            { extend: 'edit', editor: child_editor },
                            // { extend: "remove", editor: child_editor },

                            { extend: 'colvis', text: 'Columns' },
                        ],
                    }
                );

                $('#child_event_table_' + event_id).on(
                    'click',
                    'tbody .modal-group',
                    function (e) {
                        var $row = $(this).closest('tr');

                        // Get row data
                        var data = child_table.row($row).data();

                        if (DEVELOPMENT_ENVIRONMENT) console.log(data);
                        var msg = getModalGroupTableHtml(data.id);

                        // Show dialog
                        BootstrapDialog.show({
                            title: data.name + ' - Edit Groups',
                            message: msg,
                            onshown: function (dialogRef) {
                                initGroupTable(data);
                            },
                        });
                    }
                );

                $('#child_event_table_' + event_id).on(
                    'click',
                    'tbody .modal-district',
                    function (e) {
                        var $row = $(this).closest('tr');

                        // Get row data
                        var data = child_table.row($row).data();

                        if (DEVELOPMENT_ENVIRONMENT) console.log(data);
                        var msg = getModalDistrictTableHtml(data.id);

                        // Show dialog
                        BootstrapDialog.show({
                            title: data.name + ' - Edit Districts',
                            message: msg,
                            onshown: function (dialogRef) {
                                initDistrictTable(data.id);
                            },
                        });
                    }
                );

                child_editor.on('initEdit', function (e, json, data) {
                    child_editor.hide('type');
                });

                child_editor.on('initCreate', function (e, json, data) {
                    child_editor.show('type');
                });

                child_editor.dependent('type', function (val) {
                    console.warn(val);
                    if (val == EVENT_SUMMER_SCHEME) {
                        return { show: ['start_checkout_date'], hide: ['team_limit'] };
                    } else if (val == EVENT_GOLDENAGE){
                        return { show: ['team_limit'], hide: ['start_checkout_date'] };
                    }
                    else {
                        return { hide: ['start_checkout_date', 'team_limit'] };
                    }
                });

                $('#child_event_table_' + event_id).on(
                    'click',
                    'tbody .modal-price',
                    function (e) {
                        var $row = $(this).closest('tr');

                        // Get row data
                        var data = child_table.row($row).data();

                        if (DEVELOPMENT_ENVIRONMENT) console.log(data);

                        var msg = getModalEventPrice(data.id);

                        // Show dialog
                        price_popup = BootstrapDialog.show({
                            title: data.name + ' - Edit Price Event',
                            message: msg,
                            onshown: function (dialogRef) {
                                initEventPrice(data.id);
                            },
                        });
                    }
                );

                $('#child_event_table_' + event_id).on(
                    'click',
                    'tbody .modal-coach-fees',
                    function (e) {
                        var $row = $(this).closest('tr');

                        // Get row data
                        var data = child_table.row($row).data();

                        if (DEVELOPMENT_ENVIRONMENT) console.log(data);

                        var msg = getModalCoachFees(data.id);
                        // Show dialog
                        BootstrapDialog.show({
                            title: data.name + ' - Coach Fees',
                            message: msg,
                            onshown: function (dialogRef) {
                                initCoachFeesTable(data.id,data.type);
                                loadTrainTypes(data.id);
                            },
                        });
                    }
                );
            }
        });         
    };

    $scope.initTableEventApp = function () {
        if ($.fn.dataTable.isDataTable('#event_order_table')) {
            $('#event_order_table').DataTable().destroy();
        }

        var editor = new $.fn.dataTable.Editor({
            ajax: {
                type: 'POST',
                url: SERVER_PATH + 'event/setEventOrders',
                headers: {	
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name
                },
                data: {},
                async: false,
                dataType: 'json',
                complete: function (response) {
                    var jsonData = JSON.parse(response.responseText);
                    // --- may need to reload
                    if (DEVELOPMENT_ENVIRONMENT)
                        console.log('status = ' + jsonData.status);
                    if (jsonData.status == 'OK') {
                        table.ajax.reload();
                    }
                },
                error: function (xhr, status, error) {},
            },
            table: '#event_order_table',
            formOptions: {
                main: {
                    onBlur: 'none',
                },
            },
            fields: [
                {
                    label: 'order',
                    name: 'events.order',
                    type: 'hidden',
                },
            ],
        });
        $('#event_order_table').DataTable({
            dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
            stateSave: true,
            deferRender: true,
            ajax: {
                url: SERVER_PATH + 'event/getEventOrders',
                type: 'POST',
                headers: {	
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name
                },
                data: {},
                dataType: 'json',
                complete: function (response) {
                    // response = JSON.parse(response.responseText);
                },
                error: function (xhr, status, error) {},
            },
            language: {
                paginate: {
                    previous: '<i class="fa fa-chevron-left"></i>',
                    next: '<i class="fa fa-chevron-right"></i>',
                },
            },
            columns: [
                {
                    data: 'events.order',
                    orderable: false,
                    visible: false,
                },
                {
                    data: 'events.name',
                    orderable: false,
                },
                {
                    data: 'events.chinese_name',
                    orderable: false,
                },
                {
                    data: 'events.level',
                    orderable: false,
                },
                {
                    data: 'events.type',
                    orderable: false,
                },
            ],
            select: {
                style: 'single',
            },
            buttons: [],
            order: [
                [3, 'asc'],
                [0, 'asc'],
            ],
            lengthMenu: [
                [10, 25, 50, 100, -1],
                [10, 25, 50, 100, 'All'],
            ],
            rowReorder: {
                dataSrc: 'events.order',
                editor: editor,
            },
            "columnDefs": [
                {
                    "type": "event-level",
                    "targets": 3
                }
            ],
            drawCallback: function (settings) {
                var api = this.api();
                var rows = api.rows({ page: 'current' }).nodes();
                var last = null;

                api.column(3, { page: 'current' })
                    .data()
                    .each(function (group, i) {
                        // get number of visible columns
                        var colCount = 4;

                        if (last !== group) {
                            $(rows)
                                .eq(i)
                                .before(
                                    '<tr class="group"><td colspan="' +
                                        colCount +
                                        '"><b>' +
                                        group +
                                        '</b></td></tr>'
                                );

                            last = group;
                        }
                    });
            },
        });
    };

    $scope.initTab = function () {
        if (active_tab.trim() == 'Event') {
            $scope.initTableEvent();
        }
        if (active_tab.trim() == 'Event on app') {
            $scope.initTableEventApp();
        }
    };

    setTimeout(function () {
        $scope.initTab();
    }, 500);

    $('.nav-tabs a').on('shown.bs.tab', function (event) {
        var tab = $(event.target).text();
        active_tab = tab;
        $scope.initTab();
    });
});
