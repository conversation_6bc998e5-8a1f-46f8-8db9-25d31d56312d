app.controller(
    'teamDistrictCtrl',
    function ($scope, $rootScope, $routeParams, $http) {
        $('#page-wrapper').removeClass('nav-small');

        // get info event
        var event_id = $routeParams.id;
        jQuery.ajax({
            type: 'POST',
            url: SERVER_PATH + 'event/getGroupsByEvent',
            async: false,
            headers: {	
                'x-user-id': $rootScope.user_id,
                'x-user-email': $rootScope.user_name
            },
            data: {
                event_id: event_id,
            },
            dataType: 'json',
            complete: function (response) {
                var jsonData = JSON.parse(response.responseText);
                if (jsonData.status == 'OK') {
                    $scope.groups = jsonData.info;
                }
            },
        });

        // get districts in current events
        jQuery.ajax({
            type: 'POST',
            url:
                SERVER_PATH +
                'district-session/adminGetDistrictsInCurrentEvent',
            async: false,
            headers: {	
                'x-user-id': $rootScope.user_id,
                'x-user-email': $rootScope.user_name
            },
            data: {
                event_id: event_id,
            },
            dataType: 'json',
            complete: function (response) {
                var jsonData = JSON.parse(response.responseText);
                if (jsonData.status == 'OK') {
                    $scope.districts = jsonData.data;
                }
            },
        });

        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            'ageGroup-pre': (a) => parseInt(a.replace('U', '')),
            'ageGroup-asc': (a, b) => a - b,
            'ageGroup-desc': (a, b) => b - a,
        });

        var uploadEditor = new $.fn.dataTable.Editor({
            fields: [
                {
                    label: 'CSV file:',
                    name: 'csv',
                    type: 'upload',
                    fieldInfo:
                        'You can download the sample file <a href="' +
                        SYSTEM_IMAGE_PATH +
                        'Template_Import_Sessions.xlsx" target="_blank">here</a>',
                    ajax: function (files, done) {
                        var form = new FormData();
                        form.append('file', files[0]);
                        form.append('user_id', $rootScope.user_id);
                        form.append('event_id', event_id);
                        var settings = {
                            url:
                                SERVER_PATH +
                                'district-session/uploadSessionFiles',
                            method: 'POST',
                            headers: {	
                                'x-user-id': $rootScope.user_id,
                                'x-user-email': $rootScope.user_name
                            },
                            timeout: 0,
                            processData: false,
                            mimeType: 'multipart/form-data',
                            contentType: false,
                            data: form,
                            beforeSend: function (xhr) {
                                Swal.fire({
                                    title: 'Please Wait!',
                                    allowOutsideClick: false,
                                    didOpen: () => {
                                        Swal.showLoading();
                                    },
                                });
                            },
                        };

                        $.ajax(settings).done(function (response) {
                            Swal.close();

                            var results = JSON.parse(response);

                            if (results.status == 'ERROR') {
                                uploadEditor
                                    .field('csv')
                                    .error(
                                        ' Import was failed: ' + results.message
                                    );
                            } else {
                                uploadEditor.close();
                                Swal.fire({
                                    type: 'success',
                                    icon: 'success',
                                    title: 'Success',
                                    text: results.message,
                                    confirmButtonClass: 'btn btn-primary',
                                    buttonsStyling: false,
                                });
                                tableTeams.ajax.reload();
                            }
                        });
                    },
                },
            ],
        });

        jQuery.ajax({
            type: 'POST',
            url: SERVER_PATH + 'event/getEventInfo',
            async: false,
            headers: {	
                'x-user-id': $rootScope.user_id,
                'x-user-email': $rootScope.user_name
            },
            data: {
                event_id: event_id,
            },
            dataType: 'json',
            complete: function (response) {
                var jsonData = JSON.parse(response.responseText);
                var event = jsonData.info;
                $scope.selectedEvent = jsonData.info;
                $scope.selectedEvent.id = event_id;
                $scope.selectedEventType =
                    $scope.selectedEvent.type == 'District'
                        ? EVENT_DISTRICT
                        : EVENT_SUMMER_SCHEME;
                event_name = event.name;
                event_type = event.type;
                normalizedType = normalizeEventType(event_type);
            },
        });
        // console.log('paymentsCtrl - event_id, name, type  = ' + event_id + ', ' + event_name + ', ' + event_type);
        $scope.event_name = event_name;
        $scope.event_type = event_type;
        initTeamDistricts();

        $scope.normalizedType = normalizedType;

        function normalizeEventType(eventType) {
            switch (eventType) {
                case 'Summer Scheme':
                    return 'summer-scheme';
                case 'Regional':
                    return 'regional';
                case 'PL Junior':
                    return 'pl-junior';
                case 'Golden Age':
                    return 'golden-age';
                case 'Beginner':
                    return 'beginner';
                case 'District':
                    return 'district';
                default:
                    return '';
            }
        }

        // init table team_districts
        function initTeamDistricts() {
            var editorTeams = new $.fn.dataTable.Editor({
                ajax: {
                    type: 'POST',
                    url: SERVER_PATH + 'district/setSupervisorTeams',
                    headers: {	
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name
                    },
                    data: {
                        user_id: $rootScope.user_id,
                        event_id: event_id,
                    },
                    dataType: 'json',
                    complete: function (response) {
                        var jsonData = JSON.parse(response.responseText);
                        // --- may need to reload
                        if (DEVELOPMENT_ENVIRONMENT)
                            console.log('status = ' + jsonData.status);
                        if (jsonData.status == 'OK') {
                            if (DEVELOPMENT_ENVIRONMENT)
                                console.log('Before reload');
                            table.ajax.reload();
                        }
                    },
                    error: function (xhr, status, error) {},
                },
                table: '.team_district_tbl',
                formOptions: {
                    main: {
                        onBlur: 'none',
                    },
                },
                i18n: {
                    edit: {
                        button: 'Manage Supervisors',
                        title: 'Manage Supervisors',
                        submit: 'Save',
                    },
                    error: {
                        system: 'System error, please contact shipping user.',
                    },
                },
                fields: [
                    {
                        label: 'Supervisors:',
                        name: 'parens[].id',
                        type: 'select2',
                        opts: {
                            multiple: true,
                            placeholder: 'Search and select supervisor...',
                        },
                    },
                ],
            });

            tableTeams = $('.team_district_tbl').DataTable({
                dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
                stateSave: false,
                deferRender: true,
                bDestroy: true,
                ajax: {
                    url: SERVER_PATH + 'district/getTeamsDistrict',
                    type: 'POST',
                    headers: {	
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name
                    },
                    data: {
                        event_id: $scope.selectedEvent.id,
                        user_id: $rootScope.user_id,
                    },
                    dataType: 'json',
                    complete: function (response) {},
                    error: function (xhr, status, error) {},
                },
                language: {
                    paginate: {
                        previous: '<i class="fa fa-chevron-left"></i>',
                        next: '<i class="fa fa-chevron-right"></i>',
                    },
                },
                columns: [
                    {
                        data: null,
                        render: function (data) {
                            return (
                                '<a data-match-route="/event/' +
                                $scope.selectedEvent.id +
                                '/team_district/' +
                                data.team_districts.id +
                                '/details?team_name=' +
                                data.team_districts.name +
                                '" href="#/event/' +
                                $scope.selectedEvent.id +
                                '/team_district/' +
                                data.team_districts.id +
                                '/details?team_name=' +
                                data.team_districts.name +
                                '">' +
                                data.team_districts.name +
                                '</a>'
                            );
                        },
                    },
                    {
                        data: 'groups.name',
                    },
                    { data: 'districts.name' },
                    { data: 'parens', render: '[<br> ].email' },
                    {
                        data: 'team_districts.train_max_player',
                        visible: $scope.selectedEvent.type == EVENT_BEGINNER,
                    },
                    {
                        data: 'team_districts.main_team',
                        render: function (data, type, full, meta) {
                            var checked = data == 1 ? 'checked' : '';
                            return `
							<div class="onoffswitch onoffswitch-danger">
								<input type="checkbox" name="onoffswitch2" class="onoffswitch-checkbox" id="switch${full.team_districts.id}" ${checked}>
								<label class="onoffswitch-label" for="switch${full.team_districts.id}">
									<div class="onoffswitch-inner"></div>
									<div class="onoffswitch-switch"></div>
								</label>
							</div>
						`;
                        },
                    },
                    {
                        data: 'role',
                        className: 'center',
                        render: function (data) {
                            // console.log(data);
                            if (
                                parseInt(data) == ROLE_HEAD_COACH ||
                                parseInt(data) == USER_SUPER_ADMIN ||
                                parseInt(data) == USER_LEAGUE_ADMIN ||
                                parseInt(data) == USER_SUPERVISOR_COACH ||
                                parseInt(data) == USER_SUPERVISOR
                            ) {
                                actions =
                                    '<button type="button" data-toggle="modal" data-target="#modal-coaches" class="btn btn-primary modal-coaches" >Manage Coaches</button>';
                                return actions;
                            }
                            return '';
                        },
                    },
                ],
                initComplete: function () {
                    var age_group_column = {
                        orderColumn: 1,
                        elementId: 'age-group-content',
                        selectId: 'selType',
                        label: 'Filter by age group',
                    };

                    filterColumns = [age_group_column];

                    filterColumns.forEach((item) => {
                        this.api()
                            .columns(item.orderColumn)
                            .every(function () {
                                var column = this;

                                // set label
                                $(`#${item.elementId}`).html(
                                    `<label>${item.label}</label>`
                                );

                                // create select element
                                var select = $(
                                    `<select id="${item.orderColumn}" class="form-control"><option value="">All</option></select>`
                                )
                                    .appendTo($(`#${item.elementId}`))
                                    .on('change', function () {
                                        var val =
                                            $.fn.dataTable.util.escapeRegex(
                                                $(this).val()
                                            );

                                        column
                                            .search(
                                                val ? '^' + val + '$' : '',
                                                true,
                                                false
                                            )
                                            .draw();
                                    })
                                    .select2();
                                var column_data = column.data();
                                var select_data = [];
                                column_data.map((item) => {
                                    if (item != null) {
                                        item.indexOf(', ') > 0
                                            ? (item = item.split(', '))
                                            : (item = [item]);
                                        item.forEach((item) => {
                                            select_data.push(item);
                                        });
                                    }
                                });

                                select_data
                                    .filter(onlyUnique)
                                    .sort(function (a, b) {
                                        a = parseInt(a.replace('U', ''));
                                        b = parseInt(b.replace('U', ''));
                                        return a - b;
                                    })
                                    .map(function (d, j) {
                                        select.append(
                                            `<option value="${d}">${d}</option>`
                                        );
                                    });
                            });
                    });
                },
                select: {
                    // style: 'single',
                    selector: 'td:not(:last-child)',
                },
                order: [[1, 'asc']],
                buttons: [
                    {
                        text: 'Add new team',
                        className: 'btn-add-new',
                        action: function (e, dt, node, config) {
                            var districts = $scope.districts.map(function (
                                item
                            ) {
                                return {
                                    value: item.id,
                                    label: item.name,
                                };
                            });

                            var groups = $scope.groups.map(function (item) {
                                return {
                                    value: item.id,
                                    label: item.name,
                                };
                            });
                            let fields = [
                                {
                                    label: 'Select Group:',
                                    name: 'group_id',
                                    type: 'select',
                                    options: groups,
                                },
                                {
                                    label: 'Select District:',
                                    name: 'district_id',
                                    type: 'select',
                                    options: districts,
                                },
                                {
                                    label: 'Team Name:',
                                    name: 'team_name',
                                },
                            ];

                            if ($scope.selectedEvent.type == EVENT_BEGINNER) {
                                fields.push({
                                    label: 'Max Train Player:',
                                    name: 'train_max_players',
                                    default: 20,
                                });
                            }

                            var editor = new $.fn.dataTable.Editor({
                                ajax: {
                                    type: 'POST',
                                    url:
                                        SERVER_PATH +
                                        'district-session/adminDistrictAddTeams',
                                    dataType: 'json',
                                    headers: {	
                                        'x-user-id': $rootScope.user_id,
                                        'x-user-email': $rootScope.user_name
                                    },
                                    complete: function (response) {
                                        if (
                                            response.responseJSON.status ==
                                            'ERROR'
                                        ) {
                                            if (
                                                typeof response.responseJSON
                                                    .fieldErrors == 'undefined'
                                            ) {
                                                Swal.fire({
                                                    title: 'ERROR!',
                                                    text: response.responseJSON
                                                        .message,
                                                    icon: 'error',
                                                    type: 'error',
                                                });
                                            }
                                        } else {
                                            Swal.fire({
                                                title: 'SUCCESS!',
                                                text: response.responseJSON
                                                    .message,
                                                icon: 'success',
                                                type: 'success',
                                            });

                                            tableTeams.ajax.reload();
                                        }
                                    },
                                    error: function (xhr, status, error) {},
                                },
                                formOptions: {
                                    main: {
                                        onBlur: 'none',
                                    },
                                },
                                fields: fields,
                            });

                            editor
                                .title('Add Teams')
                                .buttons({
                                    label: 'Save',
                                    fn: function () {
                                        this.submit();
                                    },
                                })
                                .create()
                                .open();
                        },
                    },
                    {
                        text: 'Edit team',
                        className: 'btn-add-new',
                        extend: 'selectedSingle',
                        action: function (e, dt, node, config) {
                            let team_selected = tableTeams
                                .rows({ selected: true })
                                .data()
                                .toArray();
                            let team_name =
                                team_selected[0].team_districts.name;

                            let team_id = team_selected[0].team_districts.id;

                            let fields = [
                                {
                                    label: 'Team Name:',
                                    name: 'team_name',
                                    default: team_name,
                                },
                            ];

                            if ($scope.selectedEvent.type == EVENT_BEGINNER) {
                                fields.push({
                                    label: 'Max Train Player:',
                                    name: 'train_max_players',
                                    default:
                                        team_selected[0].team_districts
                                            .train_max_player,
                                });
                            }

                            var editor = new $.fn.dataTable.Editor({
                                ajax: {
                                    type: 'POST',
                                    url:
                                        SERVER_PATH +
                                        'district/adminEditDistrictTeams',
                                    dataType: 'json',
                                    headers: {	
                                        'x-user-id': $rootScope.user_id,
                                        'x-user-email': $rootScope.user_name
                                    },
                                    data: {
                                        team_id: team_id,
                                    },
                                    complete: function (response) {
                                        tableTeams.ajax.reload();
                                    },
                                    error: function (xhr, status, error) {},
                                },
                                formOptions: {
                                    main: {
                                        onBlur: 'none',
                                    },
                                },
                                fields: fields,
                            });
                            editor
                                .title('Edit Teams')
                                .buttons({
                                    label: 'Save',
                                    fn: function () {
                                        this.submit();
                                    },
                                })
                                .create()
                                .open();
                        },
                    },
                    {
                        text: 'Delete teams',
                        className: 'btn-delete',
                        extend: 'selectedSingle',
                        action: function (e, dt, node, config) {
                            // get selected rows
                            let rows = dt.rows({ selected: true }).data()[0];
                            let team_district_id = rows.team_districts.id;

                            jQuery.ajax({
                                type: 'POST',
                                url:
                                    SERVER_PATH +
                                    'district-session/adminDeleteDistrictTeams',
                                async: false,
                                headers: {	
                                    'x-user-id': $rootScope.user_id,
                                    'x-user-email': $rootScope.user_name
                                },
                                data: {
                                    team_id: team_district_id,
                                },
                                dataType: 'json',
                                complete: function (response) {
                                    if (
                                        response.responseJSON.status == 'ERROR'
                                    ) {
                                        Swal.fire({
                                            title: 'ERROR!',
                                            text: response.responseJSON.message,
                                            icon: 'error',
                                            type: 'error',
                                        });
                                    } else {
                                        Swal.fire({
                                            title: 'SUCCESS!',
                                            text: response.responseJSON.message,
                                            icon: 'success',
                                            type: 'success',
                                        });

                                        tableTeams.ajax.reload();
                                    }
                                },
                            });
                        },
                    },
                    {
                        extend: 'edit',
                        // extend: "selectedSingle",
                        className: 'btn-manage-supervisor',
                        editor: editorTeams,
                    },
                    ...$scope.event_type != EVENT_BEGINNER?
                    [
                        {
                            extend: 'selected',
                            name: 'generate_selection_sheet',
                            text: 'Generate Selection Sheet',
                            titleAttr:
                                'Generate and download player selection sheet',
                            action: function () {
                                let teamsSelected = tableTeams
                                    .rows({ selected: true })
                                    .data()
                                    .toArray();

                                let team_ids = [];
                                teamsSelected.forEach((team) => {
                                    team_ids.push(team.team_districts.id);
                                });

                                if (
                                    team_ids !== undefined &&
                                    team_ids.length > 0
                                ) {
                                    if (team_ids.length > 10) {
                                        Swal.fire({
                                            title: 'Warning',
                                            text: 'You can only generate attendance sheet for 10 teams at once. Please select less than 10 teams.',
                                            icon: 'warning',
                                            type: 'warning',
                                            confirmButtonText: 'OK',
                                            confirmButtonColor: '#ed1c24',
                                        });
                                    } else {
                                        let editor = new $.fn.dataTable.Editor({
                                            ajax: {
                                                type: 'POST',
                                                url:
                                                    SERVER_PATH +
                                                    'team/autoCreateSelectionAttendanceSheets',
                                                headers: {	
                                                    'x-user-id': $rootScope.user_id,
                                                    'x-user-email': $rootScope.user_name
                                                },
                                                data: {
                                                    type: 'approved',
                                                    team_ids:
                                                        team_ids.join(','),
                                                },
                                                dataType: 'json',
                                                complete: function (response) {
                                                    console.log(response);
                                                    var jsonData = JSON.parse(
                                                        response.responseText
                                                    );

                                                    if (
                                                        jsonData.status ==
                                                        'ERROR'
                                                    ) {
                                                        Swal.fire({
                                                            title: 'Error',
                                                            html: jsonData.message,
                                                            icon: 'error',
                                                            type: 'error',
                                                            confirmButtonText:
                                                                'OK',
                                                            confirmButtonColor:
                                                                '#ed1c24',
                                                        });
                                                    } else {
                                                        if (
                                                            jsonData.status ==
                                                            'OK'
                                                        ) {
                                                            fileNames =
                                                                jsonData.info;
                                                            fileNames.forEach(
                                                                (fileName) => {
                                                                    downloadFile(
                                                                        PRODUCT_IMAGE_PATH +
                                                                            fileName
                                                                                .split(
                                                                                    ' '
                                                                                )
                                                                                .join(
                                                                                    '%20'
                                                                                ),
                                                                        fileName
                                                                    );
                                                                }
                                                            );
                                                            Swal.fire({
                                                                title: 'Success',
                                                                html: jsonData.message,
                                                                icon: 'success',
                                                                type: 'success',
                                                                confirmButtonText:
                                                                    'OK',
                                                                confirmButtonColor:
                                                                    '#ed1c24',
                                                            });
                                                        }
                                                    }
                                                },
                                            },
                                        });
                                        let swal = null;
                                        editor.on(
                                            'preSubmit',
                                            function (e, data, action) {
                                                swal = Swal.fire({
                                                    title: 'Processing!',
                                                    html: 'Please wait...',
                                                    allowEscapeKey: false,
                                                    allowOutsideClick: false,
                                                    confirmButtonColor:
                                                        '#ed1c24',
                                                    onBeforeOpen: () => {
                                                        Swal.showLoading();
                                                    },
                                                });
                                            }
                                        );
                                        editor.on(
                                            'submitComplete',
                                            function (e, data, action) {
                                                swal.close();
                                            }
                                        );
                                        editor.create(false).submit();
                                    }
                                }
                            },
                        },
                    ]:[]
                    ,
                    {
                        text: 'Import sessions from CSV',
                        className: 'session-import',
                        action: function (e, dt, node, config) {
                            uploadEditor.create({
                                title: 'Import file sessions',
                            });
                        },
                    },
                ],
                lengthMenu: [
                    [10, 25, 50, 100, -1],
                    [10, 25, 50, 100, 'All'],
                ],
                columnDefs: [
                    {
                        type: 'ageGroup',
                        targets: 1,
                    },
                ],
            });

            tableTeams.on('init', function () {
                // hide buttons
                getRoleByUserID($scope.user_id).then(function (data) {
                    if (data.role != USER_SUPER_ADMIN && data.role != USER_LEAGUE_ADMIN) {
                        tableTeams.buttons('.session-import').remove();
                        tableTeams.buttons('.btn-add-new').remove();
                        tableTeams.buttons('.btn-delete').remove();
                        tableTeams.buttons('.btn-manage-supervisor').remove();
                    }
                });
            });

            $('.team_district_tbl').on(
                'click',
                'tbody .modal-coaches',
                function () {
                    let row = $(this).closest('tr');
                    let data = tableTeams.row(row).data();

                    var msg = getModalCoachesTableHtml(data.team_districts.id);
                    // Show dialog
                    BootstrapDialog.show({
                        title: 'Manage Coaches - ' + data.team_districts.name,
                        message: msg,
                        size: BootstrapDialog.SIZE_WIDE,
                        onshown: function (dialogRef) {
                            initCoachTbl(data);
                        },
                    });
                }
            );

            $('.team_district_tbl').on(
                'click',
                'tbody tr td .onoffswitch-checkbox',
                function () {
                    var $row = $(this).closest('tr');
                    var data = tableTeams.row($row).data();

                    var status = $(this).prop('checked') ? 1 : 0;
                    changeStatusTeamDistrict(data.team_districts.id, status);
                }
            );
        }

        function changeStatusTeamDistrict(team_district_id, status) {
            var title = 'Team District';
            var action = status == 1 ? 'Active' : 'Inactive';
            var message = 'Are you sure you want to ' + action + ' this team?';

            var dialog = BootstrapDialog.confirm(
                title,
                message,
                function (result) {
                    if (result) {
                        dialog.close();
                        jQuery.ajax({
                            type: 'POST',
                            url:
                                SERVER_PATH +
                                'district/changeDistrictTeamMainTeam',
                            async: false,
                            headers: {	
                                'x-user-id': $rootScope.user_id,
                                'x-user-email': $rootScope.user_name
                            },
                            data:
                                'team_id=' +
                                team_district_id +
                                '&status=' +
                                status,
                            dataType: 'json',
                            complete: function (response) {
                                var jsonData = JSON.parse(
                                    response.responseText
                                );
                                console.log(jsonData);
                                BootstrapDialog.show({
                                    title: jsonData.status,
                                    type:
                                        jsonData.status == 'OK'
                                            ? BootstrapDialog.TYPE_SUCCESS
                                            : BootstrapDialog.TYPE_WARNING,
                                    message: jsonData.message,
                                    onhide: function (dialogRef) {
                                        tableTeams.ajax.reload();
                                    },
                                });
                            },
                            error: function (xhr, status, error) {
                                alert(
                                    'ajaxAction.Error - status, error = ' +
                                        status +
                                        ',' +
                                        error +
                                        ',' +
                                        xhr
                                );
                            },
                        });
                    } else {
                        tableTeams.ajax.reload();
                    }
                }
            );
        }

        function downloadFile(urlToSend, fileName) {
            var req = new XMLHttpRequest();
            req.open('GET', urlToSend, true);
            req.responseType = 'blob';
            req.onload = function (event) {
                var blob = req.response;
                //if you have the fileName header available
                var link = document.createElement('a');
                link.href = window.URL.createObjectURL(blob);
                link.download = fileName;
                link.click();
            };

            req.send();
        }

        function getRoleByUserID() {
            return new Promise((resolve, reject) => {
                $.ajax({
                    type: 'POST',
                    url: SERVER_PATH + 'user/getInfoUser',
                    headers: {	
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name
                    },
                    data: {
                        user_id: $rootScope.user_id,
                    },
                    async: false,
                    dataType: 'json',
                    complete: function (response) {
                        let data = response.responseJSON;
                        if (data.status == 'OK') {
                            resolve(data.info);
                        }
                    },
                    error: function (xhr, status, error) {
                        console.log(error);
                    },
                });
            });
        }

        function getModalCoachesTableHtml(team_id) {
            var str =
                '' +
                '<div class="main-box-body clearfix">' +
                '<div class="table-responsive">' +
                // '<a data-match-route="/tables/coach" href="#/tables/coach" class="active">Add new Coach</a>'+
                '<table id="tableCoaches_' +
                team_id +
                '" class="table table-striped table-bordered table-hover" cellspacing="0" width="100%">' +
                '<thead>' +
                '<tr>' +
                '<th>Coach Name</th>' +
                '<th>Chinese Name</th>' +
                '<th>Email</th>' +
                '<th>Phone</th>' +
                '<th>Role</th>' +
                '<th>Level</th>' +
                '</tr>' +
                '</thead>' +
                '</table>' +
                '</div>' +
                '</div>' +
                '';
            return str;
        }

        function checkEmail(email, editor) {
            jQuery.ajax({
                type: 'POST',
                url: SERVER_PATH + 'group/checkEmail',
                headers: {	
                    'x-user-id': $rootScope.user_id,
                    'x-user-email': $rootScope.user_name
                },
                async: false,
                data: {
                    email: email,
                },
                dataType: 'json',
                complete: function (response) {
                    var jsonData = JSON.parse(response.responseText);
                    if (jsonData.status != 'OK') {
                        editor.field('parens.email').error(jsonData.message);

                        editor
                            .field('parens.surname')
                            .val(jsonData.data.surname)
                            .disable();
                        editor
                            .field('parens.other_name')
                            .val(jsonData.data.other_name)
                            .disable();
                        editor
                            .field('parens.phone')
                            .val(jsonData.data.phone)
                            .disable();
                    } else {
                        editor.field('parens.email').error('');
                        if (jsonData.is_new == false) {
                            editor
                                .field('district_team_coaches.coach_id')
                                .val(jsonData.data.id);
                            editor.field('users.password').val('').disable();
                            editor
                                .field('parens.surname')
                                .val(jsonData.data.surname)
                                .disable();
                            editor
                                .field('parens.other_name')
                                .val(jsonData.data.other_name)
                                .disable();
                            editor
                                .field('parens.phone')
                                .val(jsonData.data.phone)
                                .disable();

                            console.log(editor.fields());
                            //for each field in the editor has value is null or empty string '' or undefined , enable it
                            for (var key in editor.fields()) {
                                key = editor.fields()[key];
                                // console.log(key);
                                if (
                                    editor.field(key).val() == null ||
                                    (editor.field(key).val() == '' &&
                                        key != 'users.password')
                                ) {
                                    editor.field(key).enable();
                                }
                            }
                        } else {
                            editor
                                .field('district_team_coaches.coach_id')
                                .val(0);
                            editor.field('parens.surname').val('').enable();
                            editor.field('users.password').val('').enable();
                            editor.field('parens.other_name').val('').enable();
                            editor.field('parens.phone').val('').enable();
                        }
                    }
                },
            });

            return [];
        }

        function initCoachTbl(d) {
            // console.log(d);
            editorCoaches = new $.fn.dataTable.Editor({
                ajax: {
                    type: 'POST',
                    url: SERVER_PATH + 'district/setCoachInTeam',
                    headers: {	
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name
                    },
                    data: {
                        team_district_id: d.team_districts.id,
                    },
                    async: false,
                    dataType: 'json',
                    complete: function (response) {
                        var jsonData = JSON.parse(response.responseText);
                        if (typeof jsonData.fieldErrors == 'undefined') {
                            tableCoaches.ajax.reload();
                        }
                    },
                    error: function (xhr, status, error) {},
                },
                table: '#tableCoaches_' + d.team_districts.id,
                formOptions: {
                    main: {
                        onBlur: 'none',
                    },
                },
                i18n: {
                    create: {
                        button: 'New',
                        title: 'Create new coach',
                        submit: 'Create',
                    },
                    edit: {
                        button: 'Edit Role',
                        title: 'Edit Role of Coach',
                        submit: 'Update',
                    },
                    remove: {
                        button: 'Delete',
                        title: 'Delete coach',
                        submit: 'Delete',
                        confirm: {
                            _: 'Are you sure you want to delete these coach?',
                            1: 'Are you sure you want to delete this coach?',
                        },
                    },

                    error: {
                        system: 'System error, please contact administrator.',
                    },
                },
                fields: [
                    {
                        type: 'hidden',
                        name: 'district_team_coaches.team_district_id',
                        default: d.team_districts.id,
                    },
                    {
                        label: 'Select Coach',
                        name: 'district_team_coaches.coach_id',
                        type: 'select2',
                        opts: {
                            multiple: false,
                            placeholder: 'Search and select coach...',
                        },
                    },
                    // {
                    //   label: 'Email',
                    //   name: 'parens.email',
                    // },
                    // {
                    //   label: 'Password',
                    //   name: 'users.password',
                    //   attr:{
                    //     type:'password',
                    //     autocomplete:"new-password"
                    //   }
                    // },
                    // {
                    //   label: 'Surname',
                    //   name: 'parens.surname',
                    // },
                    // {
                    //   label: 'Other Name',
                    //   name: 'parens.other_name',
                    // },
                    // {
                    //   label: 'Chinese Name',
                    //   name: 'parens.chinese_name',
                    // },
                    // {
                    //   label: 'Phone',
                    //   name: 'parens.phone',
                    //   type: "telephone",
                    //   opts: {
                    //       preferredCountries: ['hk', 'cn'],
                    //       initialCountry: 'hk'
                    //   }
                    // },
                    {
                        label: 'Role',
                        name: 'district_team_coaches.team_role',
                        type: 'radio',
                        options: [
                            { label: ROLE_HEAD_COACH, value: ROLE_HEAD_COACH },
                            {
                                label: ROLE_ASSITANT_COACH,
                                value: ROLE_ASSITANT_COACH,
                            },
                            {
                                label: ROLE_GOALKEEPER_COACH,
                                value: ROLE_GOALKEEPER_COACH,
                            },
                        ],
                        default: ROLE_HEAD_COACH,
                    },
                ],
            });
            // editorCoaches.dependent('parens.email', function (val) {
            //   console.log(val);
            //   if (validateEmail(val)) {
            //     return checkEmail(val,editorCoaches);
            //   } else {
            //     if (val != '') {
            //       editorCoaches.field('parens.email').error('This is not a valid email');
            //     }
            //     editorCoaches.field('parens.surname').val('').enable();
            //     editorCoaches.field('parens.other_name').val('').enable();
            //     editorCoaches.field('parens.phone').val('').enable();

            //     return [];
            //   }
            // });

            editorCoaches.on('initEdit', function (e, json, data) {
                // editorCoaches.field('district_team_coaches.coach_id').hide();
                editorCoaches.hide();
                editorCoaches.field('district_team_coaches.team_role').show();
                // editorCoaches.field('parens.phone').show();
            });
            editorCoaches.on('initCreate', function (e, json, data) {
                editorCoaches.show();
                // editorCoaches.field('district_team_coaches.coach_id').hide();
                // editorCoaches.field('district_team_coaches.coach_id').show();
            });

            tableCoaches = $('#tableCoaches_' + d.team_districts.id).DataTable({
                dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
                stateSave: false,
                deferRender: true,
                ajax: {
                    url: SERVER_PATH + 'district/getCoachInTeam',
                    type: 'POST',
                    headers: {	
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name
                    },
                    data: {
                        team_district_id: d.team_districts.id,
                    },
                    dataType: 'json',
                    complete: function (response) {},
                    error: function (xhr, status, error) {},
                },
                language: {
                    paginate: {
                        previous: '<i class="fa fa-chevron-left"></i>',
                        next: '<i class="fa fa-chevron-right"></i>',
                    },
                },

                columns: [
                    {
                        data: null,
                        render: function (data) {
                            return (
                                data.parens.surname +
                                ' ' +
                                data.parens.other_name
                            );
                        },
                    },
                    { data: 'parens.chinese_name' },
                    { data: 'parens.email' },
                    { data: 'parens.phone' },
                    { data: 'district_team_coaches.team_role' },
                    { data: 'coach_levels.level', className: 'text-center' },
                ],
                select: {
                    style: 'single',
                    selector: 'td:not(:last-child)',
                },
                order: [[0, 'asc']],
                buttons: [
                    {
                        extend: 'create',
                        editor: editorCoaches,
                    },
                    {
                        extend: 'edit',
                        editor: editorCoaches,
                    },
                    {
                        extend: 'remove',
                        editor: editorCoaches,
                    },
                    {
                        text: '<i class="fa fa-refresh" aria-hidden="true"></i>&emsp;Sync coach',
                        action: function (e, dt, node, config) {
                            syncDistrictSessionCoaches(d.team_districts.id);
                        },
                    },
                    {
                        extend: 'colvis',
                        text: 'Columns',
                    },
                ],
                // columnDefs: [
                //   {
                //     type: 'justNum',
                //     targets: 2,
                //   },
                // ],
            });
        }

        function syncDistrictSessionCoaches(district_team_id) {
            // sweet alert confirm
            Swal.fire({
                title: 'Are you sure?',
                text: 'This action will sync all coaches from team to sessions(all coaches in sessions will be deleted)',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'Yes, sync it!',
                cancelButtonText: 'No, cancel!',
                reverseButtons: true,
            }).then((result) => {
                if (result.isConfirmed) {
                    $.ajax({
                        url:
                            SERVER_PATH + 'district/syncDistrictSessionCoaches',
                        type: 'POST',
                        headers: {	
                            'x-user-id': $rootScope.user_id,
                            'x-user-email': $rootScope.user_name
                        },
                        data: {
                            district_team_id: district_team_id,
                        },
                        dataType: 'json',
                        complete: function (response) {
                            var jsonData = JSON.parse(response.responseText);
                            if (jsonData.status == 'OK') {
                                Swal.fire({
                                    icon: 'success',
                                    title: 'Success',
                                    text: jsonData.message,
                                });
                                tableCoaches.ajax.reload();
                            } else {
                                Swal.fire({
                                    icon: 'error',
                                    title: 'Oops...',
                                    text: jsonData.message,
                                });
                            }
                        },
                        error: function (xhr, status, error) {},
                    });
                }
            });
        }

        function onlyUnique(value, index, self) {
            return self.indexOf(value) === index;
        }
    }
);
