<!DOCTYPE html>
<html ng-app="hkjflApp">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />

    <title>HKFA</title>

    <style>
        *,
        *:before,
        *:after {
            box-sizing: border-box;
        }

        * {
            margin: 0;
            padding: 0;
            font: inherit;
        }

        img,
        picture,
        svg,
        video {
            display: block;
            max-width: 100%;
        }

        input,
        select,
        textarea {
            background-color: transparent;
            outline: none;
        }

        button {
            cursor: pointer;
            background-color: transparent;
            outline: none;
            border: 0;
        }

        body {
            min-height: 100vh;
            font-weight: 400;
            font-size: 16px;
            line-height: 1;
        }

        /* latin */
        @font-face {
            font-family: 'Bentham';
            font-style: normal;
            font-weight: 100;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/Bentham-Regular.ttf) format('truetype');
        }

        @font-face {
            font-family: 'Bentham';
            font-style: normal;
            font-weight: 200;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/Bentham-Regular.ttf) format('truetype');
        }

        @font-face {
            font-family: 'Bentham';
            font-style: normal;
            font-weight: 300;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/Bentham-Regular.ttf) format('truetype');
        }

        @font-face {
            font-family: 'Bentham';
            font-style: normal;
            font-weight: 400;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/Bentham-Regular.ttf) format('truetype');
        }

        @font-face {
            font-family: 'Bentham';
            font-style: normal;
            font-weight: 500;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/Bentham-Regular.ttf) format('truetype');
        }

        @font-face {
            font-family: 'Bentham';
            font-style: normal;
            font-weight: 600;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/Bentham-Regular.ttf) format('truetype');
        }

        @font-face {
            font-family: 'Bentham';
            font-style: normal;
            font-weight: 700;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/Bentham-Regular.ttf) format('truetype');
        }

        @font-face {
            font-family: 'Bentham';
            font-style: normal;
            font-weight: 800;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/Bentham-Regular.ttf) format('truetype');
        }

        @font-face {
            font-family: 'Bentham';
            font-style: normal;
            font-weight: 900;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/Bentham-Regular.ttf) format('truetype');
        }

        /* vietnamese */
        @font-face {
            font-family: 'Dosis';
            font-style: normal;
            font-weight: 100;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/Dosis-ExtraLight.ttf) format('truetype');
        }

        /* vietnamese */
        @font-face {
            font-family: 'Dosis';
            font-style: normal;
            font-weight: 200;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/Dosis-ExtraLight.ttf) format('truetype');
        }

        /* vietnamese */
        @font-face {
            font-family: 'Dosis';
            font-style: normal;
            font-weight: 300;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/Dosis-Light.ttf) format('truetype');
        }

        /* vietnamese */
        @font-face {
            font-family: 'Dosis';
            font-style: normal;
            font-weight: 400;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/Dosis-Regular.ttf) format('truetype');
        }

        /* vietnamese */
        @font-face {
            font-family: 'Dosis';
            font-style: normal;
            font-weight: 500;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/Dosis-Medium.ttf) format('truetype');
        }

        /* vietnamese */
        @font-face {
            font-family: 'Dosis';
            font-style: normal;
            font-weight: 600;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/Dosis-SemiBold.ttf) format('truetype');
        }

        /* vietnamese */
        @font-face {
            font-family: 'Dosis';
            font-style: normal;
            font-weight: 700;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/Dosis-Bold.ttf) format('truetype');
        }

        /* vietnamese */
        @font-face {
            font-family: 'Dosis';
            font-style: normal;
            font-weight: 800;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/Dosis-ExtraBold.ttf) format('truetype');
        }

        /* vietnamese */
        @font-face {
            font-family: 'Dosis';
            font-style: normal;
            font-weight: 900;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/Dosis-ExtraBold.ttf) format('truetype');
        }

        /* cyrillic-ext */
        @font-face {
            font-family: 'Fira Sans';
            font-style: italic;
            font-weight: 100;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/FiraSans-ThinItalic.ttf) format('truetype');
        }

        /* cyrillic-ext */
        @font-face {
            font-family: 'Fira Sans';
            font-style: italic;
            font-weight: 200;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/FiraSans-ExtraLightItalic.ttf) format('truetype');
        }

        /* cyrillic-ext */
        @font-face {
            font-family: 'Fira Sans';
            font-style: italic;
            font-weight: 300;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/FiraSans-LightItalic.ttf) format('truetype');
        }

        /* cyrillic-ext */
        @font-face {
            font-family: 'Fira Sans';
            font-style: italic;
            font-weight: 400;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/FiraSans-Italic.ttf) format('truetype');
        }

        /* cyrillic-ext */
        @font-face {
            font-family: 'Fira Sans';
            font-style: italic;
            font-weight: 500;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/FiraSans-MediumItalic.ttf) format('truetype');
        }

        /* cyrillic-ext */
        @font-face {
            font-family: 'Fira Sans';
            font-style: italic;
            font-weight: 600;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/FiraSans-SemiBoldItalic.ttf) format('truetype');
        }

        /* cyrillic-ext */
        @font-face {
            font-family: 'Fira Sans';
            font-style: italic;
            font-weight: 700;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/FiraSans-BoldItalic.ttf.ttf) format('truetype');
        }

        /* cyrillic-ext */
        @font-face {
            font-family: 'Fira Sans';
            font-style: italic;
            font-weight: 800;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/FiraSans-ExtraBoldItalic.ttf) format('truetype');
        }

        /* cyrillic-ext */
        @font-face {
            font-family: 'Fira Sans';
            font-style: italic;
            font-weight: 900;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/FiraSans-BlackItalic.ttf) format('truetype');
        }

        /* cyrillic-ext */
        @font-face {
            font-family: 'Fira Sans';
            font-style: normal;
            font-weight: 100;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/FiraSans-Thin.ttf) format('truetype');
            unicode-range: U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
        }

        /* cyrillic-ext */
        @font-face {
            font-family: 'Fira Sans';
            font-style: normal;
            font-weight: 200;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/FiraSans-ExtraLight.ttf) format('truetype');
        }

        /* cyrillic-ext */
        @font-face {
            font-family: 'Fira Sans';
            font-style: normal;
            font-weight: 300;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/FiraSans-Light.ttf) format('truetype');
        }

        /* cyrillic-ext */
        @font-face {
            font-family: 'Fira Sans';
            font-style: normal;
            font-weight: 400;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/FiraSans-Regular.ttf) format('truetype');
        }

        /* cyrillic-ext */
        @font-face {
            font-family: 'Fira Sans';
            font-style: normal;
            font-weight: 500;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/FiraSans-Medium.ttf) format('truetype');
        }

        /* cyrillic-ext */
        @font-face {
            font-family: 'Fira Sans';
            font-style: normal;
            font-weight: 600;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/FiraSans-SemiBold.ttf) format('truetype');
        }

        /* cyrillic-ext */
        @font-face {
            font-family: 'Fira Sans';
            font-style: normal;
            font-weight: 700;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/FiraSans-Bold.ttf.ttf) format('truetype');
        }

        /* cyrillic-ext */
        @font-face {
            font-family: 'Fira Sans';
            font-style: normal;
            font-weight: 800;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/FiraSans-ExtraBold.ttf) format('truetype');
        }

        /* cyrillic-ext */
        @font-face {
            font-family: 'Fira Sans';
            font-style: normal;
            font-weight: 900;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/FiraSans-Black.ttf) format('truetype');
        }


        /* latin */
        @font-face {
            font-family: 'Gloria Hallelujah';
            font-style: normal;
            font-weight: 100;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/GloriaHallelujah-Regular.ttf) format('truetype');
        }

        @font-face {
            font-family: 'Gloria Hallelujah';
            font-style: normal;
            font-weight: 200;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/GloriaHallelujah-Regular.ttf) format('truetype');
        }

        @font-face {
            font-family: 'Gloria Hallelujah';
            font-style: normal;
            font-weight: 300;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/GloriaHallelujah-Regular.ttf) format('truetype');
        }

        @font-face {
            font-family: 'Gloria Hallelujah';
            font-style: normal;
            font-weight: 400;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/GloriaHallelujah-Regular.ttf) format('truetype');
        }

        @font-face {
            font-family: 'Gloria Hallelujah';
            font-style: normal;
            font-weight: 500;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/GloriaHallelujah-Regular.ttf) format('truetype');
        }

        @font-face {
            font-family: 'Gloria Hallelujah';
            font-style: normal;
            font-weight: 600;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/GloriaHallelujah-Regular.ttf) format('truetype');
        }

        @font-face {
            font-family: 'Gloria Hallelujah';
            font-style: normal;
            font-weight: 700;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/GloriaHallelujah-Regular.ttf) format('truetype');
        }

        @font-face {
            font-family: 'Gloria Hallelujah';
            font-style: normal;
            font-weight: 800;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/GloriaHallelujah-Regular.ttf) format('truetype');
        }

        @font-face {
            font-family: 'Gloria Hallelujah';
            font-style: normal;
            font-weight: 900;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/GloriaHallelujah-Regular.ttf) format('truetype');
        }

        /* thai */
        @font-face {
            font-family: 'KoHo';
            font-style: italic;
            font-weight: 100;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/KoHo-ExtraLightItalic.ttf) format('truetype');
        }

        /* thai */
        @font-face {
            font-family: 'KoHo';
            font-style: italic;
            font-weight: 200;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/KoHo-ExtraLightItalic.ttf) format('truetype');
        }

        /* thai */
        @font-face {
            font-family: 'KoHo';
            font-style: italic;
            font-weight: 300;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/KoHo-LightItalic.ttf) format('truetype');
        }

        /* thai */
        @font-face {
            font-family: 'KoHo';
            font-style: italic;
            font-weight: 400;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/KoHo-Italic.ttf) format('truetype');
        }

        /* thai */
        @font-face {
            font-family: 'KoHo';
            font-style: italic;
            font-weight: 500;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/KoHo-MediumItalic.ttf) format('truetype');
        }

        /* thai */
        @font-face {
            font-family: 'KoHo';
            font-style: italic;
            font-weight: 600;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/KoHo-SemiBoldItalic.ttf) format('truetype');
        }

        /* thai */
        @font-face {
            font-family: 'KoHo';
            font-style: italic;
            font-weight: 700;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/KoHo-BoldItalic.ttf) format('truetype');
        }

        /* thai */
        @font-face {
            font-family: 'KoHo';
            font-style: italic;
            font-weight: 800;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/KoHo-BoldItalic.ttf) format('truetype');
        }

        /* thai */
        @font-face {
            font-family: 'KoHo';
            font-style: italic;
            font-weight: 900;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/KoHo-BoldItalic.ttf) format('truetype');
        }

        /* thai */
        @font-face {
            font-family: 'KoHo';
            font-style: normal;
            font-weight: 100;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/KoHo-ExtraLight.ttf) format('truetype');
        }

        /* thai */
        @font-face {
            font-family: 'KoHo';
            font-style: normal;
            font-weight: 200;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/KoHo-ExtraLight.ttf) format('truetype');
        }

        /* thai */
        @font-face {
            font-family: 'KoHo';
            font-style: normal;
            font-weight: 300;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/KoHo-Light.ttf) format('truetype');
        }

        /* thai */
        @font-face {
            font-family: 'KoHo';
            font-style: normal;
            font-weight: 400;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/KoHo-Regular.ttf) format('truetype');
        }

        /* thai */
        @font-face {
            font-family: 'KoHo';
            font-style: normal;
            font-weight: 500;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/KoHo-Medium.ttf) format('truetype');
        }

        /* thai */
        @font-face {
            font-family: 'KoHo';
            font-style: normal;
            font-weight: 600;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/KoHo-SemiBold.ttf) format('truetype');
        }

        /* thai */
        @font-face {
            font-family: 'KoHo';
            font-style: normal;
            font-weight: 700;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/KoHo-Bold.ttf) format('truetype');
        }

        /* thai */
        @font-face {
            font-family: 'KoHo';
            font-style: normal;
            font-weight: 800;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/KoHo-Bold.ttf) format('truetype');
        }

        /* thai */
        @font-face {
            font-family: 'KoHo';
            font-style: normal;
            font-weight: 900;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/KoHo-Bold.ttf) format('truetype');
        }

        /* cyrillic-ext */
        @font-face {
            font-family: 'Montserrat';
            font-style: italic;
            font-weight: 100;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/Montserrat-ThinItalic.ttf) format('truetype');
        }

        @font-face {
            font-family: 'Montserrat';
            font-style: italic;
            font-weight: 200;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/Montserrat-ExtraLightItalic.ttf) format('truetype');
        }

        @font-face {
            font-family: 'Montserrat';
            font-style: italic;
            font-weight: 300;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/Montserrat-LightItalic.ttf) format('truetype');
        }

        @font-face {
            font-family: 'Montserrat';
            font-style: italic;
            font-weight: 400;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/Montserrat-Italic.ttf) format('truetype');
        }

        @font-face {
            font-family: 'Montserrat';
            font-style: italic;
            font-weight: 500;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/Montserrat-MediumItalic.ttf) format('truetype');
        }

        @font-face {
            font-family: 'Montserrat';
            font-style: italic;
            font-weight: 600;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/Montserrat-SemiBoldItalic.ttf) format('truetype');
        }

        @font-face {
            font-family: 'Montserrat';
            font-style: italic;
            font-weight: 700;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/Montserrat-BoldItalic.ttf) format('truetype');
        }

        @font-face {
            font-family: 'Montserrat';
            font-style: italic;
            font-weight: 800;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/Montserrat-ExtraBoldItalic.ttf) format('truetype');
        }

        @font-face {
            font-family: 'Montserrat';
            font-style: italic;
            font-weight: 900;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/Montserrat-BlackItalic.ttf) format('truetype');
        }

        /* cyrillic-ext */
        @font-face {
            font-family: 'Montserrat';
            font-style: normal;
            font-weight: 100;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/Montserrat-Thin.ttf) format('truetype');
        }

        @font-face {
            font-family: 'Montserrat';
            font-style: normal;
            font-weight: 200;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/Montserrat-ExtraLight.ttf) format('truetype');
        }

        @font-face {
            font-family: 'Montserrat';
            font-style: normal;
            font-weight: 300;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/Montserrat-Light.ttf) format('truetype');
        }

        @font-face {
            font-family: 'Montserrat';
            font-style: normal;
            font-weight: 400;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/Montserrat-Regular.ttf) format('truetype');
        }

        @font-face {
            font-family: 'Montserrat';
            font-style: normal;
            font-weight: 500;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/Montserrat-Medium.ttf) format('truetype');
        }

        @font-face {
            font-family: 'Montserrat';
            font-style: normal;
            font-weight: 600;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/Montserrat-SemiBold.ttf) format('truetype');
        }

        @font-face {
            font-family: 'Montserrat';
            font-style: italic;
            font-weight: 700;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/Montserrat-Bold.ttf) format('truetype');
        }

        @font-face {
            font-family: 'Montserrat';
            font-style: italic;
            font-weight: 800;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/Montserrat-ExtraBold.ttf) format('truetype');
        }

        @font-face {
            font-family: 'Montserrat';
            font-style: italic;
            font-weight: 900;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/Montserrat-Black.ttf) format('truetype');
        }

        /* cyrillic-ext */
        @font-face {
            font-family: 'Open Sans';
            font-style: italic;
            font-weight: 100;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/OpenSans-LightItalic.ttf) format('truetype');
        }

        /* cyrillic-ext */
        @font-face {
            font-family: 'Open Sans';
            font-style: italic;
            font-weight: 200;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/OpenSans-LightItalic.ttf) format('truetype');
        }

        /* cyrillic-ext */
        @font-face {
            font-family: 'Open Sans';
            font-style: italic;
            font-weight: 300;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/OpenSans-LightItalic.ttf) format('truetype');
        }

        /* cyrillic-ext */
        @font-face {
            font-family: 'Open Sans';
            font-style: italic;
            font-weight: 400;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/OpenSans-Italic.ttf) format('truetype');
        }

        /* cyrillic-ext */
        @font-face {
            font-family: 'Open Sans';
            font-style: italic;
            font-weight: 500;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/OpenSans-MediumItalic.ttf) format('truetype');
        }

        /* cyrillic-ext */
        @font-face {
            font-family: 'Open Sans';
            font-style: italic;
            font-weight: 600;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/OpenSans-SemiBoldItalic.ttf) format('truetype');
        }

        /* cyrillic-ext */
        @font-face {
            font-family: 'Open Sans';
            font-style: italic;
            font-weight: 700;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/OpenSans-BoldItalic.ttf) format('truetype');
        }

        /* cyrillic-ext */
        @font-face {
            font-family: 'Open Sans';
            font-style: italic;
            font-weight: 800;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/OpenSans-ExtraBoldItalic.ttf) format('truetype');
        }

        /* cyrillic-ext */
        @font-face {
            font-family: 'Open Sans';
            font-style: italic;
            font-weight: 900;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/OpenSans-ExtraBoldItalic.ttf) format('truetype');
        }

        /* cyrillic-ext */
        @font-face {
            font-family: 'Open Sans';
            font-style: normal;
            font-weight: 100;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/OpenSans-Light.ttf) format('truetype');
        }

        /* cyrillic-ext */
        @font-face {
            font-family: 'Open Sans';
            font-style: normal;
            font-weight: 200;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/OpenSans-Light.ttf) format('truetype');
        }

        /* cyrillic-ext */
        @font-face {
            font-family: 'Open Sans';
            font-style: normal;
            font-weight: 300;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/OpenSans-Light.ttf) format('truetype');
        }

        /* cyrillic-ext */
        @font-face {
            font-family: 'Open Sans';
            font-style: normal;
            font-weight: 400;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/OpenSans-Regular.ttf) format('truetype');
        }

        /* cyrillic-ext */
        @font-face {
            font-family: 'Open Sans';
            font-style: normal;
            font-weight: 500;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/OpenSans-Medium.ttf) format('truetype');
        }

        /* cyrillic-ext */
        @font-face {
            font-family: 'Open Sans';
            font-style: normal;
            font-weight: 600;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/OpenSans-SemiBold.ttf) format('truetype');
        }

        /* cyrillic-ext */
        @font-face {
            font-family: 'Open Sans';
            font-style: normal;
            font-weight: 700;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/OpenSans-Bold.ttf) format('truetype');
        }

        /* cyrillic-ext */
        @font-face {
            font-family: 'Open Sans';
            font-style: normal;
            font-weight: 800;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/OpenSans-ExtraBold.ttf) format('truetype');
        }

        /* cyrillic-ext */
        @font-face {
            font-family: 'Open Sans';
            font-style: normal;
            font-weight: 900;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/OpenSans-ExtraBold.ttf) format('truetype');
        }

        /* cyrillic-ext */
        @font-face {
            font-family: 'Roboto';
            font-style: italic;
            font-weight: 100;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/Roboto-ThinItalic.ttf) format('truetype');
        }

        @font-face {
            font-family: 'Roboto';
            font-style: italic;
            font-weight: 200;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/Roboto-ThinItalic.ttf) format('truetype');
        }

        /* cyrillic-ext */
        @font-face {
            font-family: 'Roboto';
            font-style: italic;
            font-weight: 300;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/Roboto-LightItalic.ttf) format('truetype');
        }

        /* cyrillic-ext */
        @font-face {
            font-family: 'Roboto';
            font-style: italic;
            font-weight: 400;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/Roboto-Italic.ttf) format('truetype');
        }

        /* cyrillic-ext */
        @font-face {
            font-family: 'Roboto';
            font-style: italic;
            font-weight: 500;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/Roboto-MediumItalic.ttf) format('truetype');
        }

        /* cyrillic-ext */
        @font-face {
            font-family: 'Roboto';
            font-style: italic;
            font-weight: 600;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/Roboto-MediumItalic.ttf) format('truetype');
        }

        /* cyrillic-ext */
        @font-face {
            font-family: 'Roboto';
            font-style: italic;
            font-weight: 700;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/Roboto-BoldItalic.ttf.ttf) format('truetype');
        }

        /* cyrillic-ext */
        @font-face {
            font-family: 'Roboto';
            font-style: italic;
            font-weight: 800;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/Roboto-BoldItalic.ttf.ttf) format('truetype');
        }

        /* cyrillic-ext */
        @font-face {
            font-family: 'Roboto';
            font-style: italic;
            font-weight: 900;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/Roboto-BlackItalic.ttf) format('truetype');
        }

        /* cyrillic-ext */
        @font-face {
            font-family: 'Roboto';
            font-style: normal;
            font-weight: 100;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/Roboto-Thin.ttf) format('truetype');
        }

        /* cyrillic-ext */
        @font-face {
            font-family: 'Roboto';
            font-style: normal;
            font-weight: 200;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/Roboto-Thin.ttf) format('truetype');
        }

        /* cyrillic-ext */
        @font-face {
            font-family: 'Roboto';
            font-style: normal;
            font-weight: 300;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/Roboto-Light.ttf) format('truetype');
        }

        /* cyrillic-ext */
        @font-face {
            font-family: 'Roboto';
            font-style: normal;
            font-weight: 400;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/Roboto-Regular.ttf) format('truetype');
        }

        /* cyrillic-ext */
        @font-face {
            font-family: 'Roboto';
            font-style: normal;
            font-weight: 500;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/Roboto-Medium.ttf) format('truetype');
        }

        /* cyrillic-ext */
        @font-face {
            font-family: 'Roboto';
            font-style: normal;
            font-weight: 600;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/Roboto-Medium.ttf) format('truetype');
        }

        /* cyrillic-ext */
        @font-face {
            font-family: 'Roboto';
            font-style: normal;
            font-weight: 700;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/Roboto-Bold.ttf.ttf) format('truetype');
        }

        /* cyrillic-ext */
        @font-face {
            font-family: 'Roboto';
            font-style: normal;
            font-weight: 800;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/Roboto-Bold.ttf.ttf) format('truetype');
        }

        /* cyrillic-ext */
        @font-face {
            font-family: 'Roboto';
            font-style: normal;
            font-weight: 900;
            font-display: swap;
            src: url(../../../../framework/dompdf/lib/fonts/Roboto-Black.ttf) format('truetype');
        }
    </style>

</head>

<body id="open_Certificate_Player_Page" ng-controller="openCertificatePlayer">
    <div class="container">
        <div id="open_Certificate_Player_Content"></div>
    </div>

</body>

<!-- global scripts -->
<script src="../cube/demo-skin-changer.js"></script> <!-- only for demo -->
<script src="../../../../framework/node_modules/jquery/dist/jquery.min.js"></script>
<script src="../../../../framework/node_modules/jquery/dist/jquery.min.js"></script>
<script src="../../../../framework/node_modules/bootstrap/dist/js/bootstrap.min.js"></script>
<script src="../../../../framework/node_modules/bootstrap3-dialog/dist/js/bootstrap-dialog.min.js"></script>
<script src="../../../../framework/node_modules/nanoscroller/bin/javascripts/jquery.nanoscroller.js"></script>

<!-- theme scripts -->
<script src="../../../../framework/node_modules/zakumi-admin//js/scripts.js"></script>

<!-- angular libs -->
<script src="../../../../framework/node_modules/angular/angular.min.js"></script>
<script src="https://ajax.googleapis.com/ajax/libs/angularjs/1.2.0rc1/angular-route.min.js"></script>


<!-- this page specific inline scripts -->
<script src="../config.js"></script>
<script src="./open-certificate-for-player.js"></script>