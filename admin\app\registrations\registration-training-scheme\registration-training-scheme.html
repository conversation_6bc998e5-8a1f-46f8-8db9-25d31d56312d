<div class="row">
    <div class="col-lg-12">
        <div class="clearfix">
            <h1 class="pull-left">{{event_type}} Registrations - {{event_name}}</h1>
        </div>
    </div>
</div>
<h3 style="margin-top: 0px !important;"><span>Select Course Type and Name</span></h3>
<div class="select-row">
    <div class="col">
        <select class="selectedType form-select select form-control" style="width: 300px;" id="sel2"
            ng-model="selectedType" ng-options="type for type in types" ng-change="updateEvents()">
        </select>
    </div>

    <div class="col">
        <select class="selectedCourses form-select select form-control" style="width: 300px;" id="sel"
            ng-model="selectedCourses" ng-options="course.class_code for course in courses">
        </select>
    </div>
    <div class="col">
        <button id="btn-yeargroup" type="button" class="btn btn-primary">Select</button>
    </div>
    <hr>
</div>
<div class="row">
    <div class="col-lg-12">
        <div class="main-box clearfix">
            <div class="main-box-body clearfix">
                <div class="table-responsive">
                    <table id="registration_table" class="table table-striped table-bordered table-hover"
                        cellspacing="0" width="100%">
                        <thead>
                            <tr>
                                <th>Photo</th>
                                <th>Player</th>
                                <th>DOB</th>
                                <th>Gender</th>
                                <th>Parent</th>
                                <th>Parent email</th>
                                <!-- <th>HKID</th> -->
                                <th>Res Address</th>
                                <th>Phone Number</th>
                                <th>Course</th>
                                <th>Status</th>

                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .avatar>img {
        position: relative;
        max-width: 96px;
        float: left;
        margin: auto;
        border-radius: 18%;
        background-clip: padding-box;
    }

    .class a {
        width: -webkit-fill-available;
        height: 70px;
        margin-bottom: 10px;
        text-align: center;
        border: solid;
        background-color: white;
    }

    .class a:hover {
        background-color: red;
        color: white;
    }

    .class h4 {
        margin-top: 16px;
        text-decoration: none;
    }

    .select-row{
        display: flex;
    }

    .select-row >div {
        margin-right: 20px;
    }
</style>