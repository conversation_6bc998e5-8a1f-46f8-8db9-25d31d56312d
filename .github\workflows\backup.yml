name: Daily Database Backup

on:
  schedule:
    - cron: '0 0 * * *'  # Runs daily at 00:00 UTC
  workflow_dispatch:  # Allows manual trigger

jobs:
  backup:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v3
        with:
          ref: 'backup'

      - name: Set up Python environment
        uses: actions/setup-python@v2
        with:
          python-version: '3.x'

      - name: Install dependencies
        run: |
          pip install google-api-python-client google-auth google-auth-httplib2 google-auth-oauthlib
      - name: Authenticate to Google Cloud and manage IP
        env:
          GCP_SA_KEY: ${{ secrets.GCP_SA_KEY }}
          ACTION: add
        run: |
          echo "${GCP_SA_KEY}" > /tmp/gcloud-key.json
          export GOOGLE_APPLICATION_CREDENTIALS="/tmp/gcloud-key.json"
          python scripts/network_authen.py  # Run your script to add IP
      - name: Backup MySQL database using mysqldump
        env:
          MYSQL_HOST: ${{ secrets.MYSQL_HOST }}
          MYSQL_USER: ${{ secrets.MYSQL_USER }}
          MYSQL_PASSWORD: ${{ secrets.MYSQL_PASSWORD }}
          MYSQL_DB: ${{ secrets.MYSQL_DB }}
        run: |
          TIMESTAMP=$(date +'%F')
          BACKUP_FILE="backup-$TIMESTAMP.sql"
          # Run mysqldump to create the database dump
          mysqldump -h $MYSQL_HOST -u $MYSQL_USER -p$MYSQL_PASSWORD \
            --add-drop-table \
            --create-options \
            --routines \
            --triggers \
            --lock-tables \
            --databases $MYSQL_DB > $BACKUP_FILE
          if [ ! -s "$BACKUP_FILE" ]; then
            echo "Backup file is empty. Check connection or database content."
            exit 1
          fi
          # Compress the dump file to a .gz file
          gzip -c $BACKUP_FILE > $BACKUP_FILE.gz
          # Delete sql file
          rm $BACKUP_FILE
          # Split the compressed .gz file into 20MB parts using zip
          zip -s 20m "${BACKUP_FILE}.zip" $BACKUP_FILE.gz
          # Delete the original compressed .gz file
          rm $BACKUP_FILE.gz
      - name: Configure Git
        run: |
          git config --global user.name "GitHub Actions"
          git config --global user.email "<EMAIL>"
      - name: Add and Commit Backup Files
        run: |
          git add -A
          git commit -m "Daily Backup $(date +'%F %T')" || echo "No changes to commit"
      - name: Checkout or Create Backup Branch
        run: |
          git fetch origin backup || echo "Backup branch does not exist"
          git checkout -B backup
      - name: Push Backup Files
        run: |
          git push -u origin backup
      - name: Remove temporary IP
        env:
          GCP_SA_KEY: ${{ secrets.GCP_SA_KEY }}
          ACTION: delete
        run: |
          echo "${GCP_SA_KEY}" > /tmp/gcloud-key.json
          export GOOGLE_APPLICATION_CREDENTIALS="/tmp/gcloud-key.json"
          python scripts/network_authen.py  # Remove IP from allowlist