app.controller(
    'shippingPLJCtrl',
    function ($scope, $rootScope, $routeParams, $http) {
        var event_id = $routeParams.id;
        $scope.event_id = event_id;

        jQuery.ajax({
            type: 'POST',
            url: SERVER_PATH + 'event/getEventInfo',
            async: false,
            data: {
                event_id: event_id,
            },
            headers: {	
                'x-user-id': $rootScope.user_id,
                'x-user-email': $rootScope.user_name
            },
            dataType: 'json',
            complete: function (response) {
                var jsonData = JSON.parse(response.responseText);
                var event = jsonData.info;
                $scope.event_name = event.name;
                $scope.event_type = event.type;
            },
        });
        
        $scope.goBack = function () {
            window.history.back();
        };

        setTimeout(() => {
            table = $('#shippingTable_' + event_id).DataTable({
                dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
                stateSave: true,
                destroy: true,
                deferRender: true,
                ajax: {
                    url: SERVER_PATH + 'shipping/getPLJShipping',
                    type: 'POST',
                    data: {
                        event_id: event_id,
                    },
                    headers: {	
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name
                    },
                    dataType: 'json',
                    complete: function (response) {},
                    error: function (xhr, status, error) {},
                },
                language: {
                    info: 'Showing _START_ to _END_ of _TOTAL_ shipping',
                    infoEmpty: 'Showing 0 to 0 of 0 shippings',
                    lengthMenu: 'Show _MENU_ shippings',
                    select: {
                        rows: {
                            _: 'You have selected %d shippings',
                            0: 'Click a shipping to select',
                            1: '1 shipping selected',
                        },
                    },
                    paginate: {
                        previous: '<i class="fa fa-chevron-left"></i>',
                        next: '<i class="fa fa-chevron-right"></i>',
                    },
                },
                columns: [
                    {
                        data: 'players.player_name',
                    },
                    {
                        data: 'parens.email',
                    },
                    {
                        data: 'registrations.product',
                    },
                    {
                        data: 'shipping.shipping_type',
                    },
                    {
                        data: 'invoices.invoice_number',
                    },
                    {
                        data: 'invoices.status',
                    },
                    {
                        data: 'invoices.invoice_date',
                    },
                ],
                initComplete: function () {
                    var payment_status_collunm = {
                        orderColumn: 6,
                        elementId: 'payment-status_filter',
                        selectId: 'selType',
                        label: 'Filter by status: ',
                    };

                    filterColumns = [payment_status_collunm];

                    filterColumns.forEach((item) => {
                        this.api()
                            .columns(item.orderColumn)
                            .every(function () {
                                var column = this;
                                //delete elements if they exist
                                $(`#${item.elementId}`).empty();
                                // add label
                                $(`<label>${item.label}</label>`).appendTo(
                                    `#${item.elementId}`
                                );

                                var select = $(
                                    `<select id="${item.orderColumn}" class="form-control"><option value="">All</option></select>`
                                );

                                select
                                    .appendTo($(`#${item.elementId}`))
                                    .on('change', function () {
                                        var val =
                                            $.fn.dataTable.util.escapeRegex(
                                                $(this).val()
                                            );

                                        column
                                            .search(val ? val : '', true, false)
                                            .draw();
                                    })
                                    .select2();
                                var column_data = column.data();
                                var select_data = [];
                                column_data.map((item) => {
                                    if (item != null) {
                                        item.indexOf(', ') > 0
                                            ? (item = item.split(', '))
                                            : (item = [item]);
                                        item.forEach((item) => {
                                            select_data.push(item);
                                        });
                                    }
                                });

                                select_data
                                    .filter(onlyUnique)
                                    .sort()
                                    .map(function (d, j) {
                                        select.append(
                                            `<option value="${d}">${d}</option>`
                                        );
                                    });
                            });
                    });
                },
                buttons: [
                    {
                        extend: 'colvis',
                        text: 'Columns',
                    },
                ],
            });
        }, 500);

        function onlyUnique(value, index, self) {
            return self.indexOf(value) === index;
        }
    }
);
