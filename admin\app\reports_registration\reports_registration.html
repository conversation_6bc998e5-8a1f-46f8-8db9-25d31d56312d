<div class="row" id="tab-page">
    <div class="row">
        <div class="col-lg-12">
            <ol class="breadcrumb">
                <li>Events</li>
                <li><a href="" onclick="event.preventDefault(); window.history.back();">Golden Age</a></li>
                <li class="active"><span>Report</span></li>
            </ol>
        </div>
    </div>
</div>
<div class="row">
    <div class="col-lg-12">
        <div class="clearfix">
            <h1 class="pull-left">Report</h1>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-12">
        <div class="main-box clearfix">
            <div class="main-box-body clearfix">
                <div class="table-responsive">
                    <table id="reports_registration_table" class="table table-striped table-bordered table-hover"
                        cellspacing="0" width="100%">
                        <thead>
                            <tr>
                                <th rowspan="2">Photo</th>
                                <th rowspan="2">Surname</th>
                                <th rowspan="2">Other name</th>
                                <th rowspan="2">Parent</th>
                                <th rowspan="2">DOB</th>
                                <th rowspan="2">Gender</th>
                                <th rowspan="2">Validate status</th>
                                <th colspan="2" id="ss3" class="center">N/A</th>
                                <th colspan="2" id="ss2" class="center">N/A</th>
                                <th colspan="2" id="ss1" class="center">N/A</th>
                            </tr>
                            <tr>
                                <th>Status</th>
                                <!-- <th>Invoice</th> -->
                                <th>Club</th>
                                <th>Status</th>
                                <!-- <th>Invoice</th> -->
                                <th>Club</th>
                                <th>Status</th>
                                <!-- <th>Invoice</th> -->
                                <th>Club</th>
                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .avatar>img {
        position: relative;
        max-width: 96px;
        float: left;
        margin: auto;
        border-radius: 18%;
        background-clip: padding-box;
    }

    input[type="radio"]:checked+label {
        font-weight: bold;
    }
</style>

<script type="text/javascript" language="javascript" class="init">
    user_id= localStorage.getItem('hkjflApp.user_id');
    user_name= localStorage.getItem('hkjflApp.user_name');
    jQuery.ajax({
        type: 'POST',
        url: SERVER_PATH + "player/getThreeNewEvents",
        async: false,
        headers: {	
            'x-user-id': user_id,
            'x-user-email': user_name
        },
        data: {},
        dataType: 'json',
        complete: function (response) {
            var jsonData = JSON.parse(response.responseText);
            var events = jsonData.info;
            for (let i = 0; i < events.length; i++) {
                const event = events[i];
                $('#ss' + (i + 1)).html(event.name);
            }
        }
    });
    

    $(document).ready(function () {
        var table = $('#reports_registration_table').DataTable({
            dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
            stateSave: true,
            deferRender: true,
            processing: true,
            serverSide: true,
            ajax: {
                url: SERVER_PATH + "player/getReportRegistration",
                type: 'POST',
                headers: {	
                    'x-user-id': user_id,
                    'x-user-email': user_name
                },
                data: {
                    // "pgroup_id": pgroup_id
                },
                dataType: 'json',
                complete: function (response) {
                },
                error: function (xhr, status, error) {
                },
            },
            language: {
                info: "Showing _START_ to _END_ of _TOTAL_ player",
                infoEmpty: "Showing 0 to 0 of 0 players",
                lengthMenu: "Show _MENU_ players",
                select: {
                    rows: {
                        "_": "You have selected %d players",
                        "0": "Click an player to select",
                        "1": "1 player selected"
                    },
                },
                paginate: {
                    previous: '<i class="fa fa-chevron-left"></i>',
                    next: '<i class="fa fa-chevron-right"></i>'
                }
            },
            columns: [
                {
                    data: "players.player_photo",
                    className: "avatar",
                    orderable: false,
                    render: function (data) {
                        if (data !== null && data !== '') {
                            return '<img src="' + PRODUCT_IMAGE_PATH + data + '">';
                        } else {
                            return '<img src="' + SYSTEM_IMAGE_PATH + 'favicon.png">';
                        }
                    }
                },
                {
                    data: 'players.surname'
                },
                {
                    data: 'players.other_name'
                },
                {
                    data: 'parens.surname',
                    render: function (data, type, row) {
                        return row.parens.surname + ' ' + row.parens.other_name;
                    },
                    visible: false,
                },
                { data: "players.dob", className: "center" },
                { data: "players.gender", className: "center" },
                {
                    data: "players.validate_status",
                    className: "center",
                    render: function (data, type, full, meta) {
                        switch (data) {
                            case VALIDATE_STATUS_Pending:
                                return '<span class="label label-info">' + data + '</span>';
                            case VALIDATE_STATUS_Invalid:
                                return '<span class="label label-danger">' + data + '</span>';
                            case VALIDATE_STATUS_Updated:
                                return '<span class="label label-warning">' + data + '</span>';
                            case VALIDATE_STATUS_Validated:
                                return '<span class="label label-success">' + data + '</span>';
                            default:
                                return '';
                        }
                    }
                },
                {
                    data: "players.ss3_reg_status",
                    className: "center",
                    render: function (data, type, full, meta) {
                        switch (data) {
                            case APPROVAL_STATUS_Approve:
                                return '<span class="label label-success">' + data + '</span>';
                            case APPROVAL_STATUS_Register:
                                return '<span class="label label-info">' + data + '</span>';
                            case APPROVAL_STATUS_Reject:
                                return '<span class="label label-danger">' + data + '</span>';
                            default:
                                return '';
                        }
                    }
                },
                // { 
                //     data: "players.ss3_inv_status",
                //     className: "center",

                // },
                {
                    data: "players.club_name",
                    className: "center",
                    visible: false
                },
                {
                    data: "players.ss2_reg_status",
                    className: "center",
                    render: function (data, type, full, meta) {
                        switch (data) {
                            case APPROVAL_STATUS_Approve:
                                return '<span class="label label-success">' + data + '</span>';
                            case APPROVAL_STATUS_Register:
                                return '<span class="label label-info">' + data + '</span>';
                            case APPROVAL_STATUS_Reject:
                                return '<span class="label label-danger">' + data + '</span>';
                            default:
                                return '';
                        }
                    }
                },
                // { 
                //     data: "players.ss2_inv_status",
                //     className: "center",

                // },
                {
                    data: "players.club_name",
                    className: "center",
                    visible: false
                },
                {
                    data: "players.ss1_reg_status",
                    className: "center",
                    render: function (data, type, full, meta) {
                        switch (data) {
                            case APPROVAL_STATUS_Approve:
                                return '<span class="label label-success">' + data + '</span>';
                            case APPROVAL_STATUS_Register:
                                return '<span class="label label-info">' + data + '</span>';
                            case APPROVAL_STATUS_Reject:
                                return '<span class="label label-danger">' + data + '</span>';
                            default:
                                return '';
                        }
                    }
                },
                // { 
                //     data: "players.ss1_inv_status",
                //     className: "center",

                // },
                {
                    data: "players.club_name",
                    className: "center",
                    visible: false
                },
            ],
            select: {
                style: 'single',
                selector: 'td:first-child',
            },
            order: [[1, 'asc']],
            lengthMenu: [[10, 25, 50, 100, 1000], [10, 25, 50, 100, 1000]],
            buttons: [
                {
                    extend: 'excel',
                    name: 'excel',
                    text: '<i class="fa fa-file-excel-o"></i>&emsp;Export to Excel',
                    titleAttr: 'Export data to an Excel file',
                    filename: appName + ' - Players',
                    title: appName + ' - Players',
                    exportOptions: {
                        columns: ':visible',
                        modifier: {
                            autoFilter: true,
                        }
                    }
                },
                { extend: 'colvis', text: 'Columns' }
            ],
            columnDefs: [
                { orderable: false, searchable: false, targets: 7 },
                { orderable: false, searchable: false, targets: 8 },
                { orderable: false, searchable: false, targets: 9 },
                { orderable: false, searchable: false, targets: 10 },
                { orderable: false, searchable: false, targets: 11 },
                { orderable: false, searchable: false, targets: 12 }
            ]
        });
    });

</script>