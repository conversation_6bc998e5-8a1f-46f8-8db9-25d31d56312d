app.controller(
    'teamGoldenAgeCtrl',
    function (user, $scope, $rootScope, $routeParams, $http) {
        $('#page-wrapper').removeClass('nav-small');

        // get info event
        var event_id = $routeParams.id;

        jQuery.ajax({
            type: 'POST',
            url: SERVER_PATH + 'event/getEventInfo',
            async: false,
            headers: {	
                'x-user-id': $rootScope.user_id,
                'x-user-email': $rootScope.user_name
            },
            data: {
                event_id: event_id,
            },
            dataType: 'json',
            complete: function (response) {
                var jsonData = JSON.parse(response.responseText);
                var event = jsonData.info;
                $scope.selectedEvent = jsonData.info;
                $scope.selectedEvent.id = event_id;
                $scope.selectedEventType = 'Golden Age';
                event_name = event.name;
                event_type = event.type;
                normalizedType = normalizeEventType(event_type);
            },
        });

        $scope.event_name = event_name;
        $scope.event_type = event_type;
        $scope.normalizedType = normalizedType;

        function normalizeEventType(eventType) {
            switch (eventType) {
                case 'Summer Scheme':
                    return 'summer-scheme';
                case 'Regional':
                    return 'regional';
                case 'PL Junior':
                    return 'pl-junior';
                case 'Golden Age':
                    return 'golden-age';
                case 'Beginner':
                    return 'beginner';
                case 'District':
                    return 'district';
                default:
                    return '';
            }
        }

        jQuery.ajax({
            type: 'POST',
            url: SERVER_PATH + 'event/getGroupsByEvent',
            headers: {	
                'x-user-id': $rootScope.user_id,
                'x-user-email': $rootScope.user_name
            },
            async: false,
            data: {
                event_id: event_id,
                user_id: $rootScope.user_id,
            },
            dataType: 'json',
            complete: function (response) {
                var jsonData = JSON.parse(response.responseText);
                if (jsonData.status == 'OK') {
                    $scope.groups = jsonData.info;
                    $scope.selectedGroup = $scope.groups[0];
                    // getGroup();
                }
            },
        });

        $('button').click(function (e) {
            getGroup();
        });

        function getGroup() {
            var group_id = $scope.selectedGroup.id;
            var group_name = $scope.selectedGroup.name;

            var html = '';
            html += getGroupTeamsTableHtml(group_id);
            $('#teamGoldenAgePageContent').html(html);

            // initialize data
            initGroupTeamsTable(group_id, group_name);
        }
        
        function getGroupTeamsTableHtml(group_id) {
            var str =
                '' +
                '<div class="row">' +
                '<div class="col-lg-12">' +
                '<div class="main-box clearfix">' +
                '<div class="main-box-body clearfix">' +
                '<div class="table-responsive">' +
                '<table id="tblGroupTeams_' +
                group_id +
                '" class="table table-striped table-bordered table-hover" cellspacing="0" width="100%">' +
                '<thead>' +
                '<tr>' +
                '<th>Team</th>' +
                '<th>Club</th>' +
                '<th>Chinese name</th>' +
                '<th>Manage coach</th>';
            '</tr>' +
                '</thead>' +
                '</table>' +
                '</div>' +
                '</div>' +
                '</div>' +
                '</div>' +
                '</div>';
            return str;
        }

        function initGroupTeamsTable(group_id, group_name) {
            tableGroupTeams = $('#tblGroupTeams_' + group_id).DataTable({
                dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
                stateSave: true,
                deferRender: true,
                ajax: {
                    url: SERVER_PATH + 'team/getGroupTeams',
                    type: 'POST',
                    headers: {	
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name
                    },
                    data: {
                        group_id: group_id,
                        event_id: event_id,
                        user_id: $rootScope.user_id,
                    },
                    dataType: 'json',
                    complete: function (response) {
                        // response = JSON.parse(response.responseText);
                    },
                    error: function (xhr, status, error) {},
                },
                language: {
                    paginate: {
                        previous: '<i class="fa fa-chevron-left"></i>',
                        next: '<i class="fa fa-chevron-right"></i>',
                    },
                },
                columns: [
                    {
                        data: 'teams.name',
                        className: 'center',
                        render: function (data, type, row) {
                            return (
                                '<a data-match-route="/event/' +
                                event_id +
                                '/' +
                                'team_golden_age_player/' +
                                row.teams.id +
                                '" href="#/event/' +
                                event_id +
                                '/' +
                                'team_golden_age_player/' +
                                row.teams.id +
                                '?teamName=' +
                                data +
                                '">' +
                                data +
                                '</a>'
                            );
                        },
                    },
                    {
                        data: 'clubs.name',
                    },
                    {
                        data: 'teams.chinese_name',
                    },
                    {
                        data: null,
                        render: function (data, type, row) {
                            if (user.role == USER_SUPER_ADMIN || user.role == USER_LEAGUE_ADMIN) {
                                actions =
                                    '<button type="button" data-toggle="modal" data-target="#modal-coaches" class="btn btn-primary modal-coaches" >Manage Coaches</button>';
                                return actions;
                            } else {
                                return '';
                            }
                        },
                    },
                ],
                select: {
                    style: 'single',
                },
            });

            $('#tblGroupTeams_' + group_id).on(
                'click',
                'tbody td button',
                function (e, row) {
                    let $row = $(this).closest('tr');
                    let data = tableGroupTeams.row($row).data();

                    let msg = getModalCoachesHtml(data.teams.id);

                    BootstrapDialog.show({
                        title: data.teams.name + ' - Coach Table',
                        message: msg,
                        onshown: function (dialogRef) {
                            initTableCoach(data.teams.id);
                        },
                    });
                }
            );
        }

        function getModalCoachesHtml(team_id) {
            var str =
                '' +
                '<div id="event_table" class="main-box-body clearfix">' +
                '<div class="table-responsive">' +
                '<table id="tableCoach_' +
                team_id +
                '" class="table table-striped table-bordered table-hover" cellspacing="0" width="100%">' +
                '<thead><tr>' +
                '<th>Name</th>' +
                '<th>Email</th>' +
                '</tr></thead>' +
                '</table>';
            '</div>' + '</div>';
            return str;
        }

        function initTableCoach(team_id) {
            editor = new $.fn.dataTable.Editor({
                ajax: {
                    type: 'POST',
                    url: SERVER_PATH + 'group/getSetTeamCoaches',
                    headers: {	
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name
                    },
                    data: {
                        team_id: team_id,
                    },
                    dataType: 'application/json',
                    complete: function (response) {
                        var jsonData = JSON.parse(response.responseText);
                        if (jsonData.status == 'OK') {
                            table.ajax.reload();
                        }
                    },
                    error: function (xhr, status, error) {},
                },
                table: '#tableCoach_' + team_id,
                formOptions: {
                    main: {
                        onBlur: 'none',
                    },
                },
                i18n: {
                    create: {
                        button: 'New',
                        title: 'Add new coach',
                        submit: 'Create',
                    },
                    remove: {
                        button: 'Delete',
                        title: 'Delete',
                        submit: 'Delete',
                        confirm: {
                            _: 'Are you sure you want to delete?',
                            1: 'Are you sure you want to delete?',
                        },
                    },
                    error: {
                        system: 'System error, please contact administrator.',
                    },
                },
                fields: [
                    {
                        name: 'team_coaches.team_id',
                        type: 'hidden',
                        def: team_id,
                    },
                    {
                        name: 'team_coaches.coach_id',
                        label: 'Coaches:',
                        type: 'select2',
                        opts: {
                            placeholder: 'Select a coach',
                        },
                    },
                ],
            });

            var tableCoach = $('#tableCoach_' + team_id).DataTable({
                dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
                stateSave: true,
                deferRender: true,
                ajax: {
                    url: SERVER_PATH + 'group/getSetTeamCoaches',
                    type: 'POST',
                    headers: {	
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name
                    },
                    data: {
                        team_id: team_id,
                    },
                    dataType: 'json',
                    complete: function (response) {
                        // response = JSON.parse(response.responseText);
                    },
                    error: function (xhr, status, error) {},
                },
                language: {
                    paginate: {
                        previous: '<i class="fa fa-chevron-left"></i>',
                        next: '<i class="fa fa-chevron-right"></i>',
                    },
                },
                columns: [
                    {
                        data: null,
                        render: function (data, type, row) {
                            return (
                                data.parens.surname +
                                ' ' +
                                data.parens.other_name
                            );
                        },
                    },
                    {
                        data: 'parens.email',
                    },
                ],
                select: {
                    style: 'single',
                },
                buttons: [
                    {
                        extend: 'create',
                        className: 'btn-create-coach',
                        editor: editor,
                    },
                    {
                        extend: 'remove',
                        editor: editor,
                    },
                ],
            });
        }
    }
);
