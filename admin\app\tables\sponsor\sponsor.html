<div class="row" id="tab-page">
    <div class="row">
        <div class="col-lg-12">
            <ol class="breadcrumb">
                <li>Setup</li>
                <li class="active"><span>Sponsors</span></li>
            </ol>
        </div>
    </div>
</div>
<div class="row">
    <div class="col-lg-12">
        <div class="clearfix">
            <h1 class="pull-left">Sponsors</h1>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-12">
        <div class="main-box clearfix">
            <div class="main-box-body clearfix">
                <div class="table-responsive">
                    <table id="sponsorsTable" class="table table-striped table-bordered table-hover" cellspacing="0"
                        width="100%">
                        <thead>
                            <tr>
                                <th>Order</th>
                                <th>Logo</th>
                                <th>Name</th>
                                <th>Description</th>
                                <th>Url</th>
                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript" language="javascript" class="init">
    $(document).ready(function () {
        var table;
        user_id = localStorage.getItem('hkjflApp.user_id');
		user_name = localStorage.getItem('hkjflApp.user_name');

        var editor = new $.fn.dataTable.Editor({
            ajax: {
                type: 'POST',
                url: SERVER_PATH + "setting/setSettingSponsor",
                headers: {	
                    'x-user-id': user_id,
                    'x-user-email': user_name
                },
                data: {},
                dataType: 'json',
                complete: function (response) {
                    var jsonData = JSON.parse(response.responseText);
                    table.ajax.reload();
                },
                error: function (xhr, status, error) { },
            },
            table: "#sponsorsTable",
            formOptions: {
                main: {
                    onBlur: 'none',
                }
            },
            i18n: {
                create: {
                    button: "New",
                    title: "Create new sponsor",
                    submit: "Create"
                },
                edit: {
                    button: "Edit",
                    title: "Edit sponsor",
                    submit: "Update"
                },
                remove: {
                    button: "Delete",
                    title: "Delete sponsor",
                    submit: "Delete",
                    confirm: {
                        _: "Are you sure you want to delete these sponsor?",
                        1: "Are you sure you want to delete this sponsor?"
                    }
                },
                error: {
                    system: "System error, please contact administrator."
                },
            },
            fields: [
                {
                    label: 'Order:',
                    name: 'sponsors.order',
                    fieldInfo: 'This field can only be edited via click and drag row reordering.'
                },
                {
                    label: "Name:",
                    name: "sponsors.name",
                },
                {
                    label: "Description:",
                    name: "sponsors.description",
                    type: "textarea",
                    attr: {
                        class: "form-control",
                        rows: "3",
                        cols: "40",
                    }
                },
                {
                    label: "Logo:",
                    name: "sponsors.logo",
                    type: "upload",
                    display: function (data) {
                        return '<img src="' + PRODUCT_IMAGE_PATH + data + '" width="100%">';
                    },
                    clearText: "Clear",
                    noImageText: 'No image'
                },
                {
                    label: "Url:",
                    name: "sponsors.url"
                }
            ]
        });

        var table = $('#sponsorsTable').DataTable({
            dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
            stateSave: true,
            deferRender: true,
            ajax: {
                url: SERVER_PATH + "setting/getSettingSponsor",
                type: 'POST',
                headers: {	
                    'x-user-id': user_id,
                    'x-user-email': user_name
                },
                data: {},
                dataType: 'json',
                complete: function (response) { },
                error: function (xhr, status, error) { },
            },
            language: {
                paginate: {
                    previous: '<i class="fa fa-chevron-left"></i>',
                    next: '<i class="fa fa-chevron-right"></i>'
                }
            },
            columns: [
                {
                    data: 'sponsors.order',
                    className: 'reorder'
                },
                {
                    data: "sponsors.logo",
                    className: "center",
                    orderable: false,
                    render: function (data) {
                        if (data !== null && data !== '') {
                            return '<img src="' + PRODUCT_IMAGE_PATH + data + '" width="165px">';
                        } else {
                            return data;
                        }
                    }
                },
                {
                    data: 'sponsors.name'
                },
                {
                    data: 'sponsors.description'
                },
                {
                    data: 'sponsors.url'
                },
            ],
            rowReorder: {
                dataSrc: 'sponsors.order',
                editor: editor
            },
            select: {
                style: 'single'
            },
            order: [
                [0, 'asc']
            ],
            lengthMenu: [
                [10, 25, 50, 100, -1],
                [10, 25, 50, 100, "All"]
            ],
            buttons: [{
                extend: "create",
                editor: editor
            },
            {
                extend: "edit",
                editor: editor
            },
            {
                extend: "remove",
                editor: editor
            },
            {
                extend: 'colvis',
                text: 'Columns'
            }
            ]
        });
        editor
            .on('postCreate postRemove', function () {
                // After create or edit, a number of other rows might have been effected -
                // so we need to reload the table, keeping the paging in the current position
                table.ajax.reload(null, false);
            })
            .on('initCreate', function () {
                // Enable order for create
                editor.field('sponsors.order').enable();
            })
            .on('initEdit', function () {
                // Disable for edit (re-ordering is performed by click and drag)
                editor.field('sponsors.order').disable();
            });
    })
</script>