app.controller('respectCtrl', function ($scope, $rootScope, $routeParams, $http) {
    $('#page-wrapper').removeClass('nav-small');
    var table;

    function getcropperHTML(src) {
        var cropperHTML = '<div class="cropper"><img style="height: 600px" class="js-avatar-preview" src="' + src + '"></div>';
        return cropperHTML;
    }

    var editor = new $.fn.dataTable.Editor({
        ajax: {
            type: 'POST',
            url: SERVER_PATH + "respects/setRespect",
            headers: {	
                'x-user-id': $rootScope.user_id,
                'x-user-email': $rootScope.user_name
            },
            data: {},
            async: false,
            dataType: 'json',
            complete: function (response) {
                var jsonData = JSON.parse(response.responseText);
                // --- may need to reload
                if (DEVELOPMENT_ENVIRONMENT) console.log('status = ' + jsonData.status);
                if (jsonData.status == 'OK') {
                    table.ajax.reload();
                }
            },
            error: function (xhr, status, error) { },
        },
        table: "#table",
        formOptions: {
            main: {
                onBlur: 'none',
            }
        },
        i18n: {
            create: {
                button: "New",
                title: "Create new respects",
                submit: "Create"
            },
            edit: {
                button: "Edit",
                title: "Edit respects",
                submit: "Update"
            },
            remove: {
                button: "Delete",
                title: "Delete respects",
                submit: "Delete",
                confirm: {
                    _: "Are you sure you want to delete these respects?",
                    1: "Are you sure you want to delete this respects?"
                }
            },
            error: {
                system: "System error, please contact administrator."
            },
        },
        fields: [
            {
                label: "Title",
                name: "respects.title",
            },
            {
                label: "Created at",
                name: "respects.created_at",
                type: "datetime",
                def: function () {
                    return new Date();
                },
                format: "YYYY/MM/DD"
            },
            {
                label: "Type",
                name: "respects.type",
                type: "select",
                placeholder: "Select a Type",
                options: [{
                    label: 'Document',
                    value: 'Document'
                }, {
                    label: 'Video',
                    value: 'Video'
                }, {
                    label: 'Article',
                    value: 'Article'
                }],
                def: "Article"
            },
            {
                label: "Photo",
                name: "respects.logo",
                type: "upload",
                display: function (data) {
                    return '<img src="' + UPLOAD_FILE_PATH + data + '" width="100%">';
                },
                clearText: "Clear",
                noImageText: 'No image'
            },
            {
                label: "Article",
                name: "respects.article",
                type: "ckeditor"
            },
            {
                label: 'Attachments',
                name: 'respects.file_id',
                type: 'upload',
                display: function (fileId) {
                    return editor.file('files', fileId).filename;
                },
                noFileText: 'No files'
            },
            {
                label: "Video type:",
                name: "respect_videos.video_type",
                type: "radio",
                options: [
                    { label: "Youtube", value: 'youtube' },
                    { label: "Vimeo", value: 'vimeo' },
                    { label: "Google Drive", value: 'googleDrive' }
                ],
                def: 'youtube'
            },
            {
                label: "Video Title:",
                name: "respect_videos.video_title"
            },
            {
                label: "Video id:",
                name: "respect_videos.video_id"
            },
            {
                label: "Description:",
                name: "respect_videos.description",
                type: "ckeditor",
            },
            {
                label: 'order',
                name: 'respects.order',
                type: "hidden"
            },
        ]
    });



    editor.on('preUpload', function (e, fieldName, file, ajaxData) {
        if (fieldName == 'respects.logo') {
            var promise = new Promise(function (resolve, reject) {
                const MIME_TYPE = "image/jpeg";
                const QUALITY = 0.75;
                var name = file.name;
                var validImageTypes = ["image/gif", "image/jpeg", "image/png"];
                if ($.inArray(file.type, validImageTypes) < 0) {
                    $("div.upload button i").html("Choose file...");
                    editor.field('respects.logo').error('Please upload an image file (.jpg or .png)');
                    return false;
                }
                const blobURL = URL.createObjectURL(file);
                const img = new Image();
                img.src = blobURL;
                var bootrapDialog
                bootrapDialog = BootstrapDialog.show({
                    size: BootstrapDialog.SIZE_LARGE,
                    type: BootstrapDialog.TYPE_SUCCESS,
                    closable: true,
                    closeByBackdrop: false,
                    closeByKeyboard: true,
                    draggable: true,
                    title: 'Crop Photo',
                    message: getcropperHTML(blobURL),
                    buttons: [],
                    onshown: function (dialog) {
                        var image = $('.cropper img')[0];
                        cropper = new Cropper(image, {
                            viewMode: 1,
                            aspectRatio: 16 / 9,
                            minContainerWidth: 720,
                            minContainerHeight: 405,
                            minCropBoxWidth: 720,
                            minCropBoxHeight: 405,
                            movable: true,
                            ready: function () {
                                console.log('ready');
                                console.log(cropper.ready);
                            }
                        });
                    },
                    onhide: function (){
                        $("div.upload button i").html("Choose file...");
                    },
                    buttons: [{
                        label: 'OK',
                        action: function (dialog) {
                            event.preventDefault();
                            var $button = $(this);
                            $button.text('uploading...');
                            $button.prop('disabled', true);
                            const canvas = cropper.getCroppedCanvas();
                            canvas.toBlob(
                                (blob) => {
                                    // Handle the compressed images upload or save
                                    let file_small = new File([blob], name, { type: "image/jpeg", lastModified: new Date().getTime() });
                                    let container = new DataTransfer();
                                    container.items.add(file_small);
                                    x = container.files[0];

                                    var form_data = new FormData();
                                    form_data.append('file', x);

                                    var url = SERVER_PATH + "respects/setUploadImg";
                                    $.ajax({
                                        url: url,
                                        type: 'POST',
                                        cache: false,
                                        data: form_data,
                                        async: false,
                                        processData: false,
                                        headers: {	
                                            'x-user-id': $rootScope.user_id,
                                            'x-user-email': $rootScope.user_name
                                        },
                                        mimeType: "multipart/form-data",
                                        contentType: false,
                                        timeout: 0,
                                        error: function (err) {
                                            bootrapDialog.close();
                                            reject();
                                            
                                        },
                                        success: function (response) {
                                            data=JSON.parse(response);
                                            if (data.status == 'OK') {
                                                editor.field('respects.logo').val( data.info );
                                                bootrapDialog.close();
                                                resolve(true);
                                            }
                                            else
                                            {
                                                editor.field('respects.logo').error(data.message);
                                                bootrapDialog.close();
                                                resolve(true);
                                            }
                                        }
                                    });
                                },
                                MIME_TYPE,
                                QUALITY
                            );
                        }
                    }, {
                        label: 'Cancel',
                        action: function (dialog) {
                            bootrapDialog.close();
                            $("div.upload button i").html("Choose file...");
                            reject();
                        }
                    }]
                })
            })
            return false;
        } else {
            return true;
        }
    })

    var table = $('#table').DataTable({
        dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
        stateSave: true,
        deferRender: true,
        ajax: {
            url: SERVER_PATH + "respects/getRespect",
            type: 'POST',
            headers: {	
                'x-user-id': $rootScope.user_id,
                'x-user-email': $rootScope.user_name
            },
            data: {},
            dataType: 'json',
            complete: function (response) {
                console.log(response);
            },
            error: function (xhr, status, error) { },
        },
        language: {
            paginate: {
                previous: '<i class="fa fa-chevron-left"></i>',
                next: '<i class="fa fa-chevron-right"></i>'
            }
        },
        columns: [
            {
                data: 'respects.title',
                sortable: false
            },
            {
                data: 'respects.type',
                sortable: false
            }
            ,
            {
                data: 'respects.created_at',
                className: "center",
                sortable: false
            },
            {
                data: "respects.order",
                "visible": false,
                sortable: false
            }
        ],
        rowReorder: {
            dataSrc: 'respects.order',
            editor: editor
        },
        select: {
            style: 'single',
            selector: 'td:not(:last-child)'
        },
        order: [
            [3, 'asc']
        ],
        columnDefs: [{
            "visible": false,
            "target": [3]
        }],
        lengthMenu: [
            [10, 25, 50, 100, -1],
            [10, 25, 50, 100, "All"]
        ],
        buttons: [{
            extend: "create",
            editor: editor
        },
        {
            extend: "edit",
            editor: editor
        },
        {
            extend: "remove",
            editor: editor
        },
        {
            extend: 'colvis',
            text: 'Columns'
        }
        ]
    });

    editor.dependent('respects.type', function (val) {
        switch (val) {
            case 'Document': {
                return {
                    show: ['respects.file_id'],
                    hide: ['respects.article', 'respect_videos.video_type', 'respect_videos.video_id', 'respect_videos.description', 'respect_videos.video_title'],
                };
                break;
            }
            case 'Video': {
                return {
                    show: ['respect_videos.video_type', 'respect_videos.video_id', 'respect_videos.description', 'respect_videos.video_title'],
                    hide: ['respects.article', 'respects.file_id'],
                };
                break;
            }
            case 'Article': {
                return {
                    show: ['respects.article'],
                    hide: ['respects.file_id', 'respect_videos.video_type', 'respect_videos.video_id', 'respect_videos.description', 'respect_videos.video_title'],
                };
                break;
            }
        }
    });
})
