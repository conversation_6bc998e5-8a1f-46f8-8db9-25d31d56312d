<!DOCTYPE html>
<html ng-app="hkjflApp">

<head>
	<meta charset="UTF-8" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
	<meta http-equiv="Cache-control" content="no-cache">

	<title> Dashboard</title>

	<!-- bootstrap -->
	<link rel="stylesheet" type="text/css" href="../../framework/node_modules/bootstrap/dist/css/bootstrap.min.css" />
	<link rel="stylesheet" type="text/css"
		href="../../framework/node_modules/bootstrap3-dialog/dist/css/bootstrap-dialog.min.css" />

	<!-- RTL support - for demo only -->
	<script src="app/cube/demo-rtl.js"></script>

	<!-- libraries -->
	<link rel="stylesheet" type="text/css" href="../../framework/node_modules/font-awesome/css/font-awesome.min.css" />

	<link rel="stylesheet" type="text/css" href="../../framework/node_modules/nanoscroller/bin/css/nanoscroller.css" />
	<link rel="stylesheet" type="text/css"
		href="../../framework/node_modules/angular-loading-bar/build/loading-bar.css" />
	<link rel="stylesheet" type="text/css"
		href="../../framework/node_modules/bootstrap-datepicker/dist/css/bootstrap-datepicker.min.css" />
	<link rel="stylesheet" type="text/css"
		href="../../framework/node_modules/bootstrap-daterangepicker/daterangepicker.css">
	<!-- global styles -->
	<link rel="stylesheet" type="text/css"
		href="../../framework/node_modules/zakumi-admin/css/compiled/theme_styles.css" />

	<!-- dataTables and editor-->
	<link rel="stylesheet" type="text/css"
		href="../../framework/node_modules/datatables.net-bs/css/dataTables.bootstrap.min.css">
	<link rel="stylesheet" type="text/css"
		href="../../framework/admin/DataTables/RowReorder-1.2.8/css/rowReorder.bootstrap.min.css">
	<link rel="stylesheet" type="text/css"
		href="../../framework/node_modules/datatables.net-buttons-bs/css/buttons.bootstrap.min.css">
	<link rel="stylesheet" type="text/css"
		href="../../framework/node_modules/datatables.net-responsive-bs/css/responsive.bootstrap.min.css">
	<link rel="stylesheet" type="text/css"
		href="../../framework/node_modules/datatables.net-select-bs/css/select.bootstrap.min.css">
	<link rel="stylesheet" type="text/css" href="../../framework/admin/Editor-1.9.0/css/editor.bootstrap.min.css">
	<link rel="stylesheet" type="text/css" href="../../framework/node_modules/editor-plugins/dist/css/editor.title.css">
	<link rel="stylesheet" type="text/css" href="../../framework/node_modules/select2/dist/css/select2.min.css">
	<link rel="stylesheet" type="text/css"
		href="../../framework/node_modules/datatables.net-rowgroup-dt/css/rowGroup.dataTables.min.css">
	<!-- Datatable checkbox style -->
	<link rel="stylesheet" type="text/css"
		href="../../framework/node_modules/awesome-bootstrap-checkbox/awesome-bootstrap-checkbox.css" />
	<link rel="stylesheet" type="text/css"
		href="../../framework/node_modules/jquery-datatables-checkboxes/css/dataTables.checkboxes.css" />
	<!-- Typeahead lib -->
	<link rel="stylesheet" type="text/css" href="../../framework/admin/typeahead/typeahead.css" />
	<!-- App style -->
	<link rel="stylesheet" type="text/css" href="app.css" />
	<!-- Spectrum -->

	<link rel='stylesheet' href='../../framework/spectrum/spectrum.css' />

	<!-- Favicon -->
	<link type="image/x-icon" href="images/favicon.ico" rel="shortcut icon" />

	<!-- Cropper JS -->
	<link href="./../../framework/node_modules/cropperjs/dist/cropper.css" rel="stylesheet">

	<!-- Notification Styles -->
	<link rel="stylesheet" type="text/css" href="../../framework/admin/notification-styles/css/ns-default.css" />
	<link rel="stylesheet" type="text/css" href="../../framework/admin/notification-styles/css/ns-style-growl.css" />
	<link rel="stylesheet" type="text/css" href="../../framework/admin/notification-styles/css/ns-style-bar.css" />
	<link rel="stylesheet" type="text/css" href="../../framework/admin/notification-styles/css/ns-style-attached.css" />
	<link rel="stylesheet" type="text/css" href="../../framework/admin/notification-styles/css/ns-style-other.css" />
	<link rel="stylesheet" type="text/css" href="../../framework/admin/notification-styles/css/ns-style-theme.css" />

	<!-- Summernote -->
	<link href="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/summernote.min.css" rel="stylesheet">
	<link href=”http://ajax.googleapis.com/ajax/libs/jqueryui/1.8.16/themes/ui-lightness/jquery-ui.css” rel=”stylesheet”
		type=”text/css” />

	<link rel="stylesheet" href="../../framework/node_modules/intl-tel-input/build/css/intlTelInput.css">

	<link rel="stylesheet" href="./font/fas/css/all.css">

	<link rel="stylesheet" href="../../framework/node_modules/sweetalert2/dist/sweetalert2.css">

	<link rel="preconnect" href="https://fonts.googleapis.com">
	<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
	<link
		href="https://fonts.googleapis.com/css2?family=Old+Standard+TT:ital,wght@0,400;0,700;1,400&family=Poppins:wght@300;400;500;700&display=swap"
		rel="stylesheet">
	<link
		href="https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&family=Old+Standard+TT:ital,wght@0,400;0,700;1,400&family=Poppins:wght@300;400;500;700&family=Public+Sans:ital,wght@0,100..900;1,100..900&display=swap"
		rel="stylesheet">
</head>

<body>
	<div id="theme-wrapper">
		<header class="navbar" id="header-navbar">
			<div class="container">
				<a href="#/dashboard" id="logo" class="navbar-brand">
					<img src="images/logo.png" alt="" class="normal-logo logo-white" />
					<img src="images/logo-black.png" alt="" class="normal-logo logo-black" />
					<img src="images/logo-small.png" alt="" class="small-logo hidden-xs hidden-sm hidden" />
				</a>

				<div class="clearfix">
					<button class="navbar-toggle" data-target=".navbar-ex1-collapse" data-toggle="collapse"
						type="button">
						<span class="sr-only">Toggle navigation</span>
						<span class="fa fa-bars"></span>
					</button>

					<div class="nav-no-collapse navbar-left pull-left hidden-sm hidden-xs">
						<ul class="nav navbar-nav pull-left">
							<li>
								<a class="btn" id="make-small-nav">
									<i class="fa fa-bars"></i>
								</a>
							</li>
							<li class="dropdown hidden-xs">
								<a class="btn dropdown-toggle" data-toggle="dropdown">
									{{language}}
									<i class="fa fa-caret-down"></i>
								</a>
								<ul class="dropdown-menu">
									<li class="item">
										<a data-match-route="/language/繁體中文" href="#/language/繁體中文">
											繁體中文
										</a>
									</li>
									<!-- <li class="item">
										<a data-match-route="/language/简体中文" href="#/language/简体中文">
											简体中文
										</a>
									</li> -->
									<li class="item">
										<a data-match-route="/language/English" href="#/language/English">
											English
										</a>
									</li>
								</ul>
							</li>
						</ul>
					</div>

					<div class="nav-no-collapse pull-right" id="header-nav">
						<ul class="nav navbar-nav pull-right">
							<li class="dropdown profile-dropdown">
								<a class="dropdown-toggle" data-toggle="dropdown">
									<img src="../../framework/node_modules/zakumi-admin/img/avatar.png" alt="avatar" />
									<span class="hidden-xs">{{user_name}}</span> <b class="caret"></b>
								</a>
								<ul class="dropdown-menu dropdown-menu-right">
									<li><a href="#/profile"><i class="fa fa-user"></i>Profile</a></li>
									<li><a href="#/logout"><i class="fa fa-power-off"></i>Logout</a></li>
								</ul>
							</li>
						</ul>
					</div>
				</div>
			</div>
		</header>
		<div id="page-wrapper" class="container">
			<div class="row">
				<div id="nav-col">
					<section id="col-left" class="col-left-nano">
						<div id="col-left-inner" class="col-left-nano-content">

							<div class="collapse navbar-collapse navbar-ex1-collapse" id="sidebar-nav" bs-navbar>
								<ul class="nav nav-pills nav-stacked">
									<li data-match-route="/dashboard">
										<a href="#/dashboard" cy-data="dashboard">
											<!-- <i class="fa fa-dashboard"></i> -->
											<img class="sidemenu-icon" src="images/sidemenu-icon/dashboard.png"></img>
											<span class="sidemenu-label">{{translate.DASHBOARD}}</span>
										</a>
									</li>

									<li class="nav-header hidden-sm hidden-xs text-uppercase">Events</li>

									<li data-match-route="/events/golden-age">
										<a href="#/events/golden-age" cy-data="event-goldenage">
											<!-- <i class="fa fa-trophy"></i> -->
											<img src="images/sidemenu-icon/golden_age.png" class="sidemenu-icon" style="width: 18px; height: 18px;">
											<span class="sidemenu-label">{{translate.GOLDEN_AGE}}</span>
											<!-- <i class="fa fa-angle-right drop-icon"></i> -->
										</a>
										<!-- <ul class="submenu">
											<li data-match-route="/golden-age/" id="goldenAgeOverviewContent">
												<a href="#/golden-age/overview">
													<span>{{translate.TEAMS}}</span>
												</a>
											</li>
											<li data-match-route="/golden-age/participants" id="goldenAgeParticipantsContent">
												<a href="#/golden-age/participants">
													<span>{{translate.LEAGUE}}</span>
												</a>
											</li>
											<li data-match-route="/golden-age/overview" id="goldenAgeOverviewContent">
												<a href="#/golden-age/overview">
													<span>{{translate.CLUBS}}</span>
												</a>
											</li>
											<li data-match-route="/golden-age/participants" id="goldenAgeParticipantsContent">
												<a href="#/golden-age/participants">
													<span>{{translate.YEAR_GROUPS}}</span>
												</a>
											</li>
											<li data-match-route="/golden-age/overview" id="goldenAgeOverviewContent">
												<a href="#/golden-age/overview">
													<span>{{translate.TEAM_SHEET}}</span>
												</a>
											</li>
										</ul> -->
									</li>
									<li data-match-route="/events/district">
										<a href="#/events/district" cy-data="event-district">
											<!-- <i class="fa fa-map-marker"></i> -->
											<img class="sidemenu-icon" src="images/sidemenu-icon/district.png"></img>
											<span class="sidemenu-label">{{translate.DISTRICT}}</span>
										</a>
									</li>
									<li data-match-route="/events/pl-junior">
										<a href="#/events/pl-junior" cy-data="event-plj">
											<!-- <i class="fa fa-futbol-o"></i> -->
											<img class="sidemenu-icon" src="images/sidemenu-icon/pl_junior.png"></img>
											<span class="sidemenu-label">{{translate.PL_JUNIOR}}</span>
										</a>
									</li>
									<li data-match-route="/events/summer-scheme">
										<a href="#/events/summer-scheme" cy-data="event-summer-scheme">
											<!-- <i class="fa fa-sun-o"></i> -->
											<img class="sidemenu-icon" src="images/sidemenu-icon/summer_scheme.png"></img>
											<span class="sidemenu-label">{{translate.SUMMER_SCHEME}}</span>
										</a>
									</li>
									<li data-match-route="/events/regional" >
										<a href="#/events/regional" cy-data="event-regional">
											<!-- <i class="fa fa-flag"></i> -->
											<img class="sidemenu-icon" src="images/sidemenu-icon/regional.png"></img>
											<span class="sidemenu-label">{{translate.REGIONAL}}</span>
										</a>
									</li>									
									<li data-match-route="/events/beginner">
										<a href="#/events/beginner" cy-data="event-beginner">
											<!-- <i class="fa fa-child"></i> -->
											<img class="sidemenu-icon" src="images/sidemenu-icon/beginner.png"></img>
											<span class="sidemenu-label">{{translate.BEGINNER}}</span>
										</a>
									</li>
									
									<li data-match-route="/training_videos" id="trainingVideosContent"></li>

									<li class="nav-header hidden-sm hidden-xs text-uppercase">Others</li>
									
									<li data-match-route="/shop_products" id="shopProductsContent"></li>
									<!-- <li data-match-route="/respects" id="respectsContent"></li>
									<li data-match-route="/documents" id="documentsContent"></li> -->
									<li data-match-route="/respects|/documents" id="educationsContent"></li>
									<!-- <li data-match-route="/template*">
										<a href="#/template" cy-data="menu-email-editor">
											<img class="sidemenu-icon" src="images/sidemenu-icon/email_editor.png"></img>
											<span class="sidemenu-label">{{translate.SETTINGS_LIST.EMAIL_TEMPLATE}}</span>
										</a>
									</li> -->
									<!-- <li data-match-route="/course" id="courseContent"></li> -->

									<li class="nav-header hidden-sm hidden-xs text-uppercase">Setup</li>
									
									<li data-match-route="/tables" id="tablesContent"></li>
									<li data-match-route="/message-coach*" id="courseCoachContent"></li>
									<li data-match-route="/settings|/template" id="settingsContent"></li>
									<li data-match-route="/log" id="logContent"></li>

									<!-- <li data-match-route="/registrations*" id="registrationsContent"></li> -->
									<!-- <li data-match-route="/payments*" id="paymentsContent"></li> -->
									<!-- <li data-match-route="/payment_clubs*" id="paymentClubsContent"></li> -->
									<!-- <li data-match-route="/payment_teams*" id="paymentteamsContent"></li> -->
									<!-- <li data-match-route="/club_coaches*" id="ClubCoachesContent"></li>   									 -->
									<!-- <li data-match-route="/shipping*" id="shippingContent"></li> -->
									<!-- <li data-match-route="/self-pick-up*" id="selfPickUpContent"></li> -->
									<!-- <li data-match-route="/clubs*" id="clubsContent"></li> -->
									<!-- <li data-match-route="/seasons*" id="seasonsContent"></li> -->
									<!-- <li data-match-route="/leagues*" id="leaguesContent"></li> -->
									<!-- <li data-match-route="/admin_club_manager*" id="clubManagerContent"></li> -->
									<!-- <li data-match-route="/cgroups*" id="cgroupsContent"></li> -->
									<!-- <li data-match-route="/cgroups_admin*" id="cgroupsAdminContent"></li> -->
									<!-- <li data-match-route="/team_sheet*" id="teamSheetContent"></li> -->
									<!-- <li data-match-route="/messages*" id="messagesContent"></li> -->
									<!-- <li data-match-route="/reports*" id="reportsContent"></li> -->
									<!-- <li data-match-route="/certificates*" id="certificatesContent"></li> -->
									
									<!-- <li data-match-route="/template" id="emailTemplateContent"></li> -->
								</ul>
							</div>
						</div>
					</section>
					<div id="nav-col-submenu"></div>
				</div>
				<div id="content-wrapper">
					<div class="row">
						<div class="col-lg-12">
							<div class="slide-main-container">
								<div ng-view autoscroll="true" class="slide-main-animation"></div>
							</div>

						</div>
					</div>

					<div ng-include='"views/common/footer.html"'></div>
				</div>
			</div>
		</div>
	</div>


	<!-- global scripts -->
	<script src="app/cube/demo-skin-changer.js"></script> <!-- only for demo -->

	<script src="../../framework/admin/js/jquery.js"></script>
	<script src="https://ajax.googleapis.com/ajax/libs/jqueryui/1.10.3/jquery-ui.min.js"></script>
	<script src="../../framework/node_modules/bootstrap/dist/js/bootstrap.min.js"></script>
	<script src="../../framework/node_modules/bootstrap3-dialog/dist/js/bootstrap-dialog.min.js"></script>
	<script src="../../framework/node_modules/nanoscroller/bin/javascripts/jquery.nanoscroller.js"></script>
	<script src="../../framework/node_modules/moment/min/moment.min.js"></script>
	<script src="../../framework/node_modules/intl-tel-input/build/js/intlTelInput.min.js"></script>
	<script src="../../framework/node_modules/intl-tel-input/build/js/utils.js"></script>

	<script src="app/cube/demo.js"></script> <!-- only for demo -->

	<!-- data tables and editor -->
	<script src="../../framework/node_modules/datatables.net/js/jquery.dataTables.min.js"></script>
	<script src="../../framework/node_modules/datatables.net-bs/js/dataTables.bootstrap.min.js"></script>
	<script src="../../framework/admin/DataTables/RowReorder-1.2.8/js/dataTables.rowReorder.min.js"></script>
	<script src="../../framework/node_modules/datatables.net-responsive/js/dataTables.responsive.min.js"></script>
	<script src="../../framework/node_modules/datatables.net-responsive-bs/js/responsive.bootstrap.min.js"></script>

	<!-- Locale compare -->

	<script src="../../framework/admin/localeCompare/chineseCompare.js"></script>


	<script src="../../framework/node_modules/datatables.net-buttons/js/dataTables.buttons.min.js"></script>
	<script src="../../framework/node_modules/datatables.net-buttons-bs/js/buttons.bootstrap.min.js"></script>
	<script src="../../framework/node_modules/datatables.net-select/js/dataTables.select.min.js"></script>
	<script src="../../framework/admin/Editor-1.9.0/js/dataTables.editor.min.js"></script>
	<script src="../../framework/admin/Editor-1.9.0/js/editor.bootstrap.min.js"></script>
	<script src="../../framework/node_modules/editor-plugins/dist/js/editor.title.js"></script>
	<script src="../../framework/node_modules/select2/dist/js/select2.min.js"></script>
	<script src="../../framework/node_modules/editor-plugins/dist/js/editor.select2.js"></script>
	<script src="../../framework/node_modules/editor-plugins/dist/js/editor.telephone.js"></script>

	<script src="../../framework/node_modules/zakumi-admin/js/datetime-moment.js"></script>
	<script src="../../framework/node_modules/datatables.net-rowgroup/js/dataTables.rowGroup.min.js"></script>
	<script src="../../framework/node_modules/datatables.net-rowgroup-dt/js/rowGroup.dataTables.min.js"></script>
	<script src="../../framework/node_modules/datatables.net-dt/js/dataTables.dataTables.min.js"></script>

	<!-- button libs -->
	<script src="../../framework/node_modules/datatables.net-buttons/js/buttons.flash.min.js"></script>
	<script src="../../framework/node_modules/jszip/dist/jszip.min.js"></script>
	<script src="../../framework/node_modules/pdfmake/build/pdfmake.min.js"></script>
	<script src="../../framework/node_modules/pdfmake/build/vfs_fonts.js"></script>
	<!-- modified button, put in app/ -->
	<script src="../../framework/node_modules/datatables.net-buttons/js/buttons.html5.min.js"></script>
	<script src="../../framework/node_modules/datatables.net-buttons/js/buttons.print.min.js"></script>
	<script src="../../framework/node_modules/datatables.net-buttons/js/buttons.colVis.min.js"></script>


	<!-- Plugin Editor -->
	<script src="../../framework/node_modules/zakumi-ckeditor/ckeditor.js"></script>
	<script src="../../framework/node_modules/editor-plugins/dist/js/editor.ckeditor4.js"></script>
	<script src="../../framework/admin/js/ckeditor5/build/ckeditor-simple.js"></script>
	<script src="../../framework/admin/js/ckeditor5/build/editor.ckeditor5.js"></script>

	<!-- angular libs -->
	<script src="../../framework/node_modules/angular/angular.min.js"></script>
	<script src="../../framework/node_modules/angular-route/angular-route.min.js"></script>
	<script src="../../framework/node_modules/angular-animate/angular-animate.min.js"></script>
	<script src="../../framework/node_modules/angular-loading-bar/build/loading-bar.js"></script>
	<script src="./ckeditor/angular-ckeditor.js"></script>
	<script src="./ckeditor/angular-ckeditor.min.js"></script>
	<script src="../../framework/node_modules/angular-local-storage/dist/angular-local-storage.min.js"></script>
	<script src="../../framework/admin/typeahead/typeahead.js"></script>
	<!-- charts libs -->
	<script src="../../framework/node_modules/bootstrap-datepicker/dist/js/bootstrap-datepicker.js"></script>
	<script src="../../framework/node_modules/bootstrap-datepicker/dist/js/bootstrap-datepicker.min.js"></script>
	<script src="../../framework/node_modules/bootstrap-daterangepicker/daterangepicker.js"></script>
	<script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.0.4/popper.js"></script>
	<!-- config scripts -->
	<script src="app/config.js"></script>

	<!-- angular scripts -->
	<script src="app/app.js"></script>
	<script src="app/utility.js"></script>
	<script src="app/directives.js"></script>
	<script src="app/services/season.js"></script>
	<script src="app/profile/profile.js"></script>
	<script src="app/dashboard/dashboard.js"></script>
	<script src="app/events/golden-age/golden-age.js"></script>
	<script src="app/events/pl-junior/pl-junior.js"></script>
	<script src="app/events/summer-scheme/summer-scheme.js"></script>
	<script src="app/events/district/district.js"></script>
	<script src="app/events/beginner/beginner.js"></script>
	<script src="app/events/regional/regional.js"></script>
	<script src="app/registrations/registrations.js"></script>
	<script src="app/payments/payments.js"></script>
	<script src="app/payment_clubs/payment_clubs.js"></script>
	<script src="app/club_coaches/club_coaches.js"></script>
	<script src="app/clubs/clubs.js"></script>
	<script src="app/clubcoach/clubcoach.js"></script>
	<script src="app/payment_teams/payment_teams.js"></script>
	<script src="app/seasons/seasons.js"></script>
	<script src="app/leagues_group/leagues_group.js"></script>
	<script src="app/cgroups/cgroups.js"></script>
	<script src="app/cgroups_admin/cgroups_admin.js"></script>
	<script src="app/team_sheet/team_sheet.js"></script>
	<script src="app/messages/messages.js"></script>
	<script src="app/messages/messages_details/messages_details.js"></script>
	<script src="app/reports/reports.js"></script>
	<script src="app/reports/district_report/district_report.js"></script>
	<script src="app/reports/regional_report/regional_report.js"></script>
	<script src="app/reports/beginner_report/beginner_report.js"></script>
	<script src="app/reports/summer_scheme_report/summer_scheme_report.js"></script>
	<script src="app/tables/tables.js"></script>
	<script src="app/tables/league_admin/league_admin.js"></script>
	<script src="app/tables/super_admin/super_admin.js"></script>
	<script src="app/settings/settings.js"></script>
	<script src="app/tables/sponsor/sponsor.js"></script>
	<script src="app/league_detail/league_detail.js"></script>
	<script src="app/league_list/league_list.js"></script>
	<script src="app/tables/venues/venues.js"></script>
	<script src="app/settings/general_settings/general_settings.js"></script>
	<script src="app/language/language.js"></script>
	<script src="app/tournament/tournament.js"></script>
	<script src="app/tournament_group_stage/tournament_group_stage.js"></script>
	<script src="app/tournament_knockout_stage/tournament_knockout_stage.js"></script>
	<script src="app/admin_clubs/admin_clubs.js"></script>
	<script src="app/respects/respects.js"></script>
	<script src="app/training_scheme/training_scheme.js"></script>
	<script src="app/course/course.js"></script>
	<script src="app/course/date/date.js"></script>
	<script src="app/course/team_district/team_district.js"></script>
	<script src="app/course/team_district/team_details/team_details.js"></script>
	<script src="app/course/team_golden_age/team_golden_age.js"></script>
	<script src="app/course/team_golden_age/team_golden_age_player/team_golden_age_player.js"></script>
	<script src="app/registrations/registration-training-scheme/registration-training-scheme.js"></script>
	<script src="app/registrations/registration-summer-scheme/registration-summer-scheme.js"></script>
	<script src="app/registrations/registration-beginner/registration-beginner.js"></script>
	<script src="app/tables/products/products.js"></script>
	<script src="app/payments/summer-scheme/summer-scheme.js"></script>
	<script src="app/payments/district/district.js"></script>
	<script src="app/payments/pl-junior/pl-junior.js"></script>
	<script src="app/shipping/shipping.js"></script>
	<script src="app/shipping/shipping-pl-junior/shipping.js"></script>
	<script src="app/tables/users/users.js"></script>
	<script src="../../framework/admin/js/export_all_to_excel.js"></script>

	<script src="app/training_scheme/training_scheme_exercise/training_scheme_exercise.js"></script>
	<script src="app/certificate/certificate.js"></script>
	<script src="app/certificate/certificate-create-detail/certificate-create-detail.js"></script>
	<script src="app/certificate/certificate_print/certificate_print.js"></script>
	<script src="app/messages/messages_course_coach/messages_course_coach.js"></script>
	<script
		src="app/messages/messages_course_coach/messages_course_coach_detail/messages_course_coach_detail.js"></script>
	<script src="app/documents/documents.js"></script>
	<script src="app/documents/document_sections/document_sections.js"></script>
	<script src="app/registrations/registration-district/registration-district.js"></script>
	<script src="app/messages/message_team_coach/message-team-coach.js"></script>
	<script src="app/tables/player/player_report/player_report.js"></script>
	<script src="app/tables/finance_team/finance_team.js"></script>
	<script src="app/tables/grassroots_finance/grassroots_finance.js"></script>
	<script src="app/tables/shipping-user/shipping-user.js"></script>
	<script src="app/tables/supervisor/supervisor.js"></script>
	<script src="app/tables/supervisor_coach/supervisor_coach.js"></script>
	<script src="app/self-pick-up/self-pick-up.js"></script>
	<script src="app/registrations/registration-summer-scheme/offline-registration/offline-registration.js"></script>
	<script src="app/registrations/registration-pl-junior/registration-pl-junior.js"></script>
	<script src="app/reports/plj_report/plj_report.js"></script>
	<script src="app/reports/elderly_report/elderly_report.js"></script>
	<script src="app/tables/managerial_coach/managerial_coach.js"></script>
	<script src="app/shop_products/shop_products.js" type="text/javascript"></script>
	<script src="app/shop_product_details/shop_product_details.js" type="text/javascript"></script>
	<script src="app/tables/event/event_editor.js" type="text/javascript"></script>
	<script src="app/tables/coach-certificate/coach-certificate.js"></script>
	<script src="./app/tables/log/log.js"></script>
	<script src="./app/questionnaire/questionnaire.js"></script>
	<script src="./app/questionnaire/responses/responses.js"></script>
	<script src="./app/tables/player/player_editor.js"></script>
	<script src="./app/email_manager/email_manager.js"></script>
	<script src="./app/services/playerAssignmentService.js"></script>


	<!-- theme scripts -->
	<script src="../../framework/node_modules/zakumi-admin/js/scripts.js"></script>
	<script src="../../framework/node_modules/zakumi-admin/js/pace.min.js"></script>

	<!-- Spectrum -->
	<script src='../../framework/spectrum/spectrum.js'></script>
	<script src='./color-picker/color-picker.js'></script>

	<!-- datatable plugin -->
	<script src="../../framework/node_modules/datatables.net-plugins/sorting/date-dd-MMM-yyyy.js"></script>
	<script src="../../framework/node_modules/datatables.net-plugins/sorting/date-eu.js"></script>
	<script src="../../framework/node_modules/datatables.net-plugins/sorting/date-euro.js"></script>

	<!-- datatables checkboxes -->
	<script src="../../framework/node_modules/jquery-datatables-checkboxes/js/dataTables.checkboxes.min.js"></script>
	<script src="https://cdn.jsdelivr.net/npm/jquery-validation@1.19.2/dist/jquery.validate.js"
		type="text/javascript"></script>

	<!--Cropper JS-->
	<script src="../../framework/node_modules/cropperjs/dist/cropper.js" type="text/javascript"></script>

	<script src="../../framework/node_modules/sweetalert2/dist/sweetalert2.all.js"></script>

	<!-- ngIntlTelInput -->
	<script src="../../framework/node_modules/ng-intl-tel-input/dist/ng-intl-tel-input.js"></script>

	<!-- Notification Styles -->
	<script src="../../framework/admin/notification-styles/js/modernizr.custom.js"></script>
	<script src="../../framework/admin/notification-styles/js/snap.svg-min.js"></script>
	<script src="../../framework/admin/notification-styles/js/classie.js"></script>
	<script src="../../framework/admin/notification-styles/js/notificationFx.js"></script>

	<!-- Summernote -->
	<script src="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/summernote.min.js"></script>
	<script src="../../framework/node_modules/angular-summernote/dist/angular-summernote.js"></script>

	<!-- Flot Chart -->
	<script src="../../framework/admin/js/flot/jquery.flot.js"></script>
	<script src="../../framework/admin/js/flot/jquery.flot.min.js"></script>
	<script src="../../framework/admin/js/flot/jquery.flot.pie.min.js"></script>
	<script src="../../framework/admin/js/flot/jquery.flot.stack.min.js"></script>
	<script src="../../framework/admin/js/flot/jquery.flot.resize.min.js"></script>
	<script src="../../framework/admin/js/flot/jquery.flot.time.min.js"></script>
	<script src="../../framework/admin/js/flot/jquery.flot.orderBars.js"></script>

	<!-- Chart JS -->
	<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

	<!-- socket.io -->
	<script src="./../../framework/node_modules/socket.io-client/dist/socket.io.min.js"></script>
</body>

</html>