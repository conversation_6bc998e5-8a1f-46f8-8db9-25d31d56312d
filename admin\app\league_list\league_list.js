app.controller(
    'leagueListCtrl',
    function ($scope, $rootScope, $routeParams, $http) {
        $('#page-wrapper').removeClass('nav-small');
        var eventId = $routeParams.eventId;
        $scope.eventId = eventId;
        var groupName = $routeParams.groupName;
        $scope.groupName = groupName;
        var groupId = $routeParams.groupId;
        $scope.groupId = groupId;
        var eventName = $routeParams.eventName;
        $scope.eventName = eventName;
        var leagues_sub_type_list = [];

        jQuery.ajax({
            type: 'POST',
            url: SERVER_PATH + 'event/getEventInfo',
            async: false,
            headers: {	
                'x-user-id': $rootScope.user_id,
                'x-user-email': $rootScope.user_name
            },
            data: {
                event_id: eventId,
            },
            dataType: 'json',
            complete: function (response) {
                var jsonData = JSON.parse(response.responseText);
                var event = jsonData.info;
                $scope.event_name = event.name;
                $scope.event_type = event.type;
                $scope.normalizedType = normalizeEventType($scope.event_type);
            },
        });

        jQuery.ajax({
            type: 'POST',
            url: SERVER_PATH + 'league/getLeagueSubTypes',
            async: false,
            headers: {	
                'x-user-id': $rootScope.user_id,
                'x-user-email': $rootScope.user_name
            },
            data: {},
            dataType: 'json',
            complete: function (response) {
                var jsonData = JSON.parse(response.responseText);
                if (jsonData.status == 'OK') {
                    leagues_sub_type_list = jsonData.info;
                }
            },
        });

        function normalizeEventType(eventType) {
            switch (eventType) {
                case 'Summer Scheme':
                    return 'summer-scheme';
                case 'Regional':
                    return 'regional';
                case 'PL Junior':
                    return 'pl-junior';
                case 'Golden Age':
                    return 'golden-age';
                case 'Beginner':
                    return 'beginner';
                case 'District':
                    return 'district';
                default:
                    return '';
            }
        }

        /*- get html for modal add Add Relationship -*/

        /* get html for get child row add league to group */

        function format(id) {
            // `d` is the original data object for the row
            $content = '<h2 style="padding: unset">Leagues</h2>';
            $content +=
                '<div class="table-responsive">' +
                '<table id="groupLeagues_' +
                id +
                '" class="table table-striped table-bordered table-hover" cellspacing="0" width="100%">' +
                '<thead><tr>' +
                '<th>Name</th>' +
                '</tr></thead>' +
                '</table>';
            ('</div>');

            return $content;
        }

        /* get html for get child row add league to group */

        /* init table groupLeagueId to show modal when click button "Add Relation For Leagues" */

        /* init table groupLeagueId to show modal when click button "Add Relation For Leagues" */

        initGroupLeaguesTable(groupId, groupName, eventId);

        // init table show league by age groups

        function initGroupLeaguesTable(group_id, group_name, event_id) {
            var editorGroupLeagues = new $.fn.dataTable.Editor({
                ajax: {
                    type: 'POST',
                    url: SERVER_PATH + 'league/setGroupLeagues',
                    headers: {	
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name
                    },
                    data: {
                        group_id: group_id,
                    },
                    dataType: 'json',
                    complete: function (response) {
                        tableGroupLeagues.rows({ selected: true }).deselect();
                    },
                    error: function (xhr, status, error) {},
                },
                table: '#tblGroupLeagues',
                formOptions: {
                    main: {
                        onBlur: 'none',
                    },
                },
                i18n: {
                    create: {
                        button: 'Add',
                        title: 'Add new league',
                        submit: 'Add',
                    },
                    edit: {
                        button: 'Edit',
                        title: 'Edit league',
                        submit: 'Save',
                    },
                    remove: {
                        button: 'Delete',
                        title: 'Delete league',
                        submit: 'Delete',
                        confirm: {
                            _: 'Are you sure you want to delete %d leagues? All teams, matches and results will be deleted.',
                            1: 'Are you sure you want to delete the league? All teams, matches and results will be deleted.',
                        },
                    },
                    error: {
                        system: 'System error, please contact administrator.',
                    },
                },
                fields: [
                    {
                        label: 'Group ID:',
                        name: 'leagues.group_id',
                        type: 'hidden',
                        def: group_id,
                    },
                    {
                        label: 'Name:',
                        name: 'leagues.name',
                    },
                    {
                        label: 'League Type:',
                        name: 'leagues.type',
                        type: 'radio',
                        options: [
                            {
                                label: TYPE_LEAGUE.text,
                                value: TYPE_LEAGUE.value,
                            },
                            {
                                label: TYPE_TOURNAMENT.text,
                                value: TYPE_TOURNAMENT.value,
                            },
                        ],
                        def: TYPE_LEAGUE.value,
                    },
                    {
                        label: 'Type:',
                        name: 'leagues.subtype_id',
                        type: 'radio',
                        def: 1,
                    },
                    {
                        label: 'Match duration (minute):',
                        name: 'leagues.match_duration',
                        attr: {
                            type: 'number',
                        },
                    },
                    {
                        label: 'Tie Breaker',
                        name: 'leagues.tie-breaker',
                        type: 'select2',
                        opts: {
                            multiple: 'multiple',
                            tags: true,
                            closeOnSelect: true,
                            placeholder: 'Select tie breaker',
                            tokenSeparators: [',', ' '],
                        },
                    },
                    {
                        label: 'Display TBD Match:',
                        name: 'leagues.display_tbd_match',
                        type: 'radio',
                        options: [
                            {
                                label: 'Yes',
                                value: 1,
                            },
                            {
                                label: 'No',
                                value: 0,
                            },
                        ],
                        def: 1,
                    },
                    {
                        label: 'Release',
                        name: 'leagues.release',
                        type: 'radio',
                        options: [
                            {
                                label: 'Yes',
                                value: 1,
                            },
                            {
                                label: 'No',
                                value: 0,
                            },
                        ],
                        def: 0,
                    },
                    {
                        label: 'order',
                        name: 'leagues.order',
                        type: 'hidden',
                    },
                    {
                        label: 'league_edit',
                        name: 'league_edit',
                        type: 'hidden',
                        def: 0,
                    },
                ],
            });

            editorGroupLeagues.dependent('leagues.type', function (val) {
                var results = leagues_sub_type_list.filter(function (element) {
                    if (parseInt(element.league_type) == val) {
                        return true;
                    } else {
                        return false;
                    }
                });
                console.log(leagues_sub_type_list);
                editorGroupLeagues.field('leagues.subtype_id').update(results);
            });

            editorGroupLeagues.on('preOpen', function (e, main, action) {
                editorGroupLeagues.val('league_edit', 1);
            });

            tableGroupLeagues = $('#tblGroupLeagues').DataTable({
                dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
                stateSave: true,
                deferRender: true,
                ajax: {
                    url: SERVER_PATH + 'league/getGroupLeagues',
                    type: 'POST',
                    headers: {	
                        'x-user-id': $rootScope.user_id,
                        'x-user-email': $rootScope.user_name
                    },
                    data: {
                        group_id: group_id,
                    },
                    dataType: 'json',
                    complete: function (response) {
                        Swal.close();
                        // response = JSON.parse(response.responseText);
                    },
                    error: function (xhr, status, error) {},
                    beforeSend: function (xhr) {
                        Swal.fire({
                            title: 'Please Wait!',
                            allowOutsideClick: false,
                            didOpen: () => {
                                Swal.showLoading();
                            },
                        });
                    }
                },
                language: {
                    paginate: {
                        previous: '<i class="fa fa-chevron-left"></i>',
                        next: '<i class="fa fa-chevron-right"></i>',
                    },
                },
                columns: [
                    {
                        data: 'leagues.name',
                        render: function (data, type, row) {
                            switch (parseInt(row.leagues.type)) {
                                case TYPE_LEAGUE.value: {
                                    return (
                                        '<a data-match-route="/leagues/' +
                                        event_id +
                                        '/' +
                                        row.leagues.id +
                                        '?eventName=' +
                                        eventName +
                                        '&groupName= ' +
                                        group_name +
                                        '&leagueName=' +
                                        row.leagues.name +
                                        '&leagueType=' +
                                        row.leagues.subtype_id +
                                        '" href="#/leagues/' +
                                        event_id +
                                        '/' +
                                        row.leagues.id +
                                        '?eventName=' +
                                        eventName +
                                        '&groupName= ' +
                                        group_name +
                                        '&leagueName=' +
                                        row.leagues.name +
                                        '&leagueType=' +
                                        row.leagues.subtype_id +
                                        '&groupIdSelected=' +
                                        groupId +
                                        '">' +
                                        row.leagues.name +
                                        '</a>'
                                    );
                                    break;
                                }
                                case TYPE_TOURNAMENT.value: {
                                    return (
                                        '<a data-match-route="/leagues/leagueTournament/' +
                                        event_id +
                                        '/' +
                                        row.leagues.id +
                                        '?eventName=' +
                                        eventName +
                                        '&groupName= ' +
                                        group_name +
                                        '&leagueName=' +
                                        row.leagues.name +
                                        '" href="#/leagues/leagueTournament/' +
                                        event_id +
                                        '/' +
                                        row.leagues.id +
                                        '?eventName=' +
                                        eventName +
                                        '&groupName= ' +
                                        group_name +
                                        '&leagueName=' +
                                        row.leagues.name +
                                        '&groupIdSelected=' +
                                        groupId +
                                        '">' +
                                        row.leagues.name +
                                        '</a>'
                                    );
                                    break;
                                }
                            }
                        },
                        sortable: false,
                    },
                    {
                        data: 'leagues.type_name',
                        sortable: false,
                    },
                    {
                        data: 'league_subtypes.name',
                        sortable: false,
                    },
                    {
                        data: 'leagues.match_duration',
                        render: function (data) {
                            return data + ' minutes';
                        },
                        sortable: false,
                    },
                    {
                        data: 'leagues.tie-breaker-list',
                        sortable: false,
                    },
                    {
                        data: 'leagues.display_tbd_match',
                        render: function (data) {
                            return data == 1 ? 'Yes' : 'No';
                        },
                        sortable: false,
                    },
                    {
                        data: 'leagues.release',
                        render: function (data) {
                            return data == 1 ? 'Yes' : 'No';
                        },
                        sortable: false,
                    },
                    {
                        data: 'leagues.order',
                        visible: false,
                        sortable: false,
                    },
                ],
                rowReorder: {
                    dataSrc: 'leagues.order',
                    editor: editorGroupLeagues,
                },
                select: {
                    style: 'single',
                },
                buttons: [
                    {
                        extend: 'create',
                        editor: editorGroupLeagues,
                        text: 'Add',
                    },
                    {
                        extend: 'edit',
                        editor: editorGroupLeagues,
                        text: 'Edit',
                    },
                    {
                        extend: 'remove',
                        editor: editorGroupLeagues,
                        text: 'Delete',
                    },
                ],
                order: [[7, 'asc']],
                columnDefs: [
                    {
                        visible: false,
                        target: [7],
                    },
                ],
                displayLength: -1,
            });

            editorGroupLeagues.on('preSubmit', function (e, data, action) {
                if (action == 'remove') {
                    return confirm('Ask again, are you sure?');
                }
            });

            editorGroupLeagues.on('postCreate postRemove', function () {
                tableGroupLeagues.ajax.reload(null, false);
            });

            editorGroupLeagues.on('open', function (e, data, action) {
                var regex = new RegExp('([0-9]+)');
                var results = regex.exec(groupName);
                console.log(results);
                if (parseInt(results[1]) <= 10 && action == 'create') {
                    editorGroupLeagues.field('leagues.type').enable();
                } else {
                    editorGroupLeagues.field('leagues.type').disable();
                }
                $('select').on('select2:select', function (evt) {
                    var element = evt.params.data.element;
                    var $element = $(element);
                    $element.detach();
                    $(this).append($element);
                    $(this).trigger('change');
                });
                var data = tableGroupLeagues.rows({ selected: true }).data();
                if (typeof data[0] != 'undefined' && action == 'edit') {
                    var tieBreaker = [
                        //{ text: 'Head To Head', id: 1 },
                        { text: 'Goal Difference', id: 2 },
                        { text: 'Goal Scored', id: 3 },
                        { text: 'Goal Conceded', id: 4 },
                        { text: 'Respect Score', id: 5 },
                        { text: 'Head to Head Goals Scored', id: 6 },
                        { text: 'Head to Head Goal Difference', id: 8 },
                        { text: 'Head to Head Points', id: 9 },
                    ];
                    tieBreakerSelected = data[0]['leagues']['tie-breaker'];
                    if (typeof data[0] != 'undefined') {
                        var tieBreakerUpdate = [];
                        var index = tieBreakerSelected.length;
                        tieBreaker.forEach((e) => {
                            var flat = 0;
                            tieBreakerSelected.forEach((v, k) => {
                                if (e.id == v) {
                                    tieBreakerUpdate[k] = e;
                                    flat = 1;
                                }
                            });
                            if (flat == 0) {
                                tieBreakerUpdate[index] = e;
                                index++;
                            }
                        });
                        html_select = '';
                        tieBreakerUpdate.forEach((e) => {
                            html_select +=
                                "<option value='" +
                                e.id +
                                "'>" +
                                e.text +
                                '</option>';
                        });
                        $('#DTE_Field_leagues-tie-breaker').empty();
                        $('#DTE_Field_leagues-tie-breaker').append(html_select);
                        $('#DTE_Field_leagues-tie-breaker').select2();
                        $('#DTE_Field_leagues-tie-breaker')
                            .val(tieBreakerSelected)
                            .trigger('change');
                    }
                }
            });
        }

        // init table show league by age groups
    }
);
