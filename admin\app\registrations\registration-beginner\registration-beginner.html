<style>
    .shipping_chkbox {
        width: 20px;
        height: 20px;
        border: 1px solid #ccc;
        border-radius: 5px;
        margin: auto;
    }

    .shipping_chkbox:checked {
        width: 20px;
        height: 20px;
        border: 1px solid #ccc;
        border-radius: 5px;
        margin: auto;
    }

    .toggle-switch {
        display: inline-block;
        width: 60px;
        height: 28px;
        background-color: #ccc;
        border-radius: 17px;
        position: relative;
        cursor: pointer;
    }

    .toggle-switch::before {
        content: "";
        position: absolute;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background-color: #fff;
        top: 4px;
        left: 4px;
        transition: transform 0.3s ease;
    }

    #goalkeeper:checked+.toggle-switch::before {
        transform: translateX(30px);
    }

    #goalkeeper:checked+.toggle-switch {
        background-color: red;
    }

    #goalkeeper {
        display: none;
    }

    #goalkeeper-content .form-control {
        margin-top: 7%;
        display: flex;
        align-items: center;
        gap: 16px;
    }

    #goalkeeper-content .form-control {
        background-color: unset !important;
        padding: 0px;
    }

    #goalkeeper-content .form-control label {
        margin: 0px;
    }
</style>
<div class="row" id="tab-page">
    <div class="row">
        <div class="col-lg-12">
            <ol class="breadcrumb">
                <li>Events</li>
                <li><a href="" ng-click="goBack()">{{event_type}}</a></li>
                <li>{{event_name}}</li>
                <li class="active"><span>Registrations</span></li>
            </ol>
        </div>
    </div>
</div>
<div class="row">
    <div class="col-lg-12">
        <div class="clearfix">
            <h1 class="pull-left">Registrations</h1>
        </div>
    </div>
</div>

<!-- form -->
<form role="form">
    <div class="row">
        <!-- Location -->
        <div class="form-group col-lg-3" id="age-group-content">
            <label>Filter by age group</label>
        </div>
        <div class="form-group col-lg-3" id="district-content">
            <label>Filter by district</label>
        </div>
        <div class="form-group col-lg-3" id="approval-content">
            <label>Filter by approval status </label>
        </div>
        <div class="form-group col-lg-3" id="goalkeeper-content">
            <div class="form-control">
                <input type="checkbox" id="goalkeeper" />
                <label for="goalkeeper" class="toggle-switch"></label>
                Filter by goalkeeper
            </div>
        </div>
    </div>
</form>

<div class="row">
    <div class="col-lg-12">
        <div class="main-box clearfix">
            <div class="main-box-body clearfix">
                <div class="table-responsive">
                    <table id="table_beginner" class="table table-striped table-bordered table-hover" cellspacing="0"
                        width="100%">
                        <thead>
                            <tr>
                                <th></th>
                                <th>Photo</th>
                                <th>Player</th>
                                <th>Chinese name</th>
                                <th>DOB</th>
                                <th>Gender</th>
                                <th>Parent</th>
                                <th>Parent email</th>
                                <th>Parent phone</th>
                                <th>Age group</th>
                                <th>District/Region</th>
                                <th>Goalkeeper</th>
                                <th>Registered date</th>
                                <th>Status</th>
                                <th>Emailed</th>
                                <th>Sent invoice email</th>
                                <th>Approved District</th>
                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>