# create action to build production and upload to google play (ionic appflow)

name: Build and upload to Google Play
on:
  workflow_dispatch:
jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      #checkout the repo from input
      - uses: actions/checkout@v2
        # show current branch
      - name: Show current branch
        run: echo ${{ github.ref }}
      - name: Setup node 16
        uses: actions/setup-node@v1
        with:
          node-version: 16.x
      - run: npm i -g @ionic/cli
      - run: npm install -g @capacitor/cli
      - run: cd client && npm i --force && npx jetify  && npm run build-android-prod
      - name: Set up JDK 11
        uses: actions/setup-java@v1
        with:
          java-version: 17.0.6
      - name: Setup Gradle
        uses: gradle/gradle-build-action@v2
      - name: Make gradlew executable
        run: cd client && cd android && chmod +x ./gradlew
      - name: Build the app
        run: cd client && cd android && ./gradlew bundle
      - uses: r0adkll/upload-google-play@v1
        with:
          track: internal
          serviceAccountJsonPlainText: ${{ secrets.SERVICE_ACCOUNT_JSON }}
          releaseFiles: client/android/app/build/outputs/bundle/release/app-release.aab
          status: draft
          packageName: com.ezactive.hkfa
