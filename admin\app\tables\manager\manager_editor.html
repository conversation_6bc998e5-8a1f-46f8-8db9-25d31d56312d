<div class="row">
    <div class="col-lg-12">
        <div class="clearfix">
            <h1 class="pull-left">Club Managers</h1>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-12">
        <div class="main-box clearfix">
            <div class="main-box-body clearfix">
                <div class="table-responsive">
                    <table id="tableClubManager" class="table table-striped table-bordered table-hover" cellspacing="0"
                        width="100%">
                        <thead>
                            <tr>
                                <th>Surname</th>
                                <th>Other name</th>
                                <th>Email</th>
                                <th>Phone</th>
                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript" language="javascript" class="init">

    $(document).ready(function () {
        user_id = localStorage.getItem('hkjflApp.user_id');
		user_name = localStorage.getItem('hkjflApp.user_name');
        var table;
        var editorClubManager = new $.fn.dataTable.Editor({
            ajax: {
                type: 'POST',
                url: SERVER_PATH + "paren/setClubManagers",
                headers: {	
                    'x-user-id': user_id,
                    'x-user-email': user_name
                },
                data: {
                    // "pgroup_id": pgroup_id
                },
                dataType: 'json',
                complete: function (response) {
                    var jsonData = JSON.parse(response.responseText);
                    // --- may need to reload
                    if (DEVELOPMENT_ENVIRONMENT) console.log('status = ' + jsonData.status);
                    if (jsonData.status == 'OK') {
                        if (DEVELOPMENT_ENVIRONMENT) console.log('Before reload');
                        tableClubManager.ajax.reload();
                    }
                },
                error: function (xhr, status, error) {
                },
            },
            table: "#tableClubManager",
            formOptions: {
                main: {
                    onBlur: 'none'
                }
            },
            i18n: {
                create: {
                    button: "New",
                    title: "Create new club manager",
                    submit: "Create"
                },
                edit: {
                    button: "Edit",
                    title: "Edit club manager",
                    submit: "Save"
                },
                remove: {
                    button: "Delete",
                    title: "Delete club manager",
                    submit: "Delete",
                    confirm: {
                        _: "Are you sure you want to delete these club managers?",
                        1: "Are you sure you want to delete this club manager?"
                    }
                },
                error: {
                    system: "System error, please contact club manager."
                },
            },
            fields: [
                {
                    label: "Surname:",
                    name: "parens.surname"
                }, {
                    label: "Other name",
                    name: "parens.other_name"
                }, {
                    label: "Email",
                    name: "parens.email"
                }, {
                    name: "parens.country_code",
                    type: "hidden",
                    def: '+852'
                },
                {
                    name: "parens.iso_code",
                    type: "hidden",
                    def: 'hk'
                },
                {
                    label: "Phone number",
                    name: "parens.phone",
                    type: "telephone",
                    opts: {
                        preferredCountries: ['hk', 'cn'],
                        initialCountry: 'hk'
                    }
                }, {
                    name: "parens.type",
                    type: "hidden",
                    def: TYPE_CLUB_ADMINISTRATOR
                },
                {
                    label: "Create user:",
                    name: "create_user",
                    type: "checkbox",
                    separator: "|",
                    options: [
                        { label: '', value: 1 }
                    ]
                },
            ]
        });

        var tableClubManager = $('#tableClubManager').DataTable({
            dom: '<"row"B><"col-sm-6"l><"col-sm-6"f>rt<"col-sm-6"i><"col-sm-6"p>',
            stateSave: true,
            deferRender: true,
            ajax: {
                url: SERVER_PATH + "paren/getClubManagers",
                type: 'POST',
                headers: {	
                    'x-user-id': user_id,
                    'x-user-email': user_name
                },
                data: {
                    // "pgroup_id": pgroup_id
                },
                dataType: 'json',
                complete: function (response) {
                },
                error: function (xhr, status, error) {
                },
            },
            language: {
                info: "Showing _START_ to _END_ of _TOTAL_ club manager",
                infoEmpty: "Showing 0 to 0 of 0 club managers",
                lengthMenu: "Show _MENU_ club managers",
                select: {
                    rows: {
                        "_": "You have selected %d club managers",
                        "0": "Click a club manager to select",
                        "1": "1 club manager selected"
                    },
                },
                paginate: {
                    previous: '<i class="fa fa-chevron-left"></i>',
                    next: '<i class="fa fa-chevron-right"></i>'
                }
            },
            columns: [
                { data: "parens.surname" },
                { data: "parens.other_name" },
                { data: "parens.email" },
                { data: "parens.phone" }
            ],
            select: {
                style: 'single',
                selector: 'td:first-child',
            },
            order: [[1, 'asc']],
            lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "All"]],
            buttons: [
                { extend: "create", editor: editorClubManager },
                { extend: "edit", editor: editorClubManager },
                {
                    extend: 'selectedSingle',
                    text: 'Change club manager to managerial coach',
                    action: function (e, dt, node, config) {
                        let data = tableClubManager.rows({ selected: true }).data();

                        let parent_id = data[0]['parens']['id'];

                        editorCoach = new $.fn.dataTable.Editor({
                            ajax: {
                                type: 'POST',
                                url: SERVER_PATH + "paren/changeClubManagerToManagerialCoach",
                                async: false,
                                headers: {	
                                    'x-user-id': user_id,
                                    'x-user-email': user_name
                                },
                                data: {
                                    'parent_id': parent_id
                                },
                                dataType: 'json',
                                complete: function (response) {
                                    var jsonData = JSON.parse(response.responseText);
                                    if (typeof jsonData.fieldErrors == 'undefined') {
                                        if (jsonData.status == "OK") {
                                            BootstrapDialog.show({
                                                title: 'SUCCESS',
                                                type: BootstrapDialog.TYPE_SUCCESS,
                                                message: jsonData.message
                                            });

                                            tableClubManager.ajax.reload();
                                        } else {
                                            BootstrapDialog.show({
                                                title: 'Error',
                                                type: BootstrapDialog.TYPE_DANGER,
                                                message: jsonData.message
                                            });
                                        }
                                    }
                                },
                                error: function (xhr, status, error) {

                                },
                            },
                            formOptions: {
                                main: {
                                    onBlur: 'none'
                                }
                            },
                            fields: [
                                {
                                    label: "Coach ID:",
                                    name: "coach_id",
                                }
                            ]
                        });
                        editorCoach
                            .title('Add to managerial coach')
                            .buttons(
                                {
                                    label: "Submit",
                                    fn: function () { this.submit(); }
                                }
                            )
                            .edit()
                            .open();
                    }
                },
                { extend: "remove", editor: editorClubManager },
                { extend: 'colvis', text: 'Columns' }
            ]
        });
        editorClubManager.on('initEdit', function (e, type) {
            editorClubManager.disable('create_user');
            editorClubManager.hide('create_user');
        });
        editorClubManager.on('initCreate', function (e, type) {
            editorClubManager.enable('create_user');
            editorClubManager.show();
        });
    });

</script>